# Android项目无用资源终极扫描器

这是一个专为Android项目设计的无用资源和类文件扫描工具，支持现代Android开发模式，帮助您准确识别可能的冗余文件。

## 🚀 功能特性

- 🔍 **终极智能扫描**: 支持ViewBinding、DataBinding、Navigation组件
- 📊 **详细报告**: 生成Markdown格式的详细扫描报告，包含安全等级标识
- 🌍 **多语言友好**: 自动排除多语言资源文件，避免误报
- 📱 **现代Android支持**: 专门针对现代Android开发模式优化
- 🎯 **精确分析**: 支持多种引用模式检测，大幅减少误报
- 🛡️ **安全分析**: 仅进行分析，不会自动删除任何文件
- 📈 **进度显示**: 实时显示扫描进度，用户体验友好

## 支持的文件类型

### 资源文件
- 图片资源: `.png`, `.jpg`, `.jpeg`, `.webp`, `.gif`, `.svg`
- XML资源: 布局文件、drawable、values、动画等
- 其他资源: raw、mipmap等

### 代码文件
- Java文件: `.java`
- Kotlin文件: `.kt`

## 安装要求

- Python 3.6+
- Android项目（包含`app/build.gradle`或`app/build.gradle.kts`）

## 使用方法

### 1. 快速开始

将脚本文件复制到您的Android项目根目录，然后运行：

```bash
# 方法1: 使用简化脚本（推荐）
python run_scanner.py

# 方法2: 直接使用主脚本
python android_unused_scanner.py

# 方法3: 扫描指定项目
python android_unused_scanner.py /path/to/your/android/project
```

### 2. 查看扫描报告

扫描完成后，会在项目根目录生成详细报告：
- `unused_resources_final_report_YYYYMMDD_HHMMSS.md` - 终极扫描报告

报告包含以下信息：
- 🔧 **扫描特性说明**: ViewBinding/DataBinding/Navigation支持状态
- 📸 **未使用资源文件**: 按类型分组，包含安全等级标识
- 💻 **未使用类文件**: 按语言分组，包含类型说明
- 📊 **ViewBinding映射统计**: 显示布局文件的Binding映射状态
- ⚠️ **删除建议**: 按安全等级分类的删除建议
- 🔧 **验证步骤**: 详细的验证指导

### 3. 安全等级说明

报告中的安全等级标识：
- 🟢 **相对安全**: 如`.DS_Store`系统文件，可优先删除
- 🟡 **需要谨慎**: 如布局文件、工具类，建议手动验证
- 🔴 **高风险**: 如应用图标、主要组件，强烈建议保留

### 4. 手动清理文件

⚠️ **重要**: 本工具只进行分析，不会自动删除文件。请根据报告手动检查和删除文件。

建议的清理流程：
1. 优先删除🟢标记的文件（如`.DS_Store`）
2. 谨慎处理🟡标记的文件，使用IDE的"Find Usages"功能验证
3. 保留🔴标记的文件
4. 分批删除并测试项目功能

## 命令行参数

### android_unused_scanner.py
```bash
python android_unused_scanner.py [项目路径]

参数:
  项目路径              Android项目根目录路径（默认: 当前目录）

特性:
  ✅ 自动检测ViewBinding/DataBinding
  ✅ 自动检测Navigation组件
  ✅ 自动排除多语言资源
  ✅ 实时进度显示
  ✅ 安全等级标识

示例:
  python android_unused_scanner.py                     # 扫描当前项目
  python android_unused_scanner.py /path/to/project    # 扫描指定项目
```

## 扫描原理

### 资源文件检测
1. 扫描`app/src/main/res`目录下的所有资源文件
2. 在以下位置查找引用：
   - Java/Kotlin源码中的`R.xxx.xxx`引用
   - XML文件中的`@xxx/xxx`引用
   - 字符串形式的资源名引用
   - AndroidManifest.xml中的引用

### 类文件检测
1. 扫描`app/src/main/java`目录下的所有Java/Kotlin文件
2. 在以下位置查找引用：
   - import语句
   - 类名直接引用
   - AndroidManifest.xml中的组件声明

## 注意事项

### ⚠️ 重要警告
1. **动态引用**: 脚本无法检测通过反射、字符串拼接等方式的动态引用
2. **第三方库**: 某些资源可能被第三方库使用
3. **测试代码**: 确保检查测试代码中的引用
4. **配置文件**: 某些类可能在配置文件中被引用

### 🛡️ 安全建议
1. **仔细检查**: 手动检查报告中的每个文件
2. **备份项目**: 在删除文件前务必备份整个项目
3. **分批清理**: 建议分批删除并测试
4. **测试运行**: 删除文件后进行完整的功能测试
5. **检查Git信息**: 利用提交者信息联系相关开发者确认

## 示例输出

### 扫描过程
```
🔍 开始扫描Android项目...
📁 项目根目录: /path/to/your/project
📦 扫描模块: app
📋 检测到Git仓库，将获取文件提交信息...

📸 扫描资源文件...
📂 扫描模块 app 的资源文件...
   找到 1250 个资源文件
📋 总计找到 1250 个资源文件

💻 扫描代码文件...
📂 扫描模块 app 的代码文件...
   找到 320 个代码文件
📋 总计找到 320 个代码文件

🔗 分析引用关系...
🔍 分析资源引用...
🔍 分析类引用...
📊 发现 45 个未使用的资源文件
📊 发现 12 个未使用的类文件

📊 生成扫描报告...
📄 报告已生成:
   Markdown: unused_resources_report_20231201_143022.md
   JSON: unused_resources_report_20231201_143022.json

✅ 扫描完成！
```

### 报告示例
生成的Markdown报告包含以下信息：

```markdown
## 📸 未使用的资源文件

### 模块: app

#### Image/Drawable-Xxhdpi (15 个文件)

| 文件名 | 路径 | 大小 | 最后提交者 | 提交时间 | 提交信息 |
|--------|------|------|-----------|----------|----------|
| icon_unused.png | drawable-xxhdpi/icon_unused.png | 12.3 KB | 张三 | 2023-11-15 | 添加新图标资源 |
| old_logo.webp | drawable-xxhdpi/old_logo.webp | 8.7 KB | 李四 | 2023-10-20 | 更新应用logo |

#### 贡献者统计

| 贡献者 | 提交次数 |
|--------|----------|
| 张三 <<EMAIL>> | 8 |
| 李四 <<EMAIL>> | 5 |
```

## 故障排除

### 常见问题

1. **"不是有效的Android项目"**
   - 确保在Android项目根目录运行
   - 检查是否存在`app/build.gradle`文件

2. **"扫描结果不准确"**
   - 检查是否有动态引用
   - 确认第三方库的使用情况

3. **"删除后项目无法运行"**
   - 从备份目录恢复文件
   - 检查是否有遗漏的引用

### 获取帮助

如果遇到问题，请：
1. 检查Python版本（需要3.6+）
2. 确认项目结构正确
3. 查看详细的错误信息
4. 使用`--verbose`参数获取更多信息

## 许可证

本工具仅供学习和项目优化使用，使用时请遵循相关开源协议。

---

**免责声明**: 使用本工具删除文件前，请务必备份项目。作者不对因使用本工具造成的任何损失承担责任。
