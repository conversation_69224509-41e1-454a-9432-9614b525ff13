#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目无用资源终极扫描器
支持ViewBinding、DataBinding、Navigation组件，排除多语言资源
"""

import os
import re
import time
import sys
from pathlib import Path
from collections import defaultdict

def print_progress(current, total, prefix="进度"):
    """打印进度条"""
    percent = (current / total) * 100
    bar_length = 30
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    sys.stdout.write(f'\r{prefix}: |{bar}| {current}/{total} ({percent:.1f}%)')
    sys.stdout.flush()

def layout_to_binding_class(layout_name):
    """将布局文件名转换为ViewBinding类名"""
    parts = layout_name.split('_')
    camel_case = ''.join(word.capitalize() for word in parts)
    return f"{camel_case}Binding"

def is_multilingual_resource(file_path):
    """判断是否为多语言资源文件"""
    parent_dir = file_path.parent.name
    # 检查是否为多语言目录
    multilingual_patterns = [
        r'values-[a-z]{2}$',           # values-en
        r'values-[a-z]{2}-r[A-Z]{2}$', # values-en-rUS
        r'values-[a-z]{2}-[a-z]{2}$',  # values-zh-cn
        r'drawable-[a-z]{2}$',         # drawable-en
        r'layout-[a-z]{2}$',           # layout-en
    ]
    
    for pattern in multilingual_patterns:
        if re.match(pattern, parent_dir):
            return True
    
    return False

def scan_android_project(project_root):
    """扫描Android项目"""
    project_path = Path(project_root)
    app_res = project_path / "app" / "src" / "main" / "res"
    app_java = project_path / "app" / "src" / "main" / "java"
    
    print(f"🔍 Android无用资源终极扫描器")
    print(f"📁 项目路径: {project_path}")
    
    if not app_res.exists():
        print("❌ 找不到app/src/main/res目录")
        return
    
    if not app_java.exists():
        print("❌ 找不到app/src/main/java目录")
        return
    
    # 检查项目配置
    print("🔧 检查项目配置...")
    has_view_binding = check_view_binding(project_path)
    has_data_binding = check_data_binding(project_path)
    has_navigation = check_navigation_component(app_res)
    has_git = is_git_repo(project_path)

    if has_view_binding:
        print("✅ 检测到ViewBinding已启用")
    if has_data_binding:
        print("✅ 检测到DataBinding已启用")
    if has_navigation:
        print("✅ 检测到Navigation组件")
    if has_git:
        print("✅ 检测到Git仓库，将获取提交信息")
    else:
        print("⚠️ 未检测到Git仓库，无法获取提交信息")
    
    # 收集所有资源文件（排除多语言）
    print("📸 收集资源文件...")
    resource_files = []
    for file_path in app_res.rglob("*"):
        if file_path.is_file() and not is_multilingual_resource(file_path):
            resource_files.append(file_path)
    
    print(f"   找到 {len(resource_files)} 个资源文件（已排除多语言资源）")
    
    # 收集所有代码文件
    print("💻 收集代码文件...")
    code_files = []
    for file_path in app_java.rglob("*"):
        if file_path.is_file() and file_path.suffix in {'.java', '.kt'}:
            code_files.append(file_path)
    
    # 收集XML文件（包括Navigation）
    xml_files = []
    for file_path in app_res.rglob("*.xml"):
        if not is_multilingual_resource(file_path):
            xml_files.append(file_path)
    
    all_check_files = code_files + xml_files
    print(f"   找到 {len(code_files)} 个代码文件, {len(xml_files)} 个XML文件")
    
    # 生成ViewBinding映射
    print("🔗 生成ViewBinding映射...")
    binding_mappings = {}
    layout_files = [f for f in xml_files if f.parent.name.startswith('layout')]
    for layout_file in layout_files:
        layout_name = layout_file.stem
        binding_class = layout_to_binding_class(layout_name)
        binding_mappings[layout_name] = binding_class
    
    print(f"   生成了 {len(binding_mappings)} 个Binding映射")
    
    # 读取所有文件内容（带进度）
    print("📖 读取文件内容...")
    all_content_map = {}
    total_files = len(all_check_files)
    
    for i, file_path in enumerate(all_check_files):
        if i % 100 == 0:
            print_progress(i + 1, total_files, "读取文件")
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                all_content_map[str(file_path)] = f.read()
        except Exception:
            all_content_map[str(file_path)] = ""
    
    print(f"\n   ✅ 读取了 {len(all_content_map)} 个文件")
    
    # 合并所有内容用于快速搜索
    print("🔄 合并内容索引...")
    all_content = " ".join(all_content_map.values())
    
    # 分析资源文件使用情况（带进度）
    print("🔍 分析资源文件使用情况...")
    unused_resources = []
    total_resources = len(resource_files)
    
    for i, file_path in enumerate(resource_files):
        if i % 50 == 0:
            print_progress(i + 1, total_resources, "分析资源")
        
        resource_name = file_path.stem
        file_name = file_path.name
        
        is_used = check_resource_usage_ultimate(
            file_path, resource_name, file_name, 
            all_content, all_content_map, binding_mappings
        )
        
        if not is_used:
            # 获取Git信息
            git_info = get_git_info(file_path, project_path) if has_git else {}
            contributors = get_file_contributors(file_path, project_path) if has_git else []

            unused_resources.append({
                'name': file_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_res)),
                'size': file_path.stat().st_size,
                'type': get_resource_type(file_path),
                'git_info': git_info,
                'contributors': contributors
            })
    
    print(f"\n   ✅ 发现 {len(unused_resources)} 个可能未使用的资源文件")
    
    # 分析代码文件使用情况（带进度）
    print("🔍 分析代码文件使用情况...")
    unused_classes = []
    total_code_files = len(code_files)
    
    for i, file_path in enumerate(code_files):
        if i % 50 == 0:
            print_progress(i + 1, total_code_files, "分析代码")
        
        class_name = extract_class_name_smart(file_path, all_content_map)
        
        # 检查类是否被使用（改进版：按类为单位检查）
        file_content = all_content_map.get(str(file_path), "")
        other_content = all_content.replace(file_content, "")

        # 先检查其他文件中的引用
        is_used = check_class_usage_smart(class_name, other_content)

        # 如果其他文件中没有引用，再检查同文件内是否有其他类/函数使用
        if not is_used:
            is_used = check_class_usage_smart(class_name, "", file_content)
        
        if not is_used:
            unused_classes.append({
                'name': file_path.name,
                'class_name': class_name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(app_java)),
                'size': file_path.stat().st_size,
                'type': file_path.suffix
            })
    
    print(f"\n   ✅ 发现 {len(unused_classes)} 个可能未使用的类文件")
    
    # 生成报告
    print("📊 生成报告...")
    generate_ultimate_report(
        project_path, unused_resources, unused_classes, 
        binding_mappings, has_view_binding, has_data_binding, has_navigation
    )
    
    print(f"\n✅ 扫描完成！")
    print(f"📊 可能未使用的资源文件: {len(unused_resources)} 个")
    print(f"📊 可能未使用的类文件: {len(unused_classes)} 个")

def check_view_binding(project_path):
    """检查是否启用了ViewBinding"""
    build_files = [
        project_path / "app" / "build.gradle",
        project_path / "app" / "build.gradle.kts"
    ]
    
    for build_file in build_files:
        if build_file.exists():
            try:
                with open(build_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'viewBinding' in content and 'true' in content:
                        return True
            except:
                pass
    return False

def check_data_binding(project_path):
    """检查是否启用了DataBinding"""
    build_files = [
        project_path / "app" / "build.gradle",
        project_path / "app" / "build.gradle.kts"
    ]
    
    for build_file in build_files:
        if build_file.exists():
            try:
                with open(build_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'dataBinding' in content and 'true' in content:
                        return True
            except:
                pass
    return False

def check_navigation_component(res_dir):
    """检查是否使用了Navigation组件"""
    navigation_dir = res_dir / "navigation"
    return navigation_dir.exists() and any(navigation_dir.glob("*.xml"))

def is_git_repo(project_path):
    """检查是否为Git仓库"""
    return (project_path / ".git").exists()

def get_git_info(file_path, project_root):
    """获取文件的Git提交信息"""
    if not is_git_repo(project_root):
        return {}

    try:
        import subprocess

        # 获取文件的最后提交信息
        cmd = [
            'git', 'log', '-1', '--pretty=format:%H|%an|%ae|%ad|%s',
            '--date=iso', '--', str(file_path)
        ]

        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=10
        )

        if result.returncode == 0 and result.stdout.strip():
            parts = result.stdout.strip().split('|', 4)
            if len(parts) >= 5:
                return {
                    'commit_hash': parts[0][:8],  # 短hash
                    'author_name': parts[1],
                    'author_email': parts[2],
                    'commit_date': parts[3],
                    'commit_message': parts[4]
                }

        return {'error': 'No git history found'}

    except Exception as e:
        return {'error': f'Git command failed: {str(e)}'}

def get_file_contributors(file_path, project_root):
    """获取文件的所有贡献者信息"""
    if not is_git_repo(project_root):
        return []

    try:
        import subprocess

        # 获取文件的所有贡献者
        cmd = [
            'git', 'shortlog', '-sne', '--', str(file_path)
        ]

        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=10
        )

        contributors = []
        if result.returncode == 0 and result.stdout.strip():
            for line in result.stdout.strip().split('\n'):
                # <AUTHOR> <EMAIL>"
                match = re.match(r'\s*(\d+)\s+(.+?)\s+<(.+?)>', line.strip())
                if match:
                    contributors.append({
                        'commits': int(match.group(1)),
                        'name': match.group(2),
                        'email': match.group(3)
                    })

        return contributors

    except Exception as e:
        return []

def check_resource_usage_ultimate(file_path, resource_name, file_name, all_content, content_map, binding_mappings):
    """终极资源使用检查（支持ViewBinding、DataBinding、Navigation）"""
    # 1. 传统引用检查
    traditional_checks = [
        f"R.drawable.{resource_name}",
        f"R.layout.{resource_name}",
        f"R.string.{resource_name}",
        f"R.id.{resource_name}",
        f"R.anim.{resource_name}",
        f"R.color.{resource_name}",
        f"@drawable/{resource_name}",
        f"@layout/{resource_name}",
        f"@string/{resource_name}",
        f"@anim/{resource_name}",
        f"@color/{resource_name}",
        f'"{resource_name}"',
        f"'{resource_name}'",
        file_name
    ]
    
    for check in traditional_checks:
        if check in all_content:
            return True
    
    # 2. ViewBinding检查（针对布局文件）
    if file_path.parent.name.startswith('layout'):
        binding_class = binding_mappings.get(resource_name, "")
        if binding_class:
            binding_checks = [
                binding_class,
                f"databinding.{binding_class}",
                f"binding.{binding_class}"
            ]
            for check in binding_checks:
                if check in all_content:
                    return True
    
    # 3. Navigation组件检查（针对动画文件）
    if file_path.parent.name.startswith('anim'):
        navigation_checks = [
            f"app:enterAnim=\"@anim/{resource_name}\"",
            f"app:exitAnim=\"@anim/{resource_name}\"",
            f"app:popEnterAnim=\"@anim/{resource_name}\"",
            f"app:popExitAnim=\"@anim/{resource_name}\"",
        ]
        for check in navigation_checks:
            if check in all_content:
                return True
    
    # 4. ID引用检查（ViewBinding会为每个ID生成属性）
    if file_path.suffix == '.xml':
        xml_content = content_map.get(str(file_path), "")
        # 提取XML中的所有ID
        id_matches = re.findall(r'android:id="@\+id/(\w+)"', xml_content)
        for id_name in id_matches:
            id_checks = [
                f".{id_name}",  # binding.idName
                f"R.id.{id_name}"  # R.id.idName
            ]
            for check in id_checks:
                if check in all_content:
                    return True
    
    return False

def check_class_usage_smart(class_name, content, current_file_content=""):
    """智能检查类使用情况（改进版：按类为单位检查）"""
    checks = [
        class_name,
        f"import {class_name}",
        f"import .{class_name}",
        f".{class_name}",
        f"extends {class_name}",
        f": {class_name}",  # Kotlin继承
        f"android:name=\"{class_name}\"",
        f"android:name=\".{class_name}\"",
        f"is {class_name}",  # Kotlin类型检查
        f"as {class_name}",  # Kotlin类型转换
        f"as? {class_name}", # Kotlin安全类型转换
        f"({class_name})",   # 类型转换
        f"<{class_name}>",   # 泛型
        f"= {class_name}(",  # 实例化
        f"new {class_name}(" # Java实例化
    ]

    # 首先在所有内容中检查
    for check in checks:
        if check in content:
            return True

    # 如果在当前文件中找到引用，需要进一步检查是否是同文件内的其他类/函数使用
    if current_file_content:
        for check in checks:
            if check in current_file_content:
                # 检查是否在类定义之外使用（即被其他类或函数使用）
                if is_used_outside_class_definition(class_name, current_file_content):
                    return True

    return False

def is_used_outside_class_definition(class_name, file_content):
    """检查类是否在其定义之外被使用"""
    lines = file_content.split('\n')
    class_definition_start = -1
    class_definition_end = -1

    # 找到类定义的范围
    for i, line in enumerate(lines):
        if re.search(rf'(?:private\s+)?class\s+{re.escape(class_name)}\b', line):
            class_definition_start = i
            break

    if class_definition_start == -1:
        return False

    # 找到类定义的结束位置（简单的大括号匹配）
    brace_count = 0
    for i in range(class_definition_start, len(lines)):
        line = lines[i]
        brace_count += line.count('{') - line.count('}')
        if brace_count == 0 and i > class_definition_start:
            class_definition_end = i
            break

    if class_definition_end == -1:
        class_definition_end = len(lines) - 1

    # 检查类定义范围之外是否有引用
    usage_patterns = [
        rf'\b{re.escape(class_name)}\b',
        rf'is\s+{re.escape(class_name)}\b',
        rf'as\s+{re.escape(class_name)}\b',
        rf'as\?\s+{re.escape(class_name)}\b'
    ]

    # 检查类定义之前的行
    for i in range(0, class_definition_start):
        line = lines[i]
        for pattern in usage_patterns:
            if re.search(pattern, line):
                return True

    # 检查类定义之后的行
    for i in range(class_definition_end + 1, len(lines)):
        line = lines[i]
        for pattern in usage_patterns:
            if re.search(pattern, line):
                return True

    return False

def extract_class_name_smart(file_path, content_map):
    """智能提取类名"""
    content = content_map.get(str(file_path), "")
    if content:
        # 匹配类、接口、枚举、object声明
        patterns = [
            r'(?:public\s+)?(?:class|interface|enum|object)\s+(\w+)',
            r'class\s+(\w+)\s*[:\(]',  # Kotlin类
            r'interface\s+(\w+)\s*[:\{]',  # 接口
            r'enum\s+class\s+(\w+)',  # Kotlin枚举
            r'object\s+(\w+)'  # Kotlin object
        ]

        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)

    return file_path.stem

def get_resource_type(file_path):
    """获取资源类型"""
    parent_dir = file_path.parent.name
    extension = file_path.suffix.lower()

    if extension in {'.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'}:
        return f"image/{parent_dir}"
    elif extension == '.xml':
        if parent_dir.startswith('layout'):
            return "layout"
        elif parent_dir.startswith('drawable'):
            return "drawable"
        elif parent_dir.startswith('values'):
            return "values"
        elif parent_dir.startswith('anim'):
            return "animation"
        elif parent_dir.startswith('navigation'):
            return "navigation"
        elif parent_dir.startswith('color'):
            return "color"
        else:
            return f"xml/{parent_dir}"
    elif extension in {'.ttf', '.otf'}:
        return "font"
    elif extension == '.json':
        return "json"
    else:
        return f"other/{parent_dir}"

def generate_ultimate_report(project_path, unused_resources, unused_classes, binding_mappings, has_view_binding, has_data_binding, has_navigation):
    """生成终极扫描报告"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    total_size = sum(r['size'] for r in unused_resources) + sum(c['size'] for c in unused_classes)

    report = f"""# Android项目无用资源终极扫描报告

## 扫描信息
- **扫描时间**: {timestamp}
- **项目路径**: {project_path}
- **扫描器版本**: 终极版（支持ViewBinding/DataBinding/Navigation）
- **ViewBinding**: {'✅ 已启用' if has_view_binding else '❌ 未启用'}
- **DataBinding**: {'✅ 已启用' if has_data_binding else '❌ 未启用'}
- **Navigation组件**: {'✅ 已检测' if has_navigation else '❌ 未检测'}
- **可能未使用的资源文件**: {len(unused_resources)} 个
- **可能未使用的类文件**: {len(unused_classes)} 个
- **总计可能节省空间**: {format_size(total_size)}

## 🔧 扫描特性

✅ **终极检测功能**:
- ✅ ViewBinding类名映射检测
- ✅ DataBinding引用检测
- ✅ Navigation组件动画引用检测
- ✅ XML ID自动属性检测
- ✅ 传统R.xxx.xxx引用检测
- ✅ 类继承和接口实现检测
- ✅ AndroidManifest组件声明检测
- ✅ 自动排除多语言资源文件

⚠️ **已知限制**:
- 动态引用（反射、字符串拼接）无法检测
- 第三方库的隐式引用可能遗漏
- 建议手动验证标记的文件

## 📸 可能未使用的资源文件

"""

    if unused_resources:
        # 按类型分组
        resources_by_type = defaultdict(list)
        for res in unused_resources:
            resources_by_type[res['type']].append(res)

        for res_type, resources in resources_by_type.items():
            report += f"### {res_type.title()} ({len(resources)} 个文件)\n\n"

            # 特殊处理不同类型的建议
            if res_type == "layout":
                report += "⚠️ **布局文件提醒**: 这些文件可能通过ViewBinding引用，已进行智能检测\n\n"
            elif res_type == "animation":
                report += "⚠️ **动画文件提醒**: 已检测Navigation组件中的动画引用\n\n"
            elif res_type.startswith("image/"):
                report += "💡 **图片文件**: 已检测传统引用和ViewBinding引用\n\n"

            report += "| 文件名 | 路径 | 大小 | 安全等级 |\n"
            report += "|--------|------|------|----------|\n"

            for res in sorted(resources, key=lambda x: x['size'], reverse=True)[:20]:
                safety_level = get_safety_level(res)
                report += f"| {res['name']} | {res['relative_path']} | {format_size(res['size'])} | {safety_level} |\n"

            if len(resources) > 20:
                report += f"| ... | ... | ... | 还有 {len(resources) - 20} 个文件 |\n"

            report += "\n"
    else:
        report += "✅ 没有发现可能未使用的资源文件\n\n"

    report += "## 💻 可能未使用的类文件\n\n"

    if unused_classes:
        # 按类型分组
        classes_by_type = defaultdict(list)
        for cls in unused_classes:
            classes_by_type[cls['type']].append(cls)

        for cls_type, classes in classes_by_type.items():
            type_name = "Kotlin文件" if cls_type == ".kt" else "Java文件"
            report += f"### {type_name} ({len(classes)} 个文件)\n\n"
            report += "| 类名 | 文件路径 | 大小 | 类型说明 |\n"
            report += "|------|----------|------|----------|\n"

            for cls in sorted(classes, key=lambda x: x['size'], reverse=True)[:20]:
                class_type_desc = get_class_type_description(cls['class_name'])
                report += f"| {cls['class_name']} | {cls['relative_path']} | {format_size(cls['size'])} | {class_type_desc} |\n"

            if len(classes) > 20:
                report += f"| ... | ... | ... | 还有 {len(classes) - 20} 个文件 |\n"

            report += "\n"
    else:
        report += "✅ 没有发现可能未使用的类文件\n\n"

    # ViewBinding映射信息
    if binding_mappings:
        report += f"""## 📊 ViewBinding映射统计

检测到 {len(binding_mappings)} 个布局文件的ViewBinding映射:

| 布局文件 | 生成的Binding类 | 状态 |
|----------|----------------|------|
"""

        # 显示前10个映射作为示例
        for layout, binding in list(binding_mappings.items())[:10]:
            status = "✅ 已检测" if f"{layout}.xml" not in [r['name'] for r in unused_resources] else "⚠️ 可能未使用"
            report += f"| {layout}.xml | {binding} | {status} |\n"

        if len(binding_mappings) > 10:
            report += f"| ... | ... | 还有 {len(binding_mappings) - 10} 个映射 |\n"

        report += "\n"

    report += """
## ⚠️ 删除建议

### 🟢 相对安全（可优先删除）:
- `.DS_Store` 等系统文件
- 明显的测试或临时文件
- 重复的图片资源

### 🟡 需要谨慎（建议手动验证）:
- 布局文件（可能通过ViewBinding使用）
- 动画文件（可能在Navigation中使用）
- 工具类和帮助类
- 自定义View和组件

### 🔴 高风险（强烈建议保留）:
- Application类和主要Activity
- 第三方库相关的类
- 字体文件（可能在主题中使用）

## 🔧 验证步骤

1. **IDE搜索**: 在Android Studio中搜索文件名
2. **Find Usages**: 使用IDE的"Find Usages"功能
3. **全局搜索**: 搜索可能的字符串引用
4. **Navigation检查**: 检查Navigation图中的引用
5. **编译测试**: 删除后进行完整编译
6. **功能测试**: 运行应用进行功能测试

## 📝 注意事项

- 本报告基于静态分析，可能存在误报
- 已自动排除多语言资源文件
- 已检测Navigation组件中的动画引用
- 删除前请务必备份项目
- 建议分批删除并逐步测试
- 如有疑问，建议保留文件
"""

    # 保存报告
    report_file = project_path / f"unused_resources_final_report_{time.strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"📄 报告已生成: {report_file}")

def get_safety_level(resource_info):
    """获取资源文件的安全等级"""
    file_name = resource_info['name']
    res_type = resource_info['type']

    if file_name == '.DS_Store':
        return "🟢 安全删除"
    elif file_name.startswith('ic_launcher'):
        return "🔴 应用图标"
    elif res_type == "layout":
        return "🟡 已检测ViewBinding"
    elif res_type == "animation":
        return "🟡 已检测Navigation"
    elif res_type.startswith("image/"):
        return "🟢 相对安全"
    elif res_type == "font":
        return "🟡 字体文件"
    elif res_type == "json":
        return "🟡 JSON资源"
    else:
        return "🟡 需要验证"

def get_class_type_description(class_name):
    """获取类类型描述"""
    name_lower = class_name.lower()

    if 'activity' in name_lower:
        return "Activity类"
    elif 'fragment' in name_lower:
        return "Fragment类"
    elif 'adapter' in name_lower:
        return "适配器类"
    elif 'util' in name_lower or 'helper' in name_lower:
        return "工具类"
    elif 'view' in name_lower:
        return "视图类"
    elif 'model' in name_lower or 'bean' in name_lower:
        return "数据类"
    elif 'dialog' in name_lower:
        return "对话框类"
    else:
        return "普通类"

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

if __name__ == "__main__":
    import sys
    project_root = sys.argv[1] if len(sys.argv) > 1 else "."
    scan_android_project(project_root)
