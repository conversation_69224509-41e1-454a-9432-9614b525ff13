/*
 * Copyright (C) 2015 Square, Inc, 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.com.vau.kit.chunk;

import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.chunk.internal.data.ChuckContentProvider;
import cn.com.vau.kit.chunk.internal.data.HttpTransaction;
import cn.com.vau.kit.chunk.internal.data.LocalCupboard;
import cn.com.vau.kit.chunk.internal.support.NotificationHelper;
import cn.com.vau.kit.chunk.internal.support.RetentionManager;
import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.internal.http.HttpHeaders;
import okio.Buffer;
import okio.BufferedSource;
import okio.GzipSource;
import okio.Okio;

/**
 * An OkHttp Interceptor which persists and displays HTTP activity in your application for later inspection.
 */
@Keep
public final class ChuckInterceptor implements Interceptor {

    public enum Period {
        /**
         * Retain data for the last hour.
         */
        ONE_HOUR,
        /**
         * Retain data for the last day.
         */
        ONE_DAY,
        /**
         * Retain data for the last week.
         */
        ONE_WEEK,
        /**
         * Retain data forever.
         */
        FOREVER
    }

    private static final String LOG_TAG = "ChuckInterceptor";
    private static final Period DEFAULT_RETENTION = Period.ONE_WEEK;
    private static final Charset UTF8 = Charset.forName("UTF-8");

    private final Context context;
    private final NotificationHelper notificationHelper;
    private RetentionManager retentionManager;
    private boolean showNotification;
    //Json文件太长,会截取一部分显示不全
    private long maxContentLength = Long.MAX_VALUE;
//    private long maxContentLength = 250000L;

    /**
     * @param context The current Context.
     */
    public ChuckInterceptor(Context context) {
        this.context = context.getApplicationContext();
        notificationHelper = new NotificationHelper(this.context);
        showNotification = true;
        retentionManager = new RetentionManager(this.context, DEFAULT_RETENTION);
    }

    /**
     * Control whether a notification is shown while HTTP activity is recorded.
     *
     * @param show true to show a notification, false to suppress it.
     * @return The {@link ChuckInterceptor} instance.
     */
    public ChuckInterceptor showNotification(boolean show) {
        showNotification = show;
        return this;
    }

    /**
     * Set the maximum length for request and response content before it is truncated.
     * Warning: setting this value too high may cause unexpected results.
     *
     * @param max the maximum length (in bytes) for request/response content.
     * @return The {@link ChuckInterceptor} instance.
     */
    public ChuckInterceptor maxContentLength(long max) {
        this.maxContentLength = max;
        return this;
    }

    /**
     * Set the retention period for HTTP transaction data captured by this interceptor.
     * The default is one week.
     *
     * @param period the peroid for which to retain HTTP transaction data.
     * @return The {@link ChuckInterceptor} instance.
     */
    public ChuckInterceptor retainDataFor(Period period) {
        retentionManager = new RetentionManager(context, period);
        return this;
    }

    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {
        Response response = null;
        Request request;
        try {
            request = chain.request();

            response = chain.proceed(request);

        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }


        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();

        if (appCallBackItf != null && !appCallBackItf.isInterceptRequest(request.headers(), response.body(), request.url().toString())) {

            RequestBody requestBody = request.body();
            boolean hasRequestBody = requestBody != null;

            HttpTransaction transaction = new HttpTransaction();
            transaction.setRequestDate(new Date());

            transaction.setMethod(request.method());
            transaction.setUrl(request.url().toString());

            Object tag = request.tag();
            if (tag != null) {
                transaction.setRequestSimpleClassName(tag.toString());
            }


            transaction.setRequestHeaders(request.headers());
            if (hasRequestBody) {
                if (requestBody.contentType() != null) {
                    transaction.setRequestContentType(requestBody.contentType().toString());
                }
                if (requestBody.contentLength() != -1) {
                    transaction.setRequestContentLength(requestBody.contentLength());
                }
            }


            transaction.setRequestBodyIsPlainText(!bodyHasUnsupportedEncoding(request.headers()));
            if (hasRequestBody && transaction.requestBodyIsPlainText()) {
                BufferedSource source = getNativeSource(new Buffer(), bodyGzipped(request.headers()));
                Buffer buffer = source.buffer();
                requestBody.writeTo(buffer);
                Charset charset = UTF8;
                MediaType contentType = requestBody.contentType();
                if (contentType != null) {
                    charset = contentType.charset(UTF8);
                }
                if (isPlaintext(buffer)) {
                    transaction.setRequestBody(readFromBuffer(buffer, charset));
                } else {
                    transaction.setResponseBodyIsPlainText(false);
                }
            }

            Uri transactionUri = create(transaction);

            long startNs = System.nanoTime();


            long tookMs = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startNs);

            ResponseBody responseBody = response.body();

            transaction.setRequestHeaders(response.request().headers()); // includes headers added later in the chain
            transaction.setResponseDate(new Date());
            transaction.setTookMs(tookMs);
            transaction.setProtocol(response.protocol().toString());
            transaction.setResponseCode(response.code());
            transaction.setResponseMessage(response.message());

            transaction.setResponseContentLength(responseBody.contentLength());
            if (responseBody.contentType() != null) {
                transaction.setResponseContentType(responseBody.contentType().toString());
            }
            transaction.setResponseHeaders(response.headers());


            String postParams = appCallBackItf.decryHttpPostParam(transaction.getFormattedRequestBody(), transaction.getUrl());
            if (postParams != null) {
                transaction.setActionParams(getActionParams(postParams));
            }

            transaction.setResponseBodyIsPlainText(!bodyHasUnsupportedEncoding(response.headers()));
            if (HttpHeaders.hasBody(response) && transaction.responseBodyIsPlainText()) {
                BufferedSource source = getNativeSource(response);
                source.request(Long.MAX_VALUE);
                Buffer buffer = source.buffer();
                Charset charset = UTF8;
                MediaType contentType = responseBody.contentType();
                if (contentType != null) {
                    try {
                        charset = contentType.charset(UTF8);
                    } catch (Exception e) {
                        if (transactionUri != null) {
                            update(transaction, transactionUri);
                        }
                        throw e;
                    }
                }
                if (isPlaintext(buffer)) {
                    String value = readFromBuffer(buffer.clone(), charset);
                    transaction.setResponseBody(value);
                    transaction.setBusinessSuccess(checkCode(value));
                    transaction.setBatchErrorPart(getErrorBatchPartText(value));
                } else {
                    transaction.setResponseBodyIsPlainText(false);
                    transaction.setBusinessSuccess(true);
                    transaction.setBatchErrorPart("");
                }
                transaction.setResponseContentLength(buffer.size());
            }


            if (transactionUri != null) {
                update(transaction, transactionUri);
            }


        }


        return response;
    }


    //获得batch接口异常的文案
    private String getErrorBatchPartText(String value) {
        try {

            if (value == null) return "";
            StringBuilder stringBuilder = new StringBuilder();

            JSONObject jsonObject = new JSONObject(value);
            JSONObject zpData = jsonObject.optJSONObject("zpData");
            if (zpData != null) {
                Iterator<String> keys = zpData.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    JSONObject itemValue = zpData.optJSONObject(key);
                    if (itemValue != null) {
                        int itemCode = itemValue.optInt("code");
                        if (itemCode != 0) {
                            stringBuilder.append(key);
                            stringBuilder.append(",");
                        }
                    }
                }
            }
            return stringBuilder.toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    //json->code==0业务层成功
    //json->code!=0业务层失败
    private boolean checkCode(String value) {
        if (value != null) {
            try {
                JSONObject jsonObject = new JSONObject(value);
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    if (TextUtils.equals(key, "code")) {
                        return jsonObject.optInt("code") == 0;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    private Uri create(HttpTransaction transaction) {
        if (ChuckContentProvider.TRANSACTION_URI != null) {
            ContentValues values = LocalCupboard.getInstance().withEntity(HttpTransaction.class).toContentValues(transaction);
            Uri uri = context.getContentResolver().insert(ChuckContentProvider.TRANSACTION_URI, values);
            if (uri != null) {
                String lastPathSegment = uri.getLastPathSegment();
                if (lastPathSegment != null) {
                    transaction.setId(Long.valueOf(lastPathSegment));
                }
                if (showNotification) {
                    notificationHelper.show(transaction);
                }
                retentionManager.doMaintenance();
            }
            return uri;
        }
        return null;
    }

    private int update(HttpTransaction transaction, Uri uri) {
        ContentValues values = LocalCupboard.getInstance().withEntity(HttpTransaction.class).toContentValues(transaction);
        int updated = context.getContentResolver().update(uri, values, null, null);
        if (showNotification && updated > 0) {
            notificationHelper.show(transaction);
        }
        return updated;
    }

    /**
     * Returns true if the body in question probably contains human readable text. Uses a small sample
     * of code points to detect unicode control characters commonly used in binary file signatures.
     */
    private boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }

    private boolean bodyHasUnsupportedEncoding(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return contentEncoding != null &&
                !contentEncoding.equalsIgnoreCase("identity") &&
                !contentEncoding.equalsIgnoreCase("gzip");
    }

    private boolean bodyGzipped(Headers headers) {
        String contentEncoding = headers.get("Content-Encoding");
        return "gzip".equalsIgnoreCase(contentEncoding);
    }

    private String readFromBuffer(Buffer buffer, Charset charset) {
        long bufferSize = buffer.size();
        long maxBytes = Math.min(bufferSize, maxContentLength);
        String body = "";
        try {
            body = buffer.readString(maxBytes, charset);
        } catch (EOFException e) {
            body += context.getString(R.string.kit_chuck_body_unexpected_eof);
        }
        if (bufferSize > maxContentLength) {
            body += context.getString(R.string.kit_chuck_body_content_truncated);
        }
        return body;
    }

    private BufferedSource getNativeSource(BufferedSource input, boolean isGzipped) {
        if (isGzipped) {
            GzipSource source = new GzipSource(input);
            return Okio.buffer(source);
        } else {
            return input;
        }
    }

    private BufferedSource getNativeSource(Response response) throws IOException {
        if (bodyGzipped(response.headers())) {
            BufferedSource source = response.peekBody(maxContentLength).source();
            if (source.buffer().size() < maxContentLength) {
                return getNativeSource(source, true);
            } else {
                Log.w(LOG_TAG, "gzip encoded response was too long");
            }
        }
        return response.body().source();
    }


    private String getActionParams(String decodeUrl) {
        if (decodeUrl == null) return null;

        try {

            String[] split = decodeUrl.split("&");
            int length = split.length;
            for (int i = 0; i < length; i++) {
                String item = split[i];
                String[] param = item.split("=");
                if (param.length == 2) {
                    String key = param[0];
                    String value = param[1];

                    if (TextUtils.equals("data", key)
                            && !TextUtils.isEmpty(value)) {

                        JSONArray jsonArray = new JSONArray(value);
                        for (int j = 0; j < jsonArray.length(); j++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            if (jsonObject == null) continue;
                            return jsonObject.optString("action");
                        }
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }
}
