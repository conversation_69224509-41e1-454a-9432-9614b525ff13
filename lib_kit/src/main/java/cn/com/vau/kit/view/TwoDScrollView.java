package cn.com.vau.kit.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.FocusFinder;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.Scroller;

import java.util.List;

public class TwoDScrollView extends FrameLayout {
    static final int ANIMATED_SCROLL_GAP = 250;
    static final float MAX_SCROLL_FACTOR = 0.5F;
    private long mLastScroll;
    private final Rect mTempRect = new Rect();
    private Scroller mScroller;
    private boolean mTwoDScrollViewMovedFocus;
    private float mLastMotionY;
    private float mLastMotionX;
    private boolean mIsLayoutDirty = true;
    private View mChildToScrollTo = null;
    private boolean mIsBeingDragged = false;
    private VelocityTracker mVelocityTracker;
    private int mTouchSlop;
    private int mMinimumVelocity;
    private int mMaximumVelocity;

    public TwoDScrollView(Context context) {
        super(context);
        this.initTwoDScrollView();
    }

    public TwoDScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.initTwoDScrollView();
    }

    public TwoDScrollView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        this.initTwoDScrollView();
    }

    protected float getTopFadingEdgeStrength() {
        if (this.getChildCount() == 0) {
            return 0.0F;
        } else {
            int length = this.getVerticalFadingEdgeLength();
            return this.getScrollY() < length ? (float)this.getScrollY() / (float)length : 1.0F;
        }
    }

    protected float getBottomFadingEdgeStrength() {
        if (this.getChildCount() == 0) {
            return 0.0F;
        } else {
            int length = this.getVerticalFadingEdgeLength();
            int bottomEdge = this.getHeight() - this.getPaddingBottom();
            int span = this.getChildAt(0).getBottom() - this.getScrollY() - bottomEdge;
            return span < length ? (float)span / (float)length : 1.0F;
        }
    }

    protected float getLeftFadingEdgeStrength() {
        if (this.getChildCount() == 0) {
            return 0.0F;
        } else {
            int length = this.getHorizontalFadingEdgeLength();
            return this.getScrollX() < length ? (float)this.getScrollX() / (float)length : 1.0F;
        }
    }

    protected float getRightFadingEdgeStrength() {
        if (this.getChildCount() == 0) {
            return 0.0F;
        } else {
            int length = this.getHorizontalFadingEdgeLength();
            int rightEdge = this.getWidth() - this.getPaddingRight();
            int span = this.getChildAt(0).getRight() - this.getScrollX() - rightEdge;
            return span < length ? (float)span / (float)length : 1.0F;
        }
    }

    public int getMaxScrollAmountVertical() {
        return (int)(0.5F * (float)this.getHeight());
    }

    public int getMaxScrollAmountHorizontal() {
        return (int)(0.5F * (float)this.getWidth());
    }

    private void initTwoDScrollView() {
        this.mScroller = new Scroller(this.getContext());
        this.setFocusable(true);
        this.setDescendantFocusability(262144);
        this.setWillNotDraw(false);
        ViewConfiguration configuration = ViewConfiguration.get(this.getContext());
        this.mTouchSlop = configuration.getScaledTouchSlop();
        this.mMinimumVelocity = configuration.getScaledMinimumFlingVelocity();
        this.mMaximumVelocity = configuration.getScaledMaximumFlingVelocity();
    }

    public void addView(View child) {
        if (this.getChildCount() > 0) {
            throw new IllegalStateException("TwoDScrollView can host only one direct child");
        } else {
            super.addView(child);
        }
    }

    public void addView(View child, int index) {
        if (this.getChildCount() > 0) {
            throw new IllegalStateException("TwoDScrollView can host only one direct child");
        } else {
            super.addView(child, index);
        }
    }

    public void addView(View child, LayoutParams params) {
        if (this.getChildCount() > 0) {
            throw new IllegalStateException("TwoDScrollView can host only one direct child");
        } else {
            super.addView(child, params);
        }
    }

    public void addView(View child, int index, LayoutParams params) {
        if (this.getChildCount() > 0) {
            throw new IllegalStateException("TwoDScrollView can host only one direct child");
        } else {
            super.addView(child, index, params);
        }
    }

    private boolean canScroll() {
        View child = this.getChildAt(0);
        if (child == null) {
            return false;
        } else {
            int childHeight = child.getHeight();
            int childWidth = child.getWidth();
            return this.getHeight() < childHeight + this.getPaddingTop() + this.getPaddingBottom() || this.getWidth() < childWidth + this.getPaddingLeft() + this.getPaddingRight();
        }
    }

    public boolean dispatchKeyEvent(KeyEvent event) {
        boolean handled = super.dispatchKeyEvent(event);
        return handled ? true : this.executeKeyEvent(event);
    }

    public boolean executeKeyEvent(KeyEvent event) {
        this.mTempRect.setEmpty();
        if (this.canScroll()) {
            boolean handled = false;
            if (event.getAction() == 0) {
                switch(event.getKeyCode()) {
                    case 19:
                        if (!event.isAltPressed()) {
                            handled = this.arrowScroll(33, false);
                        } else {
                            handled = this.fullScroll(33, false);
                        }
                        break;
                    case 20:
                        if (!event.isAltPressed()) {
                            handled = this.arrowScroll(130, false);
                        } else {
                            handled = this.fullScroll(130, false);
                        }
                        break;
                    case 21:
                        if (!event.isAltPressed()) {
                            handled = this.arrowScroll(17, true);
                        } else {
                            handled = this.fullScroll(17, true);
                        }
                        break;
                    case 22:
                        if (!event.isAltPressed()) {
                            handled = this.arrowScroll(66, true);
                        } else {
                            handled = this.fullScroll(66, true);
                        }
                }
            }

            return handled;
        } else if (this.isFocused()) {
            View currentFocused = this.findFocus();
            if (currentFocused == this) {
                currentFocused = null;
            }

            View nextFocused = FocusFinder.getInstance().findNextFocus(this, currentFocused, 130);
            return nextFocused != null && nextFocused != this && nextFocused.requestFocus(130);
        } else {
            return false;
        }
    }

    public boolean onInterceptTouchEvent(MotionEvent ev) {
        int action = ev.getAction();
        if (action == 2 && this.mIsBeingDragged) {
            return true;
        } else if (!this.canScroll()) {
            this.mIsBeingDragged = false;
            return false;
        } else {
            float y = ev.getY();
            float x = ev.getX();
            switch(action) {
                case 0:
                    this.mLastMotionY = y;
                    this.mLastMotionX = x;
                    this.mIsBeingDragged = !this.mScroller.isFinished();
                    break;
                case 1:
                case 3:
                    this.mIsBeingDragged = false;
                    break;
                case 2:
                    int yDiff = (int)Math.abs(y - this.mLastMotionY);
                    int xDiff = (int)Math.abs(x - this.mLastMotionX);
                    if (yDiff > this.mTouchSlop || xDiff > this.mTouchSlop) {
                        this.mIsBeingDragged = true;
                    }
            }

            return this.mIsBeingDragged;
        }
    }

    public boolean onTouchEvent(MotionEvent ev) {
        if (ev.getAction() == 0 && ev.getEdgeFlags() != 0) {
            return false;
        } else if (!this.canScroll()) {
            return false;
        } else {
            if (this.mVelocityTracker == null) {
                this.mVelocityTracker = VelocityTracker.obtain();
            }

            this.mVelocityTracker.addMovement(ev);
            int action = ev.getAction();
            float y = ev.getY();
            float x = ev.getX();
            int availableToScroll;
            switch(action) {
                case 0:
                    if (!this.mScroller.isFinished()) {
                        this.mScroller.abortAnimation();
                    }

                    this.mLastMotionY = y;
                    this.mLastMotionX = x;
                    break;
                case 1:
                    VelocityTracker velocityTracker = this.mVelocityTracker;
                    velocityTracker.computeCurrentVelocity(1000, (float)this.mMaximumVelocity);
                    availableToScroll = (int)velocityTracker.getXVelocity();
                    int initialYVelocity = (int)velocityTracker.getYVelocity();
                    if (Math.abs(availableToScroll) + Math.abs(initialYVelocity) > this.mMinimumVelocity && this.getChildCount() > 0) {
                        this.fling(-availableToScroll, -initialYVelocity);
                    }

                    if (this.mVelocityTracker != null) {
                        this.mVelocityTracker.recycle();
                        this.mVelocityTracker = null;
                    }
                    break;
                case 2:
                    int deltaX = (int)(this.mLastMotionX - x);
                    int deltaY = (int)(this.mLastMotionY - y);
                    this.mLastMotionX = x;
                    this.mLastMotionY = y;
                    int bottomEdge;
                    if (deltaX < 0) {
                        if (this.getScrollX() < 0) {
                            deltaX = 0;
                        }
                    } else if (deltaX > 0) {
                        bottomEdge = this.getWidth() - this.getPaddingRight();
                        availableToScroll = this.getChildAt(0).getRight() - this.getScrollX() - bottomEdge;
                        if (availableToScroll > 0) {
                            deltaX = Math.min(availableToScroll, deltaX);
                        } else {
                            deltaX = 0;
                        }
                    }

                    if (deltaY < 0) {
                        if (this.getScrollY() < 0) {
                            deltaY = 0;
                        }
                    } else if (deltaY > 0) {
                        bottomEdge = this.getHeight() - this.getPaddingBottom();
                        availableToScroll = this.getChildAt(0).getBottom() - this.getScrollY() - bottomEdge;
                        if (availableToScroll > 0) {
                            deltaY = Math.min(availableToScroll, deltaY);
                        } else {
                            deltaY = 0;
                        }
                    }

                    if (deltaY != 0 || deltaX != 0) {
                        this.scrollBy(deltaX, deltaY);
                    }
            }

            return true;
        }
    }

    private View findFocusableViewInMyBounds(boolean topFocus, int top, boolean leftFocus, int left, View preferredFocusable) {
        int verticalFadingEdgeLength = this.getVerticalFadingEdgeLength() / 2;
        int topWithoutFadingEdge = top + verticalFadingEdgeLength;
        int bottomWithoutFadingEdge = top + this.getHeight() - verticalFadingEdgeLength;
        int horizontalFadingEdgeLength = this.getHorizontalFadingEdgeLength() / 2;
        int leftWithoutFadingEdge = left + horizontalFadingEdgeLength;
        int rightWithoutFadingEdge = left + this.getWidth() - horizontalFadingEdgeLength;
        return preferredFocusable != null && preferredFocusable.getTop() < bottomWithoutFadingEdge && preferredFocusable.getBottom() > topWithoutFadingEdge && preferredFocusable.getLeft() < rightWithoutFadingEdge && preferredFocusable.getRight() > leftWithoutFadingEdge ? preferredFocusable : this.findFocusableViewInBounds(topFocus, topWithoutFadingEdge, bottomWithoutFadingEdge, leftFocus, leftWithoutFadingEdge, rightWithoutFadingEdge);
    }

    private View findFocusableViewInBounds(boolean topFocus, int top, int bottom, boolean leftFocus, int left, int right) {
        List<View> focusables = this.getFocusables(View.FOCUS_FORWARD);
        View focusCandidate = null;
        boolean foundFullyContainedFocusable = false;
        int count = focusables.size();

        for(int i = 0; i < count; ++i) {
            View view = (View)focusables.get(i);
            int viewTop = view.getTop();
            int viewBottom = view.getBottom();
            int viewLeft = view.getLeft();
            int viewRight = view.getRight();
            if (top < viewBottom && viewTop < bottom && left < viewRight && viewLeft < right) {
                boolean viewIsFullyContained = top < viewTop && viewBottom < bottom && left < viewLeft && viewRight < right;
                if (focusCandidate == null) {
                    focusCandidate = view;
                    foundFullyContainedFocusable = viewIsFullyContained;
                } else {
                    boolean viewIsCloserToVerticalBoundary = topFocus && viewTop < focusCandidate.getTop() || !topFocus && viewBottom > focusCandidate.getBottom();
                    boolean viewIsCloserToHorizontalBoundary = leftFocus && viewLeft < focusCandidate.getLeft() || !leftFocus && viewRight > focusCandidate.getRight();
                    if (foundFullyContainedFocusable) {
                        if (viewIsFullyContained && viewIsCloserToVerticalBoundary && viewIsCloserToHorizontalBoundary) {
                            focusCandidate = view;
                        }
                    } else if (viewIsFullyContained) {
                        focusCandidate = view;
                        foundFullyContainedFocusable = true;
                    } else if (viewIsCloserToVerticalBoundary && viewIsCloserToHorizontalBoundary) {
                        focusCandidate = view;
                    }
                }
            }
        }

        return focusCandidate;
    }

    public boolean fullScroll(int direction, boolean horizontal) {
        boolean right;
        int width;
        int count;
        View view;
        if (!horizontal) {
            right = direction == 130;
            width = this.getHeight();
            this.mTempRect.top = 0;
            this.mTempRect.bottom = width;
            if (right) {
                count = this.getChildCount();
                if (count > 0) {
                    view = this.getChildAt(count - 1);
                    this.mTempRect.bottom = view.getBottom();
                    this.mTempRect.top = this.mTempRect.bottom - width;
                }
            }

            return this.scrollAndFocus(direction, this.mTempRect.top, this.mTempRect.bottom, 0, 0, 0);
        } else {
            right = direction == 130;
            width = this.getWidth();
            this.mTempRect.left = 0;
            this.mTempRect.right = width;
            if (right) {
                count = this.getChildCount();
                if (count > 0) {
                    view = this.getChildAt(count - 1);
                    this.mTempRect.right = view.getBottom();
                    this.mTempRect.left = this.mTempRect.right - width;
                }
            }

            return this.scrollAndFocus(0, 0, 0, direction, this.mTempRect.top, this.mTempRect.bottom);
        }
    }

    private boolean scrollAndFocus(int directionY, int top, int bottom, int directionX, int left, int right) {
        boolean handled = true;
        int height = this.getHeight();
        int containerTop = this.getScrollY();
        int containerBottom = containerTop + height;
        boolean up = directionY == 33;
        int width = this.getWidth();
        int containerLeft = this.getScrollX();
        int containerRight = containerLeft + width;
        boolean leftwards = directionX == 33;
        View newFocused = this.findFocusableViewInBounds(up, top, bottom, leftwards, left, right);
        if (newFocused == null) {
            newFocused = this;
        }

        if (top >= containerTop && bottom <= containerBottom || left >= containerLeft && right <= containerRight) {
            handled = false;
        } else {
            int deltaY = up ? top - containerTop : bottom - containerBottom;
            int deltaX = leftwards ? left - containerLeft : right - containerRight;
            this.doScroll(deltaX, deltaY);
        }

        if (newFocused != this.findFocus() && ((View)newFocused).requestFocus(directionY)) {
            this.mTwoDScrollViewMovedFocus = true;
            this.mTwoDScrollViewMovedFocus = false;
        }

        return handled;
    }

    public boolean arrowScroll(int direction, boolean horizontal) {
        View currentFocused = this.findFocus();
        if (currentFocused == this) {
            currentFocused = null;
        }

        View nextFocused = FocusFinder.getInstance().findNextFocus(this, currentFocused, direction);
        int maxJump = horizontal ? this.getMaxScrollAmountHorizontal() : this.getMaxScrollAmountVertical();
        int scrollDelta;
        int daBottom;
        int screenBottom;
        if (!horizontal) {
            if (nextFocused != null) {
                nextFocused.getDrawingRect(this.mTempRect);
                this.offsetDescendantRectToMyCoords(nextFocused, this.mTempRect);
                scrollDelta = this.computeScrollDeltaToGetChildRectOnScreen(this.mTempRect);
                this.doScroll(0, scrollDelta);
                nextFocused.requestFocus(direction);
            } else {
                scrollDelta = maxJump;
                if (direction == 33 && this.getScrollY() < maxJump) {
                    scrollDelta = this.getScrollY();
                } else if (direction == 130 && this.getChildCount() > 0) {
                    daBottom = this.getChildAt(0).getBottom();
                    screenBottom = this.getScrollY() + this.getHeight();
                    if (daBottom - screenBottom < maxJump) {
                        scrollDelta = daBottom - screenBottom;
                    }
                }

                if (scrollDelta == 0) {
                    return false;
                }

                this.doScroll(0, direction == 130 ? scrollDelta : -scrollDelta);
            }
        } else if (nextFocused != null) {
            nextFocused.getDrawingRect(this.mTempRect);
            this.offsetDescendantRectToMyCoords(nextFocused, this.mTempRect);
            scrollDelta = this.computeScrollDeltaToGetChildRectOnScreen(this.mTempRect);
            this.doScroll(scrollDelta, 0);
            nextFocused.requestFocus(direction);
        } else {
            scrollDelta = maxJump;
            if (direction == 33 && this.getScrollY() < maxJump) {
                scrollDelta = this.getScrollY();
            } else if (direction == 130 && this.getChildCount() > 0) {
                daBottom = this.getChildAt(0).getBottom();
                screenBottom = this.getScrollY() + this.getHeight();
                if (daBottom - screenBottom < maxJump) {
                    scrollDelta = daBottom - screenBottom;
                }
            }

            if (scrollDelta == 0) {
                return false;
            }

            this.doScroll(direction == 130 ? scrollDelta : -scrollDelta, 0);
        }

        return true;
    }

    private void doScroll(int deltaX, int deltaY) {
        if (deltaX != 0 || deltaY != 0) {
            this.smoothScrollBy(deltaX, deltaY);
        }

    }

    public final void smoothScrollBy(int dx, int dy) {
        long duration = AnimationUtils.currentAnimationTimeMillis() - this.mLastScroll;
        if (duration > 250L) {
            this.mScroller.startScroll(this.getScrollX(), this.getScrollY(), dx, dy);
            this.awakenScrollBars(this.mScroller.getDuration());
            this.invalidate();
        } else {
            if (!this.mScroller.isFinished()) {
                this.mScroller.abortAnimation();
            }

            this.scrollBy(dx, dy);
        }

        this.mLastScroll = AnimationUtils.currentAnimationTimeMillis();
    }

    public final void smoothScrollTo(int x, int y) {
        this.smoothScrollBy(x - this.getScrollX(), y - this.getScrollY());
    }

    protected int computeVerticalScrollRange() {
        int count = this.getChildCount();
        return count == 0 ? this.getHeight() : this.getChildAt(0).getBottom();
    }

    protected int computeHorizontalScrollRange() {
        int count = this.getChildCount();
        return count == 0 ? this.getWidth() : this.getChildAt(0).getRight();
    }

    protected void measureChild(View child, int parentWidthMeasureSpec, int parentHeightMeasureSpec) {
        ViewGroup.LayoutParams lp = child.getLayoutParams();
        int childWidthMeasureSpec = getChildMeasureSpec(parentWidthMeasureSpec, this.getPaddingLeft() + this.getPaddingRight(), lp.width);
        int childHeightMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED);
        child.measure(childWidthMeasureSpec, childHeightMeasureSpec);
    }

    protected void measureChildWithMargins(View child, int parentWidthMeasureSpec, int widthUsed, int parentHeightMeasureSpec, int heightUsed) {
        MarginLayoutParams lp = (MarginLayoutParams)child.getLayoutParams();
        int childWidthMeasureSpec = MeasureSpec.makeMeasureSpec(lp.leftMargin + lp.rightMargin, MeasureSpec.UNSPECIFIED);
        int childHeightMeasureSpec = MeasureSpec.makeMeasureSpec(lp.topMargin + lp.bottomMargin, MeasureSpec.UNSPECIFIED);
        child.measure(childWidthMeasureSpec, childHeightMeasureSpec);
    }

    public void computeScroll() {
        if (this.mScroller.computeScrollOffset()) {
            int oldX = this.getScrollX();
            int oldY = this.getScrollY();
            int x = this.mScroller.getCurrX();
            int y = this.mScroller.getCurrY();
            if (this.getChildCount() > 0) {
                View child = this.getChildAt(0);
                this.scrollTo(this.clamp(x, this.getWidth() - this.getPaddingRight() - this.getPaddingLeft(), child.getWidth()), this.clamp(y, this.getHeight() - this.getPaddingBottom() - this.getPaddingTop(), child.getHeight()));
            } else {
                this.scrollTo(x, y);
            }

            if (oldX != this.getScrollX() || oldY != this.getScrollY()) {
                this.onScrollChanged(this.getScrollX(), this.getScrollY(), oldX, oldY);
            }

            this.postInvalidate();
        }

    }

    private void scrollToChild(View child) {
        child.getDrawingRect(this.mTempRect);
        this.offsetDescendantRectToMyCoords(child, this.mTempRect);
        int scrollDelta = this.computeScrollDeltaToGetChildRectOnScreen(this.mTempRect);
        if (scrollDelta != 0) {
            this.scrollBy(0, scrollDelta);
        }

    }

    private boolean scrollToChildRect(Rect rect, boolean immediate) {
        int delta = this.computeScrollDeltaToGetChildRectOnScreen(rect);
        boolean scroll = delta != 0;
        if (scroll) {
            if (immediate) {
                this.scrollBy(0, delta);
            } else {
                this.smoothScrollBy(0, delta);
            }
        }

        return scroll;
    }

    protected int computeScrollDeltaToGetChildRectOnScreen(Rect rect) {
        if (this.getChildCount() == 0) {
            return 0;
        } else {
            int height = this.getHeight();
            int screenTop = this.getScrollY();
            int screenBottom = screenTop + height;
            int fadingEdge = this.getVerticalFadingEdgeLength();
            if (rect.top > 0) {
                screenTop += fadingEdge;
            }

            if (rect.bottom < this.getChildAt(0).getHeight()) {
                screenBottom -= fadingEdge;
            }

            int scrollYDelta = 0;
            if (rect.bottom > screenBottom && rect.top > screenTop) {
                if (rect.height() > height) {
                    scrollYDelta += rect.top - screenTop;
                } else {
                    scrollYDelta += rect.bottom - screenBottom;
                }

                int bottom = this.getChildAt(0).getBottom();
                int distanceToBottom = bottom - screenBottom;
                scrollYDelta = Math.min(scrollYDelta, distanceToBottom);
            } else if (rect.top < screenTop && rect.bottom < screenBottom) {
                if (rect.height() > height) {
                    scrollYDelta -= screenBottom - rect.bottom;
                } else {
                    scrollYDelta -= screenTop - rect.top;
                }

                scrollYDelta = Math.max(scrollYDelta, -this.getScrollY());
            }

            return scrollYDelta;
        }
    }

    public void requestChildFocus(View child, View focused) {
        if (!this.mTwoDScrollViewMovedFocus) {
            if (!this.mIsLayoutDirty) {
                this.scrollToChild(focused);
            } else {
                this.mChildToScrollTo = focused;
            }
        }

        super.requestChildFocus(child, focused);
    }

    protected boolean onRequestFocusInDescendants(int direction, Rect previouslyFocusedRect) {
        if (direction == 2) {
            direction = 130;
        } else if (direction == 1) {
            direction = 33;
        }

        View nextFocus = previouslyFocusedRect == null ? FocusFinder.getInstance().findNextFocus(this, (View)null, direction) : FocusFinder.getInstance().findNextFocusFromRect(this, previouslyFocusedRect, direction);
        return nextFocus == null ? false : nextFocus.requestFocus(direction, previouslyFocusedRect);
    }

    public boolean requestChildRectangleOnScreen(View child, Rect rectangle, boolean immediate) {
        rectangle.offset(child.getLeft() - child.getScrollX(), child.getTop() - child.getScrollY());
        return this.scrollToChildRect(rectangle, immediate);
    }

    public void requestLayout() {
        this.mIsLayoutDirty = true;
        super.requestLayout();
    }

    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        this.mIsLayoutDirty = false;
        if (this.mChildToScrollTo != null && this.isViewDescendantOf(this.mChildToScrollTo, this)) {
            this.scrollToChild(this.mChildToScrollTo);
        }

        this.mChildToScrollTo = null;
        this.scrollTo(this.getScrollX(), this.getScrollY());
    }

    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        View currentFocused = this.findFocus();
        if (null != currentFocused && this != currentFocused) {
            currentFocused.getDrawingRect(this.mTempRect);
            this.offsetDescendantRectToMyCoords(currentFocused, this.mTempRect);
            int scrollDeltaX = this.computeScrollDeltaToGetChildRectOnScreen(this.mTempRect);
            int scrollDeltaY = this.computeScrollDeltaToGetChildRectOnScreen(this.mTempRect);
            this.doScroll(scrollDeltaX, scrollDeltaY);
        }
    }

    private boolean isViewDescendantOf(View child, View parent) {
        if (child == parent) {
            return true;
        } else {
            ViewParent theParent = child.getParent();
            return theParent instanceof ViewGroup && this.isViewDescendantOf((View)theParent, parent);
        }
    }

    public void fling(int velocityX, int velocityY) {
        if (this.getChildCount() > 0) {
            int height = this.getHeight() - this.getPaddingBottom() - this.getPaddingTop();
            int bottom = this.getChildAt(0).getHeight();
            int width = this.getWidth() - this.getPaddingRight() - this.getPaddingLeft();
            int right = this.getChildAt(0).getWidth();
            this.mScroller.fling(this.getScrollX(), this.getScrollY(), velocityX, velocityY, 0, right - width, 0, bottom - height);
            boolean movingDown = velocityY > 0;
            boolean movingRight = velocityX > 0;
            View newFocused = this.findFocusableViewInMyBounds(movingRight, this.mScroller.getFinalX(), movingDown, this.mScroller.getFinalY(), this.findFocus());
            if (newFocused == null) {
                newFocused = this;
            }

            if (newFocused != this.findFocus() && ((View)newFocused).requestFocus(movingDown ? 130 : 33)) {
                this.mTwoDScrollViewMovedFocus = true;
                this.mTwoDScrollViewMovedFocus = false;
            }

            this.awakenScrollBars(this.mScroller.getDuration());
            this.invalidate();
        }

    }

    public void scrollTo(int x, int y) {
        if (this.getChildCount() > 0) {
            View child = this.getChildAt(0);
            x = this.clamp(x, this.getWidth() - this.getPaddingRight() - this.getPaddingLeft(), child.getWidth());
            y = this.clamp(y, this.getHeight() - this.getPaddingBottom() - this.getPaddingTop(), child.getHeight());
            if (x != this.getScrollX() || y != this.getScrollY()) {
                super.scrollTo(x, y);
            }
        }

    }

    private int clamp(int n, int my, int child) {
        if (my < child && n >= 0) {
            return my + n > child ? child - my : n;
        } else {
            return 0;
        }
    }
}
