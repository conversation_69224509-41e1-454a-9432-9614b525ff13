<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <HorizontalScrollView
        android:id="@+id/hsView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb1"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:checked="true"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_cebffffff_c1e1e1e"
                android:textSize="11dp"
                tools:text="MA" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb2"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                tools:text="EMA" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb3"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                tools:text="BOLL" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb4"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                android:visibility="visible"
                tools:text="MIKE" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb5"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                android:visibility="visible"
                tools:text="BBI" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb6"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                android:visibility="visible"
                tools:text="STR" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/cb7"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/select_checkbox_trading_view_parameters"
                android:button="@null"
                android:gravity="center"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="11dp"
                android:visibility="visible"
                tools:text="STR" />
        </LinearLayout>
    </HorizontalScrollView>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvTitle1"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@id/hsView"
        app:layout_constraintTop_toBottomOf="@id/hsView"
        tools:text="EMA1" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle1"
        app:layout_constraintStart_toEndOf="@+id/tvTitle1"
        app:layout_constraintTop_toTopOf="@+id/tvTitle1" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue1"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountDown1"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown1"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountDown1"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue1"
        app:layout_constraintStart_toEndOf="@+id/etValue1"
        app:layout_constraintTop_toTopOf="@+id/etValue1" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvTitle2"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@id/nsView"
        app:layout_constraintTop_toBottomOf="@id/tvTitle1"
        tools:text="EMA2" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle2"
        app:layout_constraintStart_toStartOf="@+id/ivHandCountDown1"
        app:layout_constraintTop_toTopOf="@+id/tvTitle2" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue2"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountDown2"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown2"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountDown2"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue2"
        app:layout_constraintStart_toEndOf="@+id/etValue2"
        app:layout_constraintTop_toTopOf="@+id/etValue2" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvTitle3"
        style="@style/bold_semi_font"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@id/nsView"
        app:layout_constraintTop_toBottomOf="@id/tvTitle2"
        tools:text="EMA3" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountDown3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_sub_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle3"
        app:layout_constraintStart_toStartOf="@+id/ivHandCountDown2"
        app:layout_constraintTop_toTopOf="@+id/tvTitle3" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etValue3"
        style="@style/bold_semi_font"
        android:layout_width="100dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r4"
        android:gravity="center"
        android:imeOptions="flagNoExtractUi"
        android:inputType="number"
        android:textAlignment="gravity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountDown3"
        app:layout_constraintStart_toEndOf="@+id/ivHandCountDown3"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountDown3"
        tools:ignore="HardcodedText"
        tools:text="0.01" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHandCountUp3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:contentDescription="@string/app_name"
        android:padding="6dp"
        android:src="@drawable/icon_source2_add_stroke_circle"
        android:tint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/etValue3"
        app:layout_constraintStart_toEndOf="@+id/etValue3"
        app:layout_constraintTop_toTopOf="@+id/etValue3" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNoData"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:minHeight="270dp"
        android:text="@string/this_indicator_doesn_any_configuration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nsView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvReset"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingTop="10dp"
        android:text="@string/reset"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/etValue3"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:background="?attr/color_cffffff_c262930"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hsView"
        tools:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDetail"
            style="@style/regular_font"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:minHeight="98dp"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="locale"
            android:textSize="12dp"
            tools:text="Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively." />
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupChild1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvTitle1,ivHandCountDown1,etValue1,ivHandCountUp1" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupChild2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvTitle2,ivHandCountDown2,etValue2,ivHandCountUp2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupChild3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvTitle3,ivHandCountDown3,etValue3,ivHandCountUp3" />
</androidx.constraintlayout.widget.ConstraintLayout>