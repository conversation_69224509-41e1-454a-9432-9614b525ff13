<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:elevation="0dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                app:layout_scrollFlags="scroll">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlStrategiesManage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvStrategiesTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/my_strategies"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:style="@style/gilroy_400" />

                    <!-- 嵌套的这一层ConstraintLayout是为了解决阿拉伯语相关问题，不能删 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        app:layout_constraintBottom_toBottomOf="@id/tvStrategiesTitle"
                        app:layout_constraintStart_toEndOf="@+id/tvStrategiesTitle"
                        app:layout_constraintTop_toTopOf="@+id/tvStrategiesTitle">

                        <TextView
                            android:id="@+id/tvStrategiesPublicValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layoutDirection="ltr"
                            android:text="0"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="HardcodedText"
                            tools:style="@style/gilroy_600" />

                        <TextView
                            android:id="@+id/tvStrategiesMaxValue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layoutDirection="ltr"
                            android:text="/10"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textDirection="ltr"
                            android:textSize="14dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvStrategiesPublicValue"
                            app:layout_constraintStart_toEndOf="@+id/tvStrategiesPublicValue"
                            tools:ignore="HardcodedText"
                            tools:style="@style/gilroy_400" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/tvManageStrategies"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
                        android:paddingHorizontal="@dimen/margin_horizontal_base"
                        android:paddingVertical="6dp"
                        android:text="@string/manage"
                        android:textColor="?attr/color_cebffffff_c1e1e1e"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:style="@style/gilroy_600" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlAccountInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/draw_main_card"
                    app:layout_constraintTop_toBottomOf="@+id/ctlStrategiesManage">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/ctlScrollTop"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="@dimen/padding_horizontal_base"
                        android:paddingEnd="0dp"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tvAccount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:text="@string/manual_trading"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="14dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvEquity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:singleLine="true"
                            android:text="..."
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="26dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvAccount"
                            tools:style="@style/bold_semi_font"
                            tools:text="1210.25" />

                        <TextView
                            android:id="@+id/tvCurrency"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingStart="6dp"
                            android:paddingTop="6dp"
                            android:paddingEnd="0dp"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintBottom_toBottomOf="@+id/tvEquity"
                            app:layout_constraintStart_toEndOf="@+id/tvEquity"
                            app:layout_constraintTop_toTopOf="@+id/tvEquity"
                            tools:style="@style/bold_semi_font"
                            tools:text="USD" />

                        <TextView
                            android:id="@+id/tvResetBalance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/reset"
                            android:paddingStart="6dp"
                            android:paddingEnd="0dp"
                            android:paddingVertical="6dp"
                            android:textColor="@color/ce35728"
                            android:textSize="12dp"
                            android:visibility="gone"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvAccount"
                            app:layout_constraintEnd_toStartOf="@+id/ivAccountInfoGlossary"
                            tools:ignore="SpUsage"
                            tools:style="@style/gilroy_500"
                            tools:visibility="visible" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/ivAccountInfoGlossary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="@string/app_name"
                            android:padding="@dimen/margin_horizontal_base"
                            app:layout_constraintBottom_toBottomOf="@id/tvAccount"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/tvAccount"
                            app:srcCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff" />

                        <androidx.constraintlayout.widget.Barrier
                            android:id="@+id/floatPnlBarrier"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:barrierDirection="bottom"
                            app:constraint_referenced_ids="tvFloatingPnLTitle,tvMarginLevelTitle,tvFreeMarginTitle" />

                        <TextView
                            android:id="@+id/tvFloatingPnLTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/margin_vertical_base"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="2"
                            android:text="@string/floating_pnl"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintEnd_toStartOf="@id/guideline_t_v33"
                            app:layout_constraintStart_toStartOf="@+id/tvEquity"
                            app:layout_constraintTop_toBottomOf="@+id/tvEquity"
                            tools:ignore="SpUsage"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvFloatingPnL"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewStart"
                            android:textColor="@color/c00c79c"
                            android:textDirection="ltr"
                            android:textSize="14dp"
                            app:layout_constraintEnd_toEndOf="@+id/tvFloatingPnLTitle"
                            app:layout_constraintStart_toStartOf="@+id/tvFloatingPnLTitle"
                            app:layout_constraintTop_toBottomOf="@+id/floatPnlBarrier"
                            app:layout_goneMarginStart="0dp"
                            tools:ignore="SpUsage"
                            tools:style="@style/medium_font"
                            tools:text="-13.42" />

                        <cn.com.vau.util.widget.DashedTextView
                            android:id="@+id/tvMarginLevelTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="2"
                            android:text="@string/margin_level"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvFloatingPnLTitle"
                            app:layout_constraintEnd_toEndOf="@id/guideline_t_v66"
                            app:layout_constraintStart_toEndOf="@+id/guideline_t_v33"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvMarginLevel"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewStart"
                            android:textColor="@color/c00c79c"
                            android:textSize="14dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvFloatingPnL"
                            app:layout_constraintEnd_toEndOf="@id/tvMarginLevelTitle"
                            app:layout_constraintStart_toStartOf="@id/tvMarginLevelTitle"
                            tools:style="@style/medium_font"
                            tools:text="59,155.23%" />

                        <TextView
                            android:id="@+id/tvFreeMarginTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="12dp"
                            android:gravity="end"
                            android:maxLines="2"
                            android:text="@string/free_margin"
                            android:textAlignment="viewEnd"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvMarginLevelTitle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/guideline_t_v66"
                            tools:ignore="SpUsage"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvFreeMargin"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewEnd"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvFloatingPnL"
                            app:layout_constraintEnd_toEndOf="@id/tvFreeMarginTitle"
                            app:layout_constraintStart_toStartOf="@id/tvFreeMarginTitle"
                            tools:ignore="SpUsage"
                            tools:style="@style/medium_font"
                            tools:text="1000" />

                        <androidx.constraintlayout.widget.Barrier
                            android:id="@+id/CreditTitleBarrier"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:barrierDirection="bottom"
                            app:constraint_referenced_ids="tvCreditTitle,tvMarginTitle,tvBalanceTitle" />

                        <TextView
                            android:id="@+id/tvCreditTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/margin_vertical_base_new"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="2"
                            android:text="@string/credit"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintEnd_toEndOf="@id/guideline_t_v33"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvFloatingPnL"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvCredit"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintEnd_toEndOf="@id/tvCreditTitle"
                            app:layout_constraintStart_toStartOf="@id/tvCreditTitle"
                            app:layout_constraintTop_toBottomOf="@+id/CreditTitleBarrier"
                            tools:style="@style/medium_font"
                            tools:text="1000" />

                        <TextView
                            android:id="@+id/tvBalanceTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:layout_marginEnd="12dp"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="2"
                            android:text="@string/balance"
                            android:textAlignment="viewEnd"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/guideline_t_v66"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvBalance"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewEnd"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
                            app:layout_constraintEnd_toEndOf="@id/tvBalanceTitle"
                            app:layout_constraintStart_toStartOf="@id/tvBalanceTitle"
                            tools:style="@style/medium_font"
                            tools:text="1000" />

                        <TextView
                            android:id="@+id/tvMarginTitle"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:ellipsize="end"
                            android:gravity="start"
                            android:maxLines="2"
                            android:text="@string/margin"
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                            android:textSize="12dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvCreditTitle"
                            app:layout_constraintEnd_toEndOf="@id/guideline_t_v66"
                            app:layout_constraintStart_toStartOf="@id/guideline_t_v33"
                            tools:style="@style/medium_font" />

                        <TextView
                            android:id="@+id/tvMargin"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:singleLine="true"
                            android:text="..."
                            android:textAlignment="viewStart"
                            android:textColor="?attr/color_c1e1e1e_cebffffff"
                            android:textSize="14dp"
                            app:layout_constraintBaseline_toBaselineOf="@+id/tvCredit"
                            app:layout_constraintEnd_toEndOf="@id/tvMarginTitle"
                            app:layout_constraintStart_toStartOf="@id/tvMarginTitle"
                            tools:style="@style/medium_font"
                            tools:text="1000" />

                        <androidx.constraintlayout.widget.Group
                            android:id="@+id/groupHide"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            app:constraint_referenced_ids="tvCreditTitle,tvCredit,tvBalanceTitle,
                            tvBalance,tvMarginTitle,tvMargin"
                            tools:visibility="visible" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline_t_v33"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.33" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline_t_v66"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.66" />

                        <!--                        <androidx.constraintlayout.widget.Guideline-->
                        <!--                            android:id="@+id/guideline_t_v50"-->
                        <!--                            android:layout_width="wrap_content"-->
                        <!--                            android:layout_height="wrap_content"-->
                        <!--                            android:orientation="vertical"-->
                        <!--                            app:layout_constraintGuide_percent="0.50" />-->

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <CheckBox
                        android:id="@+id/accountCheckBox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:button="@null"
                        android:drawableEnd="@drawable/select_checkbox_arrow_vertical"
                        android:drawablePadding="4dp"
                        android:text="@string/view_more"
                        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/ctlScrollTop"
                        tools:style="@style/regular_font" />

                    <!--                    <com.airbnb.lottie.LottieAnimationView-->
                    <!--                        android:id="@+id/ivLoading"-->
                    <!--                        android:layout_width="60dp"-->
                    <!--                        android:layout_height="60dp"-->
                    <!--                        android:visibility="gone"-->
                    <!--                        app:layout_constraintBottom_toBottomOf="parent"-->
                    <!--                        app:layout_constraintEnd_toEndOf="parent"-->
                    <!--                        app:layout_constraintStart_toStartOf="parent"-->
                    <!--                        app:layout_constraintTop_toTopOf="parent"-->
                    <!--                        app:lottie_loop="true"-->
                    <!--                        app:lottie_rawRes="@raw/loading"-->
                    <!--                        app:lottie_speed="1.5" />-->

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <cn.com.vau.common.view.tablayout.DslTabLayout
                android:id="@+id/mTabLayout"
                android:layout_width="match_parent"
                android:layout_height="33dp"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivEnterHistory"
                android:layout_width="20dp"
                android:layout_height="0dp"
                android:layout_marginEnd="12dp"
                android:padding="2dp"
                android:src="?attr/icon2HistoryEnter"
                app:layout_constraintBottom_toBottomOf="@id/mTabLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/mTabLayout" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@id/mTabLayout" />

            <cn.com.vau.common.view.MultiNestedScrollableHost
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mTabLayout">

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/mViewPager2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </cn.com.vau.common.view.MultiNestedScrollableHost>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        <include-->
        <!--            android:id="@+id/layoutMarketMaintenance"-->
        <!--            layout="@layout/layout_market_maintenance"-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_behavior="@string/appbar_scrolling_view_behavior" />-->

        <ViewStub
            android:id="@+id/mViewStubMarketMaintenance"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout="@layout/layout_market_maintenance_new"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
