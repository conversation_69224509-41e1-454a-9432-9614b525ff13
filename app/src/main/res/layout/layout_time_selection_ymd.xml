<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <View
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        app:layout_constraintBottom_toBottomOf="@id/pvYear"
        app:layout_constraintTop_toTopOf="@id/pvYear" />

    <cn.com.vau.common.view.timeSelection.PickerViewNew
        android:id="@+id/pvMonth"
        android:layout_width="0dp"
        android:layout_height="176dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/pvDay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.timeSelection.PickerViewNew
        android:id="@+id/pvDay"
        android:layout_width="0dp"
        android:layout_height="176dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/pvYear"
        app:layout_constraintStart_toEndOf="@+id/pvMonth"
        app:layout_constraintTop_toTopOf="@id/pvMonth" />


    <cn.com.vau.common.view.timeSelection.PickerViewNew
        android:id="@+id/pvYear"
        android:layout_width="0dp"
        android:layout_height="176dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/pvDay"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>