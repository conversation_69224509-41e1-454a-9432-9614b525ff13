<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/mainLayoutBg"
    tools:context="cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        app:hb_titleText="@string/identity_verification"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:layout_constraintTop_toTopOf="parent"/>

    <cn.com.vau.common.view.StepOpenAccountView
        android:id="@+id/stepView"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginTop="20dp"
        android:layout_marginHorizontal="@dimen/padding_horizontal_base"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:step_num="2"
        app:step_num_total="2" />

    <TextView
        android:id="@+id/tvSecondTitle"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:text="@string/upload_your_proof_address"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepView" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/fold_content_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSecondTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/padding_horizontal_base">

            <TextView
                android:id="@+id/tvOpenAccountProgressDesc"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/please_choose_one_below"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="13dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvBankStatement"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/bank_statement"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOpenAccountProgressDesc"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                android:id="@+id/tvUtilityBills"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="6dp"
                android:paddingEnd="20dp"
                android:paddingBottom="6dp"
                android:text="@string/utility_bills_water_phone"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvBankStatement"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                android:id="@+id/tvLetterIssued"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:drawablePadding="10dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="6dp"
                android:paddingEnd="20dp"
                android:paddingBottom="6dp"
                android:text="@string/letter_issued_by_company"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvUtilityBills"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="@string/as_a_regulated_we_identity"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvLetterIssued" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
