<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layoutDirection="ltr"
    android:paddingStart="@dimen/margin_horizontal_base"
    android:paddingEnd="45dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clChartTipFloat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/draw_shape_cf3f3f3_c262930_r4"
        android:layoutDirection="locale"
        android:minWidth="90dp"
        android:padding="4dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tvChartTipTime"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="9dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="31/08/2023 14:30" />

        <TextView
            android:id="@+id/tvChartTipOpen"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="@string/open__quotation"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipOpenValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipTime" />

        <TextView
            android:id="@+id/tvChartTipOpenValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipOpen"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipOpen"
            app:layout_constraintTop_toTopOf="@id/tvChartTipOpen"
            tools:text="2383.823" />

        <TextView
            android:id="@+id/tvChartTipHigh"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/high_most"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipHighValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipOpen" />

        <TextView
            android:id="@+id/tvChartTipHighValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipHigh"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipHigh"
            app:layout_constraintTop_toTopOf="@id/tvChartTipHigh"
            tools:text="2383.823" />

        <TextView
            android:id="@+id/tvChartTipLow"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/low_most"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipLowValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipHigh" />

        <TextView
            android:id="@+id/tvChartTipLowValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipLow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipLow"
            app:layout_constraintTop_toTopOf="@id/tvChartTipLow"
            tools:text="2383.823" />

        <TextView
            android:id="@+id/tvChartTipClose"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/close__quotation"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipCloseValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipLow" />

        <TextView
            android:id="@+id/tvChartTipCloseValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipClose"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipClose"
            app:layout_constraintTop_toTopOf="@id/tvChartTipClose"
            tools:text="2383.823" />

        <!-- 涨跌额 -->
        <TextView
            android:id="@+id/tvChartTipChangeAmount"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/kline_tips_change"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipChangeAmountValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipClose" />

        <TextView
            android:id="@+id/tvChartTipChangeAmountValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipChangeAmount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipChangeAmount"
            app:layout_constraintTop_toTopOf="@id/tvChartTipChangeAmount"
            tools:text="2383.823" />

        <!-- 涨跌幅 -->
        <TextView
            android:id="@+id/tvChartTipChangePercent"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/percent_change"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipChangePercentValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipChangeAmount" />

        <TextView
            android:id="@+id/tvChartTipChangePercentValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipChangePercent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toEndOf="@id/tvChartTipChangePercent"
            app:layout_constraintTop_toTopOf="@id/tvChartTipChangePercent"
            tools:text="2383.823" />

        <!-- 振幅 -->
        <TextView
            android:id="@+id/tvChartTipmplitude"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:text="@string/fluctuation"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toStartOf="@id/tvChartTipAmplitudeValue"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvChartTipChangePercent" />

        <TextView
            android:id="@+id/tvChartTipAmplitudeValue"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:minWidth="50dp"
            android:textAlignment="viewEnd"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="9dp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/tvChartTipmplitude"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvChartTipmplitude"
            app:layout_constraintTop_toTopOf="@id/tvChartTipmplitude"
            tools:text="2383.823" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>