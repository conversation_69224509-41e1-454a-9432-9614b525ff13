package cn.com.vau.signals.presenter

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.util.mathCompTo
import cn.com.vau.data.DataObjStringBean
import io.reactivex.disposables.Disposable

/**
 * Created by liyang.
 * live
 */
class StSignalsPresenter : StSignalsContract.Presenter() {

    override fun selectVideoCount() {
        mModel?.selectVideoCount(object : BaseObserver<DataObjStringBean>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: DataObjStringBean) {
                if ("V00000" != dataBean.resultCode || dataBean.data == null) {
                    return
                }
                if (dataBean.data.obj?.mathCompTo("0") == 1) {
                    mView?.getLiveCountSucceed()
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun jumpType() {
        mModel?.selectVideoCount(object : BaseObserver<DataObjStringBean>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: DataObjStringBean) {
                if ("V00000" != dataBean.resultCode || dataBean.data == null) {
                    return
                }
                mView?.jumpType(dataBean.data.obj?.toIntOrNull()?:0)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

}