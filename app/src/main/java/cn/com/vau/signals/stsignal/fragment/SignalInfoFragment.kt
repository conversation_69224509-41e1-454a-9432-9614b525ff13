package cn.com.vau.signals.stsignal.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.*
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity.RESULT_OK
import androidx.core.content.ContextCompat
import androidx.core.text.*
import androidx.core.view.isVisible
import androidx.fragment.app.*
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants.REQUEST_CODE_PERSONAL_INFO
import cn.com.vau.common.constants.Constants.REQUEST_CODE_PHONE
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.data.profile.TelegramGetBotIdObjBean
import cn.com.vau.databinding.*
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.*
import cn.com.vau.page.login.activity.VerifyEmailCodeActivity.VerifyEmailCodeType
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.page.user.login.TelegramHelper
import cn.com.vau.profile.activity.authentication.AuthenticationActivity
import cn.com.vau.profile.activity.kycAuth.KycAuthActivity
import cn.com.vau.profile.activity.kycLink.*
import cn.com.vau.profile.activity.twoFactorAuth.activity.*
import cn.com.vau.profile.activity.updateMobileNumber.UpdateMobileNumberActivity
import cn.com.vau.profile.viewmodel.ProfileViewModel.Companion.TAG_AUDIT_STATUS
import cn.com.vau.profile.viewmodel.ProfileViewModel.Companion.TAG_AUDIT_STATUS
import cn.com.vau.signals.stsignal.activity.EditPersonalInfoActivity
import cn.com.vau.signals.stsignal.viewmodel.PersonalDetailsViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * 个人信息使用
 */
class SignalInfoFragment : BaseMvvmBindingFragment<FragmentSignalInfoBinding>() {

    private val mViewModel: PersonalDetailsViewModel by activityViewModels()
    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null
    private val arrowEndDrawable: Drawable? by lazy {
        ContextCompat.getDrawable(requireContext(), R.drawable.draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff)?.apply {
            setBounds(0, 0, this.intrinsicWidth, this.intrinsicHeight)
        }
    }
    private var vsNoData: VsLayoutNoDataScrollBinding? = null

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        sendCodeViewModel.signUpRequestBean = SignUpRequestBean() // 为空先构建一个，方便使用
        mBinding.tvUid.text = mViewModel.getUid()
        mBinding.tvVerified.text = mViewModel.verifiedStatus

        if (mViewModel.isKycAccount()) {
            mBinding.tvEmail.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, arrowEndDrawable, null)
        } else {
            mBinding.tvEmail.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, null, null)
        }
        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                vsNoData = VsLayoutNoDataScrollBinding.bind(inflated)
                vsNoData?.mNoDataScrollView?.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
                vsNoData?.mNoDataScrollView?.setHintMessage(getString(R.string.no_records_found))
                vsNoData?.mNoDataScrollView?.setBottomBtnViewClickListener {
                    initPersonalLayout()
                }
            }
        })
        val supervise = SpManager.getSuperviseNum("")
        if (supervise == "1")
            mBinding.groupAuth.isVisible = false

    }

    override fun initListener() {
        super.initListener()
        mBinding.tvInfoDescEdit.clickNoRepeat {
            openActivityForResult(EditPersonalInfoActivity.createIntent(requireContext(), description = mBinding.tvInfoDescEdit.text.toString().trim()))
        }
        // 复制uid
        mBinding.tvUid.clickNoRepeat {
            mViewModel.getUid().copyText(toast = getString(R.string.uid_copied))
            SensorsDataUtil.track(SensorsConstant.V3540.PERSONAL_PROFILE_PAGE_UID_COPY_CLICK)
        }
        // 设置认证状态
        mBinding.tvVerified.clickNoRepeat {
            if (mViewModel.isKycAccount()) {
                KycAuthActivity.openActivity(requireActivity())
            } else {
                AuthenticationActivity.openActivity(requireActivity())
            }
        }
        // 手机号
        mBinding.tvPhone.clickNoRepeat {
            if (mViewModel.isKycAccount()) {
                if (mViewModel.profileObjLiveData.value == null) return@clickNoRepeat
                when (mViewModel.profileObjLiveData.value?.phoneStatus) {
                    PersonalInfoObj.BOUND_NOT_VERIFIED -> { // 已绑定未验证跳验证
                        sendCodeViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.VERIFY_PHONE_KYC
                        addSendPhoneCodeParams()
                        VerifySmsCodeActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
                    }

                    PersonalInfoObj.BOUND_VERIFIED -> { // 已验证跳修改
                        sendCodeViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.UPDATE_PHONE_OLD_KYC
                        sendPhoneCode("3")
                    }

                    else -> { // 未绑定跳绑定
                        sendCodeViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.BIND_PHONE_KYC
                        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
                        BindPhoneKycActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
                    }
                }
                mViewModel.sensorsTrack("Mobile", mViewModel.profileObjLiveData.value?.phoneStatus)
            } else {
                openActivity(UpdateMobileNumberActivity::class.java)
            }
        }
        // 邮箱
        mBinding.tvEmail.clickNoRepeat {
            if (mViewModel.isKycAccount()) {
                if (mViewModel.profileObjLiveData.value == null) return@clickNoRepeat
                when (mViewModel.profileObjLiveData.value?.emailStatus) {
                    PersonalInfoObj.BOUND_NOT_VERIFIED -> { // 已绑定未验证跳验证
                        sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.VERIFY_EMAIL_KYC
                        addSendEmailCodeParams()
                        VerifyEmailCodeActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
                    }

                    PersonalInfoObj.BOUND_VERIFIED -> { // 已验证跳修改
                        mViewModel.getAuthConfigApi("modify-email")
                    }

                    else -> { // 未绑定跳绑定
                        sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.BIND_EMAIL_KYC
                        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
                        BindEmailKycActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
                    }
                }
                mViewModel.sensorsTrack("E-Mail", mViewModel.profileObjLiveData.value?.emailStatus)
            }
        }
        // 绑定/解绑 telegram
        mBinding.tvTelegram.clickNoRepeat {
            if (mViewModel.profileObjLiveData.value?.telegramStatus == null) return@clickNoRepeat
            if (mViewModel.profileObjLiveData.value?.telegramStatus == 2) { // 已绑定就解绑
                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.unlink_telegram_account))
                    .setContent(getString(R.string.you_will_no_quick_login))
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.unlink))
                    .setOnEndListener {
                        mViewModel.telegramUnbindingApi()
                        // 神策自定义埋点
                        SensorsDataUtil.track(SensorsConstant.ThirdLogin.UNLINK_TELEGRAM_CLICK)
                    }
                    .build()
                    .showDialog()
            } else {
                mViewModel.telegramGetBotIdApi()
                // 神策自定义埋点
                SensorsDataUtil.track(SensorsConstant.ThirdLogin.LOGIN_TELEGRAM_CLICK, JSONObject().apply {
                    put(SensorsConstant.Key.POSITION, "profile_page")
                })
            }
        }

    }

    override fun initData() {
        super.initData()
        initPersonalLayout()
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.profileObjLiveData.observe(this) {
            if (it == null) return@observe
            refreshInfo(it)
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    PersonalDetailsViewModel.EVENT_UPDATE_SIGN_CONTENT -> {
                        mBinding.tvInfoDescEdit.text = it.data.toString()
                    }

                    PersonalDetailsViewModel.EVENT_SHOW_RETRY -> {
                        showRetry()
                    }

                    PersonalDetailsViewModel.EVENT_SHOW_TELEGRAM_BINDING -> {
                        telegramBindingSuccess()
                    }

                    PersonalDetailsViewModel.EVENT_SHOW_TELEGRAM_UNBINDING -> {
                        telegramUnbindingSuccess()
                    }

                    PersonalDetailsViewModel.EVENT_TELEGRAM_BOT_ID -> {
                        telegramGetBotIdSuccess(it.data as? TelegramGetBotIdObjBean)
                    }

                    PersonalDetailsViewModel.EVENT_SHOW_RECONNECT -> {
                        //點擊來重連
                        mBinding.mVsNoDataScroll.visibility = View.VISIBLE
                        vsNoData?.mNoDataScrollView?.setBottomBtnViewClickListener {
                            initPersonalLayout()
                            if (UserDataUtil.isCopyTradingAccount()) {
                                mViewModel.getSignalInfoApi()
                            }
                        }
                    }
                    TAG_AUDIT_STATUS -> {
                        if (it.data !is AuditStatusData.Obj) {
                            // 如果网络请求错误 就显示获取认证的文案
                            updateVerifiedButton("-1", "-1")
                            return@collectLatest
                        }
                        updateVerifiedButton(it.data.accountAuditStatus.ifNull("-1"), it.data.poiAuditStatus.ifNull("-1"))
                    }
                }
            }
        }
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha(it)
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                VerifyEmailCodeActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
            } else {
                VerifySmsCodeActivity.open(requireContext(), sendCodeViewModel.signUpRequestBean)
            }
        }
        // 获取安全中心配置信息成功
        mViewModel.getAuthConfigSuccessLiveData.observe(this) {
            sendCodeViewModel.signUpRequestBean?.validateType = it?.second?.validateType
            if (it.first == "modify-email") { // 修改邮箱
                sendCodeViewModel.signUpRequestBean?.verifyEmailCodeType = VerifyEmailCodeType.UPDATE_EMAIL_OLD_KYC
                sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
                // 0=未绑定，跳转到绑定2FA页面；1=已绑定，跳转到验证2FA页面
                sendCodeViewModel.signUpRequestBean?.email = it?.second?.email // 目前只有修改邮箱会触发此流程，所以就先保存个邮箱就够了
                if (it?.second?.totpBindStatus == 1) {
                    TFAVerifyActivity.open(requireContext(), TFAVerifyActivity.FROM_KYC_UPDATE_EMAIL, signUpRequestBean = sendCodeViewModel.signUpRequestBean)
                } else {
                    TFABindActivity.open(requireContext(), TFAVerifyActivity.FROM_KYC_UPDATE_EMAIL, signUpRequestBean = sendCodeViewModel.signUpRequestBean)
                }
            }
        }
    }

    /**
     * 更新用户的验证状态
     */
    private fun updateVerifiedButton(lv1Status: String?, lv2Status: String?) {
        mBinding.tvVerified.text =
            if (lv1Status == AuthenticationActivity.TYPE_LV1_COMPLETED && lv2Status == AuthenticationActivity.TYPE_COMPLETED) {
                AuthenticationActivity.getVerifyString(requireContext(), AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
            } else if (lv1Status == AuthenticationActivity.TYPE_LV1_COMPLETED || lv2Status == AuthenticationActivity.TYPE_COMPLETED) {
                AuthenticationActivity.getVerifyString(requireContext(), AuthenticationActivity.TEXTVIEW_TYPE_SEMI_VERIFIED)
            } else {
                AuthenticationActivity.getVerifyString(requireContext(), AuthenticationActivity.TEXTVIEW_TYPE_GET_VERIFIED)
            }
    }
    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha(type: String) {
        mCaptcha = CaptchaUtil.getCaptcha(requireContext()) {
            if (sendCodeViewModel.signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) { // 邮箱
                sendCodeViewModel.sendEmailCodeApi(type, it)
            } else {// 手机号
                sendCodeViewModel.sendPhoneCodeApi(type, it)
            }
        }
        mCaptcha?.validate()
    }

    /**
     * 发送手机验证码参数
     */
    private fun addSendPhoneCodeParams() {
        sendCodeViewModel.signUpRequestBean?.countryCode = mViewModel.profileObjLiveData.value?.phoneCountryCode
        sendCodeViewModel.signUpRequestBean?.countryNum = mViewModel.profileObjLiveData.value?.phoneCode
        sendCodeViewModel.signUpRequestBean?.mobile = mViewModel.profileObjLiveData.value?.phoneStr
        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
    }

    /**
     * 发送手机验证码
     */
    private fun sendPhoneCode(type: String) {
        addSendPhoneCodeParams()
        sendCodeViewModel.sendPhoneCodeApi(type)
    }

    /**
     * 发送邮箱验证码参数
     */
    private fun addSendEmailCodeParams() {
        sendCodeViewModel.signUpRequestBean?.email =  mViewModel.profileObjLiveData.value?.emailStr
        sendCodeViewModel.signUpRequestBean?.sendCodeType = SendCodeType.EMAIL
    }

    /**
     * 发送邮箱验证码
     */
    private fun sendEmailCode(type: String) {
        addSendEmailCodeParams()
        sendCodeViewModel.sendEmailCodeApi(type)
    }

    private fun initPersonalLayout() {
        mViewModel.userformGetUserForumApi()
    }

    private fun saveIntroduction(personalInfo: String?) {
        mViewModel.userformUpdateApi(
            userNick = UserDataUtil.nickname(),
            signContent = personalInfo ?: "",
        )
    }

    @SuppressLint("SetTextI18n")
    private fun refreshInfo(dataObj: PersonalInfoObj?) {
        if (dataObj == null) return
        mBinding.mVsNoDataScroll.visibility = View.GONE

        mBinding.tvUid.text = dataObj?.crmUserId
        mBinding.tvInfoDescEdit.text = dataObj.signcontent
        if (dataObj.country?.isEmpty() == true) {
            mBinding.tvLocation.visibility = View.GONE
        } else {
            mBinding.tvLocation.visibility = View.VISIBLE
            mBinding.tvLocation.text = dataObj.country
        }

        updatePhoneState(dataObj.phoneNumDesensitization, dataObj.phoneStatus ?: PersonalInfoObj.UNBOUND)
        updateEmailState(dataObj.email, dataObj.emailStatus ?: PersonalInfoObj.UNBOUND)
        // telegram绑定状态
        telegramBindingStatus(dataObj.telegramStatus == 2) // 2=已绑定
        mBinding.tvBirthday.text = if (dataObj.birthday.isNullOrEmpty()) "—" else dataObj.birthday

        if (dataObj.country.isNullOrEmpty() && dataObj.userAddr1.isNullOrEmpty() && dataObj.userAddr2.isNullOrEmpty()) {
            mBinding.tvResidence.text = "—"
        } else {
            if (dataObj.country == dataObj.userAddr1) {
                if (dataObj.userAddr1 == dataObj.userAddr2) {
                    mBinding.tvResidence.text = dataObj.userAddr2
                } else {
                    mBinding.tvResidence.text = "${dataObj.userAddr2} ${dataObj.userAddr1}"
                }
            } else {
                mBinding.tvResidence.text = "${dataObj.userAddr2} ${dataObj.userAddr1} ${dataObj.country}"
            }
        }

        mBinding.tvName.text = if (dataObj.username.isNullOrEmpty()) "—" else dataObj.username
        mBinding.tvAddress.text = if (dataObj.userAddr3.isNullOrEmpty()) "—" else dataObj.userAddr3

        UserDataUtil.setUserRealName(dataObj.username)
        EventBus.getDefault().post(NoticeConstants.LINK_THIRD_PARTY)
    }

    /**
     * 更新手机号状态
     */
    private fun updatePhoneState(phone: String?, phoneStatus: Int) {
        when (phoneStatus) {
            PersonalInfoObj.BOUND_VERIFIED -> {
                mBinding.tvPhone.text = if (phone.isNullOrEmpty()) "—" else phone
            }

            PersonalInfoObj.UNBOUND -> {
                mBinding.tvPhone.text = buildSpannedString { color(ContextCompat.getColor(requireContext(), R.color.ce35728)) { append(getString(R.string.not_linked)) } }
            }

            PersonalInfoObj.BOUND_NOT_VERIFIED -> {
                mBinding.tvPhone.text = buildSpannedString { color(ContextCompat.getColor(requireContext(), R.color.ce35728)) { append(getString(R.string.not_verified)) } }
            }
        }
    }

    /**
     * 更新邮箱状态
     */
    private fun updateEmailState(email: String?, emailStatus: Int) {
        when (emailStatus) {
            PersonalInfoObj.BOUND_VERIFIED -> {
                mBinding.tvEmail.text = if (email.isNullOrEmpty()) "—" else email
            }

            PersonalInfoObj.UNBOUND -> {
                mBinding.tvEmail.text = buildSpannedString { color(ContextCompat.getColor(requireContext(), R.color.ce35728)) { append(getString(R.string.not_linked)) } }
            }

            PersonalInfoObj.BOUND_NOT_VERIFIED -> {
                mBinding.tvEmail.text = buildSpannedString { color(ContextCompat.getColor(requireContext(), R.color.ce35728)) { append(getString(R.string.not_verified)) } }
            }
        }
    }

    /**
     * telegram绑定状态
     */
    private fun telegramBindingStatus(isBinding: Boolean) {
        mBinding.tvTelegram.run {
            text = if (isBinding) getString(R.string.linked) else getString(R.string.not_linked)
            val drawable = compoundDrawables[2] // 获取左侧的图标
            val resId = if (isBinding) R.drawable.img_telegram_liked else AttrResourceUtil.getDrawable(requireContext(), R.attr.imgTelegramNotLinked)
            val leftDrawable = ContextCompat.getDrawable(requireContext(), resId)
            setCompoundDrawablesWithIntrinsicBounds(leftDrawable, null, drawable, null)
        }
        EventBus.getDefault().post(DataEvent(NoticeConstants.REFRESH_TELEGRAM_BINDING_RESULT, isBinding))
    }

    private fun showRetry() {
        mBinding.mVsNoDataScroll.visibility = View.VISIBLE
        mBinding.mVsNoDataScroll.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
    }

    private fun openActivityForResult(intent: Intent) {
        startForResult.launch(Intent(intent))
    }

    @SuppressLint("SetTextI18n")
    private val startForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == REQUEST_CODE_PHONE) {
                val intent = result.data
                if (intent != null) {
                    if (intent.getBooleanExtra("changedPhone", false)) {
                        mBinding.tvPhone.text = "+${UserDataUtil.areaCode()} ${UserDataUtil.userTel()}"
                    }
                }
            } else if (result.resultCode == REQUEST_CODE_PERSONAL_INFO) {
                val intent = result.data
                if (intent != null) {
                    val personalInfo = intent.getStringExtra(EditPersonalInfoActivity.KEY_DESCRIPTION)
                    saveIntroduction(personalInfo)
                }
            }
        }

    /**
     * telegram绑定成功
     */
    private fun telegramBindingSuccess() {
        mViewModel.profileObjLiveData.value?.telegramStatus = 2 // 重新赋值，避免用户再次点击按钮进行解绑操作
        telegramBindingStatus(true)
    }

    /**
     * telegram解绑成功
     */
    private fun telegramUnbindingSuccess() {
        mViewModel.profileObjLiveData.value?.telegramStatus = 1 // 重新赋值，避免用户再次点击按钮进行绑定操作
        telegramBindingStatus(false)
    }

    /**
     * telegram-获取botId成功
     */
    private fun telegramGetBotIdSuccess(bean: TelegramGetBotIdObjBean?) {
        val intent = TelegramHelper.getTelegramH5Intent(requireContext(), bean)
        telegramH5AuthLauncher.launch(intent)
    }

    /**
     * telegram H5授权完成回调
     */
    private val telegramH5AuthLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            mViewModel.telegramBindingApi()
        }
    }



    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.NOTICE_SECURITY_REFRESH,
            NoticeConstants.NETWORK_AVAILABLE -> initData()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        mCaptcha?.destroy()
    }

    companion object {
        fun newInstance(): SignalInfoFragment {
            val fragment = SignalInfoFragment()
            return fragment
        }
    }

}