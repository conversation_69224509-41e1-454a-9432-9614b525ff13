package cn.com.vau.signals.stsignal.center.adapter

import android.graphics.Paint
import android.widget.TextView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * author：lvy
 * date：2024/04/03
 * desc：信号源中心->Strategies->Public
 */
class StStrategiesPublicAdapter(private val tabType: Int) :
    BaseQuickAdapter<StrategyBean, BaseViewHolder>(R.layout.item_st_signal_center_strategies_public) {

    private val currencyType by lazy { UserDataUtil.currencyType() } // 当前登录跟单账户的币种

    override fun convert(holder: BaseViewHolder, item: StrategyBean) {
        val currency = if (tabType == 2) {
            // 创建草稿时，如果未选择账户，则显示当前登录跟单账户的币种，否则显示创建时选择的账户币种（********与yun yu确认）
            item.sourceAccountCurrency.ifNull(currencyType)
        } else {
            item.currency // 不是草稿，则显示后台返回的币种
        }
        //头像
        val ivAvatar = holder.getView<ShapeableImageView>(R.id.ivAvatar)
        ImageLoaderUtil.loadImage(context, item.avatar, ivAvatar, R.mipmap.ic_launcher)

        //策略名称，后面untitled跟PM允雨确认不需要多语言
        holder.setText(
            R.id.tvNick,
            if (item.strategyName.isNullOrBlank()) "untitled ($currency)" else "${item.strategyName} ($currency)"
        )

        //策略id
        holder.setText(R.id.tvIdKey, "${context.getString(R.string.strategy_id)}：")
        holder.setGone(R.id.tvIdKey, tabType == 2)
            .setText(
                R.id.tvId, if (tabType == 2) {
                    context.getString(
                        R.string.saved_on_x, TimeUtil.millis2String(item.localCreateTime ?: 0, "dd/MM/yyyy HH:mm:ss")
                    )
                } else {
                    item.strategyNo
                }
            )

        //跟随者数量
        holder.setText(R.id.tvCurrentCopiers, "${item.copiers ?: 0}")

        //分润周期
        holder.setText(
            R.id.tvSettlement, when (item.settlementFrequency) {
                "1" -> context.getString(R.string.daily)
                "2" -> context.getString(R.string.weekly)
                "3" -> context.getString(R.string.monthly)
                else -> context.getString(R.string.monthly)
            }
        )

        //分润比例
        val profitRatio = item.profitShareRatio.ifNull().percent(0)
        holder.setText(R.id.tvProfitSharing, "${profitRatio}%")

        //跟单资产管理规模
        holder.setText(
            R.id.tvAum,
            "${(item.copyAum ?: "0").numCurrencyFormat(currency.ifNull())} $currency"
        )

        // 未支付分润
        holder.setText(
            R.id.tvUnpaidAmount,
            "${(item.pendingProfit ?: "0").numCurrencyFormat(currency.ifNull())} $currency"
        )

        //累计获得分润
        holder.setText(
            R.id.tvTotalHistoricalPayout,
            "${(item.totalReceivedProfit ?: "0").numCurrencyFormat(currency.ifNull())} $currency"
        )

        //按钮显示
        when (tabType) {
            0 -> {
                holder.setGone(R.id.tvMore, false)
                    .setGone(R.id.ivShare, false)
                    .setText(R.id.tvDelistOrPublic, context.getString(R.string.delist))
            }

            1 -> {
                holder.setGone(R.id.tvMore, false)
                    .setGone(R.id.ivShare, true)
                    .setText(R.id.tvDelistOrPublic, context.getString(R.string.publish))
            }

            2 -> {
                holder.setGone(R.id.tvMore, true)
                    .setGone(R.id.ivShare, true)
                    .setText(R.id.tvDelistOrPublic, context.getString(R.string.publish))
            }
        }

        val tvKey5 = holder.getView<TextView>(R.id.tvKey5)
        val tvKey6 = holder.getView<TextView>(R.id.tvKey6)
        tvKey5.paintFlags = Paint.UNDERLINE_TEXT_FLAG
        tvKey6.paintFlags = Paint.UNDERLINE_TEXT_FLAG
    }
}