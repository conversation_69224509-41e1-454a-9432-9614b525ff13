package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.databinding.*
import cn.com.vau.signals.stsignal.adapter.StStrategyDetailsPortfolioAdapter
import cn.com.vau.signals.stsignal.viewmodel.StStrategyDetailsViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.NoDataView

/**
 * author：lvy
 * date：2024/03/27
 * desc：策略详情->Portfolio
 */
class StStrategyDetailsPortfolioFragment :
    BaseMvvmFragment<FragmentStStrategyDetailsPortfolioBinding, StStrategyDetailsViewModel>() {

    private var strategyId: String? = null // 策略id。上个页面传入
    private var isProfile = false // 是否是个人中心进入的

    private val mAdapter by lazy {
        StStrategyDetailsPortfolioAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_records_found))
            })
        }
    }
    private val headerView by lazy {
        HeaderRecyclerStrategyDetailsPortfolioBinding.inflate(layoutInflater, mBinding.mRecyclerView, false)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        arguments?.apply {
            strategyId = getString(Constants.STRATEGY_ID)
            isProfile = getBoolean("isProfile")
        }
    }

    override fun initView() {
        mBinding.mRecyclerView.setHasFixedSize(true)
        mBinding.mRecyclerView.adapter = mAdapter

        if (isProfile) {
            headerView.tvTips.text = getString(R.string.signal_investment_notice)
            headerView.tvDailyChange.text = getString(R.string.profit_and_loss)
        } else {
            headerView.tvTips.text = getString(R.string.the_following_information_investment_performance)
            headerView.tvDailyChange.text = getString(R.string.daily_change)
        }
    }

    override fun initFont() {
        headerView.tvTips.setFontG400()
        headerView.tvSymbol.setFontG600()
        headerView.tvType.setFontG600()
        headerView.tvDailyChange.setFontG600()
    }

    override fun initData() {
        mViewModel.stStrategyPositionApi(strategyId)
    }

    override fun createObserver() {
        // 策略持仓列表
        mViewModel.strategyPositionLiveData.observe(this) {
            if (!it?.symbolInfo.isNullOrEmpty()) {
                mAdapter.removeHeaderView(headerView.root)
                mAdapter.addHeaderView(headerView.root)
            }
            mAdapter.setList(it?.symbolInfo)
        }
    }

    companion object {
        fun newInstance(strategyId: String?, isProfile: Boolean = false) = StStrategyDetailsPortfolioFragment().apply {
            arguments = bundleOf(
                Constants.STRATEGY_ID to strategyId,
                "isProfile" to isProfile
            )
        }
    }
}