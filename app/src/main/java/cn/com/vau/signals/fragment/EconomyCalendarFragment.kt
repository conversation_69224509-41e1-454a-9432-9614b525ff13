package cn.com.vau.signals.fragment

import android.annotation.SuppressLint
import android.app.Activity
import android.app.DatePickerDialog
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.databinding.FragmentCalendarBinding
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.signals.activity.EconomicCalendarActivity
import cn.com.vau.signals.activity.FiltersActivity
import cn.com.vau.signals.adapter.EconomyCalendarAdapter
import cn.com.vau.signals.viewModel.EconomyCalendarViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.NoDataView
import org.greenrobot.eventbus.*
import org.json.JSONObject
import java.util.*

/**
 * 财经日历
 */
class EconomyCalendarFragment : BaseMvvmBindingFragment<FragmentCalendarBinding>() {

    private val mViewModel: EconomyCalendarViewModel by viewModels(ownerProducer = { requireParentFragment() })
    private val calendarAdapter by lazy {
        EconomyCalendarAdapter().apply {
            setEmptyView(NoDataView(requireContext()).apply {
                setHintMessage(getString(R.string.no_calendar))
            })
        }
    }

    // 定义一个 ActivityResultLauncher 来处理回调
    private var resultLauncher: ActivityResultLauncher<Intent>? = null

    private var selectCalendar: Calendar? = null

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        mBinding.mRecyclerView.adapter = calendarAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(8.dp2px(), dividerColor = AttrResourceUtil.getColor(context = requireContext(), R.attr.color_c0a1e1e1e_c0affffff)))
    }

    override fun initFont() {
        mBinding.tvDate.setFontG500()
        mBinding.tvFilter.setFontG500()
    }

    override fun initData() {
        super.initData()
        setDateText(Calendar.getInstance())
        mViewModel.queryCalendarList()
    }

    override fun initListener() {
        super.initListener()
        mBinding.mRefreshLayout.setEnableLoadMore(false)
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.queryCalendarList()
        }
        resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            // 在这里处理回调结果
            if (result.resultCode == Activity.RESULT_OK) {
                mViewModel.importance = result.data?.getStringExtra(FiltersActivity.KEY_IMPORTANCE) ?: "all"
                mViewModel.areaCode = result.data?.getStringExtra(FiltersActivity.KEY_AREACODE) ?: "all"
                mBinding.mRefreshLayout.autoRefresh()
            }
        }
        mBinding.tvFilter.clickNoRepeat {
            resultLauncher?.launch(FiltersActivity.createIntent(requireContext(), mViewModel.areaCode, mViewModel.importance))
        }
        mBinding.tvDate.clickNoRepeat {
            showSelectDateDialog()
        }

        calendarAdapter.setNbOnItemClickListener { _, _, position ->
            val bean = calendarAdapter.getItemOrNull(position)
            openActivity(
                EconomicCalendarActivity::class.java, bundleOf(
                    "calendar_id" to bean?.dataId
                )
            )
            // 神策自定义埋点(v3500)
            sensorsTrack(bean?.dataId.ifNull().toString(), position, "")
        }

        calendarAdapter.setOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.ivState -> {
                    if (!UserDataUtil.isLogin()) {
                        openActivity(LoginActivity::class.java)
                        return@setOnItemChildClickListener
                    }
                    val bean = calendarAdapter.getItemOrNull(position)
                    if (bean?.isRemind.ifNull() !in 0..1) {
                        return@setOnItemChildClickListener
                    }
                    if (bean?.isRemind == 0) {
                        calendarAdapter.data.getOrNull(position)?.isRemind = 1
                        mViewModel.setUpRemind(bean.dataId)
                    } else {
                        calendarAdapter.data.getOrNull(position)?.isRemind = 0
                        mViewModel.cancelRemind(bean?.dataId)
                    }
                    calendarAdapter.notifyItemChanged(position, Constants.DEFAULT_PAYLOAD_KEY)
                }
            }
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.uiListLiveData.observeUIState(viewLifecycleOwner, calendarAdapter, mBinding.mRefreshLayout)
    }

    /**
     * 选择时间
     */
    private fun showSelectDateDialog() {
        val calendar: Calendar = Calendar.getInstance()
        calendar.time = Date()
        @SuppressLint("WrongConstant") val year: Int
        @SuppressLint("WrongConstant") val month: Int
        @SuppressLint("WrongConstant") val day: Int
        val cal = selectCalendar ?: Calendar.getInstance()
        year = cal.get(Calendar.YEAR)
        month = cal.get(Calendar.MONTH)
        day = cal.get(Calendar.DAY_OF_MONTH)

        DatePickerDialog(
            requireContext(),
            R.style.VFXDateDialogTheme, { _, mYear, mMonth, dayOfMonth ->
                calendar.set(Calendar.YEAR, mYear)
                calendar.set(Calendar.MONTH, mMonth)
                calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                val date = TimeUtil.getSafeDateFormat("dd/MM/yyyy").format(Date(calendar.timeInMillis))
                //显示是英文,入参要横杆
                mViewModel.queryDate = TimeUtil.getStrTime(TimeUtil.getTimeStr(date, "dd/MM/yyyy"), "yyyy-MM-dd")
                setDateText(calendar)
                //查询
                mViewModel.queryCalendarList()
            },
            year,
            month,
            day
        ).show()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setDateText(calendar: Calendar) {
        selectCalendar = calendar
        mBinding.tvDate.text = TimeUtil.date2String(calendar.time, "dd/MM/yyyy")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.CHANGE_OF_ECONOMIC_CALENDAR -> {
                mViewModel.queryCalendarList()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            NoticeConstants.CHANGE_ECONOMIC_CALENDAR_STATE -> {
                if (event.data !is MutableMap<*, *>) return
                event.data["dataId"]?.let {
                    val dataId = runCatching { it as String }.getOrNull().ifNull()
                    val position = calendarAdapter.data.indexOfFirst { it.dataId == dataId }
                    calendarAdapter.data.getOrNull(position)?.isRemind = (event.data["isRemind"] as? Int).ifNull()
                    calendarAdapter.notifyItemChanged(position, Constants.DEFAULT_PAYLOAD_KEY)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}
