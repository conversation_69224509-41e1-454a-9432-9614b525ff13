package cn.com.vau.signals.adapter

import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.view.system.ScaleImageView
import cn.com.vau.data.discover.PromoEventData
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.setFontG400
import cn.com.vau.util.setFontG600
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 11月 02 14:06
 * @updateUser:
 * @updateDate: 2024 11月 02 14:06
 */
class PromoAdapter : BaseQuickAdapter<PromoEventData, BaseViewHolder>(R.layout.item_recycler_home) {

    override fun convert(holder: BaseViewHolder, dataBean: PromoEventData) {
        ImageLoaderUtil.loadImageWithOption(
            context, dataBean.imgUrl, holder.getViewOrNull<ScaleImageView>(R.id.mImageView), RequestOptions()
                .placeholder(R.drawable.shape_placeholder)
                .error(R.drawable.shape_placeholder)
        )

        holder.setText(R.id.tvEventTitle, dataBean.appJumpDefModel?.title)
            .setText(
                R.id.tvEventTime, if ("1" == dataBean.longTerm) {
                    if (Constants.PromoTabTxt) context.getString(R.string.ongoing_promotions) else ""
                } else "${dataBean.startTime.ifNull()} - ${dataBean.endTime.ifNull()}".arabicReverseTextByFlag(" - ")
            )
            .setText(R.id.tvEventState, context.getString(if (0 == dataBean.eventsStatus) R.string.in_progress else R.string.coming_soon))

    }

    override fun onItemViewHolderCreated(viewHolder: BaseViewHolder, viewType: Int) {
        super.onItemViewHolderCreated(viewHolder, viewType)
        with(viewHolder) {
            getViewOrNull<AppCompatTextView>(R.id.tvEventTitle)?.setFontG600()
            getViewOrNull<AppCompatTextView>(R.id.tvEventTime)?.setFontG400()
            getViewOrNull<AppCompatTextView>(R.id.tvEventState)?.setFontG600()
        }
    }
}