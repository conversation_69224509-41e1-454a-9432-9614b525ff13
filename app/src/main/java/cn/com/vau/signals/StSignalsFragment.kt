package cn.com.vau.signals

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import androidx.fragment.app.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.databinding.FragmentSignalsBinding
import cn.com.vau.page.StickyEvent
import cn.com.vau.signals.fragment.*
import cn.com.vau.signals.live.LiveListFragment
import cn.com.vau.signals.model.StSignalsModel
import cn.com.vau.signals.presenter.*
import cn.com.vau.signals.stsignal.LiveEventData
import cn.com.vau.signals.stsignal.fragment.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * 首页发现的Fragment
 * Created by roy on 2018/10/16.
 */
class StSignalsFragment : BaseFrameFragment<StSignalsPresenter, StSignalsModel>(), StSignalsContract.View {

    private val mBinding by lazy { FragmentSignalsBinding.inflate(layoutInflater) }

    private val mViewModel by activityViewModels<MainViewModel>()

    private var isViewCreated: Boolean = false
    private var isUIVisible: Boolean = false

    private val fragmentList = mutableListOf<Fragment>()
    private val tabsStrList = mutableListOf<String>()

    private var liveId: Long = 0L
    private var isInner: Int = 0
    private var willScrollTabTag: String = ""

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        isViewCreated = true
        lazyInitView()
        return mBinding.root
    }

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
    }

    @SuppressLint("ObsoleteSdkInt")
    private fun lazyInitView() {

        if (!isUIVisible || !isViewCreated) return

        isUIVisible = false
        isViewCreated = false

        mPresenter.selectVideoCount()
        initTabLayoutTabs()

        if (willScrollTabTag.isNotEmpty()) {
            selectTab(willScrollTabTag)
            willScrollTabTag = ""
        }

        LogEventUtil.setLogEvent(
            BuryPointConstant.V343.GENERAL_DISCOVER_PAGE_VIEW,
            Bundle().apply {
                putString("Page_name", BuryPointConstant.DiscoverPageType.COPY_TRADING)
                putString(
                    "Account_type", when {
                        !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                        UserDataUtil.isStLogin() -> BuryPointConstant.AccountType.COPY_TRADING
                        UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                        else -> BuryPointConstant.AccountType.LIVE
                    }
                )
            }
        )

        // 神策自定义埋点(v3500)
        // App_Tab 页面浏览 -> app内五个tab页面加载完成时触发
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TAB_NAME, "Discover") // Tab 名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, properties)
    }

    override fun initFont() {
        mBinding.tvTitle.setFontG600()
    }

    private fun selectTab(tag: String) {
        // 如StSignalsFragment为新创建时，mTabLayout组件未被初始化，则mTabLayout默认停留在第一位
        mBinding.mTabLayout.post {
            val index = tabsStrList.indexOf(tag)
            if (index != -1) {
                mBinding.mViewPager.setCurrentItem(index, false)
            }
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initTabLayoutTabs() {

        if (!tabsStrList.contains(getString(R.string.economic_calendar))) {
            fragmentList.add(0, EconomyCalendarFragment())
            tabsStrList.add(0, getString(R.string.economic_calendar))
        }

        if (!tabsStrList.contains("24/7")) {
            fragmentList.add(0, FxStreetFragment())
            tabsStrList.add(0, "24/7")
        }

        if (!tabsStrList.contains(getString(R.string.academy))) {
            fragmentList.add(0, StAcademyFragment())
            tabsStrList.add(0, getString(R.string.academy))
        }

        if (!tabsStrList.contains(getString(R.string.community))) {
            fragmentList.add(0, StCommunityFragment())
            tabsStrList.add(0, getString(R.string.community))
        }

        if (!tabsStrList.contains(getString(R.string.analysis))) {
            fragmentList.add(AnalysesFragment())
            tabsStrList.add(getString(R.string.analysis))
        }

        if (!tabsStrList.contains(getString(R.string.newsletter))) {
            fragmentList.add(NewsletterFragment())
            tabsStrList.add(getString(R.string.newsletter))
        }

        if (!tabsStrList.contains(getString(R.string.fx_tv))) {
            fragmentList.add(WebTVFragment())
            tabsStrList.add(getString(R.string.fx_tv))
        }

        mBinding.mViewPager.init(fragmentList, tabsStrList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager, tabsStrList, TabType.TEXT_SCALE) { position ->
            LogEventUtil.setLogEvent(
                BuryPointConstant.V343.GENERAL_DISCOVER_PAGE_VIEW,
                Bundle().apply {
                    putString(
                        "Page_name", when (tabsStrList.getOrNull(position)) {
                            "24/7" -> BuryPointConstant.DiscoverPageType.TWENTY_FOUR_AND_SEVEN
                            getString(R.string.economic_calendar) -> BuryPointConstant.DiscoverPageType.ECONOMIC_CALENDAR
                            getString(R.string.webinar) -> BuryPointConstant.DiscoverPageType.LIVE
                            getString(R.string.analysis) -> BuryPointConstant.DiscoverPageType.ANALYSIS
                            getString(R.string.academy) -> BuryPointConstant.DiscoverPageType.ACADEMY
                            getString(R.string.newsletter) -> BuryPointConstant.DiscoverPageType.NEWSLETTER
                            getString(R.string.fx_tv) -> BuryPointConstant.DiscoverPageType.FX_TV
                            getString(R.string.copy_trading) -> BuryPointConstant.DiscoverPageType.COPY_TRADING
                            getString(R.string.community) -> BuryPointConstant.DiscoverPageType.COMMUNITY
                            else -> ""
                        }
                    )
                    putString(
                        "Account_type", when {
                            !UserDataUtil.isLogin() -> BuryPointConstant.AccountType.NOLOGIN
                            UserDataUtil.isStLogin() -> BuryPointConstant.AccountType.COPY_TRADING
                            UserDataUtil.isDemoAccount() -> BuryPointConstant.AccountType.DEMO
                            else -> BuryPointConstant.AccountType.LIVE
                        }
                    )

                })
        }

        jumpToSelectTab()
    }

    override fun getLiveCountSucceed() {
        if (tabsStrList.none { it == getString(R.string.webinar) }) {
            mBinding.mViewPager.post {
                if (context != null) {
                    mBinding.mViewPager.addFragment(LiveListFragment(), getString(R.string.webinar), tabsStrList.indexOf(getString(R.string.economic_calendar)) + 1)
                }
            }
        }
    }

    override fun jumpType(count: Int) {
        if (count > 0) {
            var itemIndex = fragmentList.indexOfFirst {
                it is LiveListFragment
            }
            if (itemIndex != -1) {
                mBinding.mViewPager.currentItem = itemIndex
                EventBus.getDefault().post(
                    DataEvent(
                        NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM,
                        LiveEventData(liveId, 2, isInner)
                    )
                )
            } else {
                mBinding.mViewPager.currentItem = 0
                ToastUtil.showToast(getString(R.string.live_streaming_is_over))
            }
        } else {
            mBinding.mViewPager.currentItem = 0
            ToastUtil.showToast(getString(R.string.live_streaming_is_over))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {
        when (event.tag) {

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM -> {
                if (event.data !is LiveEventData) return
                val liveData: LiveEventData = event.data
                if (liveData.type == 1) {
                    liveId = liveData.liveId
                    isInner = liveData.isInner
                    mPresenter.jumpType()
                }
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_ANALYSES -> {
                willScrollTabTag = getString(R.string.analysis)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY -> {
                willScrollTabTag = getString(R.string.community)
                mViewModel.communityLiveData.value = event.data as? String
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_CALENDAR -> {
                willScrollTabTag = getString(R.string.economic_calendar)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE -> {
                willScrollTabTag = getString(R.string.webinar)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_COPY_TRADE -> {
                willScrollTabTag = getString(R.string.copy_trading)
                jumpToSelectTab()
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    private fun jumpToSelectTab() {
        if (willScrollTabTag.isNotEmpty() && fragmentList.isNotEmpty()) {
            if (willScrollTabTag == getString(R.string.webinar)) {
                val fragment = fragmentList.firstOrNull { it is LiveListFragment }
                if (fragment == null) {
                    ToastUtil.showToast(getString(R.string.live_streaming_is_over))
                    SpManager.putLivestream("")
                    willScrollTabTag = tabsStrList.first()
                }
            }
            selectTab(willScrollTabTag)
            willScrollTabTag = ""
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            NoticeConstants.STStrategy.MAIN_SHOW_SIGNALS_ITEM_COMMUNITY_FILTER -> {
                mBinding.mViewPager.currentItem = fragmentList.indexOfFirst {
                    it is StCommunityFragment
                }
            }
        }
    }

    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            isUIVisible = true
            lazyInitView()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}