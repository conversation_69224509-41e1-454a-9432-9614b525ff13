package cn.com.vau.signals.adapter

import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.discover.NewsLetterObjData
import cn.com.vau.util.*
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Created by roy on 2018/11/19.
 * 分析师列表
 */
class AnalysesRecyclerAdapter() : BaseQuickAdapter<NewsLetterObjData, BaseViewHolder>(R.layout.item_recycler_analyses_fragment) {

    private val c00c79c by lazy { ContextCompat.getColor(context, R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(context, R.color.ce35728) }
    private val arrow_up by lazy {
        ContextCompat.getDrawable(context, R.drawable.img_arrow_up_green)
    }
    private val arrow_down by lazy {
        ContextCompat.getDrawable(context, R.drawable.img_arrow_down_red)
    }

    override fun convert(holder: BaseViewHolder, item: NewsLetterObjData) {
        val myOptions = RequestOptions()
            .placeholder(R.drawable.shape_placeholder)
            .error(R.drawable.shape_placeholder)
        ImageLoaderUtil.loadImageWithOption(context, item.img ?: "", holder.getView(R.id.mImageView), myOptions)

        holder.setText(R.id.tvProdName, item.product ?: "")
            .setText(R.id.tvDate, "${item.date ?: ""} ${item.time ?: ""}")
            .setText(R.id.tvTitle, item.title ?: "")
            .setText(R.id.tvIntro, item.intro ?: "")
            .setText(R.id.tvViews, item.views)
        if (item.trend == 1) {
            holder.setTextColor(R.id.tvTitle, c00c79c)
                .setImageDrawable(R.id.ivPriceUpDown, arrow_up)
        } else {
            holder.setTextColor(R.id.tvTitle, ce35728)
                .setImageDrawable(R.id.ivPriceUpDown, arrow_down)
        }
    }
}