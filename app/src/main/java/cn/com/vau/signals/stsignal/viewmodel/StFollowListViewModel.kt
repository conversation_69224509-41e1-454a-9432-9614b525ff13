package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.strategy.StFavouriteResBean
import cn.com.vau.util.json
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2024/04/18
 * desc：关注列表
 */
class StFollowListViewModel : BaseViewModel() {

    var pageNum = 1 //当前页码
    val pageSize = 20 //每页请求数量
    var clickPos = 0 //点击的下标

    val followListLiveData = MutableLiveData<StFavouriteResBean?>() //关注列表
    val reqErrLiveData = MutableLiveData<String?>() //请求失败
    val refreshFollowStatusLiveData = MutableLiveData<Boolean>() //刷新关注状态

    /**
     * 关注列表
     */
    fun stFollowListApi(isRefresh: Boolean) {
        if (isRefresh) {
            pageNum = 1 //下拉刷新，需要重置页数
        } else {
            pageNum++
        }

        val stUserId = UserDataUtil.stUserId()
        requestNet({ stTradingService.watchedRelationListWatchingApi(stUserId, pageNum, pageSize) }, {
            if (it.isSuccess()) {
                it.data?.watchingList?.let { list ->
                    list.forEach { bean ->
                        bean.watchingStatus = true //因为进到这个页面全是已经关注的，所以这里手动全置为已关注状态
                    }
                }
                followListLiveData.value = it.data
            } else {
                reqErrLiveData.value = it.getResponseMsg()
            }
        })
    }

    /**
     * 每次点击红心本地都要做刷新状态操作
     */
    fun initStrategyFollow(strategyId: String?) {
        followListLiveData.value?.watchingList?.let {
            if (it.getOrNull(clickPos)?.watchingStatus == true) {
                stRemoveFollowApi(strategyId)
                it.getOrNull(clickPos)?.watchingStatus = false
                EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "-1"))
            } else {
                stAddFollowApi(strategyId)
                it.getOrNull(clickPos)?.watchingStatus = true
                EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "1"))
            }
            refreshFollowStatusLiveData.value = it.getOrNull(clickPos)?.watchingStatus == true
        }
    }

    /**
     * 收藏策略
     */
    private fun stAddFollowApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.watchedRelationSaveApi(requestBody) }, {
        })
    }

    /**
     * 取消收藏策略
     */
    private fun stRemoveFollowApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.watchedRelationRemoveApi(requestBody) }, {
        })
    }
}