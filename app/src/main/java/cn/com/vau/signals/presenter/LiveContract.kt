package cn.com.vau.signals.presenter

import cn.com.vau.data.BaseBean
import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.discover.AddAWSData
import cn.com.vau.data.discover.ChartTokenBean
import cn.com.vau.data.discover.FilterChartData
import cn.com.vau.data.discover.HistoryMessageData
import cn.com.vau.data.discover.LiveInfoBean
import cn.com.vau.data.discover.LiveInfoData
import cn.com.vau.data.discover.LiveLikes
import cn.com.vau.data.discover.LivePromoData
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.data.discover.PromoEventData
import io.reactivex.disposables.Disposable

/**
 * FxStreetContract
 */
interface LiveContract {

    interface Model : BaseModel {
        fun addAWSLive(userId : String,roomArn: String, channelId : Long,baseObserver: BaseObserver<AddAWSData>): Disposable
        //获取观看人数
        fun getWatchCount(channelId : Long,baseObserver: BaseObserver<LiveInfoData>): Disposable
        //获取观看人数
        fun filterChatContent(userId : String,roomId : Long,chatContent : String, baseObserver: BaseObserver<FilterChartData>): Disposable
        //点赞
        fun giveLikes(isLike : Int,channelId : Long, baseObserver: BaseObserver<LiveLikes>): Disposable
        //获取聊天内容
        fun getChatContent(channelId : Long, baseObserver: BaseObserver<HistoryMessageData>): Disposable
        //获取token
        fun getChartToken(userId : String,roomArn : String, baseObserver: BaseObserver<DataObjStringBean>): Disposable
        //直播里面的活动
        fun queryLivePromo(baseObserver: BaseObserver<LivePromoData>): Disposable
        //退出了直播
        fun exitLive(userId : String , channelId : Long,baseObserver: BaseObserver<BaseBean>): Disposable
        fun queryMT4AccountType(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable
        fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable
    }

    interface View : BaseView {
        fun getToken(token : ChartTokenBean)
        fun getLiveInfo(liveInfoData : LiveInfoBean?)
        fun filterChatContentSucceed(content : String, chatId: String)
        fun filterForbiddenChat(content : String)
        fun giveLikesSucceed(count : Long)
        fun getChartSucceed()
        fun getChartTokenSucceed(token : String)
        fun queryLivePromoSucceed(promoData : ArrayList<PromoEventData>)
        fun skipOpenAccountActivity(state: EnumLinkSkipState, objData: MT4AccountTypeObj?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun addAWSLive(userId: String,roomArn: String, channelId:Long)
        abstract fun getWatchCount(channelId:Long)
        abstract fun getHistoryWatchCount(channelId:Long)
        abstract fun filterChatContent(userId: String,roomId : Long,chatContent : String)
        abstract fun giveLikes(isLike: Int, channelId:Long)
        abstract fun getChatContent(channelId : Long)
        abstract fun getChatToken(userId : String,roomArn : String)
        abstract fun queryLivePromo()
        abstract fun eventsAddClicksCount(eventId: String)
        abstract fun exitLive(userId : String,channelId : Long)
        abstract fun queryStAccountType(needDialog: Boolean)
        abstract fun queryMT4AccountState(state: EnumLinkSkipState)
    }
}
