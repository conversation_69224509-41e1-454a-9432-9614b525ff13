package cn.com.vau.signals.adapter.live

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.data.discover.PromoEventData
import com.bumptech.glide.request.RequestOptions

/**
 * Created by liyang
 */
class LiveActiveRecyclerAdapter(
    var mContext: Context,
    var dataList: ArrayList<PromoEventData>
) : RecyclerView.Adapter<LiveActiveRecyclerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_live_active, null))

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val dataBean = dataList.elementAtOrNull(position) ?: return

        val myOptions = RequestOptions()
            .placeholder(R.drawable.shape_placeholder)
            .error(R.drawable.shape_placeholder)
        ImageLoaderUtil.loadImageWithOption(mContext,dataBean.imgUrl ?: "",holder.liveImageView,myOptions)

        holder.tvTitle.text = dataBean.eventsName
        holder.tvInfo.text = dataBean.eventsDesc

        holder.itemView.setOnClickListener {
            monItemClickListener?.onItemClick(position)
        }
    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvInfo = view.findViewById<TextView>(R.id.tvInfo)
        val liveImageView = view.findViewById<ImageView>(R.id.liveImageView)
    }

    private var monItemClickListener: onItemClickListener? = null

    interface onItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: onItemClickListener) {
        monItemClickListener = onItemClickListener
    }

}