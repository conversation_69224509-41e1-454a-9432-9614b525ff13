package cn.com.vau.signals.stsignal.viewmodel

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.*
import org.greenrobot.eventbus.EventBus

/**
 * Filename: StProviderToPublicViewModel
 * Author: GG
 * Date: 2025/2/28
 * Description:
 */
class StProviderToPublicViewModel : BaseViewModel() {

    /**
     * 是否同意声明
     */
    var isAuthorityAccepted = false

    /**
     * 是否满足一次性入金金额
     */
    var isDepositAccepted = false

    /**
     * 默认的一次性入金金额
     */
    private fun getDefaultDeposit() = when (UserDataUtil.currencyType()) {
        "EUR" -> "500 EUR"
        "HKD" -> "4000 HKD"
        "JPY" -> "80000 JPY"
        "USC" -> "50000 USC"
        "INR" -> "40000 INR"
        else -> "500 USD"
    }

    /**
     * 成为信号源(3.48.0之后新接口)
     */
    fun userBecomeSignalApi() {
        requestNet({
            stTradingService.userBecomeSignalApi(GsonUtil.buildGson().toJsonTree(mapOf("stUserId" to UserDataUtil.stUserId())).asJsonObject)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            UserDataUtil.setIsStPublicTrade(true)
            EventBus.getDefault().post(NoticeConstants.PROVIDER_TO_PUBLIC_TRADE_SUCCESS)
            sendEvent(DataEvent(FINISH_PAGE))
        }, isShowDialog = true)
    }

    /**
     * 获取一次性入金金额
     */
    fun accountGetDepositAuthorityAmountApi() {
        requestNet({
            stTradingService.accountGetDepositAuthorityAmountApi(UserDataUtil.stAccountId(), UserDataUtil.currencyType())
        }, {
            if (!it.isSuccess()) {
                sendEvent(DataEvent(DEPOSIT_AMOUNT, getDefaultDeposit()))
                return@requestNet
            }
            sendEvent(DataEvent(DEPOSIT_AMOUNT, it.data + " " + UserDataUtil.currencyType()))
        }, onError = {
            sendEvent(DataEvent(DEPOSIT_AMOUNT, getDefaultDeposit()))
        })
    }

    /**
     * 获取公开交易的状态 ， 是否满足 一次性入金  以及  接受声明
     */
    fun accountOpenConditionApi(isShowDialog: Boolean) {
        requestNet({
            stTradingService.accountOpenConditionApi(UserDataUtil.stAccountId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            isAuthorityAccepted = it.data?.authorityAccepted == "1"
            isDepositAccepted = it.data?.depositAccepted == "1"
            sendEvent(DataEvent(UPDATE_UI))
        }, isShowDialog = isShowDialog)
    }

    /**
     * 跟单公开交易页面的 接受条款
     */
    fun conditionAcceptedApi() {
        requestNet({
            stTradingService.accountUpdateAuthorityAcceptedApi(UserDataUtil.stAccountId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            accountOpenConditionApi(true)
        })
    }

    companion object {
        const val DEPOSIT_AMOUNT = "deposit_amount"
        const val UPDATE_UI = "update_ui"
        const val FINISH_PAGE = "finish_page"
    }
}