package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.strategy.StFansResBean

/**
 * author：lvy
 * date：2024/5/20
 * desc：粉丝列表
 */
class StFansListViewModel : BaseViewModel() {

    val fansListLiveData = MutableLiveData<StFansResBean?>() //粉丝列表
    val reqErrLiveData = MutableLiveData<String?>() //请求失败

    /**
     * 粉丝列表
     */
    fun stFansListApi(strategyId: String?, pageNum: Int, pageSize: Int) {
        val stUserId = UserDataUtil.stUserId()
        requestNet({
            stTradingService.watchedRelationListFansApi(stUserId, strategyId, pageNum, pageSize)
        }, {
            if (!it.isSuccess()) {
                reqErrLiveData.value = it.getResponseMsg()
                return@requestNet
            }
            fansListLiveData.value = it.getResponseData()
        })
    }
}