package cn.com.vau.signals.viewModel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.PromoEventData

/**
 * @description:
 * @author: GG
 * @createDate: 2024 11月 02 14:13
 * @updateUser:
 * @updateDate: 2024 11月 02 14:13
 */
class PromoViewModel : BaseViewModel() {

    val uiListLiveData: MutableLiveData<ListUIState<List<PromoEventData>?>> by lazy { MutableLiveData() }
    val bannerLiveData: MutableLiveData<List<PromoEventData>> by lazy { MutableLiveData() }

    init {
        refresh()
    }

    fun refresh() {
        queryEvenList()
    }

    private fun queryEvenList() {
        requestNet({
            val paramMap = hashMapOf<String, Any>("pageNo" to 1, "pageSize" to 20, "serverId" to UserDataUtil.serverId())
            if (UserDataUtil.isLogin()) {
                paramMap["userId"] = UserDataUtil.userId()
                paramMap["mt4AccountId"] = UserDataUtil.accountCd()
            }
            baseService.eventsGetList(paramMap)
        }, onSuccess = { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }

            val dataList = dataBean.data?.obj
            if (dataList.isNullOrEmpty()) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }
            val uiList = ArrayList<PromoEventData>()
            val bannerList = ArrayList<PromoEventData>()
            dataList.forEach {
                if (it.eventsHot == 0) { //非熱門
                    uiList.add(it)
                } else {  //熱門
                    bannerList.add(it)
                }
            }
            uiListLiveData.value = ListUIState.RefreshSuccess(uiList)
            bannerLiveData.value = bannerList
        }, onError = {
            uiListLiveData.value = ListUIState.Error()
        })
    }

    fun eventsAddClicksCount(eventId: String) {

        requestNet({
            baseService.eventsAddClicksCountApi(eventId, UserDataUtil.loginToken())
        }, onSuccess = {

        })
    }
}