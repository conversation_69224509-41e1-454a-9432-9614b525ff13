package cn.com.vau.signals.stsignal.center.adapter

import android.annotation.SuppressLint
import android.widget.*
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.strategy.StProfileCopyContentBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * author：lvy
 * date：2024/04/03
 * desc：信号源中心->Copier Review->Pending
 */
class StCopierReviewPendingAdapter(private val tabIndex: Int) :
    BaseQuickAdapter<StProfileCopyContentBean, BaseViewHolder>(R.layout.item_st_signal_center_copier_review_pending) {

    @SuppressLint("ClickableViewAccessibility")
    override fun convert(holder: BaseViewHolder, item: StProfileCopyContentBean) {
        //跟单者头像
        val ivAvatar = holder.getView<ShapeableImageView>(R.id.ivAvatar)
        ImageLoaderUtil.loadImage(context, item.avatar, ivAvatar, R.mipmap.ic_launcher)
        //跟单者昵称
        holder.setText(R.id.tvNick, "${item.name} (${item.followerAccountNo})")

        //第一个item的日期
        item.strategyCopyListItem?.firstOrNull()?.let {
            when (it.applyStatus) {
                "PENDING" -> {
                    val firstApplyTime = it.applyTime?.toLongOrNull() ?: 0L
                    holder.setText(
                        R.id.tvTime, context.getString(
                            R.string.applied_on_x,
                            TimeUtil.millis2String(firstApplyTime, "dd/MM/yyyy HH:mm:ss")
                        )
                    )
                }

                "APPROVED" -> {
                    val firstReviewTime = it.reviewTime?.toLongOrNull() ?: 0L
                    holder.setText(
                        R.id.tvTime, context.getString(
                            R.string.approved_on_x,
                            TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                        )
                    )
                }

                "AUTO_APPROVED" -> {
                    val firstReviewTime = it.reviewTime?.toLongOrNull() ?: 0L
                    holder.setText(
                        R.id.tvTime, context.getString(
                            R.string.auto_approved_on_x,
                            TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                        )
                    )
                }

                "REJECTED" -> {
                    val firstReviewTime = it.reviewTime?.toLongOrNull() ?: 0L
                    holder.setText(
                        R.id.tvTime, context.getString(
                            R.string.rejected_on_x,
                            TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                        )
                    )
                }

                "AUTO_REJECTED" -> {
                    val firstReviewTime = it.reviewTime?.toLongOrNull() ?: 0L
                    holder.setText(
                        R.id.tvTime, context.getString(
                            R.string.auto_rejected_on_x,
                            TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                        )
                    )
                }

                else -> {}
            }
        }

        //按钮显示状态
        when (tabIndex) {
            0 -> {
                holder.setGone(R.id.tvReject, false)
                holder.setGone(R.id.tvApprove, false)
            }

            else -> { //审核通过和拒绝不需要显示操作按钮
                holder.setGone(R.id.tvReject, true)
                holder.setGone(R.id.tvApprove, true)
            }
        }

        //child列表数据
        val mRecyclerView = holder.getView<RecyclerView>(R.id.mRecyclerView)
        mRecyclerView.isNestedScrollingEnabled = false
        val childAdapter = StCopierReviewPendingChildAdapter()
        mRecyclerView.adapter = childAdapter

        item.strategyCopyListItem?.let { list ->
            val tvViewMore = holder.getView<TextView>(R.id.tvViewMore)
            val ivArrow = holder.getView<ImageView>(R.id.ivArrow)
            holder.setImageResource(R.id.ivArrow, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
            if (list.size > 1) {
                tvViewMore.isVisible = true
                ivArrow.isVisible = true
                tvViewMore.text = context.getString(R.string.view_x_more, "${list.size - 1}")
                childAdapter.setList(list.take(1))
                tvViewMore.setOnClickListener {
                    if (childAdapter.itemCount > 1) { //展开状态
                        tvViewMore.text = context.getString(R.string.view_x_more, "${list.size - 1}")
                        childAdapter.setList(list.take(1))
                        holder.setImageResource(R.id.ivArrow, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
                    } else {
                        tvViewMore.text = context.getString(R.string.view_less)
                        childAdapter.setList(list)
                        holder.setImageResource(R.id.ivArrow, R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff)
                    }
                }
            } else {
                tvViewMore.isVisible = false
                ivArrow.isVisible = false
                childAdapter.setList(list)
            }
        }
    }
}