package cn.com.vau.signals.live

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.discover.Row
import cn.com.vau.databinding.FragmentLiveBinding
import cn.com.vau.signals.adapter.live.LiveListFragmentRecyclerAdapter
import cn.com.vau.signals.stsignal.LiveEventData
import cn.com.vau.signals.viewModel.LiveListViewModel
import cn.com.vau.util.LogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

class LiveListFragment : BaseMvvmBindingFragment<FragmentLiveBinding>() {

    private val liveListViewModel: LiveListViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private val mAdapter: LiveListFragmentRecyclerAdapter by lazy {
        LiveListFragmentRecyclerAdapter()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {
        initLayout()
        mBinding.mNoDataScroll.setHintMessage(getString(R.string.no_live_broadcast))
    }

    override fun initData() {
        liveListViewModel.refreshLiveList()
    }

    override fun createObserver() {
        super.createObserver()
        liveListViewModel.dataListTotalLiveData.observe(viewLifecycleOwner) {
            LogUtil.v("LiveListFragment", "dataListTotalLiveData 接收数据:${it.second.size}")
            val dataListNotEmpty = it.second.isNotEmpty()
            if(it.first) {
                //成功，展示请求数据或无数据
                mAdapter.submitList(it.second)
                mBinding.mRefreshLayout.finishRefresh()
                mBinding.mRefreshLayout.finishLoadMore()
                mBinding.mRefreshLayout.setEnableLoadMore(liveListViewModel.canLoadMore())
                fireBaseJump()
                appsFlyerJump()
                mBinding.mRecyclerView.visibility = if(dataListNotEmpty) View.VISIBLE else View.GONE
                mBinding.mNoDataScroll.visibility = if(dataListNotEmpty) View.GONE else View.VISIBLE
            } else {
                //失败，展示缓存数据或无数据
                mBinding.mRefreshLayout.finishRefresh(false)
                mBinding.mRefreshLayout.finishLoadMore(false)
                mBinding.mRecyclerView.visibility = if(dataListNotEmpty) View.VISIBLE else View.GONE
                mBinding.mNoDataScroll.visibility = if(dataListNotEmpty) View.GONE else View.VISIBLE
            }
        }
    }

    private fun hasFirebaseLink(): Boolean {
        val liveStream = SpManager.getLivestream("")
        return liveStream.contains("firebase") == true
    }

    private fun initLayout() {
        // Fatal Exception: java.lang.IllegalStateException
        // The specified child already has a parent. You must call removeView() on the child's parent first.
        // 这么页面报这个问题，所以这里判断一下可能有重复调用的情况
        if (mBinding.mRecyclerView.adapter != null) {
            mBinding.mRecyclerView.adapter = null
        }
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(activity)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.isNestedScrollingEnabled = false

        mAdapter.setOnItemClickListener(object :
            LiveListFragmentRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                val selectData = liveListViewModel.dataListTotalLiveData.value?.second?.elementAtOrNull(position)
                //即将直播liveStatus=0
                if (selectData?.liveStatus != 0) {
                    openAuLive(selectData)
                }
                if (selectData?.liveStatus == 0) {
                    ToastUtil.showToast(getString(R.string.upcoming_webinar))
                }
                // 神策自定义埋点(v3500)
                if (selectData?.liveStatus == 1) { // liveStatus == 1 是直播
                    sensorsTrack(selectData.id.ifNull().toString(), position, selectData.playbackUrl.ifNull())
                } else {
                    sensorsTrack(selectData?.id.ifNull().toString(), position, selectData?.recRoadcastUrl.ifNull())
                }
            }
        })

        mBinding.mRefreshLayout.setOnRefreshListener {
            liveListViewModel.refreshLiveList()
        }

        mBinding.mRefreshLayout.setOnLoadMoreListener {
            liveListViewModel.loadMoreLiveList()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.LIVE_ROOM_EXIT -> {
                liveListViewModel.refreshLiveList()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        when (event.tag) {
            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM -> {
                if (event.data !is LiveEventData) return
                val liveData: LiveEventData = event.data
                if (liveData.type != 2) return

                val listData = liveListViewModel.dataListTotalLiveData.value?.second ?: arrayListOf()
                if (listData.isNotEmpty()) {
                    //外部
                    if (liveData.isInner == 0) {
                        var isLiving = false
                        for ((index, b) in listData.withIndex()) {
                            if (b.itemType != 3) {
                                if (b.id == liveData.liveId) {
                                    if (b.liveStatus != 0) {
                                        isLiving = true
                                        openAuLive(listData[index])
                                    } else {
                                        // 该直播即将开始，敬请期待
                                        ToastUtil.showToast(getString(R.string.the_live_streaming_please_stay_tuned))
                                        return
                                    }
                                }
                            }
                        }
                        if (!isLiving) {
                            // 直播已结束，敬请期待下次直播
                            ToastUtil.showToast(getString(R.string.live_streaming_is_over))
                        }
                    } else {
                        for (live in listData) {
                            if (live.itemType != 3 && live.liveStatus == 1) {
                                openAuLive(live)
                                break
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    private fun fireBaseJump() {
        if (hasFirebaseLink()) {
            SpManager.putLivestream("")
            val selectData = liveListViewModel.dataListTotalLiveData.value?.second?.elementAtOrNull(0)
            if (selectData?.liveStatus == 1)
                openAuLive(selectData)
        }
    }

    private fun appsFlyerJump() {
        val liveNo = SpManager.getLivestream("")
        liveNo.toLongOrNull()?.let { liveNoLong -> EventBus.getDefault().post(LiveEventData(liveNoLong, 2, 0)) }
        SpManager.putLivestream("")
    }

    private fun openAuLive(mLiveData: Row?) {
        when (mLiveData?.liveStatus) {
            1 -> {
                openActivity(LivingPLayerActivity::class.java, Bundle().apply {
                    mLiveData.id?.let { it1 -> putLong("channelId", it1) }
                    mLiveData.roomId?.let { it1 -> putLong("roomId", it1) }
                    putString("roomArn", mLiveData.roomArn)
                    putString("messageNode", mLiveData.messageNode)
                    //liveStatus = 1是正在直播
                    if (mLiveData.liveStatus == 1) {
                        putString("playbackUrl", mLiveData.playbackUrl)
                        LogEventUtil.setLogEvent("livestream_spotlight_click_button")
                    } else {
                        putString("playbackUrl", mLiveData.recRoadcastUrl)
                    }
                    mLiveData.liveStatus?.let { it1 -> putInt("liveStatus", it1) }
                    putString("shareContent", mLiveData.shareContent)
                    mLiveData.virtualCount?.let { it1 -> putLong("virtualCount", it1) }
                    mLiveData.virtualLikeCount?.let { it1 -> putLong("virtualLikeCount", it1) }
                    putString("channel", mLiveData.channel)
                    putDouble("width", mLiveData.width)
                    putDouble("height", mLiveData.height)
                })
            }

            0 -> {
                //ToastUtils.showToast(getString(R.string.live_starting_soon))
            }

            else -> {
                openActivity(HistoryPlayerActivity::class.java, Bundle().apply {
                    mLiveData?.id?.let { it1 -> putLong("channelId", it1) }
                    mLiveData?.roomId?.let { it1 -> putLong("roomId", it1) }
                    putString("roomArn", mLiveData?.roomArn)
                    putString("messageNode", mLiveData?.messageNode)
                    //liveStatus = 1是正在直播
                    if (mLiveData?.liveStatus == 1) {
                        putString("playbackUrl", mLiveData.playbackUrl)
                        LogEventUtil.setLogEvent("livestream_spotlight_click_button")
                    } else {
                        putString("playbackUrl", mLiveData?.recRoadcastUrl)
                    }
                    mLiveData?.liveStatus?.let { it1 -> putInt("liveStatus", it1) }
                    mLiveData?.virtualCount?.let { it1 -> putLong("virtualCount", it1) }
                    mLiveData?.shareContent?.let { it1 -> putString("shareContent", it1) }
                    mLiveData?.virtualLikeCount?.let { it1 -> putLong("virtualLikeCount", it1) }
                    putString("channel", mLiveData?.channel)
                    mLiveData?.width?.let { putDouble("width", it) }
                    mLiveData?.height?.let { putDouble("height", it) }
                })
            }
        }
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    private fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }
}