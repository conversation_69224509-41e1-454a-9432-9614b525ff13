package cn.com.vau.signals.presenter

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.DataObjStringBean
import io.reactivex.disposables.Disposable

/**
 * LiveListContract
 */
interface StSignalsContract {

    interface Model : BaseModel {
        fun selectVideoCount(baseObserver: BaseObserver<DataObjStringBean>): Disposable
    }

    interface View : BaseView {
        fun getLiveCountSucceed()
        fun jumpType(count : Int)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun selectVideoCount()
        abstract fun jumpType()
    }
}
