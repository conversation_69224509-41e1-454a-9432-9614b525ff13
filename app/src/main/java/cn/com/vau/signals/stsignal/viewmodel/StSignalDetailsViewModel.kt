package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.strategy.SignalDetailsBean
import cn.com.vau.util.json
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * author：lvy
 * date：2024/03/25
 * desc：
 */
class StSignalDetailsViewModel : BaseViewModel() {

    val signalDetailsLiveData = MutableLiveData<SignalDetailsBean?>() //信号源详情

    /**
     * 信号源详情
     */
    fun stSignalDetailsApi(stUserId: String?) {
        val map = hashMapOf<String, Any?>()
        if (UserDataUtil.isStLogin()) {
            map["accountId"] = UserDataUtil.stAccountId()
        }
        map["stUserId"] = stUserId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.userSignalDetailsApi(requestBody) }, {
            if (it.isSuccess()) {
                signalDetailsLiveData.value = it.data
            }
        }, isShowDialog = true)
    }
}