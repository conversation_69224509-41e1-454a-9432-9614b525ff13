package cn.com.vau.signals.adapter

import android.widget.ImageView
import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.data.discover.WebTVObj
import cn.com.vau.util.*
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 15 10:31
 * @updateUser:
 * @updateDate: 2024 10月 15 10:31
 */
class WebTvAdapter() : BaseQuickAdapter<WebTVObj, BaseViewHolder>(R.layout.item_recycler_web_tv) {
    override fun convert(holder: BaseViewHolder, item: WebTVObj) {
        val myOptions = RequestOptions()
            .placeholder(R.drawable.shape_placeholder)
            .error(R.drawable.shape_placeholder)

        ImageLoaderUtil.loadImageWithOption(
            context,
            item.cover,
            holder.getViewOrNull<ImageView>(R.id.mImageView),
            myOptions
        )

        holder.setText(R.id.tvName, item.videoName.ifNull())
            .setText(R.id.tvContent, item.description.ifNull())
            .setText(R.id.tvViews, item.views.ifNull())
            .setText(R.id.tvDate, item.createTime.ifNull())

    }

    override fun onItemViewHolderCreated(viewHolder: BaseViewHolder, viewType: Int) {
        super.onItemViewHolderCreated(viewHolder, viewType)
        with(viewHolder) {
            getViewOrNull<AppCompatTextView>(R.id.tvName)?.setFontG600()
            getViewOrNull<AppCompatTextView>(R.id.tvContent)?.setFontG500()
            getViewOrNull<AppCompatTextView>(R.id.tvViews)?.setFontG500()
            getViewOrNull<AppCompatTextView>(R.id.tvDate)?.setFontG500()
        }
    }
}