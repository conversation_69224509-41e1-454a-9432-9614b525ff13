package cn.com.vau.signals.stsignal.center.adapter

import cn.com.vau.R
import cn.com.vau.data.strategy.StrategyCopyListItemBean
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.numCurrencyFormat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * author：lvy
 * date：2024/04/03
 * desc：
 */
class StCopierReviewPendingChildAdapter :
    BaseQuickAdapter<StrategyCopyListItemBean, BaseViewHolder>(R.layout.item_st_signal_center_copier_review_pending_child) {

    override fun convert(holder: BaseViewHolder, item: StrategyCopyListItemBean) {
        holder.setGone(R.id.tvTime, holder.layoutPosition == 0)
            .setText(R.id.tvStrategyName, item.strategyName)
            .setText(R.id.tvInvestment, "${item.followAmount?.numCurrencyFormat()} ${item.currency}")

        //时间
        when (item.applyStatus) {
            "PENDING" -> {
                val firstApplyTime = item.applyTime?.toLongOrNull() ?: 0L
                holder.setText(
                    R.id.tvTime,
                    context.getString(
                        R.string.applied_on_x,
                        TimeUtil.millis2String(firstApplyTime, "dd/MM/yyyy HH:mm:ss")
                    )
                )
            }

            "APPROVED" -> {
                val firstReviewTime = item.reviewTime?.toLongOrNull() ?: 0L
                holder.setText(
                    R.id.tvTime,
                    context.getString(
                        R.string.approved_on_x,
                        TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                    )
                )
            }

            "AUTO_APPROVED" -> {
                val firstReviewTime = item.reviewTime?.toLongOrNull() ?: 0L
                holder.setText(
                    R.id.tvTime,
                    context.getString(
                        R.string.auto_approved_on_x,
                        TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                    )
                )
            }

            "REJECTED" -> {
                val firstReviewTime = item.reviewTime?.toLongOrNull() ?: 0L
                holder.setText(
                    R.id.tvTime,
                    context.getString(
                        R.string.rejected_on_x,
                        TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                    )
                )
            }

            "AUTO_REJECTED" -> {
                val firstReviewTime = item.reviewTime?.toLongOrNull() ?: 0L
                holder.setText(
                    R.id.tvTime,
                    context.getString(
                        R.string.auto_rejected_on_x,
                        TimeUtil.millis2String(firstReviewTime, "dd/MM/yyyy HH:mm:ss")
                    )
                )
            }

            else -> {}
        }
    }
}