package cn.com.vau.signals.live

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.net.Uri
import android.os.*
import android.text.TextUtils
import android.view.*
import android.view.View.OnClickListener
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.*
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.discover.*
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.databinding.ActivityLivingPlayerBinding
import cn.com.vau.page.*
import cn.com.vau.page.html.*
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.signals.adapter.live.*
import cn.com.vau.signals.live.history.base.PlayerConfig
import cn.com.vau.signals.live.history.util.VideoUtils
import cn.com.vau.signals.model.LiveModel
import cn.com.vau.signals.presenter.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.amazonaws.ivs.player.*
import okhttp3.*
import okhttp3.internal.ws.RealWebSocket
import org.greenrobot.eventbus.*
import java.lang.ref.WeakReference
import java.nio.ByteBuffer
import java.util.concurrent.CopyOnWriteArrayList

class LivingPLayerActivity : BaseFrameActivity<LivePresenter, LiveModel>(), LiveContract.View, OnClickListener {

    private val mBinding by lazy { ActivityLivingPlayerBinding.inflate(layoutInflater) }

    private var player: Player? = null
    private var bundle: Bundle? = null
    private var chatToken: String? = null
    private var socket: RealWebSocket? = null
    private var messageAdapter: LiveMessageRecyclerAdapter? = null
    private var isShowMessage: Boolean = true
    private var isGiveLike: Boolean = false
    private var isPlay: Boolean = true
    private var channelId: Long = 0
    private var roomId: Long = 0
    private var roomArn: String = ""
    private var messageNode: String = ""
    private var playbackUrl: String = ""
    private var virtualCount: Long = 0
    private var virtualLikeCount: Long = 0
    private var liveStatus: Int = 0
    private var channel: String = ""

    //    private var messageList: ArrayList<AWSMessageData> = arrayListOf()
    private var messageList = CopyOnWriteArrayList<AWSMessageData>()
    private val activeAdapter: LiveActiveRecyclerAdapter by lazy { LiveActiveRecyclerAdapter(context, mPresenter.activeData) }
    private val activityDialog by lazy {
        BottomListDialog.Builder(this)
            .setTitle(getString(R.string.promo))
            .setAdapter(activeAdapter)
            .build()
    }
    private val inputTextMsgDialog: InputTextMsgDialog by lazy { InputTextMsgDialog.Builder(this).build() }
    private var userId: String = ""
    private var isFullScreen = false
    private var mSystemUiVisibility = 0
    private var playerConfig: PlayerConfig? = null
    private var originWidth: Double = 0.0
    private var originHeight = 0.0
    private var isFirst: Boolean = true
    private var shareContent: String = ""
    private var isHavePromo = false

    private val mHandler = MyHandler(WeakReference(this))

    private class MyHandler(val wrFragment: WeakReference<LivingPLayerActivity>) :
        Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            wrFragment.get()?.run {
                when (msg.what) {
                    0 -> {
                        updateControls()
                    }
                    //查看观看人数信息
                    1 -> {
                        mPresenter.getWatchCount(channelId)
                        mHandler.sendEmptyMessageDelayed(1, 5 * 1000L)
                    }

                    2 -> {
                        messageAdapter?.notifyDataSetChanged()
                        mBinding.rvMessage.post { mBinding.rvMessage.scrollToPosition(messageAdapter?.itemCount.ifNull(1) - 1) }
                    }

                    else -> {}
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // 5.0
            val window = window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS) // 確認取消半透明設置。
            window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE // 全螢幕顯示，status bar 不隱藏，activity 上方 layout 會被 status bar 覆蓋。
            // 配合其他 flag 使用，防止 system bar 改變後 layout 的變動。
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS) // 跟系統表示要渲染 system bar 背景。
            window.statusBarColor = Color.TRANSPARENT
        }
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        setContentView(mBinding.root)
        EventBus.getDefault().register(this)
    }

    fun getData() {
        bundle = intent.extras
        channelId = bundle?.getLong("channelId") ?: 0
        roomId = bundle?.getLong("roomId") ?: 0
        roomArn = bundle?.getString("roomArn") ?: ""
        messageNode = bundle?.getString("messageNode") ?: ""
        playbackUrl = bundle?.getString("playbackUrl") ?: ""
        virtualCount = bundle?.getLong("virtualCount") ?: 0
        virtualLikeCount = bundle?.getLong("virtualLikeCount") ?: 0
        liveStatus = bundle?.getInt("liveStatus") ?: 0
        channel = bundle?.getString("channel") ?: ""
        originWidth = bundle?.getDouble("width") ?: 0.0
        originHeight = bundle?.getDouble("height") ?: 0.0
        shareContent = bundle?.getString("shareContent") ?: ""

        userId = UserDataUtil.userId()
        playerConfig = PlayerConfig.Builder().build()

        LogEventUtil.setLogEvent("livestream_page_view", Bundle().apply {
            putString("page_name", channel)
        })

    }

    override fun initParam() {
        window.clearFlags(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
    }

    override fun initView() {
        super.initView()
        getData()
        player = MediaPlayer(this)
        mBinding.tvWatchCount.text = virtualCount.toString()
        mBinding.livingTopHorizontal.tvWatchCount2.text = virtualCount.toString()
        mBinding.tvLike.text = virtualLikeCount.toString()

        messageAdapter = LiveMessageRecyclerAdapter(this, messageList)
        mBinding.rvMessage.layoutManager = WrapContentLinearLayoutManager(this)
        (mBinding.rvMessage.layoutManager as LinearLayoutManager).stackFromEnd = true
        mBinding.rvMessage.adapter = messageAdapter

        if (originHeight == 9.0 && originWidth == 16.0) {
            mBinding.fullTips.visibility = View.VISIBLE
            mBinding.fullscreen.visibility = View.VISIBLE
            mBinding.tvGetIt.visibility = View.VISIBLE
        } else {
            mBinding.fullTips.visibility = View.GONE
            mBinding.fullscreen.visibility = View.GONE
            mBinding.tvGetIt.visibility = View.GONE
        }

        mBinding.surfaceView.onReady { surface ->
            player?.setSurface(surface)
            player?.load(Uri.parse(playbackUrl))
            player?.play()
            updateControls()
        }

        player?.apply {
            addListener(object : Player.Listener() {
                override fun onCue(cue: Cue) {}
                override fun onDurationChanged(l: Long) {}
                override fun onStateChanged(state: Player.State) {
                    when (state) {
                        Player.State.BUFFERING -> mBinding.streamBuffering.visibility = View.VISIBLE
                        Player.State.READY -> {
                            player?.play()
                            mBinding.streamBuffering.visibility = View.INVISIBLE
                        }

                        Player.State.IDLE -> mBinding.streamBuffering.visibility = View.INVISIBLE
                        Player.State.PLAYING -> {
                            mBinding.streamBuffering.visibility = View.INVISIBLE
                        }

                        Player.State.ENDED -> {
                            if (liveStatus == 1) {
                                ToastUtil.showToast(getString(R.string.streaming_has_ended))
                                mBinding.clLiveCancel.visibility = View.VISIBLE
                                mBinding.streamBuffering.visibility = View.GONE
                                mBinding.clAccountTop.visibility = View.GONE
                                mBinding.tvWatchCount.visibility = View.GONE
                                mBinding.livingTopHorizontal.tvWatchCount2.visibility = View.GONE
                                mBinding.clBottom.visibility = View.GONE
                                mBinding.clTexture.visibility = View.GONE
                                mBinding.rvMessage.visibility = View.GONE
                                mBinding.livingTopHorizontal.root.visibility = View.GONE
                            } else {
                                mBinding.streamBuffering.visibility = View.GONE
                                mBinding.clLiveCancel.visibility = View.GONE
                                isPlay = false
                                updateControls()
                            }
                        }
                    }
                }

                override fun onError(e: PlayerException) {}
                override fun onRebuffering() {}
                override fun onSeekCompleted(l: Long) {
                    mBinding.streamBuffering.visibility = View.GONE
                    mBinding.clLiveCancel.visibility = View.GONE
                    isPlay = false
                }

                override fun onVideoSizeChanged(i: Int, i1: Int) {
                    if (isFirst) {
                        resizeTextureView(i, i1)
                    }
                    isFirst = false
                }

                override fun onQualityChanged(quality: Quality) {}
                override fun onMetadata(mediaType: String, data: ByteBuffer) {}
            })

        }
        setTopView()
    }

    override fun initListener() {
        super.initListener()
        mBinding.fullTips.setOnClickListener(this)
        mBinding.tvGetIt.setOnClickListener(this)
        mBinding.ivShare.setOnClickListener(this)
        mBinding.ivLeft.setOnClickListener(this)
        mBinding.ivMessageLeft.setOnClickListener(this)
        mBinding.ivMessage.setOnClickListener(this)
        mBinding.ivClose.setOnClickListener(this)
        mBinding.ivFinish.setOnClickListener(this)
        mBinding.livingTopHorizontal.ivFinish2.setOnClickListener(this)
        mBinding.ivLike.setOnClickListener(this)
        mBinding.fullscreen.setOnClickListener(this)
        mBinding.fullscreen2.setOnClickListener(this)
        mBinding.tvMessage.setOnClickListener(this)
        activeAdapter.setOnItemClickListener(object : LiveActiveRecyclerAdapter.onItemClickListener {
            override fun onItemClick(position: Int) {
                val selectData = activeAdapter.dataList.elementAtOrNull(position)
                LogEventUtil
                    .setLogEvent("livestream_promo_button_click_2", Bundle().apply {
                        putString("promo_name", selectData?.eventsName ?: "")
                    })
                VAUStartUtil.openActivity(this@LivingPLayerActivity, selectData?.appJumpDefModel)
                mPresenter.eventsAddClicksCount(selectData?.eventId.ifNull())
                activityDialog.dismiss()
                exitFullScreen()
            }
        })
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.fullTips -> {
                mBinding.fullTips.visibility = View.GONE
                mBinding.tvGetIt.visibility = View.GONE
                startFullScreen(true)
            }

            R.id.tvGetIt -> {
                mBinding.fullTips.visibility = View.GONE
                mBinding.tvGetIt.visibility = View.GONE
            }

            R.id.ivShare -> {
                noRepeat {
                    // 系统分享
                    val shareIntent = Intent(Intent.ACTION_SEND)
                    shareIntent.type = "text/plain"
                    if (TextUtils.isEmpty(shareContent)) {
                        shareContent =
                            "Fire away!\n Join our live stream & ask us anything about the markets, Vantage App, promotions or even the weather! \n Download Vantage App: https://vantagemarkets.onelink.me/8SIf/webcbsh3"
                    }
                    shareIntent.putExtra(Intent.EXTRA_TEXT, shareContent)
                    startActivityForResult(shareIntent, 1000)
                    LogEventUtil.setLogEvent("livestream_share_button_click")
                }
            }

            R.id.iv_left, R.id.ivMessageLeft -> {
                activityDialog.show()
                LogEventUtil.setLogEvent("livestream_promo_button_click_1")
            }

            R.id.ivMessage -> {
                isShowMessage = !isShowMessage
                if (isShowMessage) {
                    mBinding.rvMessage.visibility = View.VISIBLE
                    mBinding.ivMessage.setImageResource(R.drawable.img_live_open_chat)
                } else {
                    mBinding.rvMessage.visibility = View.GONE
                    mBinding.ivMessage.setImageResource(R.drawable.img_live_close_chat)
                }
            }

            R.id.ivFinish, R.id.ivFinish2, R.id.ivClose -> {
                LogEventUtil.setLogEvent("livestream_page_view_close")
                exitFullScreen()
                finish()
            }

            R.id.ivLike -> {
                isGiveLike = !isGiveLike
                mBinding.mHeartLayout.addHeart(ContextCompat.getColor(this, R.color.ce35728))
                mPresenter.giveLikes(1, channelId)
                LogEventUtil.setLogEvent("livestream_like_button_click", Bundle().apply {
                    putString("like_button", "like button")
                })
            }

            R.id.fullscreen, R.id.fullscreen2 -> {
                mBinding.fullTips.visibility = View.GONE
                mBinding.tvGetIt.visibility = View.GONE
                if (!isFullScreen) {
                    startFullScreen(true)
                } else {
                    exitFullScreen()
                }
            }

            R.id.tvMessage -> {
                if (!UserDataUtil.isLogin()) {
                    openActivity(LoginActivity::class.java)
                    return
                }
                if (inputTextMsgDialog.isShowDialog() == true) {
                    return
                }

                /* if(isFullScreen){
                     //解决全屏模式下无法弹软键盘问题，去到xml内去更改
                     inputTextMsgDialog?.messageTextView?.imeOptions = EditorInfo.IME_FLAG_NO_EXTRACT_UI
                 }else{
                     inputTextMsgDialog?.messageTextView?.imeOptions = EditorInfo.IME_ACTION_SEND
                 }*/

                inputTextMsgDialog.setOnTextSendListener(object : InputTextMsgDialog.OnTextSendListener {
                    override fun onTextSend(msg: String?) {
                        if (!msg.isNullOrBlank()) {
                            mPresenter.filterChatContent(userId, roomId, msg.toString())
                        }
                    }

                    override fun dismiss() {
                        if (isFullScreen) {
                            //resetChartView(dp2px(20f))
                            startFullScreen(false)
                        } else {
                            //resetChartView(dp2px(86f))
                        }
                    }

                    override fun show() {
                        /*if(isFullScreen){
                            resetChartView(dp2px(290f))
                        }else{
                            resetChartView(960)
                        }*/
                    }
                })
                inputTextMsgDialog.show()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (isFullScreen && requestCode == 1000) {
            startFullScreen(false)
        }
    }

    private fun startFullScreen(isButton: Boolean) {
        isFullScreen = true
        if (isButton) {
            mSystemUiVisibility = window.decorView.systemUiVisibility
        }
        requestedOrientation = getFullScreenOrientation()
        VideoUtils.hideSupportActionBar(this, true)
        VideoUtils.addFullScreenFlag(this)
        VideoUtils.hideNavKey(this)
        postRunnableToResizeTexture()
        mBinding.clTop.visibility = View.GONE
        mBinding.livingTopHorizontal.root.visibility = View.VISIBLE
        mBinding.viewLeft.visibility = View.VISIBLE
        mBinding.fullscreen2.visibility = View.VISIBLE
        mBinding.fullscreen.visibility = View.GONE
        mBinding.ivMessageLeft.visibility = View.GONE
        mBinding.fullTips.visibility = View.GONE
        mBinding.tvGetIt.visibility = View.GONE
        if (isHavePromo) {
            mBinding.ivLeft.visibility = View.VISIBLE
        } else {
            mBinding.ivLeft.visibility = View.GONE
        }
        resetHeartView(36f.dp2px())
        resetChartView(20.dp2px())
    }

    private fun postRunnableToResizeTexture() {
        mBinding.clTexture.post { resizeTextureView(mBinding.surfaceView.width, mBinding.surfaceView.height) }
    }

    //根据视频内容重新调整视频渲染区域大小
    private fun resizeTextureView(width: Int, height: Int) {
        if (width == 0 || height == 0) {
            return
        }
        val aspectRation = width.toFloat() / height
        val surfaceContainer: View = mBinding.clTexture
        val parentWidth = surfaceContainer.width
        val parentHeight = surfaceContainer.height
        val w: Int
        val h: Int
        if (aspectRation >= 1) {
            w = parentWidth
            h = (w / aspectRation).toInt()
        } else {
            h = parentHeight
            w = (h * aspectRation).toInt()
        }
        val layoutParams: ViewGroup.LayoutParams = mBinding.surfaceView.layoutParams
        layoutParams.width = w
        layoutParams.height = h
        mBinding.surfaceView.layoutParams = layoutParams
    }

    private fun exitFullScreen() {
        if (isFullScreen) {
            isFullScreen = false
            requestedOrientation = exitFullScreenOrientation()
            VideoUtils.showSupportActionBar(this, true)
            VideoUtils.clearFullScreenFlag(this)
            window.decorView.systemUiVisibility = mSystemUiVisibility
            postRunnableToResizeTexture()
            mBinding.clTop.visibility = View.VISIBLE
            mBinding.livingTopHorizontal.root.visibility = View.GONE
            mBinding.viewLeft.visibility = View.GONE
            mBinding.fullscreen2.visibility = View.GONE
            mBinding.fullscreen.visibility = View.VISIBLE
            mBinding.fullTips.visibility = View.GONE
            mBinding.tvGetIt.visibility = View.GONE
            if (isHavePromo) {
                mBinding.ivMessageLeft.visibility = View.VISIBLE
            } else {
                mBinding.ivMessageLeft.visibility = View.GONE
            }
            mBinding.ivLeft.visibility = View.GONE
            resetHeartView(0f.dp2px())
            resetChartView(86.dp2px())
        }
    }

    //全屏处理
    //视频全屏策略，竖向全屏，横向全屏，还是根据宽高比来选择
    private fun getFullScreenOrientation(): Int {
        return ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    private fun exitFullScreenOrientation(): Int {
        return ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
    }

    override fun onStart() {
        super.onStart()
        player?.play()
        updateControls()
    }

    override fun onResume() {
        super.onResume()
        mPresenter.addAWSLive(userId, roomArn, channelId)
        mHandler.sendEmptyMessageDelayed(1, 500L)
        mPresenter.queryLivePromo()
    }

    override fun onPause() {
        super.onPause()
        mHandler.removeCallbacksAndMessages(null)
        mPresenter.exitLive(userId, channelId)
    }

    override fun onStop() {
        super.onStop()
        mHandler.removeCallbacksAndMessages(null)
        player?.pause()
    }

    private fun setTopView() {
        if (!UserDataUtil.isLogin()) {
            mBinding.tvLeftTip.text = getString(R.string.start_your_investing_journey)
            mBinding.livingTopHorizontal.tvLeftTip2.text = getString(R.string.start_your_investing_journey)
            mBinding.tvAccountRight.text = getString(R.string.start)
            mBinding.livingTopHorizontal.tvAccountRight2.text = getString(R.string.start)
            mBinding.tvAccountRight.background = ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.livingTopHorizontal.tvAccountRight2.background =
                ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.tvAccountRight.setOnClickListener {
                exitFullScreen()
                startActivity(Intent(this, LoginActivity::class.java))
                LogEventUtil.setLogEvent("livestream_start_button")
            }
            mBinding.livingTopHorizontal.tvAccountRight2.setOnClickListener {
                exitFullScreen()
                startActivity(Intent(this, LoginActivity::class.java))
                LogEventUtil.setLogEvent("livestream_start_button")
            }
        } else if (!UserDataUtil.isOpenLiveAccount()) {
            // 5分钟内开设Live账户
            mBinding.tvLeftTip.text = getString(R.string.open_live_account_in_minutes)
            mBinding.livingTopHorizontal.tvLeftTip2.text = getString(R.string.open_live_account_in_minutes)
            mBinding.tvAccountRight.text = getString(R.string.go_live)
            mBinding.livingTopHorizontal.tvAccountRight2.text = getString(R.string.go_live)
            mBinding.tvAccountRight.background = ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.livingTopHorizontal.tvAccountRight2.background =
                ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.tvAccountRight.setOnClickListener {
                if (SpManager.isNeedKYC()) {
                    VAUStartUtil.openAccountToKyc(this)
                } else {
                    mPresenter.queryStAccountType(false)
                }
                exitFullScreen()
                LogEventUtil.setLogEvent("livestream_go_live_button")
            }
            mBinding.livingTopHorizontal.tvAccountRight2.setOnClickListener {
                if (SpManager.isNeedKYC()) {
                    VAUStartUtil.openAccountToKyc(this)
                } else {
                    mPresenter.queryStAccountType(false)
                }
                exitFullScreen()
                LogEventUtil.setLogEvent("livestream_go_live_button")
            }
        } else {
            // 享受投资之旅
            mBinding.tvLeftTip.text = getString(R.string.enjoy_your_investing_journey)
            mBinding.livingTopHorizontal.tvLeftTip2.text = getString(R.string.enjoy_your_investing_journey)
            mBinding.tvAccountRight.text = getString(R.string.deposit)
            mBinding.livingTopHorizontal.tvAccountRight2.text = getString(R.string.deposit)
            mBinding.tvAccountRight.background = ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.livingTopHorizontal.tvAccountRight2.background =
                ContextCompat.getDrawable(this, R.drawable.shape_ce35728_r4)
            mBinding.tvAccountRight.setOnClickListener {
                exitFullScreen()
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                LogEventUtil.setLogEvent("livestream_deposit_button")
            }
            mBinding.livingTopHorizontal.tvAccountRight2.setOnClickListener {
                exitFullScreen()
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                LogEventUtil.setLogEvent("livestream_deposit_button")
            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.SWITCH_ACCOUNT -> {
                openActivity(MainActivity::class.java)
            }
        }
    }

    override fun onDestroy() {
        mHandler.removeCallbacksAndMessages(null)
        EventBus.getDefault().post(NoticeConstants.LIVE_ROOM_EXIT)
        player?.release()
        EventBus.getDefault().unregister(this)
        socket?.close(1000, "123")
        super.onDestroy()
    }

    private fun resetChartView(height: Int) {
        val set = ConstraintSet()
        set.clone(mBinding.clRoot)
        set.connect(R.id.rvMessage, ConstraintSet.BOTTOM, R.id.clRoot, ConstraintSet.BOTTOM, height)
        set.applyTo(mBinding.clRoot)
    }

    private fun resetHeartView(left: Float) {
        val animator = ObjectAnimator.ofFloat(mBinding.mHeartLayout, "translationX", -left)
        animator.duration = 100
        animator.start()
    }

    private fun updateControls() {
        if (mBinding.surfaceView.isAttachedToWindow) {
            mHandler.removeMessages(0)
            mHandler.sendEmptyMessageDelayed(0, 200L)
        }
    }

    override fun getToken(token: ChartTokenBean) {
        userId = token.userId
        saveToken(token.chatToken, false)
    }

    override fun getLiveInfo(liveInfoData: LiveInfoBean?) {
        mBinding.tvWatchCount.text = liveInfoData?.obj?.virtualCount.ifNull().toString()
        mBinding.livingTopHorizontal.tvWatchCount2.text = liveInfoData?.obj?.virtualCount.ifNull().toString()
        mBinding.tvLike.text = liveInfoData?.obj?.virtualLikeCount.ifNull().toString()
    }

    override fun filterChatContentSucceed(content: String, chatId: String) {
        val messageData = MessageData(
            "", chatId, "SEND_MESSAGE", content,
            MessageAttributes("", "")
        )
        socket?.send(messageData.json)
    }

    override fun filterForbiddenChat(content: String) {
        ToastUtil.showToast(content)
    }

    override fun giveLikesSucceed(count: Long) {
        mBinding.ivLike.setImageResource(R.drawable.img_live_like_click)
        mBinding.tvLike.text = count.ifNull().toString()
    }

    override fun getChartSucceed() {

    }

    override fun getChartTokenSucceed(token: String) {
        saveToken(token, true)
    }

    override fun queryLivePromoSucceed(promoData: ArrayList<PromoEventData>) {
        if (promoData.isEmpty()) {
            isHavePromo = false
            mBinding.ivLeft.visibility = View.GONE
            mBinding.ivMessageLeft.visibility = View.GONE
        } else {
            isHavePromo = true
            if (isFullScreen) {
                mBinding.ivLeft.visibility = View.VISIBLE
                mBinding.ivMessageLeft.visibility = View.GONE
            } else {
                mBinding.ivLeft.visibility = View.GONE
                mBinding.ivMessageLeft.visibility = View.VISIBLE
            }
        }
        mBinding.ivMessageLeft.isVisible = true
    }

    override fun skipOpenAccountActivity(linkSkipState: EnumLinkSkipState, objData: MT4AccountTypeObj?) {
        UserDataUtil.setOpenLiveAccountState(if (objData?.applyTpe == 2) "1" else "0")

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_OPEN_ACCOUNT) {
            if (objData?.status == 2) {
                ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
                return
            }
            //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑
            if (objData?.applyTpe == 2 && objData.regulator == "1") {
                openActivity(AccountManagerActivity::class.java)
//                mPresenter.getWBPStatus()
                return
            }
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_DEPOSIT) {
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_REFER) {
            if (objData?.status == 2) {
                ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
                return
            }

            if (objData?.status == 5) {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString("url", mPresenter.wbpDataBean?.refer?.activityUrl ?: "")
                    putString("title", mPresenter.wbpDataBean?.refer?.activityName ?: "")
                    putInt("tradeType", 3)
                })
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        // 默认正常跳转，直接走公共跳转
        if (linkSkipState == EnumLinkSkipState.DEFAULT) {

            //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑
            if (objData?.applyTpe == 2 && objData.regulator == "1") {
                openActivity(AccountManagerActivity::class.java)
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        val objStatus = objData?.status

        // 成功开户 模拟/返佣 进入金
        if (objStatus == 5) {
            if (linkSkipState == EnumLinkSkipState.GOLDEN) {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
            }
            return
        }

        if (objStatus == 3) {
            VAUStartUtil.dispatchOpenAccount(this)
            return
        }

        /*
         开户审核中,只打开app
         1：账户未提交 (跳步数)
         2：账户审核，
         3：账户被挂起 {跳第一步}
         4：账户被拒绝(被拒绝，不存在这种情况，被拒绝时被弹出登陆，不能登陆)
         5：账户已通过
         6：账户待审核且身份证明未提交(跳身份证明)  数据不对，也是跳步数
         */
        if (objStatus == 2) {
            ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
            return
        }

        // 跳开户第几步
        VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
    }

    private fun saveToken(token: String, isRefreshToken: Boolean) {
        chatToken = token
        socket?.close(1000, "123")
        startWebSocketDatafeed()
    }

    private fun startWebSocketDatafeed() {
        try {
            val client = OkHttpClient()
            val request = chatToken?.let {
                Request.Builder()
                    .url(messageNode)
                    .header("Sec-WebSocket-Protocol", it)
                    .header("Sec-WebSocket-Version", "13")
                    .build()
            }

            request?.let {
                socket = client.newWebSocket(it, object : WebSocketListener() {
                    override fun onOpen(webSocket: WebSocket, response: Response) {
                        super.onOpen(webSocket, response)
                    }

                    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                        super.onClosing(webSocket, code, reason)
                        webSocket.close(code, reason)
                    }

                    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                        super.onClosed(webSocket, code, reason)
                        webSocket.close(code, reason)
                    }

                    override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                        super.onFailure(webSocket, t, response)
                        if (mPresenter != null) {
                            mPresenter.getChatToken(userId, roomArn)
                        }
                    }

                    override fun onMessage(webSocket: WebSocket, text: String) {
                        super.onMessage(webSocket, text)
                        val mMessage = GsonUtil.buildGson().fromJson(text, AWSMessageData::class.java)
                        if (mMessage.EventName?.contains("DELETE_MESSAGE", true) == true) {
                            run outside@{
                                messageList.forEach { bean ->
                                    if (bean.RequestId == mMessage.RequestId) {
                                        messageList.remove(bean)
                                        mHandler.sendEmptyMessage(2)
                                        return@outside
                                    }
                                }
                            }
                        } else {
                            if (mMessage?.Sender?.Attributes?.userName?.isNotEmpty() == true && mMessage.Content.isNotEmpty()) {
                                if (TextUtils.isEmpty(mMessage.Attributes.replyUserName)) {
                                    mMessage.messageType = 0
                                } else {
                                    mMessage.messageType = 1
                                }
                                messageList.add(mMessage)
                                mHandler.sendEmptyMessage(2)
                            }
                            // 这里之前有else 弹出 “您的操作过于频繁，请稍后重试” 的提示，经排查iOS没有这个逻辑，就去掉这里的提示 并去掉多语言文案
                        }
                    }
                }) as RealWebSocket
            }
        } catch (e: Exception) {
            mPresenter.getChatToken(userId, roomArn)
        }
    }
}