package cn.com.vau.signals.adapter.live

import android.content.Context
import android.text.Html
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.discover.ChartMessage

/**
 * Created by liyang
 */
class MessageHistoryRecyclerAdapter(
    var mContext: Context,
    var dataList: List<ChartMessage>
) : RecyclerView.Adapter<MessageHistoryRecyclerAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_live_message, null))

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val dataBean = dataList.elementAtOrNull(position) ?: return
        if (TextUtils.isEmpty(dataBean.replyUserName)) {
            if ((dataBean.userName) == "Assistant" || (dataBean.userName) == "Vantage") {
                holder.tvContent.text = Html.fromHtml(
                    "${"Vantage"} : ${dataBean.chatContent}"
                )
            } else {
                holder.tvContent.text = Html.fromHtml(
                    "<font color='#ff8e5c'>${dataBean.userName} : </font>${dataBean.chatContent}"
                )
            }
        } else {
            holder.tvContent.text = Html.fromHtml(
                "${"Vantage"}${" " + mContext.getString(R.string.reply) + " "} " +
                        "<font color='#ff8e5c'>${"@"}${dataBean?.replyUserName} : </font>${dataBean?.chatContent}"
            )
        }
    }

    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvContent = view.findViewById<TextView>(R.id.tvContent)
    }

    private var monItemClickListener: onItemClickListener? = null

    interface onItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: onItemClickListener) {
        monItemClickListener = onItemClickListener
    }

}