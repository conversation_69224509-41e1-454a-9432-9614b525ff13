package cn.com.vau.signals.viewModel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.discover.FiltersCountryObj

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 30 20:19
 * @updateUser:
 * @updateDate: 2024 10月 30 20:19
 */
class FiltersViewModel : BaseViewModel() {

    val selectedCountryLivedata: MutableLiveData<String> by lazy { MutableLiveData() }
    val selectedPriorityLivedata: MutableLiveData<String> by lazy { MutableLiveData() }

    val uiListLiveData: MutableLiveData<ListUIState<List<FiltersCountryObj>?>> by lazy { MutableLiveData() }

    init {
        finCalendarAreas()
    }

    fun finCalendarAreas() {
        requestNet({
            baseService.finCalendarAreas()
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            uiListLiveData.value = ListUIState.RefreshSuccess(it.data?.obj)
        })
    }

}