package cn.com.vau.signals.adapter

import androidx.appcompat.widget.AppCompatTextView
import cn.com.vau.R
import cn.com.vau.data.discover.FxStreetBaseData
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: FxStreetAdapter
 * Author: GG
 * Date: 2024/9/20
 * Description:
 */
class FxStreetAdapter : BaseQuickAdapter<FxStreetBaseData, BaseViewHolder>(R.layout.item_recycler_fx_street) {

    override fun convert(holder: BaseViewHolder, item: FxStreetBaseData) {
        holder.setText(R.id.tvName, item.newsTitle)
            .setText(R.id.tvDetail, item.summary)
            .setText(R.id.tvDate, item.pubTime?.let { TimeUtil.descriptiveData(it) })
    }

    override fun onItemViewHolderCreated(viewHolder: BaseViewHolder, viewType: Int) {
        super.onItemViewHolderCreated(viewHolder, viewType)
        with(viewHolder) {
            getViewOrNull<AppCompatTextView>(R.id.tvName)?.setFontG600()
            getViewOrNull<AppCompatTextView>(R.id.tvDetail)?.setFontG500()
            getViewOrNull<AppCompatTextView>(R.id.tvDate)?.setFontG500()
        }
    }

}