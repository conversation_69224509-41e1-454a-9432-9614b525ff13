package cn.com.vau.signals.stsignal.adapter

import android.text.*
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.data.strategy.SearchStrategyBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

/**
 * Filename: StSearchSignalAdapter.kt
 * Author: GG
 * Date: 2024/3/29
 * Description:
 */
class StSearchSignalAdapter(
    private val isSignal: Boolean = false, private val isShowHighStr: Boolean = true, var searchKey: String? = null
) : BaseQuickAdapter<SearchStrategyBean, BaseViewHolder>(R.layout.item_recycler_signal_search) {

    init {
        addChildClickViewIds(R.id.tvButton)
    }

    override fun convert(holder: BaseViewHolder, item: SearchStrategyBean) {
        val avatar = holder.getView<ShapeableImageView>(R.id.ivIcon)
        if (isSignal) {
            holder.setText(R.id.tvName, item.nickname)
                .setText(R.id.tvType, context.getString(R.string.copiers))
                .setText(R.id.tvTypeNum, item.copiers)
                .setTextColor(
                    R.id.tvTypeNum, if (item.copiers.mathCompTo("0") == 1)
                        ContextCompat.getColor(context, R.color.c00c79c)
                    else if (item.copiers.mathCompTo("0") == -1)
                        ContextCompat.getColor(context, R.color.ce35728)
                    else
                        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                )
            avatar.post {
                val cornerSize = avatar.width / 2f
                avatar.shapeAppearanceModel = avatar.shapeAppearanceModel.toBuilder().setAllCornerSizes(cornerSize).build()
            }
        } else {
            holder.setText(R.id.tvName, item.signalName)
                .setText(R.id.tvType, context.getString(R.string.return_3m))
                .setText(R.id.tvTypeNum, item.returnRate?.percent() + "%")
                .setTextColor(
                    R.id.tvTypeNum, if (item.returnRate.mathCompTo("0") == 1)
                        ContextCompat.getColor(context, R.color.c00c79c)
                    else if (item.returnRate.mathCompTo("0") == -1)
                        ContextCompat.getColor(context, R.color.ce35728)
                    else
                        AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
                )
            avatar.post {
                avatar.shapeAppearanceModel = avatar.shapeAppearanceModel.toBuilder().setAllCornerSizes(10f.dp2px()).build()
            }
        }
        if ((item.pendingApplyApproval == true || item.isFollowed == true) && !isSignal) {
            holder.setText(R.id.tvButton, context.getString(R.string.manage))
        } else {
            holder.setText(R.id.tvButton, context.getString(R.string.view))
        }
        ImageLoaderUtil.loadImage(
            context,
            if (isSignal) item.avatar else item.profilePictureUrl,
            avatar,
            R.mipmap.ic_launcher,
            R.mipmap.ic_launcher
        )
        if (isShowHighStr)
            searchKey?.let { key ->
                holder.getViewOrNull<AppCompatTextView>(R.id.tvName)?.let { textView ->
                    set(textView, key, ContextCompat.getColor(context, R.color.ce35728))
                    textView.typeface = ResourcesCompat.getFont(context, R.font.gilroy_semi_bold)
                }
            }
    }

    private fun set(textView: TextView, span: String, @ColorInt color: Int) {
        val index = textView.text.toString().lowercase().indexOf(span.lowercase())
        val spanString = SpannableString(textView.text)
        if (index != -1) {
            spanString.setSpan(ForegroundColorSpan(color), index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            textView.text = spanString
            textView.highlightColor = ContextCompat.getColor(context, R.color.transparent)
        }
    }

}