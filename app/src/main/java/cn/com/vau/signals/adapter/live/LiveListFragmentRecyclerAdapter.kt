package cn.com.vau.signals.adapter.live

import android.view.*
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.discover.Row
import cn.com.vau.util.*
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.imageview.ShapeableImageView

/**
 * Created by liyang
 */
class LiveListFragmentRecyclerAdapter() : ListAdapter<Row, RecyclerView.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            1 -> {
                ViewHolderLive(LayoutInflater.from(parent.context).inflate(R.layout.fragment_live_list_header, parent, false))
            }

            3 -> {
                ViewHolder1(LayoutInflater.from(parent.context).inflate(R.layout.fragment_live_list_title, parent, false))
            }

            else -> {
                ViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_recycler_live, parent, false))
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return getItem(position)?.itemType ?: -1
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val dataBean = getItem(position) ?: return
        when (dataBean.itemType) {
            1 -> {
                val holderLive = holder as ViewHolderLive
                holderLive.tvLiveName?.text = dataBean.ivestreamTitle.toString()
                holderLive.tvLiveContent?.text = dataBean.introduction.toString()

                if (dataBean.liveStatus == 1) {
                    holderLive.cl_hot_count.visibility = View.VISIBLE
                } else {
                    holderLive.cl_hot_count.visibility = View.GONE
                }

                holderLive.tvLiveDate?.text = dataBean.planStartTime.toString()
                holderLive.tvHotCount?.text = dataBean.virtualCount.toString()
                val myOptions = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                holderLive.ivHeader?.let {
                    ImageLoaderUtil.loadImageWithOption(it.context, dataBean.coverPicture, it, myOptions)
                }
                holderLive.itemView.setOnClickListener {
                    mOnItemClickListener?.onItemClick(position)
                }
            }

            3 -> {
                val holder1 = holder as ViewHolder1
                holder1.tvUpcoming.text = dataBean.titleType ?: ""
            }

            else -> {
                val holder = holder as ViewHolder

                val myOptions = RequestOptions()
                    .placeholder(R.drawable.shape_placeholder)
                    .error(R.drawable.shape_placeholder)
                ImageLoaderUtil.loadImageWithOption(holder.mImageView.context, dataBean.coverPicture, holder.mImageView, myOptions)

                holder.tvTitle.text = dataBean.ivestreamTitle.toString()
                holder.tvInfo.text = dataBean.introduction.toString()
                holder.tvHistoryDate.text = dataBean.planStartTime.toString()
                if (dataBean.liveStatus == 2) {
                    holder.tvVideoLength.visibility = View.GONE
                    holder.tvVideoLength.text = dataBean.strTimeLength
                } else {
                    holder.tvVideoLength.visibility = View.GONE
                }

                holder.itemView.setOnClickListener {
                    mOnItemClickListener?.onItemClick(position)
                }
            }
        }
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val mImageView = view.findViewById<ShapeableImageView>(R.id.mImageView)
        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvInfo = view.findViewById<TextView>(R.id.tvInfo)
        val tvHistoryDate = view.findViewById<TextView>(R.id.tvHistoryDate)
        val tvVideoLength = view.findViewById<TextView>(R.id.tvVideoLength)

        init {
            tvTitle.setFontG600()
            tvInfo.setFontG500()
            tvHistoryDate.setFontG500()
            tvVideoLength.setFontG500()
        }
    }

    class ViewHolder1(view: View) : RecyclerView.ViewHolder(view) {
        val tvUpcoming = view.findViewById<TextView>(R.id.tvUpcoming)

        init {
            tvUpcoming.setFontG600()
        }
    }

    class ViewHolderLive(view: View) : RecyclerView.ViewHolder(view) {
        val tvLiveName = view.findViewById<TextView>(R.id.tvLiveName)
        val tvLiveContent = view.findViewById<TextView>(R.id.tvLiveContent)
        val tvLiveDate = view.findViewById<TextView>(R.id.tvLiveDate)
        val tvHotCount = view.findViewById<TextView>(R.id.tvHotCount)
        val ivHeader = view.findViewById<ImageView>(R.id.ivHeader)
        val cl_hot_count = view.findViewById<ConstraintLayout>(R.id.cl_hot_count)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }


    class DiffCallback : DiffUtil.ItemCallback<Row>() {
        override fun areItemsTheSame(oldItem: Row, newItem: Row): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Row, newItem: Row): Boolean {
            return oldItem == newItem
        }
    }
}
