package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.common.view.popup.adapter.StCommunityFilterBean
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.util.GsonUtil

/**
 * Filename: StCommunityViewModel.kt
 * Author: GG
 * Date: 2024/3/26
 * Description:
 */
class StCommunityViewModel : BaseViewModel() {

    var overallSort: String = "1"
    var dataTimeBean: StCommunityFilterBean? = null
    var dataReturnBean: StCommunityFilterBean? = null
    var dataRiskBandBean: StCommunityFilterBean? = null
    var dataWinRateBean: StCommunityFilterBean? = null
    var dataTradingBean: StCommunityFilterBean? = null

    val uiListLiveData: MutableLiveData<ListUIState<List<StrategyBean>?>> by lazy { MutableLiveData() }

    var pageNum = 1
    private val pageSize = 20
    var selectQuickIndex = 0

    private val requestMap = mutableMapOf<String, String?>()

    var totalScrollDistance = 0

    init {
        refresh()
    }

    fun clearData() {
        dataTimeBean = null
        dataReturnBean = null
        dataRiskBandBean = null
        dataWinRateBean = null
        dataTradingBean = null
    }

    fun refresh() {
        pageNum = 1
        filterSignalApi()
    }

    fun loadMore() {
        pageNum++
        filterSignalApi()
    }

    /**
     * 策略排行榜
     */
    private fun filterSignalApi() {
        requestNet({
            requestMap.clear()
            requestMap["overallSort"] = overallSort
            requestMap["winRate"] = dataWinRateBean?.requestData
            requestMap["returnRate"] = dataReturnBean?.requestData
            requestMap["months"] = dataTimeBean?.requestData ?: "3"
            requestMap["riskBandLevel"] = dataRiskBandBean?.requestData
            requestMap["tradingCategories"] = dataTradingBean?.requestData
            requestMap["pageNum"] = pageNum.toString()
            requestMap["pageSize"] = pageSize.toString()
            stTradingService.strategyFilterSignalApi(GsonUtil.buildGson().toJsonTree(requestMap).asJsonObject)
        }, { dataBean ->
            if (!dataBean.isSuccess()) {
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            if (dataBean.data.isNullOrEmpty() && pageNum == 1) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }

            if (pageNum == 1) {
                uiListLiveData.value = ListUIState.RefreshSuccess(dataBean.data)
            } else {
                if (dataBean.data.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(dataBean.data)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(dataBean.data)
                }
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = false)

    }

    /**
     * 账户状态查询
     */
    fun queryMT4AccountTypeApi() {
        requestNet({
            baseService.crmGetMt4AccountApplyTypeApi(UserDataUtil.loginToken())
        }, onSuccess = { data ->
            val obj = data.data?.obj
            /*applyTpe: 申请类型(0:不能申请, 1:真实账户开通，
              2:同名账户，3:重新申请，
              4：身份证明或地址证明未通过，5：只读账户身份证明未通过，
              6：账户被拒绝，7：未上传身份证明)
              若applyTpe返回2表示Live帳號已完全開通完畢 則此時表示開通跟單帳戶可直接跳一頁式的頁面來開通
              否則遵照原本開通LIVE的判斷來決定下一步頁面
            openActivity(OpenStAccountActivity::class.java)*/
            sendEvent(obj)
        })
    }
}