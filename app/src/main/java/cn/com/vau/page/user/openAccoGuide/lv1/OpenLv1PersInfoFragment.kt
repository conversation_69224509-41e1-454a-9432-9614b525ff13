package cn.com.vau.page.user.openAccoGuide.lv1

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.*
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.VerifyCompHandler
import cn.com.vau.data.account.*
import cn.com.vau.databinding.FragmentOpenLv1PersinfoBinding
import cn.com.vau.page.common.selectNation.SelectNationalityActivity
import cn.com.vau.page.user.bindEmail.BindEmailActivity
import cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity
import cn.com.vau.page.user.openAccoGuide.lv1.vm.OpenLv1ViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import org.greenrobot.eventbus.*
import org.json.JSONObject

class OpenLv1PersInfoFragment : BaseFragment() {

    private val REQUEST_CODE_RESIDENCE = 0xaa
    private val REQUEST_CODE_NATIONALITY = 0xab
    private val binding: FragmentOpenLv1PersinfoBinding by lazy { FragmentOpenLv1PersinfoBinding.inflate(layoutInflater) }
    private var verifyHandler = VerifyCompHandler()
    private val viewModel: OpenLv1ViewModel by lazy { ViewModelProvider(requireActivity())[OpenLv1ViewModel::class.java] }

    private var selectedResidenceId = ""
    private var selectedNationalityId = ""
    private var genderList = mutableListOf<AccoSelectItem>()
    private var selectedGender: AccoSelectItem? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun initParam() {
        super.initParam()
        LogEventUtil.setLogEvent(
            BuryPointConstant.V334.REGISTER_LIVE_PAGE_VIEW, hashMapOf(
                "Page_name" to "${(activity as? OpenAccoGuideBaseActivity<*>)?.buryPointMsg}-Lvl1-1"
            )
        )
    }

    override fun initView() {
        super.initView()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        verifyHandler.add(
            binding.viewEtEmail,
            binding.viewEtFirstName,
            binding.viewEtLastName,
            binding.viewDdGender,
            binding.viewEtBirth,
            binding.viewEtResidence,
            binding.viewEtNationality
        ).to(
            binding.tvNext
        ).submit {
            if (viewModel.checkParamSame(
                    hashMapOf(
                        "token" to UserDataUtil.loginToken(),
                        "step" to "1",
                        "openAccountMethod" to 1,
                        "email" to binding.viewEtEmail.text(),
                        "firstName" to binding.viewEtFirstName.text(),
                        "middleName" to binding.viewEtMiddleName.text(),
                        "lastName" to binding.viewEtLastName.text(),
                        "nationalityId" to selectedNationalityId,
                        "countryId" to selectedResidenceId,
                        "dob" to binding.viewEtBirth.text(),
                        "gender" to selectedGender?.valueName.ifNull(),
                        "confirm" to viewModel.confirmDialog,
                    )
                )
            ) {
                activity?.let { activity ->
                    (activity as OpenAccoGuideBaseActivity<*>).tabSelected(1)
                }
            } else {
                context?.let {
                    if (selectedResidenceId == "6907" && !viewModel.confirmDialog) {
                        CenterActionDialog.Builder(requireActivity())
                            .setTitle(getString(R.string.important_information)) //设置则展示标题，否则不展示
                            .setContent(getString(R.string.the_application_you_if_please_by_the_fca)) //设置内容
                            .setStartText(getString(R.string.cancel))//设置左侧按钮文本
                            .setEndText(getString(R.string.proceed))//设置右侧按钮文本
                            //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                            .setOnStartListener { binding.viewEtEmail.reenter() }
                            .setOnEndListener {
                                viewModel.confirmDialog = true
                                showEmailDialog()
                            }
                            .build()
                            .showDialog()
                    } else {
                        showEmailDialog()
                    }
                }
            }

            // 神策自定义埋点(v3500)，点击事件
            sensorsTrackClick()
        }
    }

    private fun showEmailDialog() {
        CenterActionDialog.Builder(requireActivity())
            .setTitle(getString(R.string.please_check_your_email)) //设置则展示标题，否则不展示
            .setContent(binding.viewEtEmail.text()) //设置内容
            .setStartText(getString(R.string.edit))//设置左侧按钮文本
            .setEndText(getString(R.string.confirm))//设置右侧按钮文本
            //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
            .setOnStartListener {  binding.viewEtEmail.reenter() }
            .setOnEndListener { checkEmail() }
            .build()
            .showDialog()
    }

    override fun initData() {
        super.initData()
        viewModel.getAccountSelect()
        viewModel.getProcess("1", true)
        binding.viewEtResidence.setTarget(
            lanucher,
            SelectCountryResidenceActivity.createIntent(
                requireContext(), SelectCountryResidenceActivity.TYPE_FORM_NOT_ASIC_LV1, REQUEST_CODE_RESIDENCE
            )
        )
        binding.viewEtNationality.setTarget(
            lanucher,
            Intent(context, SelectNationalityActivity::class.java).apply {
                this.putExtras(bundleOf("requestCode" to REQUEST_CODE_NATIONALITY))
            })
    }

    private val lanucher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Activity.RESULT_OK) {
            when (it.data?.getIntExtra("requestCode", -1)) {
                REQUEST_CODE_RESIDENCE -> {
                    val name = it.data?.getStringExtra("name")
                    val id = it.data?.getStringExtra("id")
                    binding.viewEtResidence.setText(name.ifNull())
                    selectedResidenceId = id.ifNull()
                }

                REQUEST_CODE_NATIONALITY -> {
                    val bean = it.data?.getSerializableExtra(Constants.SELECT_NATIONALITY_DATA)
                    bean?.let { obj ->
                        if (obj is SelectNationalityObjDetail) {
                            binding.viewEtNationality.setText(obj.nationality.ifNull())
                            selectedNationalityId = obj.id.ifNull()
                        }
                    }
                }
            }
        }
    }

    override fun initListener() {
        super.initListener()
        viewModel.getProcessLiveData1.observe(this) {
            if ("V00000" == it.resultCode) {
                it.data?.obj?.let { bean ->
                    binding.viewEtEmail.setText(bean.email.ifNull())
                    binding.viewEtFirstName.setText(bean.firstName.ifNull())
                    binding.viewEtMiddleName.setText(bean.middleName.ifNull())
                    binding.viewEtLastName.setText(bean.lastName.ifNull())
                    binding.viewDdGender.setText(bean.gender.ifNull())
                    binding.viewEtBirth.setText(bean.dob.ifNull())
                    binding.viewEtResidence.setText(bean.countryName.ifNull())
                    binding.viewEtNationality.setText(bean.nationalityName.ifNull())
                    selectedNationalityId = bean.nationalityId.ifNull(-1).toString()
                    selectedResidenceId = bean.countryId.ifNull(-1).toString()
                    viewModel.confirmDialog = bean.confirmDialog
                    if (bean.gender.isNullOrEmpty().not()) {
                        //如需要反显性别下拉列表中数据，可以把这个data先存起来，然后请求性别列表接口，然后按选中项反显
                        selectedGender = AccoSelectItem(valueName = bean.gender)
                    }
                }
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }

        viewModel.accountSelectLiveData.observe(this) {
            if ("V00000" == it.resultCode) {
                val list = it.data?.obj?.accountGenderList
                genderList.clear()
                genderList.addAll(list ?: arrayListOf())
                val dataList = mutableListOf<String>()
                dataList.addAll((list ?: arrayListOf()).map { data -> data.displayName.ifNull() })
                binding.viewDdGender.setData(dataList).onSelected { position ->
                    selectedGender = genderList.elementAtOrNull(position)
                }
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }

        viewModel.checkEmailLiveData.observe(this) {
            if ("V00000" == it.resultCode) {
                if (it.data?.obj?.emailStatus == true) {
                    hideNetDialog()
                    // need bind email
                    CenterActionDialog.Builder(requireActivity())
                        .setContent(getString(R.string.this_email_has_would_account)) //设置内容
                        .setStartText(getString(R.string.cancel))//设置左侧按钮文本
                        .setEndText(getString(R.string.link))//设置右侧按钮文本
                        //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                        .setOnStartListener { binding.viewEtEmail.reenter() }
                        .setOnEndListener {
                            openActivity(
                                BindEmailActivity::class.java,
                                bundleOf("email" to binding.viewEtEmail.text())
                            )
                        }
                        .build()
                        .showDialog()
                } else {
                    sendToNext()
                }
            } else {
                hideNetDialog()
                ToastUtil.showToast(it.msgInfo)
            }
        }

        viewModel.saveProcessLiveData1.observe(this) {
            hideNetDialog()
            if ("V00000" == it.resultCode) {
                viewModel.processParam1.clear()
                viewModel.processParam1.putAll(viewModel.tempParam)
                viewModel.tempParam.clear()
                binding.viewEtEmail.setEnableEdit(false)
                EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
                viewModel.persInfoSaveCompleted = true
                activity?.let { activity ->
                    (activity as OpenAccoGuideBaseActivity<*>).tabSelected(1)
                }
                //绑定神策业务ID，场景5
                SensorsDataUtil.bindBusinessIdForMerge(it.data?.obj?.emailEventID)
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }
    }

    private fun checkEmail() {
        showNetDialog()
        //TODO Felix 请求失败loading不会消失
        viewModel.checkEmail(binding.viewEtEmail.text())
    }

    private fun sendToNext() {
        viewModel.saveProcess(
            1, hashMapOf(
                "token" to UserDataUtil.loginToken(),
                "step" to "1",
                "openAccountMethod" to 1,
                "email" to binding.viewEtEmail.text(),
                "firstName" to binding.viewEtFirstName.text(),
                "middleName" to binding.viewEtMiddleName.text(),
                "lastName" to binding.viewEtLastName.text(),
                "nationalityId" to selectedNationalityId,
                "countryId" to selectedResidenceId,
                "dob" to binding.viewEtBirth.text(),
                "gender" to selectedGender?.valueName.ifNull(),
                "confirm" to viewModel.confirmDialog,
            )
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(tag: String) {
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    override fun onResume() {
        super.onResume()
        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrackPageView()
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrackPageView() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv1.Account Opening") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv1.Account Opening") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "Lv1-Personal Information") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, "Lv1-Next") // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }

    companion object {
        fun newInstance() = OpenLv1PersInfoFragment()
    }

}