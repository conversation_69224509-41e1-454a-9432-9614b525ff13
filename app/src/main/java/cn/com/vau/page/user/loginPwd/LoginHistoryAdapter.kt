package cn.com.vau.page.user.loginPwd

import android.graphics.Typeface
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.SelectTitle
import cn.com.vau.util.AttrResourceUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * 输入描述
 * Created by THINKPAD on 2019/9/10.
 */
class LoginHistoryAdapter : BaseQuickAdapter<SelectTitle, BaseViewHolder>(R.layout.item_login_history) {
    private var selectedTitle: String? = null

    override fun convert(holder: BaseViewHolder, item: SelectTitle) {
        holder.setText(R.id.tvHistory, item.getTitle())
        if (selectedTitle == item.getTitle()) {
            holder.setBackgroundColor(R.id.tvHistory, ContextCompat.getColor(context, R.color.c0e00c79c))
                .setTextColor(R.id.tvHistory, AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            val myFont = ResourcesCompat.getFont(context, R.font.gilroy_semi_bold)
            val boldTypeface = Typeface.create(myFont, Typeface.NORMAL)
            holder.getView<TextView>(R.id.tvHistory).typeface = boldTypeface
        } else {
            holder.setBackgroundColor(R.id.tvHistory, AttrResourceUtil.getColor(context, R.attr.color_c0a1e1e1e_c262930))
                .setTextColor(R.id.tvHistory, AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
            val myFont = ResourcesCompat.getFont(context, R.font.gilroy_medium)
            val boldTypeface = Typeface.create(myFont, Typeface.NORMAL)
            holder.getView<TextView>(R.id.tvHistory).typeface = boldTypeface
        }
    }

    fun updateSelect(text: String?) {
        selectedTitle = text.toString()
        notifyDataSetChanged()
    }

}