package cn.com.vau.page.user.login

import android.annotation.SuppressLint
import android.os.*
import android.text.TextUtils
import android.view.*
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentForgetPwdSecondBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.CountDownTextHelper
import cn.com.vau.page.user.forgotPwdSecond.*
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoWithIconListDialog
import com.netease.nis.captcha.*
import kotlinx.coroutines.delay

/**
 * 忘记密码 第二步
 */
class ForgetPwdSecondFragment : BaseFrameFragment<ForgotPwdSecondPresenter, ForgetPwdSecondModel>(), ForgetPwdSecondContract.View {

    var captcha: Captcha? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { requireContext().getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    private val mBinding: FragmentForgetPwdSecondBinding by lazy { FragmentForgetPwdSecondBinding.inflate(layoutInflater) }

    override fun initParam() {
        super.initParam()
        arguments?.let {
            mPresenter.email = it.getString("email")
            mPresenter.txId = it.getString("txId")
            mPresenter.mobile = it.getString("mobile")
            mPresenter.smsSendType = it.getString("smsSendType", VerificationActivity.TYPE_SEND_SMS)
            mPresenter.countryCode = it.getString("countryCode")
            mPresenter.code = it.getString("code")
            mPresenter.handleType = it.getInt(Constants.HANDLE_TYPE, 0)
            mPresenter.pwd = it.getString("pwd")
            mPresenter.firstEmailLogin = it.getBoolean("firstEmailLogin", false)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    @SuppressLint("ObsoleteSdkInt", "SetTextI18n")
    override fun initView() {
        super.initView()

        mBinding.tvNotReceiveCodeTips.isVisible = mPresenter.smsSendType == VerificationActivity.TYPE_SEND_EMAIL // email 认证才显示提示文案

        mBinding.tvLoginType.text = "${getString(R.string.the_verification_code_has_been_sent_to)}:"

        mBinding.tvPhoneNumber.text = if (mPresenter.smsSendType == VerificationActivity.TYPE_SEND_EMAIL) mPresenter.email else "+${mPresenter.code} ${mPresenter.mobile}"

        mBinding.passwordView.setPasswordListener(object : PasswordView.PasswordListener {
            override fun passwordChange(changeText: String?) {
            }

            override fun passwordComplete() {
                val code = mBinding.passwordView.getPassword()
                if (code.length == 6) {
                    if (mPresenter.smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
                        if (mPresenter.firstEmailLogin) { // 邮箱首次登录时验证邮箱
                            mPresenter.validateEmailFirstLoginCodeApi(validateCode = code)
                        } else {
                            mPresenter.validateEmailForgetPwdCodeApi(validateCode = code)
                        }
                    } else {
                        mPresenter.validateSmsForgetPwdCodeApi(validateCode = code)
                    }
                    mBinding.passwordView.hiddenSoftInputFromWindow()
                }
            }

            override fun keyEnterPress(password: String?, isComplete: Boolean) {
            }
        })

        configButtonState()

        lifecycleScope.launchWhenResumed {
            delay(500)
            mBinding.passwordView.showSoftInput()
        }
    }

    private fun configButtonState() {
        when (mPresenter.smsSendType) {
            VerificationActivity.TYPE_SEND_SMS -> {
                mBinding.groupWhatsApp.isVisible = true
                mBinding.tvOr.isVisible = true
                mBinding.tvSendEms.isVisible = false
            }

            VerificationActivity.TYPE_SEND_WA -> {
                mBinding.groupWhatsApp.isVisible = false
                mBinding.tvOr.isVisible = true
                mBinding.tvSendEms.isVisible = true
            }

            else -> {
                mBinding.groupWhatsApp.isVisible = false
                mBinding.tvSendEms.isVisible = false
                mBinding.tvOr.isVisible = false
            }
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.run {
            setStartBackIconClickListener {
                NavHostFragment.findNavController(this@ForgetPwdSecondFragment).popBackStack()
            }
            setEndIconClickListener {
                openActivity(HelpCenterActivity::class.java)
            }
        }
        mBinding.tvReSendEms.setOnClickListener(this)
        // 收不到验证码提示文案
        mBinding.tvNotReceiveCodeTips.setOnClickListener {
            showNotReceiveCodeDialog()
        }
        mBinding.tvSendEms.setOnClickListener(this)
        mBinding.viewWhatsApp.setOnClickListener(this)

        mPresenter.initSendCodeUtil(object : SendCodeUtil.SendCodeListener {
            override fun onTick(millisUntilFinished: Int) {
                val second = millisUntilFinished.toString() // 倒计时秒数
                val fullText = getString(R.string.resend_code_in_x_seconds, second)
                mBinding.tvReSendEms.setTextColor(color_c1e1e1e_cebffffff)
                mBinding.tvReSendEms.text = countDownTextHelper.updateCountDownText(fullText, second)
                mBinding.tvReSendEms.isEnabled = false

                mBinding.tvSendEms.isEnabled = false
                mBinding.viewWhatsApp.isEnabled = false
                configButtonState()
                if (mBinding.tvSendEms.isVisible) {
                    mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
                    mBinding.tvSendEms.setTextColor(
                        AttrResourceUtil.getColor(requireActivity(), R.attr.color_c731e1e1e_c61ffffff)
                    )
                }
                if (mBinding.viewWhatsApp.isVisible)
                    mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            }

            override fun onFinish() {
                mPresenter.stopSendCodeUtil()
                mBinding.tvReSendEms.text = ac.getString(R.string.resend)
                mBinding.tvReSendEms.isEnabled = true
                mBinding.tvReSendEms.setTextColor(ce35728)

                mBinding.tvSendEms.isEnabled = true
                mBinding.viewWhatsApp.isEnabled = true
                if (mBinding.tvSendEms.isVisible) {
                    mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                    mBinding.tvSendEms.setTextColor(
                        AttrResourceUtil.getColor(requireActivity(), R.attr.color_cebffffff_c1e1e1e)
                    )
                }
                if (mBinding.viewWhatsApp.isVisible)
                    mBinding.viewWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            }
        })
        if (mPresenter.isFirstCount) {
            mPresenter.startSendCodeUtil()
            mPresenter.isFirstCount = false
        }

    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        mBinding.passwordView.hiddenSoftInputFromWindow()
        val data = if (mPresenter.smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_email_address),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips3)
                ),
                HintLocalData(
                    getString(R.string.check_your_spam_junk_folder),
                    getString(R.string.sometimes_the_email_by_mistake),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips4)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips2)
                )
            )
        } else {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_phone_number),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips1)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips2)
                ),
                HintLocalData(
                    getString(R.string.try_another_verification_method),
                    getString(R.string.switch_verification_methods_your_otp),
                    AttrResourceUtil.getDrawable(requireContext(), R.attr.imgNotReceiveCodeTips3)
                )
            )
        }
        BottomInfoWithIconListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getVerificationCodeApi(validate)
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvReSendEms -> {
                if (mPresenter.firstEmailLogin) {
                    mPresenter.emailSendEmailCodeApi()
                } else {
                    mPresenter.getVerificationCodeApi("")
                }
            }

            R.id.tvSendEms -> {
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_SMS
                mBinding.passwordView.clearInput()
                mPresenter.getVerificationCodeApi("")
            }

            R.id.viewWhatsApp -> {
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_WA
                mBinding.passwordView.clearInput()
                mPresenter.getVerificationCodeApi("")
            }
        }
    }

    override fun back() {
        if (arguments?.getInt("isFrom", 0) == 2) {
            activity?.finish()
        } else {
            NavHostFragment
                .findNavController(this)
                .popBackStack(R.id.forgetPwdFirstFragment, true)
        }
    }

    override fun onDetach() {
        super.onDetach()
        mPresenter.stopSendCodeUtil()
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    override fun goThird(validateCode: String?) {
        val bundle = Bundle()
        bundle.putString("email", mPresenter.email)
        bundle.putString("txId", mPresenter.txId)
        bundle.putString("mobile", mPresenter.mobile)
        bundle.putString("countryCode", mPresenter.countryCode)
        bundle.putString("code", mPresenter.code)
        bundle.putString("smsSendType", mPresenter.smsSendType)
        //randStr 邮箱改密码 需要 验证码的字段
        bundle.putString("randStr", validateCode)
        bundle.putInt(Constants.HANDLE_TYPE, mPresenter.handleType)
        NavHostFragment.findNavController(this).navigate(R.id.action_forget_second_to_third, bundle)
    }

    // 因为用了MVP框架，复用了P，这个方法被迫得实现，不过这里用不到
    override fun showWithdrawRestrictionMsg(msg: String?) {

    }

    /**
     * 跳转到绑定手机号页面
     */
    override fun goBindPhone(bundle: Bundle) {
        NavHostFragment.findNavController(this).navigate(R.id.action_forgetPwdSecondFragment_to_loginBindFragment, bundle)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (captcha != null) captcha?.destroy()
    }
}