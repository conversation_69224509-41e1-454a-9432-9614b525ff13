package cn.com.vau.page.common.selectResidence.activity;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.ViewStub;
import android.widget.AbsListView;
import android.widget.EditText;
import android.widget.ExpandableListView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.base.DataEvent;
import cn.com.vau.common.base.activity.BaseFrameActivity;
import cn.com.vau.common.view.WrapContentLinearLayoutManager;
import cn.com.vau.common.view.system.MyRecyclerView;
import cn.com.vau.data.account.ResidenceObj;
import cn.com.vau.data.account.ResidenceObjList;
import cn.com.vau.databinding.VsLayoutNoDataBinding;
import cn.com.vau.page.ResidenceEvent;
import cn.com.vau.page.common.selectResidence.adapter.BigLetterAdapter;
import cn.com.vau.page.common.selectResidence.adapter.ResidenceAdapter;
import cn.com.vau.page.common.selectResidence.adapter.SelectRegionAdapter;
import cn.com.vau.util.widget.HeaderBar;

/**
 * 选择城市
 * Created by zhy on 2018/12/20.
 */
public class SelectCityActivity extends BaseFrameActivity<SelectResidencePresenter, SelectResidenceModel>
        implements SelectResidenceContract.View {

    private EditText etSearch;
    private ExpandableListView elvResidenceList;
    private RecyclerView rcyBigLetter;
    private HeaderBar mHeaderBar;

    private List<ResidenceObj> mList = new ArrayList<>();
    private ResidenceAdapter residenceAdapter;//内容数据adapter
    private BigLetterAdapter bigLetterAdapter;//大写字母adapter

    private String tempCountryEn = "";//国家英文
    private String tempCountryId = "";//国家Id
    private String tempProvinceEn = "";//省份英文
    private String tempProvinceCode = "";//省份Code
    private String tempCityEn = "";//城市英文
    private String tempCityCode = "";//城市Code

    private boolean filterClick = false;

    private ConstraintLayout ctlSearch;
    private MyRecyclerView searchRecyclerView;
    private ViewStub viewStub;
    private List<ResidenceObjList> searchList = new ArrayList<>();
    private SelectRegionAdapter residenceResultAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_select_residence);
    }

    @Override
    public void initParam() {
        super.initParam();
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            tempCountryId = bundle.getString("countryId");
            tempCountryEn = bundle.getString("countryEn");
            tempProvinceCode = bundle.getString("provinceCode");
            tempProvinceEn = bundle.getString("provinceEn");
        }
    }

    @SuppressLint("WrongConstant")
    @Override
    public void initView() {
        super.initView();
        mHeaderBar = findViewById(R.id.mHeaderBar);
        etSearch = findViewById(R.id.etSearch);
        elvResidenceList = findViewById(R.id.elvResidenceList);
        rcyBigLetter = findViewById(R.id.rcyBigLetter);

        ctlSearch = findViewById(R.id.ctlSearch);
        searchRecyclerView = findViewById(R.id.searchRecyclerView);
        viewStub = findViewById(R.id.mVsNoData);
        viewStub.setOnInflateListener((stub, inflated) -> {
            VsLayoutNoDataBinding vs = VsLayoutNoDataBinding.bind(inflated);
            vs.mNoDataView.setHintMessage(getString(R.string.not_found_desc1) + "\n" + getString(R.string.not_found_desc2));
        });

        etSearch.setHint(getString(R.string.search_for_country) + "/" + getString(R.string.region));

        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(context);
        linearLayoutManager.setOrientation(OrientationHelper.VERTICAL);
        searchRecyclerView.setLayoutManager(linearLayoutManager);
        residenceResultAdapter = new SelectRegionAdapter(context, searchList, 2);
        searchRecyclerView.setAdapter(residenceResultAdapter);
        searchRecyclerView.setEmptyView(viewStub);

        residenceResultAdapter.setOnItemClickListener((view, position) -> {
            tempCityEn = searchList.get(position).cityNameEn;
            tempCityCode = searchList.get(position).cityCode;
            //结束
            EventBus.getDefault().post(
                    new DataEvent(
                            "",
                            new ResidenceEvent(
                                    tempCountryId,
                                    tempCountryEn,
                                    tempProvinceCode,
                                    tempProvinceEn,
                                    tempCityCode,
                                    tempCityEn
                            )
                    )
            );
            finish();
        });

    }

    @Override
    public void initData() {
        super.initData();
        mHeaderBar.setTitleText(getString(R.string.select_city));
        etSearch.setHint(getString(R.string.search_for_city));
        //查询城市
        mPresenter.queryCity(tempProvinceCode, "", 0);

    }

    @Override
    public void initListener() {
        super.initListener();
        mHeaderBar.setStartBackIconClickListener(() -> {
            finish();
            return null;
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() == 0) {
                    ctlSearch.setVisibility(View.GONE);
                } else {
                    ctlSearch.setVisibility(View.VISIBLE);
                    //条件搜索查询数据
                    mPresenter.queryCity(tempProvinceCode, etSearch.getText().toString().trim(), 1);
                }
            }
        });
    }

    @Override
    public void refreshResidence(List<ResidenceObj> list) {

    }

    @Override
    public void refreshProvince(List<ResidenceObj> list) {

    }

    @SuppressLint("WrongConstant")
    @Override
    public void refreshCity(List<ResidenceObj> list) {
        mList.clear();
        mList.addAll(list);
        //内容数据
        residenceAdapter = new ResidenceAdapter(this, mList, 2);
        residenceAdapter.setOnNationSelectedListener(new ResidenceAdapter.OnNationSelectedListener() {
            @Override
            public void onSelected(int groupPosition, int childPosition) {
                tempCityEn = mList.get(groupPosition).list.get(childPosition).cityNameEn;
                tempCityCode = mList.get(groupPosition).list.get(childPosition).cityCode;
                //结束
                EventBus.getDefault().post(
                        new DataEvent(
                                "",
                                new ResidenceEvent(
                                        tempCountryId,
                                        tempCountryEn,
                                        tempProvinceCode,
                                        tempProvinceEn,
                                        tempCityCode,
                                        tempCityEn
                                )
                        )
                );
                finish();
            }
        });
        elvResidenceList.setAdapter(residenceAdapter);
        for (int i = 0; i < mList.size(); i++) {
            elvResidenceList.expandGroup(i);
        }
        elvResidenceList.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView parent, View v, int groupPosition, long id) {
                return true;
            }
        });
        //大写字母
        WrapContentLinearLayoutManager linearLayoutManager = new WrapContentLinearLayoutManager(this);
        linearLayoutManager.setOrientation(OrientationHelper.VERTICAL);
        rcyBigLetter.setLayoutManager(linearLayoutManager);
        bigLetterAdapter = new BigLetterAdapter(this, mList);
        rcyBigLetter.setAdapter(bigLetterAdapter);
        bigLetterAdapter.setOnItemClickListener(new BigLetterAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {
                elvResidenceList.setSelectionFromTop(elvResidenceList.getFlatListPosition(ExpandableListView.getPackedPositionForGroup(position)), 0);
                String letterName = list.get(position).lettername;
                bigLetterAdapter.showSelectedName(letterName);
                filterClick = true;
            }
        });

        elvResidenceList.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {

            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                long packedPosition = elvResidenceList.getExpandableListPosition(firstVisibleItem);
                int positionType = ExpandableListView.getPackedPositionType(packedPosition);
                if (positionType != ExpandableListView.PACKED_POSITION_TYPE_NULL) {
                    int groupPosition = ExpandableListView.getPackedPositionGroup(packedPosition);
                    if (filterClick) {
                        filterClick = false;
                    } else {   //透過點擊選擇快捷字母  事後滑動到該字母頁面後就不必再重設定點擊的快捷字母
                        String letterName = list.get(groupPosition).lettername;
                        bigLetterAdapter.showSelectedName(letterName);
                    }
                }
            }
        });
    }

    @Override
    public void resultResidence(List<ResidenceObj> list) {

    }

    @Override
    public void resultProvince(List<ResidenceObj> list) {

    }

    @Override
    public void resultCity(List<ResidenceObj> list) {
        if (list.size() != 0) {//有数据
            //遍历数据
            final List<ResidenceObjList> objList = new ArrayList<>();//总数据
            for (int i = 0; i < list.size(); i++) {
                for (int j = i; j < list.get(i).list.size(); j++) {
                    objList.add(list.get(i).list.get(j));
                }
            }
            searchList.clear();
            searchList.addAll(objList);
            residenceResultAdapter.notifyDataSetChanged();
        } else {//无数据
            searchList.clear();
            residenceResultAdapter.notifyDataSetChanged();
        }
    }

}
