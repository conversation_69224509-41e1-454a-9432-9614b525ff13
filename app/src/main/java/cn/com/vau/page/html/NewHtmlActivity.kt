package cn.com.vau.page.html

import android.content.*
import android.net.Uri
import android.os.*
import android.view.*
import android.webkit.*
import android.widget.ProgressBar
import androidx.activity.*
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.DrawableRes
import androidx.core.view.*
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.utils.OpenUploadToH5Util
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.common.view.pdfview.PDFView
import cn.com.vau.databinding.ActivityHtmlNewBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.util.*
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.widget.WebViewInitializer
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import cn.com.vau.util.widget.webview.preload.PreloadWebViewFactory
import com.google.gson.JsonObject
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.*
import org.json.JSONObject

/**
 * Filename: NewHtmlActivity
 * Author: GG
 * Date: 2025/1/8
 * Description: 新的 html 页面 ， 目前只有noTitle的url使用这个页面
 */
class NewHtmlActivity() : BaseMvvmBindingActivity<ActivityHtmlNewBinding>(), WebViewInitializer {

    override val mViewModel by viewModels<NewHtmlViewModel>()

    override val mWebView by lazy { PreloadWebViewFactory.getInstance().acquire(this) }
    override val sensorsHelper = HtmlSensorsHelper()

    override var videoView: View? = null

    override var uploadMessageAboveL: ValueCallback<Array<Uri>>? = null
    override val mWebViewContainer by lazy { mBinding.mWebViewContainer }
    override val pdfView: PDFView by lazy { mBinding.pdfView }
    override val progressBar: ProgressBar by lazy { mBinding.progressBar }
    override val loadingDialog: CommonProcessDialog by lazy { CommonProcessDialog(this) }

    override val openUploadToH5Util: OpenUploadToH5Util = OpenUploadToH5Util(this)

    override val imageChooserLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (null == uploadMessageAboveL) {
                onActivityResultAboveL(null)
                return@registerForActivityResult
            }

            // 在这里处理回调结果
            if (result.resultCode == RESULT_OK) {
                onActivityResultAboveL(result.data)
            } else {
                onActivityResultAboveL(null)
            }
        }

    override fun useEventBus(): Boolean = true

    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        intent.extras?.keySet()?.filter { key ->
            // 过滤 title 和 url ，是否展示客服按钮以外的 键值对 存到map中
            key != KEY_TITLE && key != KEY_URL && key != KEY_IS_DEPOSIT3
        }?.forEach { key ->
            mViewModel.dataMap[key] = intent.getStringExtra(key).ifNull()
        }
        mViewModel.isDeposit3 = intent.getBooleanExtra(KEY_IS_DEPOSIT3, false)
        mViewModel.appTitle = intent.getStringExtra(KEY_TITLE).ifNull()
        mViewModel.title = mViewModel.appTitle
        mViewModel.oldTitle = mViewModel.appTitle
        mViewModel.url = intent.getStringExtra(KEY_URL).ifNull()
    }

    override fun initView() {
        configHeaderBar()
        setWebView(this)

        val callback = onBackPressedDispatcher.addCallback(this) {
            back()
        }
        callback.isEnabled = true
    }

    /**
     * 设置 headerBar的相关功能和显示
     */
    private fun configHeaderBar() {
        // 设置title bar 基础显示
        mBinding.mHeaderBar
            .setStartBackIconClickListener {
                back()
            }
        if (!mViewModel.hideDefaultTitleUrl())
            mBinding.mHeaderBar.setTitleText(mViewModel.title)
        mBinding.progressBar.isVisible = mViewModel.isNeedProgress()

        if (mViewModel.isDeposit3) {
            listOf(Constants.ICON_TYPE_CLOSE, Constants.ICON_TYPE_CUSTOMER).forEachIndexed { index, item ->
                addTopIcon(item, index)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v5_H5_All_Finish)
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v5_H5_Create_LoadUrl)
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        super.initData()
        LogUtil.w("url ----> ${mViewModel.url}")
        if (mViewModel.isPdfUrl()) {
            mViewModel.isPDFOnly = true
            loadPdfFile(mViewModel.url)
        } else {
            processOfflineUrl(mWebView, mViewModel.url)
            PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v5_H5_Create_LoadUrl)
            PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v5_H5_LoadUrl_Finish)
        }
        // h5流程神策埋点 -> 打开页面
        sensorsHelper.init(mViewModel.url)
        sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.OPEN_PAGE, url = mViewModel.url)
    }

    override fun createObserver() {
        super.createObserver()

        lifecycleScope.launch {
            mViewModel.eventFlow.collectLatest { event ->
                if (event !is DataEvent) return@collectLatest
                when (event.tag) {
                    NoticeConstants.JS.HTML_TITLE -> {
                        if (!mViewModel.isUseAppTitle) {
                            mViewModel.oldTitle = mViewModel.title
                            mViewModel.title = event.data.toString()
                            mBinding.mHeaderBar.setTitleText(mViewModel.title)
                        }
                    }

                    NoticeConstants.JS.HTML_IS_USE_APP_TITLE -> {
                        mViewModel.isUseAppTitle = event.data == true
                        if (mViewModel.isUseAppTitle) {
                            mViewModel.title = mViewModel.appTitle
                            mBinding.mHeaderBar.setTitleText(mViewModel.title)
                        }
                    }

                    NoticeConstants.JS.HTML_BACK -> {
                        back()
                    }

                    NoticeConstants.JS.HTML_IS_CAN_BACK -> {
                        mViewModel.htmlIsCanBack = event.data == true
                    }

                    NoticeConstants.JS.HTML_ICON_LIST -> {
                        val list = event.data as? List<*>
//                        LogUtil.w("HTML_ICON_LIST ----> $list")
                        // 如果 isUserH5Icon 为 true 说明是使用过h5传过来的icon列表，那么再走这个方法就 先重置一下 headerBar 右边的图标 防止出现 前一个页面传来三个，下一个页面传了两个 导致 右边的图标错乱
                        if (mViewModel.isUserH5Icon == true) {
                            clearIcon()
                        }
                        if (list.isNullOrEmpty()) return@collectLatest
                        mViewModel.isUserH5Icon = true
                        list.forEachIndexed { index, item ->
                            if (item is String) {
                                addTopIcon(item, index)
                            }
                        }
                    }

                    NoticeConstants.JS.HTML_CONTROL_LOADING -> {
                        if (event.data == true) {
                            showLoadDialog()
                            if (mViewModel.isNeedProgress()) {
                                mBinding.progressBar.isVisible = true
                            }
                        } else {
                            hideLoadDialog()
                            if (mViewModel.isNeedProgress()) {
                                mBinding.progressBar.isVisible = false
                            }
                        }
                    }

                    NoticeConstants.JS.HTML_SHOW_CAPTCHA -> { // kyc h5触发人机验证
                        if (event.data !is String) return@collectLatest
                        showH5Captcha(event.data)
                    }

                    NoticeConstants.JS.HTML_IS_CAN_CLOSE -> { // kyc h5触发人机验证
                        mViewModel.htmlIsCanClose = event.data == true
                    }

                    NoticeConstants.JS.HTML_IS_HIDE_BACK -> { // kyc h5触发人机验证
                        if (event.data == true && mBinding.mHeaderBar.getStartBackIconVisible()) {
                            mBinding.mHeaderBar.setStartBackIconVisible(false)
                        } else if (event.data == false && !mBinding.mHeaderBar.getStartBackIconVisible()) {
                            mBinding.mHeaderBar.setStartBackIconVisible(true)
                        }
                    }
                }
            }
        }
    }

    /**
     * 重置 headerBar 右边的图标的 状态
     */
    private fun clearIcon() {
        configEndIcon(null, null)
        configEndIcon1(null, null)
        configEndIcon2(null, null)
        configEndIcon3(null, null)
    }

    /**
     * 按照从右到左的顺序 设置 icon 图标 以及 点击事件
     */
    private fun addTopIcon(type: String, index: Int) {
        when (index) {
            0 -> configEndIcon(getIcon(type), getIconListener(type))

            1 -> configEndIcon1(getIcon(type), getIconListener(type))

            2 -> configEndIcon2(getIcon(type), getIconListener(type))

            3 -> configEndIcon3(getIcon(type), getIconListener(type))
        }
    }

    private fun getIcon(type: String): Int {
        return when (type) {
            Constants.ICON_TYPE_CLOSE -> R.drawable.draw_bitmap2_close16x16_c731e1e1e_c61ffffff
            Constants.ICON_TYPE_MENU -> AttrResourceUtil.getDrawable(this, R.attr.icon1Menu)
            Constants.ICON_TYPE_CUSTOMER -> AttrResourceUtil.getDrawable(this, R.attr.icon1Cs)
            Constants.ICON_TYPE_SHARE -> AttrResourceUtil.getDrawable(this, R.attr.icon1Share)
            Constants.ICON_TYPE_QUESTION -> AttrResourceUtil.getDrawable(this, R.attr.icon1Faq)
            Constants.ICON_TYPE_HISTORY -> AttrResourceUtil.getDrawable(this, R.attr.icon1WalletHistory)
            else -> 0
        }
    }

    /**
     * 获取图标点击事件
     */
    private fun getIconListener(type: String): (() -> Unit)? {
        return when (type) {
            Constants.ICON_TYPE_CLOSE -> {
                {
                    if (mViewModel.isDeposit3) {
                        showDeposi3Dialog()
                    } else if (mViewModel.htmlIsCanClose) {
                        clickClose()
                    } else {
                        finish()
                    }
                }
            }

            Constants.ICON_TYPE_MENU -> {
                {
                    clickMenu()
                }
            }

            Constants.ICON_TYPE_CUSTOMER -> {
                {
                    // 跳转到帮助中心页面
                    openActivity(HelpCenterActivity::class.java)
                }
            }

            Constants.ICON_TYPE_SHARE -> {
                {
                    clickShare()
                }
            }

            Constants.ICON_TYPE_QUESTION -> {
                {
                    clickFaq()
                }
            }

            Constants.ICON_TYPE_HISTORY -> {
                {
                    clickWalletHistory()
                }
            }

            else -> null
        }
    }

    /**
     * 展示三方入金的退出挽留弹窗
     */
    private fun showDeposi3Dialog() {
        CenterActionWithIconDialog.Builder(this)
            .setTitle(getString(R.string.are_you_sure_page))
            .setContent(getString(R.string.you_wont_be_funds))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.stay_on_this_page))
            .setLinkText(getString(R.string.leave_this_page))
            .setOnSingleButtonListener {
                EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_NOTICE_SENSORS_TRACK, data = mapOf("button_name" to "Stay on this page")))
            }
            .setLinkListener {
                finish()
                EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_NOTICE_SENSORS_TRACK, data = mapOf("button_name" to "Leave this page")))
                EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_NOTICE_DEPOSIT_REFRESH))
            }
            .build()
            .showDialog()
    }

    /**
     * 设置headerBar的 end 按钮图标和点击事件
     * @param resId 图标资源id
     * @param listener 点击事件
     */
    private fun configEndIcon(@DrawableRes resId: Int?, listener: (() -> Unit)? = null) {
        if (resId == null) {
            mBinding.mHeaderBar.setEndIconVisible(false)
            return
        }
        mBinding.mHeaderBar
            .setEndIconResource(resId)
            .setEndIconClickListener(listener)
            .setEndIconVisible(true)
    }

    private fun configEndIcon1(@DrawableRes resId: Int?, listener: (() -> Unit)? = null) {
        if (resId == null) {
            mBinding.mHeaderBar.setEndIcon1Visible(false)
            return
        }
        mBinding.mHeaderBar
            .setEndIcon1Resource(resId)
            .setEndIcon1ClickListener(listener)
            .setEndIcon1Visible(true)
    }

    private fun configEndIcon2(@DrawableRes resId: Int?, listener: (() -> Unit)? = null) {
        if (resId == null) {
            mBinding.mHeaderBar.setEndIcon2Visible(false)
            return
        }
        mBinding.mHeaderBar
            .setEndIcon2Resource(resId)
            .setEndIcon2ClickListener(listener)
            .setEndIcon2Visible(true)
    }

    private fun configEndIcon3(@DrawableRes resId: Int?, listener: (() -> Unit)? = null) {
        if (resId == null) {
            mBinding.mHeaderBar.setEndIcon3Visible(false)
            return
        }
        mBinding.mHeaderBar
            .setEndIcon3Resource(resId)
            .setEndIcon3ClickListener(listener)
            .setEndIcon3Visible(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(data: DataEvent) {
        when (data.tag) {
            // 设置资金安全密码成功的通知
            NoticeConstants.SECURITY_PWD_ADD_SUCCESS -> {
                setSecurityPwdSuccess()
            }

            NoticeConstants.HTML_JUMP_OPEN_ACCOUNT_BACK -> {
                loadVFXMethod("initWallet")
            }

            NoticeConstants.JS.HTML_NOTICE_DEPOSIT_REFRESH -> {
                loadVFXMethod("closeLastWeb")
            }

            NoticeConstants.JS.HTML_NOTICE_SENSORS_TRACK -> {
                if (data.data is Map<*, *>) {
                    loadVFXMethod("sensorsTrack", data.data as Map<String, String>)
                }
            }
        }
    }

    /**
     * 点击了钱包历史按钮
     */
    private fun clickWalletHistory() {
        loadVFXMethod("toWalletHistory")
    }

    /**
     * 点击了常见问题按钮
     */
    private fun clickFaq() {
        loadVFXMethod("toQuestions")
    }

    /**
     * 点击了x按钮
     */
    private fun clickClose() {
        loadVFXMethod("clickAppClose")
    }

    /**
     * 点击了分享按钮
     */
    private fun clickShare() {
        loadVFXMethod("appOnShare")
    }

    /**
     * 点击了菜单按钮
     */
    private fun clickMenu() {
        loadVFXMethod("appOnMenu")
    }

    /**
     * 设置资金安全密码成功
     */
    private fun setSecurityPwdSuccess() {
        loadVFXMethod("setFundPassword", mutableMapOf("isSetted" to true))
    }

    override fun back() {
        LogUtil.w("onBackPressedDispatcher ---> ${AppUtil.getSystemVersion()}")

        KeyboardUtil.hideSoftInput(this)

        LogUtil.w("mViewModel.htmlIsCanBack ----> ${mViewModel.htmlIsCanBack}")
        LogUtil.w("mWebView?.canGoBack()----> ${mWebView?.canGoBack()}")

        // 如果是PDF页面，先执行隐藏PDF组件
        if (mBinding.pdfView.visibility == View.VISIBLE && !mViewModel.isPDFOnly) {
            mViewModel.title = mViewModel.oldTitle
            mBinding.mHeaderBar.setTitleText(mViewModel.title)
            mBinding.pdfView.visibility = View.GONE
            mBinding.pdfView.recycle()
            mBinding.mWebViewContainer.visibility = View.VISIBLE
            return
        }

        if (mViewModel.htmlIsCanBack) {
            htmlBack()
            return
        }

        // 逐级返回逻辑
        if (mWebView?.canGoBack() == true) {
            mWebView?.settings?.cacheMode = WebSettings.LOAD_NO_CACHE
            mWebView?.goBack()
            return
        }
        // 如果是入金的三方页面，点击返回 到了栈低的话 就显示提醒弹窗
        if (mViewModel.isDeposit3) {
            showDeposi3Dialog()
            return
        }

        finish()
    }

    /**
     * 调用h5的返回方法，控制h5页面回退
     */
    private fun htmlBack() {
        loadVFXMethod("clickNavBack")
    }

    override fun hideLoading() {
        super.hideLoading()
        hideLoadDialog()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onMsgEvent(tag: String) {
        when (tag) {
            // 登陆账户
            NoticeConstants.SWITCH_ACCOUNT -> {
                if (!mViewModel.isListenKycClose)
                    initData()
                // 拦截一次以后清除，功能是只拦截一次
                mViewModel.isListenKycClose = false
            }
            // 开通真实账户 提交
            NoticeConstants.REFRESH_ACCOUNT_MANAGER, NoticeConstants.JS.INTERFACE_104 -> {
                finish()
            }

            NoticeConstants.JS.INTERFACE_50, NoticeConstants.JS.OPEN_ACCOUNT_OPEN_APP -> {
                showLoadDialog()
            }

            "html_dialog_net_finish" -> {
                hideLoadDialog()
            }

            // 通知h5账户类型
            NoticeConstants.NOTICE_H5_ACCOUNT_TYPE -> {
                val json = JsonObject()
                json.addProperty("accountId", UserDataUtil.accountCd())
                json.addProperty("isDemo", UserDataUtil.isDemoAccount())
                mWebView?.loadUrl("javascript:responseCallback(${json})")
            }

            NoticeConstants.NOTICE_H5_KYC_REFRESH -> {
                loadVFXMethod("refreshPageState")
            }

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(data: DataEvent) {
        when (data.tag) {
            // 出金-验证2fa 回传结果
            NoticeConstants.TFA_VERIFY_BACK -> {
                mWebView?.loadUrl("javascript:retrieve2FAVerificationBack()")
            }

            NoticeConstants.TFA_VERIFY_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("code", data.data as String)
                json.addProperty("appVersion", AppUtil.getVersionName())
                json.addProperty("isSuccess", true)
                mWebView?.loadUrl("javascript:retrieve2FAVerificationStatus(${json})")
                //                LogUtil.d("wj", "[出金-验证2fa 回传结果]: $json")
            }

            // 出金-绑定2fa 回传结果
            NoticeConstants.TFA_BIND_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data == true)
                mWebView?.loadUrl("javascript:retrieve2FASettingStatus(${json})")
                //                LogUtil.d("wj", "[出金-绑定2fa 回传结果]: $json")
            }

            // 出金-重置2fa 回传结果
            NoticeConstants.TFA_RESET_SUCCESS -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data == true)
                mWebView?.loadUrl("javascript:retrieve2FAResetStatus(${json})")
                //                LogUtil.d("wj", "[出金-重置2fa 回传结果]: $json")
            }
            // 通知h5人脸识别结果
            NoticeConstants.NOTICE_H5_SUMSUB_RESULT -> {
                val json = JsonObject()
                json.addProperty("isSuccess", data.data as Boolean)
                mWebView?.loadUrl("javascript:retrieveSumsubStatus(${json})")
            }

            NoticeConstants.JS.HTML_KYC_CLOSE -> {
                mViewModel.isListenKycClose = true
                loadVFXMethod("closeKYC", mapOf("isAPP" to (data.data == true)))
            }
            /**
             * 接收通知 调用方法， 通知钱包等页面要跳转 入金页面
             */
            NoticeConstants.WS.SOCKET_KYC_JUMP_DEPOSIT -> {
                loadVFXMethod(data.data.toString())
            }
        }
    }

    /**
     * kyc h5触发人机验证
     */
    private fun showH5Captcha(jsJson: String) {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            val jsonObject = JSONObject(jsJson)
            val map = mutableMapOf<String, Any>()
            map["verifyCode"] = it
            map["smsCodeId"] = jsonObject.optString("smsCodeId")
            map["smsSendType"] = jsonObject.optString("smsSendType")
            loadJSMethod("SMSReceiveCode", map)
        }

        mCaptcha?.validate()
    }

    override fun onDestroy() {
        EventBus.getDefault().post(DataEvent(NoticeConstants.JS.HTML_KYC_CLOSE, false))
        PreloadWebViewFactory.getInstance().release(mWebView)
        EventBus.getDefault().unregister(this)
        super.onDestroy()
        // h5流程神策埋点 -> 关闭页面
        sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.CLOSE_PAGE, url = mViewModel.url)
    }

    companion object {
        var isFirst = true
        const val KEY_URL = "vau_html_url"
        const val KEY_TITLE = "vau_html_title"
        private const val KEY_IS_DEPOSIT3 = "KEY_IS_DEPOSIT3"

        fun openActivity(context: Context?, url: String, title: String? = null, dataMap: Map<String, Any>? = null, isDeposit3: Boolean = false, endFlow: (() -> Unit)? = null) {
            // 如果是钱包的url，并且没有登录，跳转到登录页面
            if (NewHtmlViewModel.isNeedLogin(url) && !UserDataUtil.isLogin()) {
                context?.startActivity(Intent(context, LoginActivity::class.java).setFlags(Intent.FLAG_ACTIVITY_NEW_TASK))
                return
            }

            val intent = Intent(context, NewHtmlActivity::class.java)
            intent.putExtra(KEY_URL, url)
            intent.putExtra(KEY_TITLE, title)
            intent.putExtra(KEY_IS_DEPOSIT3, isDeposit3)
            dataMap?.forEach {
                if (isBasicType(it.value))
                    intent.putExtra(it.key, it.value.toString())
            }
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context?.startActivity(intent)
            // 执行跳转以后的操作
            endFlow?.invoke()
        }

        private fun isBasicType(value: Any?): Boolean {
            return when (value) {
                is Number, is CharSequence, is Boolean, is Char -> true
                else -> false
            }
        }
    }
}