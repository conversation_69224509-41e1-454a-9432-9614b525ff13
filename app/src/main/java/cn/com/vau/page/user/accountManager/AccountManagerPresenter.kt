package cn.com.vau.page.user.accountManager

import android.text.TextUtils
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.ws.StWsManager
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.data.*
import cn.com.vau.data.account.*
import cn.com.vau.data.init.TradeAccountLoginBean
import cn.com.vau.util.*
import cn.com.vau.util.opt.AbUtil
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

/**
 * Created by Haipeng on 2017/10/12.
 */
class AccountManagerPresenter : AccountManagerContract.Presenter() {

    var demoListData = mutableListOf<AccountTradeBean>()
    var liveListData = mutableListOf<AccountTradeBean>()
    var copyTradingListData = mutableListOf<AccountTradeBean>()
    var listTradingAccount = mutableListOf<AccountTradeBean>()  // 所有账户集合
    var isAppraisal = false // 是否通过测评 (false:未通过 true:已通过)
    var currentAccountCd: String? = null
    var currentAccountDealType: String? = null
    var stToken: String? = null
    var needShowOpenDemoBtn: Boolean = false
    var needShowBindBtn: Boolean = false
    var needShowNewLiveBtn: Boolean = false
    var needShowOpenStBtn: Boolean = false
    var isShowStAccount: Boolean? = null

    var openAccountData: AccountOpeningGuideObj? = null
    var switchDemoAccountList: MutableList<DemoAccountDetail>? = null

    // 账户列表1  queryAccountList
    override fun getAccountFirst() {

        mView?.showNetDialog()

        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.getAccountFirst(map, object : BaseObserver<AccountListFirstBean>() {

            override fun onNext(info: AccountListFirstBean) {

                mView?.hideNetDialog()

                if (info.resultCode != "V00000") {
                    ToastUtil.showToast(info.msgInfo)
                    mView?.initRetryView(true)
                    return
                }

                dealFirstAccountData(info.data?.obj)
                // 非KYC流程下检查账号开户状态
                if (SpManager.isNeedKYC().not()) {
                    accountOpeningGuide()
                }

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.initRetryView(true)
                mView?.hideNetDialog()
            }
        })

    }

    override fun queryDemoAccountList(openPop: Boolean) {
        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryDemoAccountList(
            map,
            object : BaseObserver<DemoAccountListData>() {
                override fun onNext(t: DemoAccountListData) {
                    if (t.resultCode != "V00000") {
                        ToastUtil.showToast(t.msgInfo)
                        return
                    }
                    t.data?.obj?.let {
                        switchDemoAccountList = mutableListOf<DemoAccountDetail>().apply { addAll(it) }
                        if (openPop) {
                            mView?.popupWindow()
                        }
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager?.add(d)
                }

            })
    }

    // 开户引导 accountOpeningGuide
    override fun accountOpeningGuide() {

        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken()

        mModel?.accountOpeningGuide(map, object : BaseObserver<AccountOpeningGuideBean>() {

            override fun onNext(info: AccountOpeningGuideBean) {

                if (info.resultCode != "V00000") {
                    ToastUtil.showToast(info.msgInfo)
                    return
                }

                val type = info.data?.obj?.type

                // 提示类型（1：开户信息未完善提示，2：不显示提示，3：开户信息有误提示，4：身份证明有误提示，5：地址证明有误提示，6：弹出登录，无法登录APP）
                if (type == 1 || type == 3 || type == 4) {
                    openAccountData = info.data?.obj
                    mView?.syncLiveCard()
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

    override fun queryAccountEquitList() {
        mModel?.queryAccountEquitList(
            UserDataUtil.loginToken(),
            object : BaseObserver<AccountsEquityData>() {
                override fun onNext(dataBean: AccountsEquityData?) {
                    if ("V00000" != dataBean?.resultCode) {
                        initAccountsEquityData(null)
                        return
                    }
                    initAccountsEquityData(dataBean.data?.obj)
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    initAccountsEquityData(null)
                }

            })
    }

    private fun initAccountsEquityData(objData: AccountsEquityObj?) {
        if (objData == null) {
            demoListData.forEach {
                if (it is AccountTradeBean) {
                    it.equitySuccess = "2"
                }
            }
            liveListData.forEach {
                if (it is AccountTradeBean) {
                    it.equitySuccess = "2"
                }
            }
            copyTradingListData.forEach {
                if (it is AccountTradeBean) {
                    it.equitySuccess = "2"
                }
            }
            mView?.freshAllAdapter()
            return
        }


        copyTradingListData.forEach {
            val stDetailData = objData.listStAccount?.firstOrNull { tradIt ->
                TextUtils.equals(it.acountCd, tradIt.serverAccountId)
            }

            // 这里重新赋值 会导致杠杆数据有问题 显示 null:1
//            it.stDetailData = StAccMarginBean.Data()
            it.stDetailData.equity = stDetailData?.equity ?: ""
            it.stDetailData.followEquity = stDetailData?.followEquity ?: ""
            it.stDetailData.accountId = stDetailData?.accountId ?: ""
            it.stDetailData.currencyType = stDetailData?.currencyType ?: ""
            it.equitySuccess = if (stDetailData == null) "2" else "1"
            it.isArchive = stDetailData?.isArchive.ifNull()
        }
        demoListData.forEach {
            val detailData = objData.listTradingAccount?.firstOrNull { tradIt ->
                TextUtils.equals(it.acountCd, tradIt.acountCd)
            }
            if (it.detailData == null)
                it.detailData = AccountTradeSecondDetail()
            it.detailData?.equity = detailData?.equity
            it.detailData?.currencyType = detailData?.currencyType
            it.detailData?.readyOnlyAccount = detailData?.readyOnlyAccount == true
            it.equitySuccess = if (detailData == null) "2" else "1"
            it.isArchive = detailData?.isArchive.ifNull()
        }
        liveListData.forEach {
            val detailData = objData.listTradingAccount?.firstOrNull { tradIt ->
                TextUtils.equals(it.acountCd, tradIt.acountCd)
            }
            if (it.detailData == null)
                it.detailData = AccountTradeSecondDetail()
            it.detailData?.equity = detailData?.equity
            it.detailData?.currencyType = detailData?.currencyType
            it.detailData?.readyOnlyAccount = detailData?.readyOnlyAccount == true
            it.equitySuccess = if (detailData == null) "2" else "1"
            it.isArchive = detailData?.isArchive.ifNull()
        }
        mView?.freshAllAdapter()
    }

    override fun queryAccountInfo(adapterType: Int, position: Int, accountInfo: AccountTradeBean?) {
        if (accountInfo == null) {
            return
        }
        mView?.showNetDialog()
        accountInfo.secondSuccess = "0"
        val paramMap = hashMapOf<String, Any>()
        paramMap["token"] = UserDataUtil.loginToken()
        paramMap["accountId"] = accountInfo.acountCd ?: ""
        paramMap["serverId"] = accountInfo.accountServer ?: ""
        mModel?.queryAccountInfo(paramMap, object : BaseObserver<AloneAccountInfoData>() {
            override fun onNext(dataBean: AloneAccountInfoData?) {
                mView?.hideNetDialog()
                if ("V00000" != dataBean?.resultCode) {
                    accountInfo.secondSuccess = "2"
                    when (adapterType) {
                        1 -> mView?.freshDemoAdapter(position)
                        2 -> mView?.freshLiveAdapter(position)
                        3 -> mView?.freshCopyTradingAdapter(position)
                    }
                    return
                }

                val objData = dataBean.data?.obj

                if ("5" == accountInfo.accountDealType) {
                    accountInfo.stDetailData.nickName = objData?.nickName ?: ""
                    accountInfo.stDetailData.followProfit = objData?.followProfit ?: ""
                    accountInfo.stDetailData.returnRate = objData?.returnRate ?: ""
                    accountInfo.stDetailData.followFloatingPl = objData?.followFloatingPl
                    accountInfo.stDetailData.followBalance = objData?.followBalance
                    accountInfo.stDetailData.followFreeMargin = objData?.followFreeMargin
                    accountInfo.stDetailData.followMarginUsed = objData?.followMarginUsed
                    accountInfo.stDetailData.totalHistoryProfit = objData?.totalHistoryProfit
                    accountInfo.stDetailData.wholeHistoryProfit = objData?.wholeHistoryProfit ?: 0.0
                    accountInfo.stDetailData.profit = objData?.profit
                    accountInfo.stDetailData.marginLevel = objData?.marginLevel
                    accountInfo.stDetailData.credit = objData?.credit
                    accountInfo.stDetailData.floatingPl = objData?.floatingPl
                    accountInfo.stDetailData.balance = objData?.balance
                    accountInfo.stDetailData.freeMargin = objData?.freeMargin
                    accountInfo.stDetailData.masterPortfolioId = objData?.masterPortfolioId
                    accountInfo.stDetailData.marginUsed = objData?.marginUsed
                    accountInfo.stDetailData.leverage = objData?.leverage
                    accountInfo.stDetailData.lastLogin = objData?.lastLogin ?: 0L
                    accountInfo.stDetailData.lastLoginDate = objData?.lastLoginDate ?: ""
                    accountInfo.stDetailData.serverId = objData?.serverId
                    accountInfo.stDetailData.createTime = objData?.createTime ?: 0L
                    accountInfo.stDetailData.isSignal = objData?.isSignal ?: false
                    accountInfo.stDetailData.accountTypeName = objData?.accountTypeName ?: ""
                } else {
                    if (accountInfo.detailData == null)
                        accountInfo.detailData = AccountTradeSecondDetail()
                    accountInfo.detailData?.profit = objData?.profit ?: ""
                    accountInfo.detailData?.guarantee = objData?.guarantee ?: ""
                    accountInfo.detailData?.profitRate = objData?.profitRate ?: ""
                    accountInfo.detailData?.payRate = objData?.payRate ?: ""
                    accountInfo.detailData?.lastLoginDate = objData?.lastLoginDate ?: ""
                    accountInfo.detailData?.isArchive = objData?.isArchive ?: false
                    accountInfo.detailData?.leverage = objData?.leverage ?: ""
                    accountInfo.detailData?.ecnUpgrade = objData?.ecnUpgrade ?: false
                    accountInfo.detailData?.accountTypeName = objData?.accountTypeName ?: ""
                }
                accountInfo.secondSuccess = "1"

                when (adapterType) {
                    1 -> mView?.freshDemoAdapter(position)
                    2 -> mView?.freshLiveAdapter(position)
                    3 -> mView?.freshCopyTradingAdapter(position)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                accountInfo.secondSuccess = "2"
                when (adapterType) {
                    1 -> mView?.freshDemoAdapter(position)
                    2 -> mView?.freshLiveAdapter(position)
                    3 -> mView?.freshCopyTradingAdapter(position)
                }
            }
        })
    }

    // 处理第一次返回账户数据
    override fun dealFirstAccountData(data: AccountListFirstObj?) {

        demoListData.clear()
        liveListData.clear()
        copyTradingListData.clear()
        listTradingAccount.clear()

        currentAccountCd = UserDataUtil.accountCd()
        // 获取并设置Demo数量
        mView?.setDemoAccountNum(
            data?.demoAccountNum.toIntCatching()
        )

        if (data?.listTradingAccount.isNullOrEmpty().not()) {
            listTradingAccount.addAll(data.listTradingAccount ?: emptyList())
            mView?.initRetryView(false)
            data?.listTradingAccount?.forEach {
                when (it.accountDealType) {
                    "3" -> {
                        demoListData.add(it)
                    }

                    "5" -> {
                        //test
//                        it.isArchive = true
                        copyTradingListData.add(it)
                    }

                    "2" -> {}
                    else -> {
                        liveListData.add(it)
                    }
                }
            }
        }

        // 是否通过测评
        isAppraisal = data?.isAppraisal ?: false

        val havingLiveAccount = data?.listTradingAccount?.any {
            it.accountDealType == "1"
        }

        // 是否开通真实账户
        UserDataUtil.setOpenLiveAccountState(if (havingLiveAccount == true) "1" else "0")
        UserDataUtil.setEmail(data?.email ?: "")

        needShowOpenDemoBtn = data?.havingDemoAccount == false
        // 當前返回需bindEmail 則顯示綁定帳號按鈕
        needShowBindBtn = data?.bindingEmail == true
        // 當前有交易帳號時 則需顯示NewLiveAccount開通另一個交易帳號按鈕
        needShowNewLiveBtn = data?.havingAccount == true
        // 當前無跟單帳號 & 無審核中跟單帳號時 需顯示開通跟單帳號按鈕
        needShowOpenStBtn =
            (data?.havingStAccount == false && data.havingAuditWritesStAccount == false)
        // 是否展示跟单账户（包括空布局）
        isShowStAccount = data?.showStAccount

        UserDataUtil.setOpenAccountType(
            if (havingLiveAccount == false && data.havingStAccount == false)
                1
            else if (havingLiveAccount == true && data.havingStAccount == false)
                2
            else if (havingLiveAccount == false && data.havingStAccount == true)
                3
            else if (havingLiveAccount == true && data.havingStAccount == true)
                4
            else
                0
        )

        mView?.setAdapterData()
        // 没有模拟账户，显示添加模拟账户按钮, 显示綁訂邮箱,  显示New Live Account,  显示開通跟單帳號
//        mView?.refreshBottomButton()
        queryAccountEquitList()
    }

    override fun getCopyTradingDefaultImg() {
        mModel?.getCopyTradingDefaultImg(
            UserDataUtil.loginToken(),
            object : BaseObserver<DataObjStringBean>() {
                override fun onNext(dataBean: DataObjStringBean?) {
                    if ("V00000" != dataBean?.resultCode) {
                        ToastUtil.showToast(dataBean?.msgInfo)
                        return
                    }
                    val oldPath = SpManager.getAccountManageCopyTradingUrl("")
                    val newPath = dataBean.data?.obj.ifNull()
                    if (oldPath != newPath) {
                        SpManager.putAccountManageCopyTradingUrl(newPath)
                        mView?.refreshCopyTradingImg()
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
    }

    /**
     * 升级账户类型
     */
    override fun accountUpgradeGroup(clickPos: Int, mt4Account: String?) {
        mView?.showNetDialog()
        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken() //用户token
        map["mt4Account"] = mt4Account ?: "" //用户账户号
        mModel?.accountUpgradeGroup(map, object : BaseObserver<DataObjBooleanBean>() {
            override fun onNext(bean: DataObjBooleanBean?) {
                mView?.hideNetDialog()
                if (bean?.resultCode == "V00000") {
                    mView?.accountUpgradeIsSuccess(bean.data?.obj == true, clickPos)
                } else {
                    ToastUtil.showToast(bean?.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    // 正常账户
    override fun selectCommonAccount(data: AccountTradeBean) {
        // 交易账户 && 未通过评测
        if (TextUtils.equals(data.accountDealType, "1") && !isAppraisal) {
            mView?.hideNetDialog()
            mView?.showQuestionDialog()
            return
        }
        bindMT4Login(data)
    }

    override fun synDemo(data: AccountTradeBean) {
        mView?.showNetDialog()
        val map = hashMapOf<String, Any>()
        map["token"] = UserDataUtil.loginToken()
        map["accountId"] = data.acountCd.ifNull()
        mModel?.synDemo(map, object : BaseObserver<BaseBean>() {
            override fun onNext(result: BaseBean) {

                if (result.resultCode != "V00000") {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(result.msgInfo)
                    return
                }

                selectCommonAccount(data)

                LogEventUtil.setLogEvent(
                    BuryPointConstant.V330.PROFILE_ACC_MGMT_DEMO_SWITCH_BUTTON_CLICK,
                    hashMapOf(
                        "Previous_Demo" to demoListData.elementAtOrNull(0)?.acountCd.ifNull(),
                        "New_Demo" to data.acountCd.ifNull()
                    )
                )

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

    // st账户
    override fun selectSocialTradingAccount(data: AccountTradeBean) {
        // 问卷
        if (!isAppraisal) {
            mView?.showQuestionDialog()
            return
        }
        stAccountLogin(data)
    }

    // mt4登陆 trade/account/login
    private fun bindMT4Login(data: AccountTradeBean) {

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", data.acountCd ?: "")
        jsonObject.addProperty("serverId", data.accountServer ?: "")
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())
        val requestBody =
            jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())

        mView?.showNetDialog()

        mModel?.tradeAccountLogin(requestBody, object : BaseObserver<TradeAccountLoginBean>() {
            override fun onNext(baseBean: TradeAccountLoginBean) {

                mView?.hideNetDialog()

                if (baseBean.code == "********") { // 过期
                    ToastUtil.showToast(context.getString(R.string.this_account_has_please_management))
                    return
                }
                if (baseBean.code == "********") { // 真实账户归档
                    ToastUtil.showToast(context.getString(R.string.this_account_has_been_archived_please_customer_service))
                    return
                }

                if (baseBean.code != "200") {
                    ToastUtil.showToast(baseBean.info)
                    return
                }
                saveAccountData(baseBean, data)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    private fun saveAccountData(mt4Bean: TradeAccountLoginBean, data: AccountTradeBean) {

        UserDataUtil.setAccountCd(data.acountCd ?: "")
        UserDataUtil.setTradeToken(mt4Bean.data?.token ?: "")
        UserDataUtil.setServerId(data.accountServer ?: "-1")
        UserDataUtil.setAccountDealType(data.accountDealType ?: "")
        UserDataUtil.setMt4State(
            when (data.accountDealType) {
                "0" -> "1"
                "1" -> "2"
                "2" -> "4"
                "3" -> "3"
                else -> "3"
            }
        )
        UserDataUtil.setCurrencyType(data.detailData?.currencyType ?: "")
        UserDataUtil.setAccountType(data.accountType ?: "")
        UserDataUtil.setPlatForm(data.platform ?: "")
        UserDataUtil.setReadOnly(data.detailData?.readyOnlyAccount)
        EventBus.getDefault().post(NoticeConstants.SWITCH_ACCOUNT)

        if (currentAccountDealType == "5") {    // 跟单账户
            StWsManager.getInstance().breakSocket()
            HttpUrl.applyBaseUrl()
            mView?.reStartApp()
            mView?.ac?.finish()
        } else {
            ActivityManagerUtil.getInstance().finishOtherActivities(MainActivity::class.java)
        }
    }

    // account/login  获取 st 账户 token
    override fun stAccountLogin(data: AccountTradeBean) {
        mView?.showNetDialog()
        val jsonObject = JsonObject()
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        jsonObject.addProperty("accountId", data.mtsAccount)
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        mModel?.stAccountLogin(requestBody, object : BaseObserver<StAccountLoginBean>() {
            override fun onNext(stAccountLogin: StAccountLoginBean) {

                if (stAccountLogin.code != "200") {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(stAccountLogin.msg)
                    return
                }
                /**每呼叫一次跟單login, 接口都會刷新Token
                 *因此必須每次都刷新數據庫stToken 避免其他接口使用舊的Token
                 */
                val stAccountToken = stAccountLogin.data?.token ?: ""
                UserDataUtil.setStToken(stAccountToken)
                UserDataUtil.setStUserId(stAccountLogin.data?.stUserId ?: "")
                //test
//                stAccountLogin.data?.area = "GS"
                UserDataUtil.setStUserArea(stAccountLogin.data?.area ?: "")

                getStAccountInfo(data, stAccountToken)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    // account/acc-margin 账户信息
    override fun getStAccountInfo(data: AccountTradeBean, stAccountToken: String) {
        mModel?.stAccountAccMargin(stAccountToken, object : BaseObserver<StAccMarginBean>() {
            override fun onNext(stAccMarginData: StAccMarginBean) {
                if (stAccMarginData.code != "200") {
                    ToastUtil.showToast(stAccMarginData.msg)
                    mView?.hideNetDialog()
                    return
                }
                saveStAccountData(data, stAccMarginData)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    private fun saveStAccountData(data: AccountTradeBean, stAccMarginData: StAccMarginBean) {
        val stData = stAccMarginData.data?.firstOrNull { it.accountId == data.mtsAccount }
        if (stData == null) {
            ToastUtil.showToast(stAccMarginData.msg)
            mView?.hideNetDialog()
            return
        }

        UserDataUtil.setStAccountId(stData.accountId ?: "")
        UserDataUtil.setMasterPortfolioId(stData.masterPortfolioId ?: "")
        UserDataUtil.setAccountCd(stData.serverAccountId ?: "")
        UserDataUtil.setServerId(data.accountServer.ifNull())
        UserDataUtil.setAccountDealType(data.accountDealType ?: "")
        UserDataUtil.setPlatForm(data.platform ?: "")
        UserDataUtil.setIsStPublicTrade(stData.isSignal)
        UserDataUtil.setCurrencyType(stData.currencyType ?: "")
        UserDataUtil.setMt4State("5")
        UserDataUtil.setReadOnly(stData.readOnly == true)

        EventBus.getDefault().post(NoticeConstants.SWITCH_ACCOUNT)
        if (!AbUtil.isClearAccount()) {
            VAUSdkUtil.collectSymbolList.clear()
        }

        WsManager.getInstance().breakSocket()
        mView?.reStartApp()
        mView?.ac?.finish()
    }

    /**
     * 跟單帳號用--查询可开通的账户类型
     * 與queryMT4AccountType相似,未來可能會改 先複製一份function出來
     */
    override fun queryStAccountType(isSelectedCopyTrading: Boolean) {

        mView?.showNetDialog()

        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountType(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onNext(resMT4AccountTypeModel: MT4AccountTypeBean) {

                mView?.hideNetDialog()

                if ("V00000" != resMT4AccountTypeModel.resultCode) {
                    ToastUtil.showToast(resMT4AccountTypeModel.msgInfo)
                    return
                }

                val obj = resMT4AccountTypeModel.data?.obj
                /**applyTpe: 申请类型(0:不能申请, 1:真实账户开通，
                 * 2:同名账户，3:重新申请，
                 * 4：身份证明或地址证明未通过，5：只读账户身份证明未通过，
                 * 6：账户被拒绝，7：未上传身份证明)
                 * 若applyTpe返回2表示Live帳號已完全開通完畢 則此時表示開通跟單帳戶可直接跳一頁式的頁面來開通
                 * 否則遵照原本開通LIVE的判斷來決定下一步頁面*/

//                if (obj?.applyTpe == 2) {
//                    openActivity(HtmlActivity::class.java, Bundle().apply {
//                        putInt("tradeType", 17)
//                    })
////                        openActivity(OpenStAccountActivity::class.java)
//                } else {

                // 开通同名账户 从跳H5页面改为跳转原生页面 by Yunyu.Wang  https://hytechapp.notion.site/6994e9da113a4536b8061f0623d50a14
                obj?.let {
                    VAUStartUtil.openAccountGuide(mView.ac, it, 0, false, isSelectedCopyTrading)
                }

//                }

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 查询可开通的账户类型
     */
    override fun queryMT4AccountType(isSelectedCopyTrading: Boolean) {

        mView?.showNetDialog()

        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountType(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onNext(resMT4AccountTypeModel: MT4AccountTypeBean) {

                mView?.hideNetDialog()

                if ("V00000" != resMT4AccountTypeModel.resultCode) {
                    ToastUtil.showToast(resMT4AccountTypeModel.msgInfo)
                    return
                }

                val obj = resMT4AccountTypeModel.data?.obj ?: return
                VAUStartUtil.openAccountGuide(mView?.ac, obj, 0, isAllowClosePage = false, isSelectedCopyTrading = isSelectedCopyTrading)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    // 修改昵称
    override fun modifyNickName(
        data: AccountTradeBean,
        newNick: String?,
        adapterType: Int,
        position: Int
    ) {

        mView?.showNetDialog()

        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        map["nickName"] = newNick ?: ""
        map["acountCd"] = data.acountCd ?: ""
        map["state"] = if (data.accountDealType == "1") "2" else "3"
        map["loginUserId"] = UserDataUtil.userId()
        map["platform"] = data.platform.ifNull()
        map["accountType"] = data.accountType.ifNull()
        map["accountServer"] = data.accountServer.ifNull()
        map["currencyType"] = data.detailData?.currencyType ?: ""
        mModel?.modifyNickName(map, object : BaseObserver<BaseBean>() {
            override fun onNext(info: BaseBean) {
                mView?.hideNetDialog()
                if (info.resultCode == "V00000") {
                    data.nickName = newNick
                    when (adapterType) {
                        1 -> mView?.freshDemoAdapter(position)
                        2 -> mView?.freshLiveAdapter(position)
                    }
                }
                KeyboardUtil.hideSoftInput(mView?.ac)
                ToastUtil.showToast(info.msgInfo)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

    /**
     * 修改跟單昵称
     */
    override fun modifyStNickName(data: AccountTradeBean, newNick: String?, position: Int) {

        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["loginUserId"] = UserDataUtil.userId()
        map["mtsAccountId"] = data.mtsAccount ?: ""
        map["serverId"] = data.accountServer ?: ""
        map["nickName"] = newNick ?: ""
        mModel?.modifyStNickName(map, object : BaseObserver<BaseBean>() {
            override fun onNext(info: BaseBean) {
                mView?.hideNetDialog()
                if (info.resultCode == "V00000") {
                    data.nickName = newNick
                    mView?.freshCopyTradingAdapter(position)
                }
                KeyboardUtil.hideSoftInput(mView?.ac)
                ToastUtil.showToast(info.msgInfo)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }

}