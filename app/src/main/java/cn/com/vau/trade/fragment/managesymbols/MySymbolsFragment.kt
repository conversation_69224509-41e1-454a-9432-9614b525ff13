package cn.com.vau.trade.fragment.managesymbols

import android.annotation.SuppressLint
import android.graphics.PorterDuff
import android.os.Bundle
import android.view.*
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.FragmentMySymbolsBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.trade.adapter.OptionalSelectedRecyclerAdapter
import cn.com.vau.trade.model.MySymbolsModel
import cn.com.vau.trade.presenter.*
import cn.com.vau.util.*
import org.greenrobot.eventbus.*
import java.util.Collections

class MySymbolsFragment : BaseFrameFragment<MySymbolsPresenter, MySymbolsModel>(), MySymbolsContract.View {

    private val mBinding by lazy { FragmentMySymbolsBinding.inflate(layoutInflater) }

    private var onAdapter: OptionalSelectedRecyclerAdapter? = null
    var itemTouchHelper: ItemTouchHelper? = null
    var itemSelectedCount: Int = 0

    private val selectedColor by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val unSelectedColor by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c731e1e1e_c61ffffff) }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? = mBinding.root

    override fun initParam() {
        super.initParam()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        itemSelectedCount = 0
        for (position in VAUSdkUtil.collectSymbolList.indices) {
            VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = false
        }
    }

    @SuppressLint("WrongConstant")
    override fun initView() {
        super.initView()

        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setHintMessage(getString(R.string.no_symbols))
            }
        })

        // 已选产品RecyclerView
        mBinding.onRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mBinding.onRecyclerView.setHasFixedSize(true)
        mBinding.onRecyclerView.isNestedScrollingEnabled = false
        onAdapter = OptionalSelectedRecyclerAdapter(
            requireContext(),
            VAUSdkUtil.collectSymbolList
        )
        mBinding.onRecyclerView.adapter = onAdapter
        mBinding.onRecyclerView.setEmptyView(mBinding.mVsNoData)
        mBinding.rlEditBottom.visibility =
            if (VAUSdkUtil.collectSymbolList.size == 0) View.INVISIBLE else View.VISIBLE
        itemTouchHelper = ItemTouchHelper(object : ItemTouchHelper.Callback() {
            /**
             * 返回我们要监控的方向，上下左右，我们做的是上下拖动，要返回都是UP和DOWN
             * 如果是列表类型的RecyclerView的只存在UP和DOWN，如果是网格类RecyclerView则还应该多有LEFT和RIGHT
             */
            override fun getMovementFlags(
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder
            ): Int {
                return if (recyclerView.layoutManager is GridLayoutManager) {
                    val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN or
                            ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
                    val swipeFlags = 0
                    makeMovementFlags(dragFlags, swipeFlags)
                } else {
                    val dragFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN
                    val swipeFlags = 0
                    // final int swipeFlags = ItemTouchHelper.START | ItemTouchHelper.END;
                    makeMovementFlags(dragFlags, swipeFlags)
                }
            }

            /**
             * 拖动某个item的回调，在return前要更新item位置
             * @param viewHolder 正在拖动item
             * @param target  要拖到的目标
             * @return true 表示消费事件
             */
            override fun onMove(
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder,
                target: RecyclerView.ViewHolder
            ): Boolean {
                //得到当拖拽的viewHolder的Position
                val fromPosition = viewHolder.adapterPosition
                //拿到当前拖拽到的item的viewHolder
                val toPosition = target.adapterPosition
                if (VAUSdkUtil.collectSymbolList.isNotEmpty()) {
                    if (fromPosition < toPosition) {
                        for (i in fromPosition..<toPosition) {
                            Collections.swap(VAUSdkUtil.collectSymbolList, i, i + 1)
                        }
                    } else {
                        for (i in fromPosition downTo toPosition + 1) {
                            Collections.swap(VAUSdkUtil.collectSymbolList, i, i - 1)
                        }
                    }
                    mPresenter.netBean.moveToIndex = toPosition
                    onAdapter?.notifyItemMoved(fromPosition, toPosition)
                }
                return true
            }

            // 左右拖动的回调 我们只做上下，不做任何处理
            override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {}

            override fun canDropOver(
                recyclerView: RecyclerView,
                current: RecyclerView.ViewHolder,
                target: RecyclerView.ViewHolder
            ): Boolean {
                return true
            }

            // 是否开启长按 拖动
            override fun isLongPressDragEnabled(): Boolean {
                return false
            }

            /**
             * Item被选中时候回调
             * @param actionState
             *          当前Item的状态
             *          ItemTouchHelper.ACTION_STATE_IDLE   闲置状态
             *          ItemTouchHelper.ACTION_STATE_SWIPE  滑动中状态
             *          ItemTouchHelper#ACTION_STATE_DRAG   拖拽中状态
             */
            override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
                if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
                    viewHolder?.itemView?.background?.alpha =
                        if (mPresenter.netBean.moveFromIndex % 2 == 0) 25 else 200
                }
                super.onSelectedChanged(viewHolder, actionState)
            }

            /**
             * 手指松开的时候还原，刷新adapter
             * @param recyclerView
             * @param viewHolder
             */
            override fun clearView(
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder
            ) {
                super.clearView(recyclerView, viewHolder)
                // viewHolder.itemView.setBackgroundColor(0);
                // 联网改变产品位置，成功刷新adapter显示，失败重新获取我的自选产品列表恢复位置
                if (mPresenter.netBean.moveFromIndex != mPresenter.netBean.moveToIndex) {
                    mPresenter.updOptionalProdApi()
                }
                refreshAdapter()
            }
        })
        itemTouchHelper?.attachToRecyclerView(mBinding.onRecyclerView)
    }

    override fun initListener() {
        super.initListener()
        onAdapter?.setOnItemClickListener(object :
            OptionalSelectedRecyclerAdapter.OnItemClickListener {
            override fun onItemEditLongClickListener(
                holder: RecyclerView.ViewHolder,
                position: Int
            ) {
                mPresenter.netBean.moveFromNameEn = VAUSdkUtil.collectSymbolList.getOrNull(position)?.symbol.ifNull()
                mPresenter.netBean.moveFromIndex = position
                itemTouchHelper?.startDrag(holder)
            }

            override fun onItemToTopClickListener(holder: RecyclerView.ViewHolder, position: Int) {
                val tempItem = VAUSdkUtil.collectSymbolList.elementAtOrNull(position) ?: return
                VAUSdkUtil.collectSymbolList.removeAt(position)
                VAUSdkUtil.collectSymbolList.add(0, tempItem)
                mPresenter.updOptionalProdApi()
                refreshAdapter()
            }

            override fun onItemCheckBoxClickListener(
                holder: OptionalSelectedRecyclerAdapter.ViewHolder,
                position: Int
            ) {
                if (holder.cbSelect.isChecked) {
                    VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = true
                    itemSelectedCount++
                    if (itemSelectedCount == VAUSdkUtil.collectSymbolList.size) {
                        mBinding.cbSelectAll.isChecked = true
                    }
                } else {
                    VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = false
                    // VAUSdkUtils.getInstance().optionalList[position].isOptionSelected = false
                    itemSelectedCount--
                    mBinding.cbSelectAll.isChecked = false
                }

                if (itemSelectedCount < 0) {
                    itemSelectedCount = 0
                }

                changeDeleteBtnState()
            }

        })
        mBinding.llEditDelete.setOnClickListener(this)
        mBinding.llEditSelectAll.setOnClickListener(this)
        mBinding.cbSelectAll.setOnClickListener {
            val isChecked = mBinding.cbSelectAll.isChecked
            if (isChecked) {
                itemSelectedCount = VAUSdkUtil.collectSymbolList.size
                for (position in VAUSdkUtil.collectSymbolList.indices) {
                    VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = true
                    // VAUSdkUtils.getInstance().optionalList[position].isOptionSelected = true
                }
            } else {
                itemSelectedCount = 0
                for (position in VAUSdkUtil.collectSymbolList.indices) {
                    VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = false
                    // VAUSdkUtils.getInstance().optionalList[position].isOptionSelected = false
                }
            }
            changeDeleteBtnState()
            refreshAdapter()
        }

    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.ll_edit_select_all -> {
                mBinding.cbSelectAll.isChecked = !mBinding.cbSelectAll.isChecked

                val isChecked = mBinding.cbSelectAll.isChecked
                if (isChecked) {
                    itemSelectedCount = VAUSdkUtil.collectSymbolList.size
                    for (position in VAUSdkUtil.collectSymbolList.indices) {
                        VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = true
                        // VAUSdkUtils.getInstance().optionalList[position].isOptionSelected = true
                    }
                } else {
                    itemSelectedCount = 0
                    for (position in VAUSdkUtil.collectSymbolList.indices) {
                        VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = false
                        // VAUSdkUtils.getInstance().optionalList[position].isOptionSelected = false
                    }
                }
                changeDeleteBtnState()
                refreshAdapter()

            }

            R.id.ll_edit_delete -> {
                if (itemSelectedCount > 0) {
                    itemSelectedCount = 0
                    for (position in VAUSdkUtil.collectSymbolList.size - 1 downTo 0) {
                        if (true == VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected) {
                            VAUSdkUtil.collectSymbolList.getOrNull(position)?.isOptionSelected = false
                            VAUSdkUtil.collectSymbolList.removeAt(position)
                        }
                    }
                    refreshAdapter()
                    mPresenter.updOptionalProdApi()
                    EventBus.getDefault().post(NoticeConstants.REFRESH_ADD_SYMBOLS_LIST)
                    changeDeleteBtnState()
                }
                mBinding.cbSelectAll.isChecked = false
            }
        }
    }

    override fun refreshAdapter() {
        mBinding.onRecyclerView.post {
            onAdapter?.notifyDataSetChanged()
        }
        mBinding.rlEditBottom.visibility =
            if (VAUSdkUtil.collectSymbolList.size == 0) View.GONE else View.VISIBLE
    }

    private fun changeDeleteBtnState() {
        if (itemSelectedCount > 0) {
            mBinding.ivDelete.setColorFilter(selectedColor, PorterDuff.Mode.SRC_IN)
            mBinding.tvDelete.setTextColor(selectedColor)
        } else {
            mBinding.ivDelete.setColorFilter(unSelectedColor, PorterDuff.Mode.SRC_IN)
            mBinding.tvDelete.setTextColor(unSelectedColor)
            mBinding.cbSelectAll.isChecked = false
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.REFRESH_MY_SYMBOLS_LIST -> {
                mPresenter.updOptionalProdApi()
                mBinding.cbSelectAll.isChecked = false
                refreshAdapter()
            }
        }
    }

    fun activityNotifyChange() {
        onAdapter?.notifyDataSetChanged()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}