package cn.com.vau.trade.fragment.kchart

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PointF
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.Html
import android.text.TextUtils
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewStub
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.mvvm.state.UIState
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.kchart.adapter.FocusedCoordinateAdapter
import cn.com.vau.common.view.kchart.viewbeans.BrokenLine
import cn.com.vau.common.view.kchart.viewbeans.CandleLine
import cn.com.vau.common.view.kchart.viewbeans.ChartType
import cn.com.vau.common.view.kchart.viewbeans.Coordinates
import cn.com.vau.common.view.kchart.viewbeans.CrossLine
import cn.com.vau.common.view.kchart.viewbeans.HistogramView
import cn.com.vau.common.view.kchart.viewbeans.IndicatorLine
import cn.com.vau.common.view.kchart.viewbeans.MACDHistogram
import cn.com.vau.common.view.kchart.viewbeans.MovableOrderLine
import cn.com.vau.common.view.kchart.views.ChartViewImp
import cn.com.vau.common.view.kchart.views.ChartViewImp.OnOrderLineClickListener
import cn.com.vau.common.view.kchart.views.ChartViewImp.OnOrderLineMoveListener
import cn.com.vau.common.view.popup.BottomKLineNewGuideDialog
import cn.com.vau.common.view.popup.bean.TradingViewSettingData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.KChartBean
import cn.com.vau.data.trade.KChartBean.DataBean.ChartsBean
import cn.com.vau.databinding.FootKlineTabIntervalBinding
import cn.com.vau.databinding.FragmentKlineChartBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.activity.HKLineChartActivity
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.activity.OrderActivity
import cn.com.vau.trade.adapter.KLineChartIntervalTabAdapter
import cn.com.vau.trade.adapter.KLineChartTypeTabAdapter
import cn.com.vau.trade.bean.KLineEvent
import cn.com.vau.trade.bean.kchart.KLineSettingData
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.IndexLineDataUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.MyExtremeCalculator
import cn.com.vau.trade.kchart.MyFocusedCoordinateAdapter
import cn.com.vau.trade.kchart.VolumeCoordinateAdapter
import cn.com.vau.trade.kchart.pop.BottomKLinePriceChangeDialog
import cn.com.vau.trade.kchart.pop.BottomKLineSettingsDialog
import cn.com.vau.trade.kchart.tradingview.ChartCandleLandscapeActivity
import cn.com.vau.trade.model.KLineChartViewModel
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.util.AppUtil
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.formatProductPrice
import cn.com.vau.util.ifNull
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.mathSub
import cn.com.vau.util.noRepeat
import cn.com.vau.util.numFormat
import cn.com.vau.util.setFontG400
import cn.com.vau.util.setFontG500
import cn.com.vau.util.setFontG600
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.toLongCatching
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.tracking.SensorsHelper.getTradeType
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshListener
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.Calendar
import java.util.Formatter
import java.util.Locale

class KLineChartFragment : BaseMvvmBindingFragment<FragmentKlineChartBinding>() {

    val mViewModel: KLineChartViewModel by activityViewModels()
    private val activityViewModel: KLineViewModel by activityViewModels()
    private val shape_cf44040_r100: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.shape_cf44040_r100) }
    private val shape_c00c79c_r100: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.shape_c00c79c_r100) }
    private val draw_shape_c731e1e1e_c61ffffff_r100: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.draw_shape_c731e1e1e_c61ffffff_r100) }
    private var isShowMore = false
    private var crossBoardHalfStart = true
    private var mIndexToEnd = -1
    private var isShareShow = false
    private var firstLoaded = false
    private var titleToggleTop: Int = -1
    var isShowTitle = false

    private var candleLine: CandleLine? = null
    private var indicatorLine: IndicatorLine? = null
    private var timeIndicatorLine: IndicatorLine? = null
    private var macd0IndicatorLine: IndicatorLine? = null
    private var buyLine: IndicatorLine? = null
    private var timeBuyLine: IndicatorLine? = null
    private var timeBrokenLine: BrokenLine? = null
    private var timeChartAdapter: MyFocusedCoordinateAdapter? = null

    private var mainChartAdapter: MyFocusedCoordinateAdapter? = null
    private var subChartAdapter: MyFocusedCoordinateAdapter? = null
    private var volumeChartAdapter: FocusedCoordinateAdapter? = null

    private var isShowDragTip = false

    /**
     * k线设置弹窗
     */
    private var settingDialog: BottomKLineSettingsDialog? = null

    /**
     * k线新版本引导弹窗
     */
    private var bottomKLineNewGuideDialog: BottomKLineNewGuideDialog? = null

    private var wrPriceChangeDialog: WeakReference<BottomKLinePriceChangeDialog>? = null
    private var mHandler: MyHandler = MyHandler(this)

    private val arrowUpDrawable: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.img_kline_arrow_up_white) }
    private val arrowDownDrawable: Drawable? by lazy { ContextCompat.getDrawable(requireContext(), R.drawable.img_kline_arrow_down_white) }

    private val footerView: FootKlineTabIntervalBinding by lazy { FootKlineTabIntervalBinding.inflate(layoutInflater, mBinding.intervalRecyclerView, false) }
    private val intervalAdapter by lazy {
        KLineChartIntervalTabAdapter().apply {
            addFooterView(footerView.root, orientation = LinearLayout.HORIZONTAL)
        }
    }
    private val chartTypeAdapter by lazy { KLineChartTypeTabAdapter() }
    private val tradeSentimentTipDialog by lazy {
        BottomContentDialog.Builder(requireActivity())
            .setTitle(getString(R.string.traders_sentiment))
            .setContent(getString(R.string.traders_sentiment_tip))
            .build()
    }

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.data = activityViewModel.data
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        KLineDataUtils.isFrontPortrait = true
        HttpUrl.isSwitchTradingView = Build.VERSION.SDK_INT > Build.VERSION_CODES.P
        if (KLineDataUtils.userData == null) {
            KLineDataUtils.userData = KLineSettingData.getUserData()
        }
        if (activityViewModel.selectedOrderNo.isNotEmpty()) {
            KLineDataUtils.selectedOrderNo = activityViewModel.selectedOrderNo
        }
        // 获取主副图类型数据源
        setChartTypeDataList()
        mViewModel.selectedInterval = SpManager.getChartTypeText("1D")   // 默认1D
        mViewModel.isTimeShare = mViewModel.selectedInterval == "Tick"
        mViewModel.chartTypeName = if (KLineDataUtils.userData == null) "MA" else KLineDataUtils.userData.mainChartName
        mViewModel.chartSubTypeName = if (KLineDataUtils.userData == null) "VOL" else KLineDataUtils.userData.subChartName
        // 埋点
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_VIEW)
    }

    private fun setChartTypeDataList() {
        val list = mutableListOf<Pair<Int, String>>()
        mViewModel.mainChartNames.forEach {
            list.add(Pair(1, it))
        }
        mViewModel.subChartNames.forEach {
            list.add(Pair(2, it))
        }
        mViewModel.chartTypeAllList.clear()
        mViewModel.chartTypeAllList.addAll(list)
    }

    override fun initView() {
        mBinding.intervalRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.intervalRecyclerView.adapter = intervalAdapter
        intervalAdapter.setList(mViewModel.intervalList)
        selectInterval(mViewModel.selectedInterval)
        mBinding.chartTypeRecyclerView.layoutManager = WrapContentLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mBinding.chartTypeRecyclerView.adapter = chartTypeAdapter
        chartTypeAdapter.setList(mViewModel.chartTypeAllList)
        chartTypeAdapter.changeChartSelected(mViewModel.chartTypeName, mViewModel.chartSubTypeName)
        // 初始化图表参数
        ChartUIParamUtil.initKLineColor()
        mBinding.root.post {
            // Firebase报 mBinding.intervalRecyclerView 空指针？？？
            mBinding ?: return@post
            mBinding.intervalRecyclerView ?: return@post
//            Log.i("wj", "initView: top:${mBinding.tvBoardDiff.top}  bottom:${mBinding.tvBoardDiff.bottom}")
            titleToggleTop = mBinding.tvBoardDiff.bottom
            val top = mBinding.flSubchart.top
            mBinding.chartview.subViewTopHeight = top
            mBinding.chartSubView.topHeight = top
            mBinding.volumeChart.topHeight = top
        }

        mBinding.timeChartContainer.isVisible = mViewModel.isTimeShare
        // 开盘价不变
        mBinding.tvBoardOpen.text = mViewModel.data?.open.numFormat(mViewModel.data?.digits.ifNull(), false)
        val drawable = ContextCompat.getDrawable(requireActivity(), R.drawable.icon2_close_36x36)
        drawable?.setBounds(0, 0, 12.dp2px(), 12.dp2px())
        if (LanguageHelper.isRtlLanguage()){
            mBinding.clCharttipFloat.layoutDirection = View.LAYOUT_DIRECTION_RTL
            mBinding.tvDragTip.setCompoundDrawables(drawable, null, null, null)
        }else {
            mBinding.clCharttipFloat.layoutDirection = View.LAYOUT_DIRECTION_LTR
            mBinding.tvDragTip.setCompoundDrawables(null, null, drawable, null)
        }

        //神策埋点，页面浏览事件
        sensorsTrack(false, "")
        addPerformance()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    override fun initFont() {
        super.initFont()
        mBinding.tvBoardSellPrice.setFontG600()
        mBinding.tvBoardDiff.setFontG500()
        mBinding.tvBoardOpen.setFontG500()
        mBinding.tvBoardClose.setFontG500()
        mBinding.tvBoardLow.setFontG500()
        mBinding.tvBoardHigh.setFontG500()
        mBinding.tvBid.setFontG500()
        mBinding.tvAsk.setFontG500()
        mBinding.tvSpread.setFontG400()
        mBinding.tvTradeTime.setFontG400()
        mBinding.tv1DValue.setFontG500()
        mBinding.tv7DValue.setFontG500()
        mBinding.tv30DValue.setFontG500()
    }

    override fun initData() {
        super.initData()
        mViewModel.initOrderData(mBinding.chartview)
        initCrossLine(context, mBinding.chartview, mBinding.chartSubView, mBinding.volumeChart, mBinding.chartTimeView, mViewModel.data)
        initChartView(context, mBinding.chartview, mBinding.chartSubView, mBinding.volumeChart, mBinding.chartTimeView)
        // 蜡烛图 和 现价线
        candleLine = mViewModel.getCandleLine(context)
        indicatorLine = mViewModel.getIndicatorLine(context)
        timeIndicatorLine = mViewModel.getIndicatorLine(context)
        macd0IndicatorLine = mViewModel.getMACD0IndicatorLine(context)
        buyLine = mViewModel.getBuyLine(context)
        timeBuyLine = mViewModel.getBuyLine(context)
        mViewModel.data?.let { dataBean ->
            // 保证原始价格不为0
            if (dataBean.originalAsk == 0f) dataBean.originalAsk = dataBean.ask
            if (dataBean.originalBid == 0f) dataBean.originalBid = dataBean.bid
        }
        updateProdInfo()
    }

    override fun createObserver() {
        super.createObserver()
        // loading
        mViewModel.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
        // 空布局显示
        mViewModel.noDataLiveData.observe(this) {
            mBinding.mVsNoData.isVisible = it
            firstLoaded = true
        }
        // 行情刷新
        activityViewModel.refreshCallBack.observe(this) {
            updateProdInfo()
            refreshPriceChange()
            getPriceChangeDialog()?.refresh()
        }
        // 分享
        activityViewModel.shareLiveData.observe(this) {
            isShareShow = it.ifNull()
        }
        // 交易情绪
        mViewModel.tradeEmotionUiState.observe(this) {
            when (it) {
                is UIState.Success -> {
                    val bean = it.data
                    if (bean != null) {
                        mBinding.clTradeSentiment.isVisible = true
                        val sellMarkupOrder = bean.sellMarkupOrder.toFloatCatching().toInt()
                        if (sellMarkupOrder >= 0 && sellMarkupOrder <= 100) {
                            mBinding.mProgressBar.progress = (100 - sellMarkupOrder)
                            mBinding.tvSentimentSellValue.text = "${100 - sellMarkupOrder}%"
                            mBinding.tvSentimentBuyValue.text = "$sellMarkupOrder%"
                        }
                    }
                }

                is UIState.Error -> {
                    mBinding.clTradeSentiment.isVisible = false
                    ToastUtil.showToast(it.errMsg)
                }

                else -> {}
            }
        }
        // 交易价格变动 - 更多
        mViewModel.priceChangeDataMore.observe(this) {
            getPriceChangeDialog()?.fillData(it)
        }
        // 事件处理
        lifecycleScope.launch {
            mViewModel.eventFlow.collectLatest {
                when (it) {
                    // 重新计算延时
                    NoticeConstants.SEND_EVENT_TAG_KLINE_DELAY_TIMER -> {
                        delayTimer()
                    }

                    NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_CHART -> {
                        setAdapterDigits()
                        refreshKchart()
                    }

                    NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL_COMPLETE -> {
                        activityViewModel.isSwitching = false
//                        Log.i("wj", "NoticeConstants.SEND_EVENT_TAG_SWITCH_SYMBOL: 变更产品数据完成")
                    }
                }
            }
        }
        mViewModel.tokenErrorLiveData.observe(this){
            showTokenErrorDialog(it.ifNull())
        }
    }

    private fun getPriceChangeDialog(): BottomKLinePriceChangeDialog? {
        return wrPriceChangeDialog?.get()
    }

    override fun initListener() {
        super.initListener()
        // 监听NestedScrollView滑动高度 显示标题
        mBinding.mNestedScrollView.setOnScrollChangeListener(object : NestedScrollView.OnScrollChangeListener {
            override fun onScrollChange(v: NestedScrollView, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int) {
                val isShow = scrollY >= titleToggleTop
                activityViewModel.showTitleLiveData.value = isShow
                isShowTitle = isShow
            }
        })
        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
                vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoConnection))
                vs.mNoDataView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataView.setBottomBtnViewClickListener {
                    // 点击重试
                    resetRequest(mViewModel.data?.symbol.ifNull(), true)
                }
            }
        })
        //下拉刷新
        mBinding.smartRefreshLayout.setOnRefreshListener(OnRefreshListener { refreshLayout: RefreshLayout ->
            refreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            resetRequest(mViewModel.data?.symbol.ifNull(), false)
        })
        // 图表时间切换
        intervalAdapter.setOnItemClickListener { _, _, position ->
            val interval = intervalAdapter.getItem(position)
            intervalAdapter.changeSelected(interval)
            selectInterval(interval)
        }
        // 图表类型切换
        chartTypeAdapter.setOnItemClickListener { _, _, position ->
            closeCrossLine()
            val item = chartTypeAdapter.getItem(position)
            when (item.first) {
                // 1 主图
                1 -> {
                    val mainChartType = item.second
                    if (mViewModel.chartTypeName == mainChartType) {
                        // 判断显示/隐藏线
                        when (mainChartType) {
                            "MA" -> {
                                if (!maLineVisible) {
                                    mainChartSingleSelect("MA")
                                    switchMainChartType(mainChartType)
                                } else {
                                    mainChartTypeUnselected()
                                    mBinding.chartview.removeChild(ma5)
                                    mBinding.chartview.removeChild(ma10)
                                    mBinding.chartview.removeChild(ma20)
                                    mBinding.chartview.removeChild(ma30)
                                }
                            }

                            "BOLL" -> {
                                if (!bollLineVisible) {
                                    mainChartSingleSelect("BOLL")
                                    switchMainChartType(mainChartType)
                                } else {
                                    mainChartTypeUnselected()
                                    mBinding.chartview.removeChild(bollMid)
                                    mBinding.chartview.removeChild(bollUpper)
                                    mBinding.chartview.removeChild(bollLower)
                                }
                            }

                            "MIKE" -> {
                                if (!mikeLineVisible) {
                                    mainChartSingleSelect("MIKE")
                                    switchMainChartType(mainChartType)
                                } else {
                                    mainChartTypeUnselected()
                                    mBinding.chartview.removeChild(mikeWr)
                                    mBinding.chartview.removeChild(mikeMr)
                                    mBinding.chartview.removeChild(mikeSr)
                                    mBinding.chartview.removeChild(mikeWs)
                                    mBinding.chartview.removeChild(mikeMs)
                                    mBinding.chartview.removeChild(mikeSs)
                                }
                            }

                            "BBI" -> {
                                if (!bbiLineVisible) {
                                    mainChartSingleSelect("BBI")
                                    switchMainChartType(mainChartType)
                                } else {
                                    mainChartTypeUnselected()
                                    mBinding.chartview.removeChild(bbiline)
                                }
                            }
                        }
                    } else {
                        // 保存主图类型
                        mViewModel.chartTypeName = mainChartType

                        when (mainChartType) {
                            "MA" -> {
                                mainChartSingleSelect("MA")
                                switchMainChartType(mainChartType)
                            }

                            "BOLL" -> {
                                mainChartSingleSelect("BOLL")
                                switchMainChartType(mainChartType)
                            }

                            "MIKE" -> {
                                mainChartSingleSelect("MIKE")
                                switchMainChartType(mainChartType)
                            }

                            "BBI" -> {
                                mainChartSingleSelect("BBI")
                                switchMainChartType(mainChartType)
                            }
                        }
                    }

                    // 保存最新的defaultShowPointNums
                    if (candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.minShownPointNums) {
                        ChartUIParamUtil.defaultShowPointNums = candleLine?.shownPointNums.ifNull()
                    }
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.mainChartName = mainChartType
                    }

                    indicatorBuryPoint("Main", mainChartType)
                }
                // 2 副图
                2 -> {
                    val subChartType = item.second
                    if (mViewModel.chartSubTypeName == subChartType) {
                        val subChartVisible = mBinding.flSubchart.isVisible
                        if (subChartVisible) {
                            // 隐藏副图
                            mBinding.flSubchart.isVisible = false
                            chartTypeAdapter.changeSubSelected("")
                        } else {
                            // 显示副图
                            mBinding.flSubchart.isVisible = true
                            chartTypeAdapter.changeSubSelected(subChartType)
                        }
                    } else {
                        chartTypeAdapter.changeSubSelected(subChartType)
                        mViewModel.chartSubTypeName = subChartType
                        mBinding.flSubchart.isVisible = true
                        // 切换副图类型 对应的图表显示
                        initSubChartTypeView(subChartType)
                        if (KLineDataUtils.userData != null) {
                            KLineDataUtils.userData.subChartName = subChartType
                        }
                    }
                    indicatorBuryPoint("Sub", subChartType)
                }
            }
        }
        // interval 更多
        footerView.root.setOnClickListener {
            closeCrossLine()
            mBinding.llMoreInterval.isVisible = !isShowMore
            footerView.ivArrow.rotation = if (isShowMore) 0f else 180f
            isShowMore = !isShowMore
        }
        mBinding.tv5M.setOnClickListener {
            selectInterval("5m")
        }
        mBinding.tv30M.setOnClickListener {
            selectInterval("30m")
        }
        mBinding.tv4H.setOnClickListener {
            selectInterval("4h")
        }
        mBinding.tv1M.setOnClickListener {
            selectInterval("1M")
        }

        mBinding.taskView.setOnTouchListener(object : OnTouchListener {
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                if (event?.action == MotionEvent.ACTION_DOWN) {
                    retract()
                    closeCrossLine()
                }
                return false
            }
        })

        mBinding.ivSetting.setOnClickListener {
//            showKLineConfigPopUp()
            showKLineSettingPopUp()
            closeCrossLine()
        }

        mBinding.ivSwitchVh.setOnClickListener {
            gotoHKLineScreen()
        }

        mBinding.ivSwitchVhTime.setOnClickListener {
            gotoHKLineScreen()
        }

        mBinding.tvTradeSentiment.setOnClickListener {
            closeCrossLine()
            tradeSentimentTipDialog.show()
        }

        mBinding.tvPriceChangeMore.setOnClickListener {
            fun more() {
                closeCrossLine()
                val priceChangeDialog = BottomKLinePriceChangeDialog.Builder(requireActivity())
                    .setData(mViewModel.data)
                    .build()
                priceChangeDialog.show()
                wrPriceChangeDialog = WeakReference<BottomKLinePriceChangeDialog>(priceChangeDialog)
                mViewModel.tradeOrderTradePriceChange(mViewModel.data?.symbol.ifNull(), true)
                // 埋点
                SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_PRICE_CHANGE_CLICK)
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { more() }) return@setOnClickListener
            }
            more()
        }

        // SELL 下单
        mBinding.llBid.setOnClickListener {
            orderBuryPoint("sell")
            if ("0" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
                return@setOnClickListener
            }
            if ("1" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_can_only_be_closed))
                return@setOnClickListener
            }
            if (mViewModel.data?.marketClose == true) {
                ToastUtil.showToast(getString(R.string.trading_disabled))
                return@setOnClickListener
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { startNewOrderActivity(1) }) return@setOnClickListener
            }
            startNewOrderActivity(1)
        }

        // BUY 下单
        mBinding.llAsk.setOnClickListener {
            orderBuryPoint("buy")
            if ("0" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
                return@setOnClickListener
            }
            if ("1" == mViewModel.data?.enable) {
                ToastUtil.showToast(getString(R.string.this_symbol_can_only_be_closed))
                return@setOnClickListener
            }
            if (mViewModel.data?.marketClose == true) {
                ToastUtil.showToast(getString(R.string.trading_disabled))
                return@setOnClickListener
            }
            if (mViewModel.data?.marketClose == true) {
                ToastUtil.showToast(getString(R.string.trading_disabled))
                return@setOnClickListener
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { startNewOrderActivity(0) }) return@setOnClickListener
            }
            startNewOrderActivity(0)
        }

        mBinding.chartview.crossLine.setOnCrossLineMoveListener(object : CrossLine.OnCrossLineMoveListener {
            override fun onCrossLineMove(crossIndexInScreen: Int, drawPointIndex: Int, crossPointF: PointF?, fingerPointF: PointF?) {
                baseCrossLineMove(mBinding.chartview.crossLine, crossIndexInScreen, drawPointIndex)
            }

            override fun onCrossLineDismiss() {
                baseCrossLineDismiss()
            }

            override fun onCrossIndicateYScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int, crossPointF: PointF?, yMin: Float, yMax: Float): String? {
                return baseCrossIndicateYScale(crossPointF?.y.ifNull().toInt(), mBinding.chartview.coordinateHeight, yMin, yMax, ChartUIParamUtil.digits)
            }

            override fun onCrossIndicateXScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int): String? {
                return baseCrossIndicateXScale(crossIndexInScreen, drawPointIndex)
            }
        })
        mBinding.chartSubView.crossLine.setOnCrossLineMoveListener(object : CrossLine.OnCrossLineMoveListener {
            override fun onCrossLineMove(crossIndexInScreen: Int, drawPointIndex: Int, crossPointF: PointF?, fingerPointF: PointF?) {
                baseCrossLineMove(mBinding.chartSubView.crossLine, crossIndexInScreen, drawPointIndex)
            }

            override fun onCrossLineDismiss() {
                baseCrossLineDismiss()
            }

            override fun onCrossIndicateYScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int, crossPointF: PointF?, yMin: Float, yMax: Float): String? {
                return baseCrossIndicateYScale(crossPointF?.y.ifNull().toInt(), mBinding.chartSubView.coordinateHeight, yMin, yMax, ChartUIParamUtil.digits)
            }

            override fun onCrossIndicateXScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int): String? {
                return baseCrossIndicateXScale(crossIndexInScreen, drawPointIndex)
            }
        })
        mBinding.chartTimeView.crossLine.setOnCrossLineMoveListener(object : CrossLine.OnCrossLineMoveListener {
            override fun onCrossLineMove(crossIndexInScreen: Int, drawPointIndex: Int, crossPointF: PointF?, fingerPointF: PointF?) {
                baseCrossLineMove(mBinding.chartTimeView.crossLine, crossIndexInScreen, drawPointIndex)
            }

            override fun onCrossLineDismiss() {
                baseCrossLineDismiss()
            }

            override fun onCrossIndicateYScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int, crossPointF: PointF?, yMin: Float, yMax: Float): String? {
                return baseCrossIndicateYScale(crossPointF?.y.ifNull().toInt(), mBinding.chartTimeView.coordinateHeight, yMin, yMax, ChartUIParamUtil.digits)
            }

            override fun onCrossIndicateXScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int): String? {
                return baseCrossIndicateXScale(crossIndexInScreen, drawPointIndex)
            }
        })
        mBinding.volumeChart.crossLine.setOnCrossLineMoveListener(object : CrossLine.OnCrossLineMoveListener {
            override fun onCrossLineMove(crossIndexInScreen: Int, drawPointIndex: Int, crossPointF: PointF?, fingerPointF: PointF?) {
                baseCrossLineMove(mBinding.volumeChart.crossLine, crossIndexInScreen, drawPointIndex)
            }

            override fun onCrossLineDismiss() {
                baseCrossLineDismiss()
            }

            override fun onCrossIndicateYScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int, crossPointF: PointF?, yMin: Float, yMax: Float): String? {
                return baseCrossIndicateYScale(crossPointF?.y.ifNull().toInt(), mBinding.volumeChart.coordinateHeight, yMin, yMax, 0)
            }

            override fun onCrossIndicateXScale(crossIndexInScreen: Int, drawPointIndex: Int, showPointNums: Int): String? {
                return baseCrossIndicateXScale(crossIndexInScreen, drawPointIndex)
            }
        })

        candleLine?.setOnMoveListener { viewContainer, drawPointIndex, currentShownNums, yMax, yMin ->
            ChartUIParamUtil.chartShowEndPosition = drawPointIndex + currentShownNums
        }

        // 现价线
        indicatorLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val candleLine = dataList?.getOrNull(dataList.size - 1) as? CandleLine.CandleLineBean
                return candleLine?.originalBid?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始卖价（未加点）
            }
        })
        // 分时图现价线
        timeIndicatorLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val timeShareBean = dataList?.getOrNull(dataList.size - 1) as? KChartBean.DataBean.TimeChartBean
                return timeShareBean?.close?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始卖价（未加点）
            }
        })
        // MACD 0线
        macd0IndicatorLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                return 0f
            }
        })
        // 买价线
        buyLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val candleLine = dataList?.getOrNull(dataList.size - 1) as? CandleLine.CandleLineBean
                return candleLine?.originalAsk?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始买价（未加点）
            }
        })
        // 分时图买价线
        timeBuyLine?.setIndicatorLineDataParser(object : IndicatorLine.IndicatorLineDataParser<Any> {
            override fun indicateData(dataList: List<Any>?, drawPointIndex: Int, showPointNums: Int, yMax: Float, yMin: Float): Float {
                val timeShareBean = dataList?.getOrNull(dataList.size - 1) as? KChartBean.DataBean.TimeChartBean
                return timeShareBean?.originalAsk?.formatProductPrice(ChartUIParamUtil.digits, true).toFloatCatching(0f)  //原始买价（未加点）
            }
        })

        // 删除止盈止损线监听
        mBinding.chartview.setOnOrderLineClickListener(object : OnOrderLineClickListener {
            override fun onTPCancel() {
                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.confirm))
                    .setContent(getString(R.string.cancel_take_profit))
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.confirm))
                    .setOnEndListener { textView: TextView? ->
                        mViewModel.setTakeProfitOrStopLoss("0", mViewModel.shareOrderData?.stopLoss.ifNull("0"), mViewModel.shareOrderData)
                        sensorsTrack("TP")
                    }.build().showDialog()
            }

            override fun onSLCancel() {
                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.confirm))
                    .setContent(getString(R.string.cancel_stop_loss))
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.confirm))
                    .setOnEndListener { textView: TextView? ->
                        mViewModel.setTakeProfitOrStopLoss(mViewModel.shareOrderData?.takeProfit.ifNull("0"),"0",  mViewModel.shareOrderData)
                        sensorsTrack("SL")
                    }.build().showDialog()
            }

            override fun onPositionLineClick() {
                mBinding.chartview.positionLine?.actived()
                mBinding.chartview.takeProfitLine?.disactived()
                mBinding.chartview.stopLossLine?.disactived()
                showDragTip()
            }

            override fun onTPLineClick() {
                mBinding.chartview.takeProfitLine?.actived()
                mBinding.chartview.positionLine?.disactived()
                mBinding.chartview.stopLossLine?.disactived()
                showDragTip()
            }

            override fun onSLLineClick() {
                mBinding.chartview.stopLossLine?.actived()
                mBinding.chartview.takeProfitLine?.disactived()
                mBinding.chartview.positionLine?.disactived()
                showDragTip()
            }

            override fun onAllCancel() {
                mBinding.tvDragTip.isVisible = false
            }
        })

        // 拖动止盈止损线监听
        mBinding.chartview.setOnOrderLineMoveListener(object : OnOrderLineMoveListener {
            override fun onMoveOver(yScale: Float, isTP: Boolean) {
                mBinding.chartview.movableLine.state = MovableOrderLine.State.POSITIONED
                val priceStr = yScale.numFormat(ChartUIParamUtil.digits, true)
                CenterActionDialog.Builder(requireActivity())
                    .setTitle(getString(R.string.use_dragn_drop_loss))
                    .setContent(
                        if (isTP) "${getString(R.string.confirm_to_take_profit)}\n${getString(R.string.take_profit_price)}:$priceStr".trimIndent() else "${getString(R.string.confirm_to_stop_loss)}\n${getString(R.string.stop_loss_price)}:${priceStr}".trimIndent())
                    .setStartText(getString(R.string.cancel))
                    .setEndText(getString(R.string.confirm))
                    .setOnStartListener { textView: TextView? ->
                        resetMoveableLine()
                    }
                    .setOnEndListener { textView: TextView? ->
                            if (isTP) {
                                mViewModel.setTakeProfitOrStopLoss(priceStr, mViewModel.shareOrderData?.stopLoss.ifNull("0"), mViewModel.shareOrderData)
                            } else {
                                mViewModel.setTakeProfitOrStopLoss(mViewModel.shareOrderData?.takeProfit.ifNull("0"), priceStr, mViewModel.shareOrderData)
                            }
                    }.build().showDialog()
//                isDragging = false
            }

            override fun onMoving(yScale: Float, isTP: Boolean) {
                if (mViewModel.shareOrderData != null) {
//                    isDragging = true
                    mBinding.tvDragTip.setVisibility(View.GONE)
                }
            }
        })

        mBinding.tvDragTip.clickNoRepeat {
            mBinding.tvDragTip.isVisible = false
            cancelAllLine()
        }
    }

    private fun cancelAllLine(){
        mBinding.chartview.stopLossLine?.disactived()
        mBinding.chartview.takeProfitLine?.disactived()
        mBinding.chartview.positionLine?.disactived()
    }

    private fun showDragTip() {
        if (mBinding.tvDragTip.isVisible.not() && isShowDragTip.not()) {
            isShowDragTip = true
            mBinding.tvDragTip.isVisible = true
        }
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(requireActivity())
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnSingleButtonListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    fun resetMoveableLine() {
        mBinding.chartview.getMovableLine().setShow(false)
        mBinding.chartview.getMovableLine().setState(MovableOrderLine.State.STANDBY)
        mBinding.chartview.setMovingPostionLine(false)
    }

    private fun gotoHKLineScreen() {
        closeCrossLine()
        if (KLineDataUtils.mainList.isEmpty()) return

        noRepeat(2000) {
            KLineDataUtils.isJumpToHKLine = true
            KLineDataUtils.isStartActivity = true
            var intent: Intent?
            if (HttpUrl.isSwitchTradingView && SpManager.getSelectTradingViewMode(true)) {
                SpManager.putSelectTradingViewMode(true)
                intent = Intent(context, ChartCandleLandscapeActivity::class.java)
                // 同步来自Lite模式的LineExtra设置
                val userDataTV = TradingViewSettingData.getHistoryData()
                val lineExtra = userDataTV.line
                if (lineExtra != null) {
                    lineExtra.ask?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.askLineDisplay) 1 else 0
                    lineExtra.bid?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.bidLineDisplay) 1 else 0
                    lineExtra.tp?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.tpLineDisplay) 1 else 0
                    lineExtra.sl?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.slLineDisplay) 1 else 0
                    lineExtra.position?.status = if (KLineDataUtils.userData != null && KLineDataUtils.userData.positionLineDisplay) 1 else 0
                    userDataTV.save()
                }
            } else {
                intent = Intent(context, HKLineChartActivity::class.java)
            }
            intent.putExtra(Constants.PARAM_PRODUCT_NAME, mViewModel.data?.symbol.ifNull())
            val data = mViewModel.data
            intent.putExtra("product_name_cn", data?.symbol)
            intent.putExtra("chart_type_name", mViewModel.chartTypeName)
            intent.putExtra("chart_sub_type_name", mViewModel.chartSubTypeName)
            intent.putExtra("chart_period", mViewModel.period)
            intent.putExtra("chart_period_position", mViewModel.intervalToPosition(mViewModel.selectedInterval))
            intent.putExtra("chart_digits", ChartUIParamUtil.digits)
            intent.putExtra("chart_season", Constants.season)
            startActivityForResult(intent, 0)
            KLineDataUtils.isFrontPortrait = false
            // 因十字线长按与此方法可能同时触发，造成跳转前出现十字线，所以采取延时关闭
            HandlerUtil().postDelayTask(500) { closeCrossLine() }

            // 埋点
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SWITH_LANDSCAPE_BTN_CLICK)
        }
    }

    // 跳转下单页面
    private fun startNewOrderActivity(direction: Int) {
        if (!UserDataUtil.isLogin()) {
            openActivity(LoginActivity::class.java)
            return
        }
        val askLogBundle = Bundle()
        askLogBundle.putString("Trade", if (direction == 0) "Buy" else "Sell")
        askLogBundle.putString("Account", if (UserDataUtil.isDemoAccount()) "Demo" else "Live")
        LogEventUtil.setLogEvent("KLine_Trade", askLogBundle)
//        // 神策埋点，按钮点击事件
//        sensorsTrack(true, if (direction == 0) "Buy" else "Sell")

        if (UserDataUtil.isRebateAccount()) {
            activity?.let {
                CenterActionDialog.Builder(it)
                    .setContent(getString(R.string.you_must_be_trade))
                    .setEndText(getString(R.string.log_in))
                    .setOnEndListener {
                        val bundle = Bundle()
                        bundle.putInt(Constants.IS_FROM, 2)
                        startActivity(Intent(activity, AccountManagerActivity::class.java).putExtras(bundle))
                    }
                    .build()
                    .showDialog()
            }
            return
        }
        // 如果是从订单页跳转过来，直接finish 避免页面循环
        if (activityViewModel.typeFrom == "order") {
            activity?.finish()
            return
        }
        val bundle = Bundle()
        bundle.putString(Constants.PARAM_ORDER_TYPE, direction.toString() + "")
        bundle.putString(Constants.PARAM_PRODUCT_NAME, mViewModel.data?.symbol)
        OrderActivity.open(context, bundle, SpManager.isNeedKYC().not())
    }

    private fun showKLineSettingPopUp() {
        settingDialog = BottomKLineSettingsDialog.Builder(requireActivity()).build()
        settingDialog?.newGuideClick {
            showNewGuideDialog()
            val accountType = if (!UserDataUtil.isLogin()) {
                BuryPointConstant.AccountType.NOLOGIN
            } else if (UserDataUtil.isStLogin()) {
                BuryPointConstant.AccountType.COPY_TRADING
            } else if (UserDataUtil.isDemoAccount()) {
                BuryPointConstant.AccountType.DEMO
            } else {
                BuryPointConstant.AccountType.LIVE
            }

            LogEventUtil.setLogEvent(
                BuryPointConstant.V3474.TRADE_KLINE_USER_GUIDE_BUTTON_CLICK,
                bundleOf(Pair<String, String>("Type_of_account", accountType), Pair<String, String>("Mode", "Lite-vertical-settings"))
            )
        }

        settingDialog?.onSelectLineListener { position: Int, isSelect: Boolean ->
            var selectline = ""
            var toggle = ""
            val bundle = Bundle()
            when (position) {
                0 -> {
                    buyLine?.setShow(isSelect)
                    timeBuyLine?.setShow(isSelect)
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.askLineDisplay = isSelect
                    }

                    selectline = "Ask"
                    toggle = if (isSelect) "On" else "Off"
                }

                1 -> {
                    indicatorLine?.setShow(isSelect)
                    timeIndicatorLine?.setShow(isSelect)
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.bidLineDisplay = isSelect
                    }

                    selectline = "Bid"
                    toggle = if (isSelect) "On" else "Off"
                }

                2 -> {
                    mBinding.chartview.positionLine.setShow(isSelect)
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.positionLineDisplay = isSelect
                    }

                    selectline = "Open"
                    toggle = if (isSelect) "On" else "Off"
                }

                3 -> {
                    mBinding.chartview.takeProfitLine.setShow(isSelect)
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.tpLineDisplay = isSelect
                    }

                    selectline = "TP"
                    toggle = if (isSelect) "On" else "Off"
                }

                4 -> {
                    mBinding.chartview.stopLossLine.setShow(isSelect)
                    if (KLineDataUtils.userData != null) {
                        KLineDataUtils.userData.slLineDisplay = isSelect
                    }

                    selectline = "SL"
                    toggle = if (isSelect) "On" else "Off"
                }

                else -> {}
            }

            bundle.putString("Account_type", KLineActivity.getPointAccountType())
            bundle.putString("Mode", "Lite-vertical")
            bundle.putString("Line", selectline)
            bundle.putString("Toggle", toggle)
            LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK, bundle)
        }

        settingDialog?.show()
        // 埋点
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SETTING_BTN_CLICK)
    }

    private fun showNewGuideDialog() {
        bottomKLineNewGuideDialog =  BottomKLineNewGuideDialog.Builder(requireActivity()).build()
        bottomKLineNewGuideDialog?.show()
    }

    private fun changeChartInterval(interval: String) {
        // 如果和上次点击条目一样直接返回
        if (interval == mViewModel.selectedInterval) return
        mViewModel.selectedInterval = interval
        mViewModel.isTimeShare = interval == "Tick"
        SpManager.putChartTypeText(interval)
        // 定时刷新false
        mViewModel.isAutoRefresh = false
        KLineDataUtils.mainList.clear()
        refreshKchart()
        mBinding.timeChartContainer.isVisible = mViewModel.isTimeShare
        if (candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.minShownPointNums) {
            ChartUIParamUtil.defaultShowPointNums = candleLine?.shownPointNums.ifNull()
        }
        mHandler.removeMessages(1000)
        mIndexToEnd = -1
        mViewModel.switchChartPeriod(mViewModel.data?.symbol.ifNull(), true)
        // 埋点
        intervalBuryPoint()
    }

    private fun closeCrossLine() {
        if (mBinding.chartview.crossLine?.isShow == true) {
            mBinding.chartview.crossLine.isShow = false
            mBinding.chartview.crossLine.crossDismiss()
            mBinding.chartview.invalidate()
        }
        if (mBinding.chartSubView.crossLine?.isShow == true) {
            mBinding.chartSubView.crossLine?.isShow = false
            mBinding.chartSubView.crossLine?.crossDismiss()
            mBinding.chartSubView.invalidate()
        }
        if (mBinding.chartTimeView.crossLine?.isShow == true) {
            mBinding.chartTimeView.crossLine?.isShow = false
            mBinding.chartTimeView.crossLine?.crossDismiss()
            mBinding.chartTimeView.invalidate()
        }
        if (mBinding.volumeChart.crossLine?.isShow == true) {
            mBinding.volumeChart.crossLine?.isShow = false
            mBinding.volumeChart.crossLine?.crossDismiss()
            mBinding.volumeChart.invalidate()
        }
        if (mBinding.clCharttipFloat.isVisible) {
            mBinding.clCharttipFloat.isVisible = false
        }
    }

    private fun drawNextUpdateCrossLine() {
        if (mBinding.chartview.crossLine?.isShow == true) {
            mBinding.chartview.crossLine?.drawPointIndex += 1
            mBinding.chartview.invalidate()
        }
        if (mBinding.chartSubView.crossLine?.isShow == true) {
            mBinding.chartSubView.crossLine?.drawPointIndex += 1
            mBinding.chartSubView.invalidate()
        }
        if (mBinding.chartTimeView.crossLine?.isShow == true) {
            mBinding.chartTimeView.crossLine?.drawPointIndex += 1
            mBinding.chartTimeView.invalidate()
        }
        if (mBinding.volumeChart.crossLine?.isShow == true) {
            mBinding.volumeChart.crossLine?.drawPointIndex += 1
            mBinding.volumeChart.invalidate()
        }
    }

    // ---------------------------- Cross Line 回调 提取出来的公共部分 --------------------------------------------------------
    private fun baseCrossLineMove(crossLine: CrossLine, crossIndexInScreen: Int, drawPointIndex: Int) {
        val crossedPointIndex = crossIndexInScreen + drawPointIndex
        ChartUIParamUtil.chartShowEndPosition = crossedPointIndex
        ChartUIParamUtil.currentMoveIndex = crossedPointIndex
        var screenShownNums = crossLine.shownPointNums
        var isHalfOfStart = true
        if (screenShownNums != 0) {
            isHalfOfStart = (crossIndexInScreen - 5) >= (screenShownNums / 2)
        }
        var chartsBean = KLineDataUtils.mainList.getOrNull(crossedPointIndex)
        var timeZone = AppUtil.getTimeZoneRawOffsetToHour()
        var timeStamp = (chartsBean?.timestamp + "000").toLongCatching()
        var nowStamp = timeStamp - (timeZone * 3600 * 1000)
        mBinding.clCharttipFloat.isVisible = true

        if (crossBoardHalfStart != isHalfOfStart) {
            crossBoardHalfStart = isHalfOfStart
            var lp = mBinding.clCharttipFloat.layoutParams as? FrameLayout.LayoutParams
            if (crossBoardHalfStart) {
                lp?.gravity = Gravity.START
            } else {
                lp?.gravity = Gravity.END
            }
            mBinding.clCharttipFloat.setLayoutParams(lp)
        }
        mBinding.tvCharttipTime.text = TimeUtil.formatDateTime(nowStamp, "dd/MM/yyyy HH:mm")
        mBinding.tvCharttipOpenValue.text = chartsBean?.open.numFormat(ChartUIParamUtil.digits, false)
        mBinding.tvCharttipHighValue.text = chartsBean?.high.numFormat(ChartUIParamUtil.digits, false)
        mBinding.tvCharttipLowValue.text = chartsBean?.low.numFormat(ChartUIParamUtil.digits, false)
        mBinding.tvCharttipCloseValue.text = chartsBean?.close.numFormat(ChartUIParamUtil.digits, true)
        updateChartTip(chartsBean)
    }

    /**
     * 涨跌额、涨跌幅、振幅
     */
    private fun updateChartTip (chartsBean: ChartsBean?){
        if (chartsBean == null) {
            mBinding.tvCharttipChangeAmountValue.text = "--"
            mBinding.tvCharttipChangePercentValue.text = "--"
            mBinding.tvCharttipChangeAmountValue.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            mBinding.tvCharttipChangePercentValue.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            mBinding.tvCharttipAmplitudeValue.text = "0.0"
            return
        }
        val closeValue = chartsBean.close.toString().ifNull()
        val prevCloseValue = chartsBean.prevClose?.toString().ifNull()
        val highValue = chartsBean.high.toString().ifNull()
        val lowValue = chartsBean.low.toString().ifNull()
        val openValue = chartsBean.open.toString().ifNull()
        if (prevCloseValue.isNotEmpty()) {
            val difference = closeValue.mathSub(prevCloseValue)//收盘价-前周期收盘价 或 最新价-前周期收盘价，**这里收盘价和最新价统一使用了close字段，drawNextCandle 和 updateProdInfo方法中有对close设置最新价逻辑**
            //涨跌额： 历史涨跌额：收盘价-前周期收盘价；当前涨跌额：最新价-前周期收盘价。
            val changeAmountValue = difference.numFormat(ChartUIParamUtil.digits, true)
            mBinding.tvCharttipChangeAmountValue.text = changeAmountValue
            //涨跌幅： 历史涨跌幅：(收盘价-前周期收盘价)/前周期收盘价*100%； 当前涨跌幅：(最新价-前周期收盘价)/前周期收盘价*100%
            val changeNum = difference.mathDiv(prevCloseValue, 4).mathMul("100")
            val changeStr = changeNum.formatProductPrice(2, false, "0.00") + "%"
            mBinding.tvCharttipChangePercentValue.text = changeStr
            val differenceNum = difference.toDoubleCatching()
            mBinding.tvCharttipChangeAmountValue.setTextColorDiff(
                when {
                    differenceNum > 0 -> ChartUIParamUtil.candleUpColor
                    differenceNum < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
            val changeNumDouble = changeNum.toDoubleCatching()
            mBinding.tvCharttipChangePercentValue.setTextColorDiff(
                when {
                    changeNumDouble > 0 -> ChartUIParamUtil.candleUpColor
                    changeNumDouble < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
        } else {
            mBinding.tvCharttipChangeAmountValue.text = "--"
            mBinding.tvCharttipChangePercentValue.text = "--"
            mBinding.tvCharttipChangeAmountValue.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            mBinding.tvCharttipChangePercentValue.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
        }
        //振幅： 历史振幅：(同一周期最高价-同一周期最低价)/同一周期开盘价*100%；  当前振幅：(当前周期最高价-当前周期最低价)/当前周期开盘价*100%
        val amplitude = if (openValue.toDoubleCatching() != 0.0) {
            (highValue.mathSub(lowValue)).mathDiv(openValue, 4)
        } else {
            "0.0"
        }
        val amplitudeStr = (amplitude.mathMul("100")).formatProductPrice(2, false, "0.00") + "%"
        mBinding.tvCharttipAmplitudeValue.text = amplitudeStr
    }

    private fun baseCrossLineDismiss() {
        mBinding.clCharttipFloat.isVisible = false
        crossBuryPoint()
    }

    private fun baseCrossIndicateYScale(crossLineY: Int, height: Float, yMin: Float, yMax: Float, digits: Int): String? {
        val crossPrice = (yMax - yMin) / height * (height - crossLineY) + yMin
        return crossPrice.numFormat(digits, false)
    }

    private fun baseCrossIndicateXScale(crossIndexInScreen: Int, drawPointIndex: Int): String? {
        return TimeUtil.formatDateTime(
            (KLineDataUtils.mainList.getOrNull(crossIndexInScreen + drawPointIndex)?.timestamp).toLongCatching() * 1000 -
                    AppUtil.getTimeZoneRawOffsetToHour() * 60 * 60 * 1000, "dd/MM HH:mm"
        )
    }
// ---------------------------- Cross Line 回调 提取出来的公共部分 --------------------------------------------------------

    private fun initCrossLine(context: Context?, mChartViewImp: ChartViewImp, mChartSubViewImp: ChartViewImp, mVolumeChart: ChartViewImp, mChartTimeViewImp: ChartViewImp, dataBean: ShareProductData?) {
        context ?: return
        // 主图十字光标
        val crossLine = mChartViewImp.crossLine
        crossLine.setLineColor(ChartUIParamUtil.crossLineColor)
        crossLine.setTextBackgroundColor(ChartUIParamUtil.crossLineScaleBgColor)
        crossLine.setTextColor(ChartUIParamUtil.crossLineScaleColor)
        crossLine.setData(dataBean)
        // 跟随手势随意data
        crossLine.setLatitudeFollowData(false)

        // 副视图的十字线
        val subCrossLine = mChartSubViewImp.crossLine
        subCrossLine.setLineColor(ChartUIParamUtil.crossLineColor)
        subCrossLine.setTextBackgroundColor(ChartUIParamUtil.crossLineScaleBgColor)
        subCrossLine.setTextColor(ChartUIParamUtil.crossLineScaleColor)
        subCrossLine.setData(dataBean)
        //        subCrossLine.setShowLatitude(false)
        subCrossLine.setLatitudeFollowData(false)

        // 交易量图的十字线
        val volumeCrossLine = mVolumeChart.crossLine
        volumeCrossLine.setLineColor(ChartUIParamUtil.crossLineColor)
        volumeCrossLine.setTextBackgroundColor(ChartUIParamUtil.crossLineScaleBgColor)
        volumeCrossLine.setTextColor(ChartUIParamUtil.crossLineScaleColor)
        volumeCrossLine.setData(dataBean)
        //        volumeCrossLine.setShowLatitude(false)
        volumeCrossLine.setLatitudeFollowData(false)

        // 分时图十字光标
        val timeCrossLine = mChartTimeViewImp.crossLine
        timeCrossLine.setLineColor(ChartUIParamUtil.crossLineColor)
        timeCrossLine.setTextBackgroundColor(ChartUIParamUtil.crossLineScaleBgColor)
        timeCrossLine.setTextColor(ChartUIParamUtil.crossLineScaleColor)
        timeCrossLine.setData(dataBean)
        timeCrossLine.setLatitudeFollowData(false)
    }

    private fun initChartView(context: Context?, mChartViewImp: ChartViewImp, mChartSubViewImp: ChartViewImp, mVolumeChart: ChartViewImp, mChartTimeViewImp: ChartViewImp) {
        context ?: return
        // 打开竖屏的功能限制
        //        mChartViewImp.isPortraitScreen = true
        //        mChartSubViewImp.isPortraitScreen = true
        //        mChartTimeViewImp.isPortraitScreen = true
        //        mVolumeChart.isPortraitScreen = true

        mChartViewImp.isFuncUnlock = true
        mChartViewImp.setCoordinateBackground(ChartUIParamUtil.coordinateBgColor)
        mChartSubViewImp.setCoordinateBackground(ChartUIParamUtil.coordinateBgColor)
        mChartTimeViewImp.setCoordinateBackground(ChartUIParamUtil.coordinateBgColor)
        mVolumeChart.setCoordinateBackground(ChartUIParamUtil.coordinateBgColor)

        // 分时图
        mChartTimeViewImp.setMarginRight(ChartUIParamUtil.coordinateMarginRightDp.dp2px())
        // 设置坐标系的线的样式
//        mChartTimeViewImp.setCoordinateLineEffect(ChartUIParamUtil.commonDashEffect)
        // 设置坐标系文字居中模式
        mChartTimeViewImp.setCoordinateTextGravity(Coordinates.TextGravity.VERTICAL_CENTER_LINE)
        mChartTimeViewImp.type = ChartType.TIME_CHART
        mChartTimeViewImp.setCoordinateLineColor(ChartUIParamUtil.coordinateLineColor)
        mChartTimeViewImp.setCoordinateTextColor(ChartUIParamUtil.coordinateScaleColor)
        mChartTimeViewImp.setCoordinateLatitudeNum(ChartUIParamUtil.timeCoordinateLatitudeNum)
        mChartTimeViewImp.setCoordinateLongitudeNum(ChartUIParamUtil.timeCoordinateLongitudeNum)
        mChartTimeViewImp.setYPaddingPercent(ChartUIParamUtil.paddingPercent)
        timeChartAdapter = MyFocusedCoordinateAdapter(mViewModel.data)
        mChartTimeViewImp.setCoordinateScaleAdapter(timeChartAdapter)
        mChartTimeViewImp.setXScaleAdapter { scaleIndex, listIndex, indexToEnd, _ ->
            timeChartXScaleHandle(scaleIndex, listIndex, indexToEnd)
        }
        mChartTimeViewImp.invalidate()

        // 设置主图副图右边距
        mChartViewImp.setMarginRight(ChartUIParamUtil.coordinateMarginRightDp.dp2px())
        mChartViewImp.setCoordinateRightTextSize(10f)
        mChartViewImp.type = ChartType.MAIN_CHART
        // 虚线
//        mChartViewImp.setCoordinateLineEffect(ChartUIParamUtil.commonDashEffect)
        mChartViewImp.setCoordinateTextGravity(Coordinates.TextGravity.VERTICAL_CENTER_LINE)
        mChartViewImp.setCoordinateLineColor(ChartUIParamUtil.coordinateLineColor)
        mChartViewImp.setCoordinateTextColor(ChartUIParamUtil.coordinateScaleColor)
        mChartViewImp.setCoordinateLatitudeNum(ChartUIParamUtil.mainCoordinateLatitudeNum)
        mChartViewImp.setCoordinateLongitudeNum(ChartUIParamUtil.mainCoordinateLongitudeNum)
        mChartViewImp.setYPaddingPercent(ChartUIParamUtil.paddingPercent)
        mainChartAdapter = MyFocusedCoordinateAdapter(mViewModel.data)     // MyExtremeCalculator
        // 设置坐标系的刻度适配器
        mChartViewImp.setCoordinateScaleAdapter(mainChartAdapter)
        mChartViewImp.setCoordinateBottomTextSize(0f)   // Coordinates相关实现已经注释掉了
        mChartViewImp.setXScaleAdapter { scaleIndex, listIndex, indexToEnd, obj ->
            mainChartXScaleHandle(scaleIndex, listIndex, indexToEnd, obj)
        }
        // invalidate图表
        mChartViewImp.invalidate()

        // 副图跟随滑动
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartSubViewImp.type = ChartType.SUB_CHART
        mChartSubViewImp.setMarginRight(ChartUIParamUtil.coordinateMarginRightDp.dp2px())
//        mChartSubViewImp.setCoordinateLineEffect(ChartUIParamUtil.commonDashEffect)
        mChartSubViewImp.setCoordinateTextGravity(Coordinates.TextGravity.VERTICAL_CENTER_LINE)
        mChartSubViewImp.setCoordinateLineColor(ChartUIParamUtil.coordinateLineColor)
        mChartSubViewImp.setCoordinateTextColor(ChartUIParamUtil.coordinateScaleColor)
        mChartSubViewImp.setCoordinateLatitudeNum(ChartUIParamUtil.subCoordinateLatitudeNum)
        mChartSubViewImp.setCoordinateLongitudeNum(ChartUIParamUtil.subCoordinateLongitudeNum)
        subChartAdapter = MyFocusedCoordinateAdapter(mViewModel.data)
        mChartSubViewImp.setCoordinateScaleAdapter(subChartAdapter)
        mChartSubViewImp.setCoordinateBottomTextSize(0f)   // Coordinates相关实现已经注释掉了
        mChartSubViewImp.invalidate()

        mVolumeChart.followTouch(mChartViewImp)
        mVolumeChart.type = ChartType.VOLUME_CHART
        mVolumeChart.setMarginRight(ChartUIParamUtil.coordinateMarginRightDp.dp2px())
//        mVolumeChart.setCoordinateLineEffect(ChartUIParamUtil.commonDashEffect)
        mVolumeChart.setCoordinateTextGravity(Coordinates.TextGravity.VERTICAL_CENTER_LINE)
        mVolumeChart.setCoordinateLineColor(ChartUIParamUtil.coordinateLineColor)
        mVolumeChart.setCoordinateTextColor(ChartUIParamUtil.coordinateScaleColor)
        volumeChartAdapter = VolumeCoordinateAdapter(mViewModel.data)
        mVolumeChart.setCoordinateLatitudeNum(ChartUIParamUtil.volumeCoordinateLatitudeNum)
        mVolumeChart.setCoordinateLongitudeNum(ChartUIParamUtil.volumeCoordinateLongitudeNum)
        mVolumeChart.setCoordinateScaleAdapter(volumeChartAdapter)
        mVolumeChart.setCoordinateBottomTextSize(0f)   // Coordinates相关实现已经注释掉了
        mVolumeChart.invalidate()

        mChartViewImp.setBindSubView(mChartSubViewImp)
        mChartViewImp.setBindVolumeView(mVolumeChart)
        mChartSubViewImp.setBindMainView(mChartViewImp)
        mChartSubViewImp.setBindVolumeView(mVolumeChart)
        mVolumeChart.setBindMainView(mChartViewImp)
        mVolumeChart.setBindSubView(mChartSubViewImp)
    }

    private fun mainChartXScaleHandle(scaleIndex: Int, listIndex: Int, indexToEnd: Int, obj: Any) {
        if (scaleIndex == 0) {
            mIndexToEnd = indexToEnd
            if (listIndex == 0 && mViewModel.isRequestEnd && !mViewModel.isDataEnd) {
                mViewModel.symbolsChart(mViewModel.data?.symbol.ifNull(), true, false, mViewModel.selectedInterval, true)
            }
        }
        if (obj is CandleLine.CandleLineBean) {
            val bean = obj
            if (mViewModel.selectedInterval == "1D") {                                              //日线
                var time = TimeUtil.formatDateTime(bean.mt4TimeMills.ifNull(), "dd/MM")
                if (scaleIndex == 0) {
                    mBinding.tvMainScaleLeft.text = time
                } else if (scaleIndex == 1) {
                    mBinding.tvMainScaleMiddle.text = time
                } else {
                    mBinding.tvMainScaleRight.text = time
                }
            } else if (mViewModel.selectedInterval == "1W" || mViewModel.selectedInterval == "1M") { //周线 & 月线
                var time = TimeUtil.formatDateTime(bean.mt4TimeMills.ifNull(), "MM/yyyy")
                if (scaleIndex == 0) {
                    mBinding.tvMainScaleLeft.text = time
                } else if (scaleIndex == 1) {
                    mBinding.tvMainScaleMiddle.text = time
                } else {
                    mBinding.tvMainScaleRight.text = time
                }
            } else {                                                                                //分时 & 1分钟~4小时线
                var time = TimeUtil.formatDateTime(bean.mt4TimeMills.ifNull(), "dd/MM HH:mm")
                if (scaleIndex == 0) {
                    mBinding.tvMainScaleLeft.text = time
                } else if (scaleIndex == 1) {
                    mBinding.tvMainScaleMiddle.text = time
                } else {
                    mBinding.tvMainScaleRight.text = time
                }
            }
        }
    }

    private fun timeChartXScaleHandle(scaleIndex: Int, listIndex: Int, indexToEnd: Int) {
        if (scaleIndex == 0) {
            mIndexToEnd = indexToEnd
            if (listIndex == 0 && mViewModel.isRequestEnd && !mViewModel.isDataEnd) {
                mViewModel.symbolsChart(mViewModel.data?.symbol.ifNull(), true, false, mViewModel.selectedInterval, true)
            }
        }
        val bean = mViewModel.timeShareList.getOrNull(listIndex)
        if (mViewModel.selectedInterval == "1D") {                                              //日线
            var time = TimeUtil.formatDateTime(bean?.mt4TimeMills.ifNull(), "dd/MM")
            if (scaleIndex == 0) {
                mBinding.tvTimeScaleLeft.text = time
            } else if (scaleIndex == 1) {
                mBinding.tvTimeScaleMiddle.text = time
            } else {
                mBinding.tvTimeScaleRight.text = time
            }
        } else if (mViewModel.selectedInterval == "1W" || mViewModel.selectedInterval == "1M") { //周线 & 月线
            var time = TimeUtil.formatDateTime(bean?.mt4TimeMills.ifNull(), "MM/yyyy")
            if (scaleIndex == 0) {
                mBinding.tvTimeScaleLeft.text = time
            } else if (scaleIndex == 1) {
                mBinding.tvTimeScaleMiddle.text = time
            } else {
                mBinding.tvTimeScaleRight.text = time
            }
        } else {                                                                                //分时 & 1分钟~4小时线
            var time = TimeUtil.formatDateTime(bean?.mt4TimeMills.ifNull(), "dd/MM HH:mm")
            if (scaleIndex == 0) {
                mBinding.tvTimeScaleLeft.text = time
            } else if (scaleIndex == 1) {
                mBinding.tvTimeScaleMiddle.text = time
            } else {
                mBinding.tvTimeScaleRight.text = time
            }
        }
    }

    private fun setAdapterDigits() {
        timeChartAdapter?.setKeepNums(ChartUIParamUtil.digits)
        mainChartAdapter?.setKeepNums(ChartUIParamUtil.digits)
        subChartAdapter?.setKeepNums(ChartUIParamUtil.digits)
        volumeChartAdapter?.setKeepNums(0)
    }

    private fun selectInterval(interval: String) {
        mBinding.tv5M.setBackgroundResource(
            if (interval == "5m") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv30M.setBackgroundResource(
            if (interval == "30m") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv4H.setBackgroundResource(
            if (interval == "4h") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        mBinding.tv1M.setBackgroundResource(
            if (interval == "1M") R.drawable.shape_stroke_c00c79c_solid_c1f00c79c_r100
            else R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        )
        closeCrossLine()
        setMoreInterval(interval)
        // 切换加载数据
        changeChartInterval(interval)
        // 埋点
        if (interval != "Tick") {
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_TIME_BTN_CLICK, JSONObject().apply {
                put("time_interval", interval)
                put(SensorsConstant.Key.SCREEN_ORIENTATION, "Portrait")
            })
        }
    }

    private fun retract() {
        // 收起
        mBinding.llMoreInterval.isVisible = false
        footerView.ivArrow.rotation = 0f
        isShowMore = false
    }

    private fun setMoreInterval(interval: String) {
        // 收起
        retract()
        // 赋值
        val isSelectedMore = interval in mViewModel.moreIntervalList
        intervalAdapter.changeSelected(interval)    // 同步选中状态
        if (isSelectedMore) {
            footerView.tvTab.text = interval
            footerView.tvTab.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
            footerView.tvTab.setFontG600()
            footerView.root.setBackgroundResource(R.drawable.draw_shape_stroke_c1f1e1e1e_c1fffffff_solid_c0a1e1e1e_c0affffff_r100)
        } else {
            footerView.tvTab.text = getString(R.string.more)
            footerView.tvTab.setTextColor(AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff))
            footerView.tvTab.setFontG500()
            footerView.root.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    // 初始化分时图数据
    private fun initTimeShareData() {
        mViewModel.timeShareList.clear()
        KLineDataUtils.timeShareList.clear()
        mViewModel.timeShareList.addAll(mViewModel.getTimeShareDataList())
        timeIndicatorLine?.setKeepNums(ChartUIParamUtil.digits)
        timeIndicatorLine?.dataList = mViewModel.timeShareList
        timeIndicatorLine?.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.bidLineDisplay)
        timeBuyLine?.setKeepNums(ChartUIParamUtil.digits)
        timeBuyLine?.dataList = mViewModel.timeShareList
        timeBuyLine?.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.askLineDisplay)
    }

    private fun refreshChartViewImpData() {
        if (KLineDataUtils.userData == null) {
            KLineDataUtils.userData = KLineSettingData.getUserData()
        }
        mViewModel.candleDataList.clear()
        mViewModel.candleDataList.addAll(mViewModel.getCandleDataList())

        candleLine?.dataList = mViewModel.candleDataList
        indicatorLine?.setKeepNums(ChartUIParamUtil.digits)
        indicatorLine?.dataList = mViewModel.candleDataList
        buyLine?.setKeepNums(ChartUIParamUtil.digits)
        buyLine?.dataList = mViewModel.candleDataList
        indicatorLine?.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.bidLineDisplay)
        buyLine?.setShow(KLineDataUtils.userData != null && KLineDataUtils.userData.askLineDisplay)

        if (mIndexToEnd != -1) {
            candleLine?.setDrawPointIndex(ChartUIParamUtil.chartShowEndPosition - mIndexToEnd)
        } else if (mViewModel.candleDataList.size < candleLine?.shownPointNums.ifNull()) {
            candleLine?.setDrawPointIndex(0)
        } else {
            candleLine?.setDrawPointIndex(ChartUIParamUtil.chartShowEndPosition - candleLine?.shownPointNums.ifNull())
        }
        if (candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.minShownPointNums) {
            ChartUIParamUtil.defaultShowPointNums = candleLine?.shownPointNums.ifNull()
        }
        switchMainChartType(mViewModel.chartTypeName)
        // 再加载副图
        initSubChartTypeView(mViewModel.chartSubTypeName)
    }

    private fun initSubChartTypeView(type: String) {
        if (type == "VOL") {
            showVolumeChart(true)
        } else {
            showVolumeChart(false)
        }
        if (candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.defaultShowPointNums) ChartUIParamUtil.defaultShowPointNums = candleLine?.shownPointNums.ifNull()
        switchSubChartType(type)
    }

    private fun showVolumeChart(display: Boolean) {
        mBinding.volumeChart.isVisible = display
        mBinding.chartSubView.isVisible = !display
        if (display) {
            mBinding.chartSubView.removeAllChildren()
        }
    }

    private fun switchMainChartType(typeName: String) {
        when (typeName) {
            "MA" -> initMaChart()
            "BOLL" -> initBollChart()
            "MIKE" -> initMikeChart()
            "BBI" -> initBbiChart()
        }
        initMainChartTitle()
    }

    // 切勿直接调用该方法 如切换副图请调用 initSubChartTypeView(type: String)
    private fun switchSubChartType(typeName: String) {
        when (typeName) {
            "VOL" -> initVolumeChart()
            "MACD" -> {
                initMacdChart()
            }

            "KDJ" -> {
                initKdjChart()
            }

            "RSI" -> {
                initRsiChart()
            }

            "CCI" -> {
                initCciChart()
            }

            "KD" -> {
                initKdChart()
            }
        }
        initSubChartTitle()
    }

    // 初始化分时图
    private fun initTimeShareChart() {
        mBinding.chartTimeView.removeAllChildren()
        timeBrokenLine = mViewModel.getTimeBrokenLine(context)
        timeBrokenLine?.requestFocused()
        timeBrokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mBinding.chartTimeView, mViewModel.data)) // 实际分时图的最大最小值显示不是通过这个计算类计算的 而是在BrokenLine中自定义方法计算的
        var rate = 0f
        val dataBean = mViewModel.data
        if (dataBean != null) {
            rate = dataBean.rose
        }
        if (rate < 0) {
            timeBrokenLine?.setLineColor(ContextCompat.getColor(requireContext(), R.color.ce35728))
            timeBrokenLine?.setLineFillColor(ContextCompat.getColor(requireContext(), R.color.ce35728), ContextCompat.getColor(requireContext(), R.color.transparent), 100)
        } else if (rate >= 0) { // 这里先与iOS保持一致，后面由产品决定是否会展示灰色
            timeBrokenLine?.setLineColor(ContextCompat.getColor(requireContext(), R.color.c00c79c))
            timeBrokenLine?.setLineFillColor(ContextCompat.getColor(requireContext(), R.color.c00c79c), ContextCompat.getColor(requireContext(), R.color.transparent), 100)
        } else {
            val isLight = AppUtil.isLightTheme()
            timeBrokenLine?.setLineColor(
                if (isLight) ContextCompat.getColor(requireContext(), R.color.c731e1e1e)
                else ContextCompat.getColor(requireContext(), R.color.c61ffffff)
            )
            timeBrokenLine?.setLineFillColor(
                if (isLight) ContextCompat.getColor(requireContext(), R.color.c731e1e1e)
                else ContextCompat.getColor(requireContext(), R.color.c61ffffff), ContextCompat.getColor(requireContext(), R.color.transparent), 100
            )
        }
        mBinding.chartTimeView.addChild(timeBrokenLine)
        mBinding.chartTimeView.addChild(timeBuyLine)
        mBinding.chartTimeView.addChild(timeIndicatorLine)
    }

    var ma5: BrokenLine? = null
    var ma10: BrokenLine? = null
    var ma20: BrokenLine? = null
    var ma30: BrokenLine? = null
    var maLineVisible = false

    // 初始化MA主图
    private fun initMaChart() {
        val mChartViewImp = mBinding.chartview
        chartTypeAdapter.changeMainSelected("MA")
        mChartViewImp.removeAllChildren()
        ma5 = mViewModel.getBrokenLine(context, KLineDataUtils.mA5List, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        ma10 = mViewModel.getBrokenLine(context, KLineDataUtils.mA10List, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 })
        ma20 = mViewModel.getBrokenLine(context, KLineDataUtils.mA20List, ChartUIParamUtil.kColorArray.getOrElse(2) { -1 })
        ma30 = mViewModel.getBrokenLine(context, KLineDataUtils.mA30List, ChartUIParamUtil.kColorArray.getOrElse(3) { -1 })
        mChartViewImp.addChild(ma5)
        mChartViewImp.addChild(ma10)
        mChartViewImp.addChild(ma20)
        mChartViewImp.addChild(ma30)
        //        mChartViewImp.addChild(model.getBrokenLine(KLineDataUtils.mA60List, kClolrArray[4]));     //因指标值去掉60，所以相应的这根线也去掉
        mChartViewImp.addChild(candleLine)
        candleLine?.requestFocused()
        candleLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartViewImp, mViewModel.data))
        mChartViewImp.addChild(buyLine)
        mChartViewImp.addChild(indicatorLine)
    }

    var bollMid: BrokenLine? = null
    var bollUpper: BrokenLine? = null
    var bollLower: BrokenLine? = null
    var bollLineVisible = false

    // 初始化BOLL主图
    private fun initBollChart() {
        val mChartViewImp = mBinding.chartview
        chartTypeAdapter.changeMainSelected("BOLL")
        mChartViewImp.removeAllChildren()
        bollMid = mViewModel.getBrokenLine(context, KLineDataUtils.midList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        bollUpper = mViewModel.getBrokenLine(context, KLineDataUtils.upperList, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 })
        bollLower = mViewModel.getBrokenLine(context, KLineDataUtils.lowerList, ChartUIParamUtil.kColorArray.getOrElse(2) { -1 })
        mChartViewImp.addChild(bollMid)
        mChartViewImp.addChild(bollUpper)
        mChartViewImp.addChild(bollLower)
        mChartViewImp.addChild(candleLine)
        candleLine?.requestFocused()
        candleLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartViewImp, mViewModel.data))
        mChartViewImp.addChild(buyLine)
        mChartViewImp.addChild(indicatorLine)
    }

    var mikeWr: BrokenLine? = null
    var mikeMr: BrokenLine? = null
    var mikeSr: BrokenLine? = null
    var mikeWs: BrokenLine? = null
    var mikeMs: BrokenLine? = null
    var mikeSs: BrokenLine? = null
    var mikeLineVisible = false

    // 初始化MIKE主图
    private fun initMikeChart() {
        val mChartViewImp = mBinding.chartview
        chartTypeAdapter.changeMainSelected("MIKE")
        mChartViewImp.removeAllChildren()
        mikeWr = mViewModel.getBrokenLine(context, KLineDataUtils.wrList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mikeMr = mViewModel.getBrokenLine(context, KLineDataUtils.mrList, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 })
        mikeSr = mViewModel.getBrokenLine(context, KLineDataUtils.srList, ChartUIParamUtil.kColorArray.getOrElse(2) { -1 })
        mikeWs = mViewModel.getBrokenLine(context, KLineDataUtils.wsList, ChartUIParamUtil.kColorArray.getOrElse(3) { -1 })
        mikeMs = mViewModel.getBrokenLine(context, KLineDataUtils.msList, ChartUIParamUtil.kColorArray.getOrElse(4) { -1 })
        mikeSs = mViewModel.getBrokenLine(context, KLineDataUtils.ssList, ChartUIParamUtil.kColorArray.getOrElse(5) { -1 })
        mChartViewImp.addChild(mikeWr)
        mChartViewImp.addChild(mikeMr)
        mChartViewImp.addChild(mikeSr)
        mChartViewImp.addChild(mikeWs)
        mChartViewImp.addChild(mikeMs)
        mChartViewImp.addChild(mikeSs)
        mChartViewImp.addChild(candleLine)
        candleLine?.requestFocused()
        candleLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartViewImp, mViewModel.data))
        mChartViewImp.addChild(buyLine)
        mChartViewImp.addChild(indicatorLine)
    }

    var bbiline: BrokenLine? = null
    var bbiLineVisible = false

    // 初始化BBI主图
    private fun initBbiChart() {
        val mChartViewImp = mBinding.chartview
        chartTypeAdapter.changeMainSelected("BBI")
        mChartViewImp.removeAllChildren()
        bbiline = mViewModel.getBrokenLine(context, KLineDataUtils.bbiList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mChartViewImp.addChild(bbiline)
        mChartViewImp.addChild(candleLine)
        candleLine?.requestFocused()
        candleLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartViewImp, mViewModel.data))
        mChartViewImp.addChild(buyLine)
        mChartViewImp.addChild(indicatorLine)
    }

    private fun mainChartTypeUnselected() {
        chartTypeAdapter.changeMainSelected("")
        maLineVisible = false
        bollLineVisible = false
        mikeLineVisible = false
        bbiLineVisible = false
    }

    private fun mainChartSingleSelect(chartType: String) {
        maLineVisible = chartType == "MA"
        bollLineVisible = chartType == "BOLL"
        mikeLineVisible = chartType == "MIKE"
        bbiLineVisible = chartType == "BBI"
    }

    // 初始化交易量图
    private fun initVolumeChart() {
        val mChartViewImp = mBinding.chartview
        val mVolumeChart = mBinding.volumeChart
        mVolumeChart.removeAllChildren()
        val histogram = mViewModel.getHistogram(context)
        if (mIndexToEnd != -1) {
            histogram?.setDrawPointIndex(ChartUIParamUtil.chartShowEndPosition - mIndexToEnd)
        } else if (mViewModel.candleDataList.size < candleLine?.shownPointNums.ifNull()) {
            histogram?.setDrawPointIndex(0)
        } else {
            histogram?.setDrawPointIndex(ChartUIParamUtil.chartShowEndPosition - candleLine?.shownPointNums.ifNull())
        }
        mVolumeChart.addChild(histogram)
        histogram?.requestFocused()
        histogram?.setExtremeCalculatorInterface(MyExtremeCalculator(mVolumeChart, mViewModel.data))
        mVolumeChart.followTouch(mChartViewImp)
        mChartViewImp.requestSyncDataWithFocused()
        mChartViewImp.invalidate()
        mVolumeChart.invalidate()
    }

    // 初始化MACD副图
    private fun initMacdChart() {
        val mChartViewImp = mBinding.chartview
        val mChartSubViewImp = mBinding.chartSubView
        macd0IndicatorLine?.setKeepNums(ChartUIParamUtil.digits)
        mChartSubViewImp.removeAllChildren()
        val macdHistogram = mViewModel.getMacdHistogram(context)
        mChartSubViewImp.addChild(macdHistogram)
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.diffList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 }))
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.deaList, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 }))
        mChartSubViewImp.addChild(macd0IndicatorLine)
        macdHistogram?.requestFocused()
        macdHistogram?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartSubViewImp, mViewModel.data))
        mChartViewImp.requestSyncDataWithFocused()
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartViewImp.invalidate()
        mChartSubViewImp.invalidate()
    }

    // 初始化KDJ副图
    private fun initKdjChart() {
        val mChartViewImp = mBinding.chartview
        val mChartSubViewImp = mBinding.chartSubView
        mChartSubViewImp.removeAllChildren()
        val brokenLine = mViewModel.getBrokenLine(context, KLineDataUtils.kList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mChartSubViewImp.addChild(brokenLine)
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.dList, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 }))
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.jList, ChartUIParamUtil.kColorArray.getOrElse(2) { -1 }))
        brokenLine?.requestFocused()
        brokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartSubViewImp, mViewModel.data))
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartViewImp.requestSyncDataWithFocused()
        mChartViewImp.invalidate()
        mChartSubViewImp.invalidate()
    }

    // 初始化RSI副图
    private fun initRsiChart() {
        val mChartViewImp = mBinding.chartview
        val mChartSubViewImp = mBinding.chartSubView
        mChartSubViewImp.removeAllChildren()
        val brokenLine = mViewModel.getBrokenLine(context, KLineDataUtils.rsi1List, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mChartSubViewImp.addChild(brokenLine)
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.rsi2List, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 }))
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.rsi3List, ChartUIParamUtil.kColorArray.getOrElse(2) { -1 }))
        brokenLine?.requestFocused()
        brokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartSubViewImp, mViewModel.data))
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartViewImp.requestSyncDataWithFocused()
        mChartViewImp.invalidate()
        mChartSubViewImp.invalidate()
    }

    // 初始化CCI副图
    private fun initCciChart() {
        val mChartViewImp = mBinding.chartview
        val mChartSubViewImp = mBinding.chartSubView
        mChartSubViewImp.removeAllChildren()
        val brokenLine = mViewModel.getBrokenLine(context, KLineDataUtils.cciList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mChartSubViewImp.addChild(brokenLine)
        brokenLine?.requestFocused()
        brokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartSubViewImp, mViewModel.data))
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartViewImp.requestSyncDataWithFocused()
        mChartViewImp.invalidate()
        mChartSubViewImp.invalidate()
    }

    // 初始化KD副图
    private fun initKdChart() {
        val mChartViewImp = mBinding.chartview
        val mChartSubViewImp = mBinding.chartSubView
        mChartSubViewImp.removeAllChildren()
        val brokenLine = mViewModel.getBrokenLine(context, KLineDataUtils.kList, ChartUIParamUtil.kColorArray.getOrElse(0) { -1 })
        mChartSubViewImp.addChild(brokenLine)
        mChartSubViewImp.addChild(mViewModel.getBrokenLine(context, KLineDataUtils.dList, ChartUIParamUtil.kColorArray.getOrElse(1) { -1 }))
        brokenLine?.requestFocused()
        brokenLine?.setExtremeCalculatorInterface(MyExtremeCalculator(mChartSubViewImp, mViewModel.data))
        mChartSubViewImp.followTouch(mChartViewImp)
        mChartViewImp.requestSyncDataWithFocused()
        mChartViewImp.invalidate()
        mChartSubViewImp.invalidate()
    }

    private fun updateProdInfo() {
        if (InitHelper.isNotSuccess() || activityViewModel.isSwitching) return
        val dataBean = mViewModel.data ?: return
        mBinding.run {
            val bid = if (dataBean.bidUI == "-") Constants.DOUBLE_LINE else dataBean.bidUI
            tvBid.text = dataBean.bidUI
            tvAsk.text = dataBean.askUI
            tvSpread.text = OrderUtil.getSpread(dataBean.ask, dataBean.bid, dataBean.digits)

            tvBoardSellPrice.text = bid
            tvBoardHigh.text = dataBean.maxprice.numFormat(dataBean.digits, false)
            tvBoardClose.text = bid
            tvBoardLow.text = dataBean.minprice.numFormat(dataBean.digits, false)
            val rose = dataBean.rose
            val add = if (rose > 0) "+" else ""
            val roseStr = " ($add${dataBean.roseUI}%)"
            val diff = dataBean.diff
            val add2 = if (diff > 0 && dataBean.diffUI != Constants.DOUBLE_LINE) "+" else ""
            tvBoardDiff.text = add2 + dataBean.diffUI + roseStr
            activityViewModel.setTextColorByDiff(context, tvBoardDiff, diff.toDouble())

            tvBid.setCompoundDrawablesWithIntrinsicBounds(
                null, null,
                if (dataBean.bidType == 2) arrowDownDrawable else arrowUpDrawable, null
            )
            tvAsk.setCompoundDrawablesWithIntrinsicBounds(
                null, null,
                if (dataBean.askType == 2) arrowDownDrawable else arrowUpDrawable, null
            )

            //交易时间
            if (dataBean.marketClose) { //闭市中
                llBid.background = draw_shape_c731e1e1e_c61ffffff_r100
                llAsk.background = draw_shape_c731e1e1e_c61ffffff_r100

                tvTradeTime.text = getString(R.string.market_closed)
            } else if ("1" == dataBean.enable) {    // 只可平仓
                llBid.background = draw_shape_c731e1e1e_c61ffffff_r100
                llAsk.background = draw_shape_c731e1e1e_c61ffffff_r100
            } else { //交易时间段内
                llBid.background = shape_cf44040_r100
                llAsk.background = shape_c00c79c_r100

                val realTime = dataBean.lasttime
                if ("0" == realTime) {
                    tvTradeTime.visibility = View.INVISIBLE     // 因右侧开、收盘指数要根据这个TextView位置对齐显示
                } else {
                    tvTradeTime.visibility = View.VISIBLE
                    val formatTime = TimeUtil.getKlineTradeDate(realTime, "MM/dd HH:mm:ss")
                    tvTradeTime.text = Formatter(Locale.ENGLISH).format(getString(R.string.trading) + " (%s GMT+%d)", formatTime, Constants.season).toString()
                }
            }
        }

        // 现价线
        if (mViewModel.candleDataList.isNotEmpty() && (dataBean.ask != 0f && dataBean.bid != 0f)) {
            val candleLineBean = mViewModel.candleDataList.getOrNull(mViewModel.candleDataList.size - 1)
            if (dataBean.ask != 0f) {   // 因为ask不为0 所以originalAsk也一定不为0
                candleLineBean?.originalAsk = dataBean.originalAsk
            }
            if (dataBean.bid != 0f && dataBean.originalBid != 0f) {
                candleLineBean?.originalBid = dataBean.originalBid
                candleLineBean?.closePrice = dataBean.originalBid
                if (dataBean.bid > candleLineBean?.heightPrice.ifNull()) {
                    candleLineBean?.heightPrice = dataBean.bid
                }
                if (dataBean.bid < candleLineBean?.lowPrice.ifNull()) {
                    candleLineBean?.lowPrice = dataBean.bid
                }
                candleLine?.dataList = mViewModel.candleDataList
//                    indicatorLine?.dataList = mViewModel.candleDataList
//                    buyLine?.dataList = mViewModel.candleDataList
                mBinding.chartview.requestSyncDataWithFocused()
                mBinding.chartview.invalidate()

                // 刷新交易量副图
                mBinding.volumeChart.children.firstOrNull {
                    it is HistogramView
                }?.run {
                    dataList = mViewModel.getHistogramDataList()
                    mBinding.volumeChart.invalidate()
                }

                // 刷新MACD副图
                mBinding.chartSubView.children.firstOrNull {
                    it is MACDHistogram
                }?.run {
                    dataList = mViewModel.getMacdDataList()
                    mBinding.volumeChart.invalidate()
                }
            }
        }

        val mainList = KLineDataUtils.mainList
        // 更改蜡烛图数据
        if (mainList == null || mainList.isEmpty()) return
        val mainBean = mainList.getOrNull(mainList.size - 1)
        if (dataBean.bid != 0f && dataBean.originalBid != 0f) {
            mainBean?.close = "${dataBean.originalBid}".toDoubleCatching().ifNull()
            if (mViewModel.isTimeShare && mViewModel.timeShareList.isNotEmpty()) {
                if (KLineDataUtils.timeShareList.size >= mViewModel.timeShareList.size) {
                    KLineDataUtils.timeShareList[mViewModel.timeShareList.size - 1] = dataBean.originalBid.toString()
                }
                val timeShareBean = mViewModel.timeShareList.getOrNull(mViewModel.timeShareList.size - 1)
                timeShareBean?.close = dataBean.originalBid.toDouble()
                timeShareBean?.originalAsk = dataBean.originalAsk.toDouble()
//                    timeIndicatorLine?.dataList = mViewModel.timeShareList
//                    timeBuyLine?.dataList = mViewModel.timeShareList
                mBinding.chartTimeView.requestSyncDataWithFocused()
                mBinding.chartTimeView.invalidate()
            }
            if (dataBean.bid > mainBean?.high.ifNull()) mainBean?.high = dataBean.bid.toDouble()
            if (dataBean.bid < mainBean?.low.ifNull()) mainBean?.low = dataBean.bid.toDouble()
        }
    }

    private fun refreshPriceChange() {
        val dataBean = mViewModel.data
        val data = mViewModel.priceChangeData
        val avaiable = dataBean != null && data != null
        if (avaiable) {
            // 1D
            val close1D = data.yesterdayClose.toDoubleCatching(-1.0)
            if (close1D == -1.0) {
                mBinding.tv1DValue.text = Constants.DOUBLE_LINE
                mBinding.tv1DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close1D
                val rate = diff.div(close1D).times(100)
                mBinding.tv1DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv1DValue, diff.toDouble())
            }
            // 7D
            val close7D = data.weekClose.toDoubleCatching(-1.0)
            if (close7D == -1.0) {
                mBinding.tv7DValue.text = Constants.DOUBLE_LINE
                mBinding.tv7DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close7D
                val rate = diff.div(close7D).times(100)
                mBinding.tv7DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv7DValue, diff.toDouble())
            }
            // 30D
            val close30D = data.monthClose.toDoubleCatching(-1.0)
            if (close30D == -1.0) {
                mBinding.tv30DValue.text = Constants.DOUBLE_LINE
                mBinding.tv30DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            } else {
                val diff = dataBean.bid - close30D
                val rate = diff.div(close30D).times(100)
                mBinding.tv30DValue.text = "${if (diff >= 0) "+" else ""}${rate.numFormat(2, true)}%"
                activityViewModel.setTextColorByDiff(context, mBinding.tv30DValue, diff.toDouble())
            }
        } else {
            mBinding.tv1DValue.text = Constants.DOUBLE_LINE
            mBinding.tv1DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            mBinding.tv7DValue.text = Constants.DOUBLE_LINE
            mBinding.tv7DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
            mBinding.tv30DValue.text = Constants.DOUBLE_LINE
            mBinding.tv30DValue.setTextColor(ContextCompat.getColor(requireContext(), R.color.c1e1e1e))
        }
    }

    // 开启定时，每隔一时间段请求一次数据
    private fun startTimer() {
        mHandler.removeMessages(1000)
        // 1小时以外不用开启定时 && 不是分时图
        if (mViewModel.period <= 60) {
            mHandler.sendEmptyMessageDelayed(1000, (60 * 1000 * mViewModel.period).toLong())
        }
    }

    private fun delayTimer() {
        mHandler.removeMessages(1000)
        // 1小时以外不用开启定时 && 不是分时图
        if (mViewModel.period <= 60) {
            mHandler.sendEmptyMessageDelayed(1000, caclDelayTime())
        }
    }

    private fun caclDelayTime(): Long {
        val second = (60 * mViewModel.period).toLong() // 每个时间段的总计秒数
        val calendar = Calendar.getInstance()
        val currentMinute = calendar.get(Calendar.MINUTE)
        val currentSecond = calendar.get(Calendar.SECOND)
        //        LogUtil.d("wj", "V delay = "+(second - (currentMinute * 60 + currentSecond) % second) * 1000);
        return (second - (currentMinute * 60 + currentSecond) % second) * 1000
    }

    // 利用行情数据画出下一根蜡烛图
    private fun drawNextCandle() {
        val dataBean = mViewModel.data
        if (dataBean?.marketClose == false && !KLineDataUtils.mainList.isEmpty()) {
            val lastBean = KLineDataUtils.mainList.getOrNull(KLineDataUtils.mainList.size - 1)
            val newBean = ChartsBean()
            newBean.open = "${dataBean.originalBid}".toDoubleCatching().ifNull()
            newBean.close = "${dataBean.originalBid}".toDoubleCatching().ifNull()
            newBean.prevClose = lastBean?.close.ifNull()
            newBean.high = "${dataBean.originalBid}".toDoubleCatching().ifNull()
            newBean.low = "${dataBean.originalBid}".toDoubleCatching().ifNull()
            newBean.volume = 0.0
            newBean.timestamp = (lastBean?.timestamp?.toLongCatching().ifNull() + 60 * mViewModel.period).toString()
            KLineDataUtils.mainList.add(newBean)
            drawNextUpdateCrossLine()
            refreshKchart()
        }
    }

    private fun refreshKchart() {
        ChartUIParamUtil.chartShowEndPosition = KLineDataUtils.mainList.size
        if (mViewModel.isTimeShare) {
            // 分时图
            initTimeShareData()
            initTimeShareChart()
        } else {
            IndexLineDataUtil.getInstance().initMaData()
            IndexLineDataUtil.getInstance().initBollData()
            IndexLineDataUtil.getInstance().initMikeData()
            IndexLineDataUtil.getInstance().initBbiData()
            IndexLineDataUtil.getInstance().initMacdData()
            IndexLineDataUtil.getInstance().initKdjData()
            IndexLineDataUtil.getInstance().initRsiData()
            IndexLineDataUtil.getInstance().initCciData()

            refreshChartViewImpData()
        }
        hideLoadDialog()
    }

    private fun initMainChartTitle() {
        mBinding.tvMainChartTitle.text = "${mViewModel.chartTypeName} ${mViewModel.getMainChartTitle()}"
        mBinding.tvMainChartTitle.isVisible = mViewModel.chartTypeName == "BOLL" || mViewModel.chartTypeName == "BBI"
        mBinding.tvMainChartInfo.text = Html.fromHtml(mViewModel.getMainChartText())
    }

    private fun initSubChartTitle() {
        mBinding.tvSubChartTitle.text = "${mViewModel.chartSubTypeName} ${mViewModel.getSubChartTitle()}"
        mBinding.tvSubChartInfo.text = Html.fromHtml(mViewModel.getSubChartText())
    }

    private fun resetRequest(symbol: String, loading: Boolean, isSwitchingSymbol: Boolean = false) {
        mIndexToEnd = -1
        mViewModel.switchChartPeriod(symbol, loading, isSwitchingSymbol)
        mViewModel.tradeOrderTradeEmotion(symbol)
        mViewModel.tradeOrderTradePriceChange(symbol)
    }

    private fun crossBuryPoint() {
        LogEventUtil.setLogEvent(
            BuryPointConstant.V345.TRADE_KLINE_FOCUS_BUTTON_CLICK, bundleOf(
                "Instrument_name" to activityViewModel.symbol,
                "Account_type" to KLineActivity.getPointAccountType()
            )
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventBus(tag: String) {
        // 切换账户时关闭页面(登陆)
        if (NoticeConstants.SWITCH_ACCOUNT == tag) {
            activity?.finish()
            return
        }
        if (NoticeConstants.Init.DATA_SUCCESS_ORDER == tag) {
            // 刷新持仓订单列表
            mViewModel.initOrderData(mBinding.chartview)
            mBinding.chartview.requestSyncDataWithFocused()
            mBinding.chartview.invalidate()
        }
        if (NoticeConstants.Init.DATA_SUCCESS_GOODS == tag) {
            val data = VAUSdkUtil.symbolList().find { it.symbol == activityViewModel.symbol }
            data?.let {
                mViewModel.data = it
            }
        }
        if (NoticeConstants.APP_ON_RESUME == tag && !isShareShow) {
            if (mViewModel.data?.symbol.isNullOrEmpty()) {
                activity?.finish()
            }
        }
        // 切换产品
        if (tag == NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT) {
            mViewModel.data = activityViewModel.data
            if (KLineDataUtils.userData == null) {
                KLineDataUtils.userData = KLineSettingData.getUserData()
            }
            firstLoaded = false
//            initData()    // initData里会走一遍updateProdInfo() 其他代码也没必要走
            resetRequest(activityViewModel.symbol, !firstLoaded, true)
            mViewModel.initOrderData(mBinding.chartview)
            mBinding.chartview.requestSyncDataWithFocused()
            mBinding.chartview.invalidate()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun refreshChart(event: KLineEvent) {
        if (isShareShow) {
            return
        }
        startTimer()
        if (candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.minShownPointNums) {
            ChartUIParamUtil.defaultShowPointNums = candleLine?.shownPointNums.ifNull()
        }

        // 分时类型  （分时，1分，5分）
        val interval = mViewModel.positionToInterval(event.periodPosition)  // 因为这里涉及到 原生K线和TradingView共同返回的处理（position），所以先不改
        mViewModel.selectedInterval = interval
        selectInterval(interval)
        SpManager.putChartTypeText(interval)
        mHandler.removeMessages(1000)
        mIndexToEnd = -1
        mViewModel.switchChartPeriod(mViewModel.data?.symbol.ifNull(), false)  // TODO wj onResume会重新请求数据，这里重复了

        // 同步来自TradingView的LineExtra的设置
        if (event.userDataTV != null) {
            val userDataTV = event.userDataTV
            val lineExtra = userDataTV.line
            if (KLineDataUtils.userData != null) {
                KLineDataUtils.userData.askLineDisplay = lineExtra?.ask?.status == 1
                KLineDataUtils.userData.bidLineDisplay = lineExtra?.bid?.status == 1
                KLineDataUtils.userData.tpLineDisplay = lineExtra?.tp?.status == 1
                KLineDataUtils.userData.slLineDisplay = lineExtra?.sl?.status == 1
                KLineDataUtils.userData.positionLineDisplay = lineExtra?.position?.status == 1
            }
        }
        // 因为TradingView与竖屏和Lite横屏主副图类型不匹配，所以TradingView返回会传空，在这里不进行赋值处理
        if (!TextUtils.isEmpty(event.chartTypeName)) {
            mViewModel.chartTypeName = event.chartTypeName
            switchMainChartType(event.chartTypeName)
            chartTypeAdapter.changeMainSelected(event.chartTypeName)
        }
        // 因为TradingView与竖屏和Lite横屏主副图类型不匹配，所以TradingView返回会传空，在这里不进行赋值处理
        if (!TextUtils.isEmpty(event.chartSubTypeName)) {
            val subTypeName = event.chartSubTypeName
            mViewModel.chartSubTypeName = subTypeName
            // 显示副图
            mBinding.flSubchart.isVisible = true
            initSubChartTypeView(subTypeName)
            chartTypeAdapter.changeSubSelected(subTypeName)
        }
        // 分时图显示
        mBinding.timeChartContainer.visibility = if (event.periodPosition == 0) View.VISIBLE else View.INVISIBLE
        mViewModel.isTimeShare = event.periodPosition == 0
        ChartUIParamUtil.chartShowEndPosition = KLineDataUtils.mainList.size

        mViewModel.setOrderLine(mBinding.chartview)
    }

    private class MyHandler(fragment: KLineChartFragment?) : Handler() {
        var weakReference: WeakReference<Fragment?> = WeakReference<Fragment?>(fragment)

        override fun handleMessage(msg: Message) {
            val fragment = weakReference.get() as? KLineChartFragment
            if (fragment == null || fragment.context == null) return
            when (msg.what) {
                1000 -> {
                    val dataBean = fragment.mViewModel.data
                    if (dataBean?.ask == 0f && dataBean.bid == 0f) return
                    fragment.mViewModel.isAutoRefresh = true
                    ChartUIParamUtil.chartShowEndPosition = fragment.candleLine?.drawPointIndex.ifNull() + fragment.candleLine?.shownPointNums.ifNull()
                    if (fragment.candleLine?.shownPointNums.ifNull() >= ChartUIParamUtil.minShownPointNums) {
                        ChartUIParamUtil.defaultShowPointNums = fragment.candleLine?.shownPointNums.ifNull()
                    }
                    if (KLineDataUtils.isFrontPortrait) {
                        fragment.startTimer()
                        fragment.mViewModel.symbolsChart(fragment.mViewModel.data?.symbol.ifNull(), false, false, fragment.mViewModel.selectedInterval, false)
                        fragment.drawNextCandle()
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
//        Log.i("wj", "onResume: ---------")
        // 控制onResume刷新 切换时自主刷新 不用onResume刷新
        if (isShareShow || activityViewModel.isSwitching) {
            return
        }
        KLineDataUtils.isFrontPortrait = true
//        Log.i("wj", "onResume: ---- data: ${mViewModel.data.json}")

        resetRequest(mViewModel.data?.symbol.ifNull(), !firstLoaded)
    }

    override fun onPause() {
        super.onPause()
        closeCrossLine()
        if (isShareShow) {
            return
        }
        mHandler.removeCallbacksAndMessages(null)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        mHandler.removeCallbacksAndMessages(null)
    }

    private fun indicatorBuryPoint(category: String?, indicator: String?) {
        SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_METRICS_TAB_CLICK, JSONObject().apply {
            put("metrics_tab", indicator)
        })
    }

    private fun intervalBuryPoint() {
        val bundle = Bundle()
        bundle.putString("Account_type", KLineActivity.getPointAccountType())
        bundle.putString("Mode", "Lite-vertical")
        bundle.putString("Timeline", mViewModel.selectedInterval)
        LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_TIMELINE_BUTTON_CLICK, bundle)
    }

    private fun orderBuryPoint(type: String) {
        SensorsDataUtil.track(
            if (type == "sell") {
                SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_SELL_BTN_CLICK
            } else {
                SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_BUY_BTN_CLICK
            }
        )
    }

    private fun sensorsTrack(type: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.ORDER_ID, mViewModel.shareOrderData?.order?:"")
        properties.put(SensorsConstant.Key.ORDER_TYPE, type)
        SensorsDataUtil.track(SensorsConstant.V361.CANCELTPSL_CONFIRMCLICK, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     */
    private fun sensorsTrack(isClickBtnTrack: Boolean, buttonName: String?) {
        try {
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.TRADE_TYPE, getTradeType()) // 交易类型
            properties.put(SensorsConstant.Key.IS_OPTIONAL, if (activityViewModel.isAddOptional) 1 else 0) // 是否自选
            properties.put(SensorsConstant.Key.PRODUCT_GROUP, "") // 交易产品组
            properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, mViewModel.data?.symbol) // 交易产品
            if (isClickBtnTrack) {
                properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
                properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
                properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
                properties.put(SensorsConstant.Key.MKT_ID, "") // 素材id
                properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
                properties.put(SensorsConstant.Key.MKT_RANK, "") // 素材排序
                properties.put(SensorsConstant.Key.TARGET_URL, "") // 跳转链接
                // 交易产品详情页点击 -> 交易产品详情页点击时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_CLICK, properties)
            } else {
                // 交易产品详情页浏览 -> 交易产品详情页加载完成时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_VIEW, properties)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    companion object {
        fun instance(): KLineChartFragment = KLineChartFragment()
    }
}