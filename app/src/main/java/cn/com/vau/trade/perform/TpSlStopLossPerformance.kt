package cn.com.vau.trade.perform

import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.DialogTakeProfitStopLossBinding
import cn.com.vau.trade.dialog.BottomTakeProfitStopLossDialog
import cn.com.vau.trade.ext.ComputeMode
import cn.com.vau.trade.ext.EditState
import cn.com.vau.trade.ext.TpSlUtil
import cn.com.vau.trade.ext.TpSlUtil.createComputeModeMap
import cn.com.vau.trade.ext.TpSlUtil.findNthSubstringIndexOrLast
import cn.com.vau.trade.ext.TpSlUtil.getPnlByProfitLossPrice
import cn.com.vau.trade.ext.TradeType
import cn.com.vau.trade.ext.computeMarketStopLoss
import cn.com.vau.trade.ext.stopLossLevel
import cn.com.vau.trade.ext.toTradeType
import cn.com.vau.trade.viewmodel.TakeProfitStopLossViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathSub
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.widget.dialog.BottomItemBean
import cn.com.vau.util.widget.dialog.BottomListAdapter
import cn.com.vau.util.widget.dialog.base.BottomListDialog

/**
 * 止损
 */
class TpSlStopLossPerformance(
    val activity: FragmentActivity,
    val dialog: BottomTakeProfitStopLossDialog,
    val mBinding: DialogTakeProfitStopLossBinding,
    val mViewModel: TakeProfitStopLossViewModel,
    val callback: ((enable: Boolean) -> Unit)? = null
) : AbsPerformance() {

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(activity, R.attr.color_c731e1e1e_c61ffffff)
    }
    private val color_c1e1e1e_cebffffff by lazy {
        AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff)
    }

    private val computeModeMap: LinkedHashMap<BottomItemBean, ComputeMode> by lazy {
        createComputeModeMap(activity)
    }

    private val computeModeList: MutableList<BottomItemBean> by lazy {
        computeModeMap.keys.toMutableList()
    }

    /**
     * 止损选择计算方式dialog
     */
    private var computeModeDialog: BottomListDialog? = null

    /**
     * 止损选择计算方式dialog adapter
     */
    private val computeModeAdapter: BottomListAdapter by lazy {
        BottomListAdapter(activity, computeModeList, getSlComputeModeSelectIndex()).apply {
            setOnItemClickListener { position, itemBean ->
                val computeMode = computeModeMap[itemBean] ?: return@setOnItemClickListener
                if (mBinding.viewStopLoss.getCurrentComputeMode() == computeMode) {
                    computeModeDialog?.dismissDialog()
                    return@setOnItemClickListener
                }
                mBinding.viewStopLoss.setCurrentComputeMode(computeMode)
                updateModeEdit()
                computeModeDialog?.dismissDialog()
            }
        }
    }

    private fun getSlComputeModeSelectIndex(): Int {
        return computeModeMap.values.toList().indexOf(mBinding.viewStopLoss.getCurrentComputeMode())
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initStopLoss()
        initListener()
    }

    override fun onCallback() {
        //检查止盈止损是否合法
        checkStopLossTip()
    }

    fun initListener() {
        /*止损titleAction监听*/
        mBinding.viewStopLoss.setOnTitleActionClickListener { currentMode: ComputeMode ->
            showComputeModeDialog()
        }

        /*止损输入框监听*/
        mBinding.viewStopLoss.setOnPriceTextChangedListener { s ->
            updateModeEdit()
            checkStopLossTip()
        }

        /* 监听：计算方式焦点变化 */
        mBinding.viewStopLoss.setOnModeFocusChangeListener { hasFocus ->
            if (!hasFocus) {
                updateModeEdit()
            } else {
                updatePriceEdit()
            }
        }

        /*计算方式输入框监听*/
        mBinding.viewStopLoss.setOnComputeModeTextChangedListener { s ->
            updatePriceEdit()
        }

        /*止损开关监听*/
        mBinding.viewStopLoss.setOnSelectedItemListener { select ->
            selectStopLoss(select)
            updateModeEdit()
            KeyboardUtil.hideSoftInput(dialog.getHostWindow())
        }

        /*点击加号，止损价格计算*/
        mBinding.viewStopLoss.setAddOperator { s, computeMode ->
            mBinding.viewStopLoss.setPriceTextNotUpdateModeText(addStopLoss())
            updateModeEdit()
        }

        /*点击减号，止损价格计算*/
        mBinding.viewStopLoss.setSubOperator { s, computeMode ->
            mBinding.viewStopLoss.setPriceTextNotUpdateModeText(subStopLoss())
            updateModeEdit()
        }

    }

    /**
     * 设置止损item数据
     */
    private fun initStopLoss() {
        val orderBean = mViewModel.orderBean ?: return
        /*绑定数据*/
        mBinding.viewStopLoss.initData(mViewModel.productData, orderBean)
        /*标题*/
        mBinding.viewStopLoss.setTitle(activity.getString(R.string.stop_loss))
        /*标题右边按钮*/
        mBinding.viewStopLoss.setCurrentComputeMode(ComputeMode.PNL)
        /*选中状态*/
        val select = orderBean.stopLoss.toDoubleCatching() > 0
        mBinding.viewStopLoss.setSelectedItem(select)
        /*输入框*/
        mBinding.viewStopLoss.setPriceEditTitle("${activity.getString(R.string.price)} (${orderBean.priceCurrency})".arabicReverseTextByFlag(" ").ifNull())
        mBinding.viewStopLoss.setPriceEditHint("${activity.getString(R.string.price)} (${orderBean.priceCurrency})".arabicReverseTextByFlag(" ").ifNull())
        if (select) {
            mBinding.viewStopLoss.setPriceTextNotUpdateModeText(orderBean.stopLoss!!)
        }
        selectStopLoss(select)

        /*计算方式*/
        updateModeEdit()
    }

    /**
     * 设置止损item数据
     */
    private fun selectStopLoss(isSelected: Boolean) {
        val tpSlEditText = mBinding.viewStopLoss.getPriceEditText()
        val stopLossRange = getStopLossRange()
        mBinding.viewStopLoss.setPriceTextNotUpdateModeText(
            if (isSelected) {
                if (TextUtils.isEmpty(tpSlEditText)) {
                    stopLossRange
                } else {
                    tpSlEditText
                }
            } else {
                ""
            }
        )
        if (isSelected) {
            checkStopLossTip()
        }
    }

    /**
     * 点击加号，止损价格计算
     */
    private fun addStopLoss(): String {
        val currentCount = mBinding.viewStopLoss.getPriceEditText()
        if (currentCount.isEmpty()) {
            return getStopLossRange()
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        return editCount.numFormat(mViewModel.digits, false)
    }

    /**
     * 点击减号，止损价格计算
     */
    private fun subStopLoss(): String {
        val currentCount = mBinding.viewStopLoss.getPriceEditText()
        if (currentCount.isEmpty()) {
            return getStopLossRange()
        }
        if (currentCount.mathCompTo("0") != 1) return ""//小于等于0的话，返回“”，点击减号输入框不做处理
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        return editCount.numFormat(mViewModel.digits, false)
    }

    /**
     * 计算市价的止损范围
     */
    fun getStopLossRange(): String {
        val productData = mViewModel.productData ?: return ""
        val stopLossRange = productData.computeMarketStopLoss(mViewModel.isBuy.toTradeType(), productData.stopLossLevel(), mViewModel.digits)
        return stopLossRange
    }

    /**
     * 检查止盈止损是否合法
     */
    private fun checkStopLossTip() {
        if (mBinding.viewStopLoss.isSelectedItem().not()) {
            callback?.invoke(true)
            return
        }

        val stopLoss = mBinding.viewStopLoss.getPriceEditText()
        val stopLossRange = getStopLossRange()
        val compareResult = if (mViewModel.isBuy.toTradeType() == TradeType.TRADE_SELL) 1 else -1

        TpSlUtil.checkStopLoss(activity, stopLoss, stopLossRange, -compareResult,
            emptyBlock = {
                mBinding.viewStopLoss.showTips(false)
                callback?.invoke(true)
            },
            validBlock = {
                showEstimated()
                mBinding.viewStopLoss.setTipsTextColor(color_c731e1e1e_c61ffffff)
                mBinding.viewStopLoss.checkPriceEditFocus()
                callback?.invoke(true)
            },
            invalidBlock = { tipStr ->
                mBinding.viewStopLoss.showTips(true, tipStr)
                mBinding.viewStopLoss.setTipsTextColor(ContextCompat.getColor(activity, R.color.cf44040))
                mBinding.viewStopLoss.setPriceEditState(EditState.ERROR)
                callback?.invoke(false)
            })
    }

    /**
     * 更新左边价格输入框内容
     */
    private fun updatePriceEdit() {

        if (mBinding.viewStopLoss.isSelectedItem().not()) {
            mBinding.viewStopLoss.setPriceTextNotUpdateModeText("")
            return
        }
        val text = mBinding.viewStopLoss.getModeEditText()
        val currentMode = mBinding.viewStopLoss.getCurrentComputeMode()
        val closePrice = when (currentMode) {
            ComputeMode.PNL -> getProfitLossPriceByPnl(text)
            ComputeMode.OFFSET -> getProfitLossPriceByOffset(text)
        }
        mBinding.viewStopLoss.setPriceTextNotUpdateModeText(closePrice)
    }

    /**
     * 更新右边计算方式输入框内容
     */
    private fun updateModeEdit() {

        if (mBinding.viewStopLoss.isSelectedItem().not()) {
            mBinding.viewStopLoss.setModeTextNotUpdatePriceText("")
            return
        }

        val closePrice = mBinding.viewStopLoss.getPriceEditText()
        val closePriceLeqZero = closePrice.ifNull("0").mathCompTo("0") != 1
        val currentMode = mBinding.viewStopLoss.getCurrentComputeMode()
        val modeText = when (currentMode) {
            ComputeMode.PNL -> {
                if (closePriceLeqZero) {
                    "0".numCurrencyFormat()
                } else {
                    getProfitLoss()
                }
            }

            ComputeMode.OFFSET -> {
                if (closePriceLeqZero) {
                    "0".numFormat(2)
                } else {
                    getOffset()
                }
            }
        }
        mBinding.viewStopLoss.setModeTextNotUpdatePriceText(modeText)
    }

    /**
     * 获取止盈止损价by Pnl
     */
    private fun getProfitLossPriceByPnl(profitLoss: String): String {
        val data = mViewModel.productData ?: return "0".numFormat(mViewModel.digits)
        val volume = mViewModel.orderBean?.volume ?: return "0".numFormat(mViewModel.digits)
        val openPrice = mViewModel.orderBean?.openPrice.ifNull()
        val orderType = if (mViewModel.isBuy) "0" else "1"
        val closePrice = TpSlUtil.getProfitLossPriceByPnl(data, openPrice, volume, orderType, profitLoss, mViewModel.digits)
        return closePrice
    }

    /**
     * 获取止盈止损价by Offset
     */
    private fun getProfitLossPriceByOffset(offset: String): String {
        val data = mViewModel.productData ?: return "0".numFormat(mViewModel.digits)
        val openPrice = mViewModel.orderBean?.openPrice.ifNull()
        val closePrice = TpSlUtil.getProfitLossPriceByOffset(openPrice, offset, mViewModel.digits)
        return closePrice
    }

    /**
     * 获取价格的涨跌幅
     */
    private fun getOffset(): String {
        val data = mViewModel.productData ?: return "0".numFormat(2)
        val openPrice = mViewModel.orderBean?.openPrice.ifNull()
        val closePrice = mBinding.viewStopLoss.getPriceEditText()
        return TpSlUtil.getOffset(openPrice, closePrice)
    }

    /**
     * 获取预估盈亏
     */
    private fun getProfitLoss(): String {
        val data = mViewModel.productData ?: return "0".numFormat(mViewModel.digits)
        val volume = mViewModel.orderBean?.volume ?: return "0".numFormat(mViewModel.digits)
        val openPrice = mViewModel.orderBean?.openPrice.ifNull()
        val orderType = if (mViewModel.isBuy) "0" else "1"
        val closePrice = mBinding.viewStopLoss.getPriceEditText()
        val profitLoss = getPnlByProfitLossPrice(data, openPrice, volume, orderType, closePrice)
        return profitLoss
    }

    /**
     * 市价计算预估损失
     */
    private fun showEstimated() {
        val orderBean = mViewModel.orderBean ?: return
        val closePrice = mBinding.viewStopLoss.getPriceEditText()
        val closePriceLeqZero = closePrice.ifNull("0").mathCompTo("0") != 1
        val closePriceUI = if (closePriceLeqZero) {
            Constants.DOUBLE_LINE
        } else {
            (closePrice + " " + orderBean.priceCurrency).arabicReverseTextByFlag(" ").ifNull()
        }
        val profitLoss = getProfitLoss()
        val isPositiveNum = profitLoss.mathCompTo("0") == 1
        val profit = if (closePriceLeqZero) {
            Constants.DOUBLE_LINE
        } else {
            ((if (isPositiveNum) "+" else "") + profitLoss.numCurrencyFormat() + " " + UserDataUtil.currencyType()).arabicReverseTextByFlag(" ").ifNull()
        }
        val marketOrder = activity.getString(R.string.market_order)
        val tips = activity.getString(R.string.when_the_last_x_it_will_and_the_will_be_x, closePriceUI, marketOrder, profit)

        //设置止盈止损价格颜色，黑色
        val spannedText = SpannableString(tips)
        val closePriceStartIndex = tips.indexOf(closePriceUI)
        if (closePriceStartIndex != -1) {
            spannedText.setSpan(
                ForegroundColorSpan(color_c1e1e1e_cebffffff),
                closePriceStartIndex,
                closePriceStartIndex + closePriceUI.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        //设置市价单颜色，黑色
        val marketOrderStartIndex = tips.indexOf(marketOrder)
        if (marketOrderStartIndex != -1) {
            spannedText.setSpan(
                ForegroundColorSpan(color_c1e1e1e_cebffffff),
                marketOrderStartIndex,
                marketOrderStartIndex + marketOrder.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        //设置预估盈亏颜色，红色or绿色
        val profitStartIndex = findNthSubstringIndexOrLast(tips, profit, 2)
        if (profitStartIndex != -1) {
            spannedText.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(activity, if (profitLoss.mathCompTo("0") != 1) R.color.cf44040 else R.color.c00c79c)),
                profitStartIndex,
                profitStartIndex + profit.length,
                SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        mBinding.viewStopLoss.showTips(true, spannedText)
    }

    /**
     * 选择计算方式dialog
     */
    private fun showComputeModeDialog() {
        computeModeDialog = BottomListDialog.Builder(activity)
            .setTitle(activity.getString(R.string.tp_sl_settings))
            .setAdapter(computeModeAdapter)
            .build()
        computeModeDialog?.showDialog()
    }

}




