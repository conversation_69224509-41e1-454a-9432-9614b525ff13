package cn.com.vau.ui.deal.activity

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.dialog.GenericDialog
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.data.depositcoupon.UserAccountData
import cn.com.vau.databinding.ActivityLossOrderBinding
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.ui.deal.adapter.LossOrderRcyAdapter
import cn.com.vau.ui.deal.adapter.StLossOrderRcyAdapter
import cn.com.vau.ui.deal.viewmodel.LossOrderViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull

/**
 * 亏损订单
 */
class LossOrderActivity : BaseActivity() {

    private val mBinding: ActivityLossOrderBinding by lazy { ActivityLossOrderBinding.inflate(layoutInflater) }

    private val viewModel by lazy { ViewModelProvider(this).get(LossOrderViewModel::class.java) }

    private var adapter: LossOrderRcyAdapter? = null
    private var stAdapter: StLossOrderRcyAdapter? = null

    private val tradeTypePopup: BottomSelectPopup? by lazy {
        val typeAdapter: SelectAccountAdapter<UserAccountData.Account> by lazy { SelectAccountAdapter(isChangeSelectTextColor = false) }
        typeAdapter.setNewInstance(viewModel.accountList)
        typeAdapter.selectTitle = typeAdapter.data.getOrNull(viewModel.currentSelect)?.getShowItemValue()
        typeAdapter.setOnItemClickListener { _, _, position ->
            if (position == viewModel.currentSelect) return@setOnItemClickListener
            viewModel.currentSelect = position
            typeAdapter.selectTitle = typeAdapter.data.getOrNull(position)?.getShowItemValue()
            typeAdapter.notifyDataSetChanged()
            val accountBean = viewModel.accountList.elementAtOrNull(position) as? UserAccountData.Account
            mBinding.mHeaderBar.setTitleText(accountBean?.name.ifNull())
            accountBean?.let {
                viewModel.currentAccount = it.accountId ?: ""
                viewModel.currentAccountServiceId = it.serverId ?: ""
                viewModel.currentAccountName = it.name ?: ""
                viewModel.currentCurrency = it.currencyType.ifNull()
                if (accountBean.mtsAccount.isNullOrEmpty()) {
                    showNetDialog()
                    viewModel.isSt = false
                    viewModel.onRefreshLossOrders()
                } else {
                    viewModel.isSt = true
                    viewModel.getTradeLossOrder(accountBean.mtsAccount.ifNull())
                }
            }
            tradeTypePopup?.dismiss()
        }
        BottomSelectPopup.build(this, getString(R.string.switch_account), typeAdapter)
    }

    override fun initParam() {
        super.initParam()
        if (intent?.extras?.containsKey("currentCoupon") == true)
            viewModel.couponBean = intent?.extras?.getSerializable("currentCoupon") as DepositCouponDetail
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)

    }

    override fun initView() {
        super.initView()

        mBinding.mHeaderBar.setEndIconVisible(false).setEndIconClickListener {
            tradeTypePopup?.show()
        }

        mBinding.mVsNoData.visibility = View.VISIBLE

        viewModel.socialTradingAccountText = getString(R.string.copy_trading_account)
        viewModel.initData()

        mBinding.mHeaderBar.setTitleText(viewModel.currentAccountName)

        mBinding.mSmartRefreshLayout.setNoMoreData(true)
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        adapter = LossOrderRcyAdapter(this, viewModel.dataList, viewModel.currentCurrency)
        stAdapter = StLossOrderRcyAdapter(this, viewModel.tradeDataList)
        mBinding.mRecyclerView.adapter = adapter

        observe()
    }

    override fun initData() {
        super.initData()
        viewModel.initAccountList()
    }

    private fun initLiveAdapter() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        adapter = LossOrderRcyAdapter(this, viewModel.dataList, viewModel.currentCurrency)
        mBinding.mRecyclerView.adapter = adapter
        adapter?.setOnItemClickListener(object : LossOrderRcyAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {

                if (viewModel.dataList.getOrNull(position)?.checkState == true) return

                val indexOfFirst = viewModel.dataList.indexOfFirst { it.checkState }
                viewModel.dataList.getOrNull(indexOfFirst)?.let {
                    it.checkState = false
                }
                viewModel.dataList.getOrNull(position)?.let {
                    it.checkState = true
                }
                adapter?.notifyItemChanged(indexOfFirst)
                adapter?.notifyItemChanged(position)

            }

        })
    }

    private fun initStAdapter() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)
        stAdapter = StLossOrderRcyAdapter(this, viewModel.tradeDataList)
        mBinding.mRecyclerView.adapter = stAdapter

        stAdapter?.setOnItemClickListener(object : StLossOrderRcyAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {

                if (viewModel.tradeDataList.getOrNull(position)?.checkState == true) return

                val indexOfFirst = viewModel.tradeDataList.indexOfFirst { it.checkState }
                viewModel.tradeDataList.getOrNull(indexOfFirst)?.let {
                    it.checkState = false
                }
                viewModel.tradeDataList.getOrNull(position)?.let {
                    it.checkState = true
                }
                stAdapter?.notifyItemChanged(indexOfFirst)
                stAdapter?.notifyItemChanged(position)

            }

        })
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvSubmit.setOnClickListener(this)
        mBinding.mSmartRefreshLayout.setOnRefreshListener { viewModel.onRefreshLossOrders() }
    }

    private fun observe() {

        viewModel.accountsLiveData.observe(this, Observer { result ->
            if (result.isFailure) {
                hideNetDialog()
                return@Observer
            }
            val dataBean = result.getOrNull() ?: return@Observer
            if ("V00000" != dataBean.resultCode) return@Observer
            val currentAccount = UserDataUtil.accountCd()
            val listAccount = dataBean.data?.obj?.listAccount
            viewModel.accountList.clear()
            listAccount?.forEach {
                if (TextUtils.isEmpty(it.mtsAccount)) {
                    it.name = it.accountId
                } else {
                    it.name = getString(R.string.copy_trading_account)
                }
                viewModel.accountList.add(it)
            }
            viewModel.currentSelect = viewModel.accountList.indexOfFirst {
                it.accountId == currentAccount
            }

            // 模拟
            if (UserDataUtil.isDemoAccount()) {
                if (viewModel.accountList.size <= 0) {
                    GenericDialog.Builder()
                        .setDetail(getString(R.string.account_information_is_please_again_later))
                        .setIsOneButton(true)
                        .setOneButtonText(getString(R.string.confirm))
                        .setOneButtonClick {
                            finish()
                        }
                        .show(this)
                    return@Observer
                }
                viewModel.accountList.getOrNull(0)?.let {
                    viewModel.currentAccount = it.accountId ?: ""
                    mBinding.mHeaderBar.setTitleText(it.getShowItemValue())
                    viewModel.currentAccountServiceId = it.serverId ?: ""
                    viewModel.currentAccountName = it.name ?: ""
                    viewModel.currentCurrency = it.currencyType.ifNull()
                }
            }
            // 这个接口只适用于非跟单，跟单账户列表数据的获取在 viewModel.initData() 中已经请求了，这里要判断一下
            viewModel.onRefreshLossOrders()
            // 数据请求成功才显示切换账户按钮
            mBinding.mHeaderBar.setEndIconVisible(true)
        })

        viewModel.lossOrdersLiveData.observe(this, Observer { result ->
            hideNetDialog()
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            if (result.isFailure) return@Observer
            val dataBean = result.getOrNull() ?: return@Observer
            if ("200" != dataBean.code) return@Observer
            val objDataList = dataBean.obj
            viewModel.dataList.clear()
            viewModel.dataList.addAll(objDataList ?: arrayListOf())
            // mBinding.layoutNoData.ctlNoData.visibility = if (objDataList?.isEmpty() == true) View.VISIBLE else View.GONE
            mBinding.mVsNoData.isVisible = objDataList.isNullOrEmpty()
            mBinding.tvSubmit.isVisible = !objDataList.isNullOrEmpty()

            if (viewModel.currentAccountName != getString(R.string.copy_trading_account)) {
                initLiveAdapter()
            }
        })

        viewModel.tradeLossOrder.observe(this, Observer {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            viewModel.tradeDataList.clear()
            viewModel.tradeDataList.addAll(it ?: arrayListOf())
            mBinding.mVsNoData.isVisible = it.isNullOrEmpty()
            mBinding.tvSubmit.isVisible = !it.isNullOrEmpty()
            if (viewModel.currentAccountName == getString(R.string.copy_trading_account)) {
                initStAdapter()
            }
        })

        viewModel.userCouponLiveData.observe(this, Observer { result ->
            hideNetDialog()
            if (result.isFailure) return@Observer
            val dataBean = result.getOrNull() ?: return@Observer
            if ("********" != dataBean.resultCode) {
                ToastUtil.showToast(dataBean.msgInfo)
                return@Observer
            }

            GenericDialog.Builder()
                .setDetail(getString(R.string.rescue_your_losses_successfully))
                .setIsOneButton(true)
                .setOneButtonText(getString(R.string.confirm))
                .setOneButtonClick {
                    setResult(5)
                    finish()
                }
                .show(this)
        })

        viewModel.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showNetDialog()
            } else { //关闭弹窗
                hideNetDialog()
            }
        }

    }

    override fun onClick(v: View?) {
        super.onClick(v)
        when (v?.id) {
            R.id.tvSubmit -> {
                if (viewModel.isSt) {
                    val dataBean = viewModel.tradeDataList.firstOrNull {
                        it.checkState
                    }
                    if (dataBean == null) {
                        ToastUtil.showToast(getString(R.string.please_select_the_order))
                        return
                    }
                    showNetDialog()
                    viewModel.useLossCoupon(dataBean.positionId, dataBean.profit)
                } else {
                    val dataBean = viewModel.dataList.firstOrNull {
                        it.checkState
                    }
                    if (dataBean == null) {
                        ToastUtil.showToast(getString(R.string.please_select_the_order))
                        return
                    }
                    showNetDialog()
                    viewModel.useLossCoupon(dataBean.orderNo, dataBean.profit)
                }

//                viewModel.dataList.firstOrNull {
//                    it.checkState
//                }?.apply {
//                    viewModel.useLossCoupon(orderNo.toString(), profit)
//                } ?: ToastUtils.showToast(activity?.getString(R.string.please_select_the_order))
            }
        }
    }

}