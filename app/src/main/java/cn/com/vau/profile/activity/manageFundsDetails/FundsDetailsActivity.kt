package cn.com.vau.profile.activity.manageFundsDetails

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.core.view.isInvisible
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.ActivityFundsDetailsBinding
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.widget.dialog.CenterActionDialog
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 入金详情 / 出金详情
 * Created by zhy on 2018/11/19.
 */
class FundsDetailsActivity :
    BaseFrameActivity<FundsDetailsPresenter, FundsDetailsModel>(),
    FundsDetailsContract.View {

    val mBinding by lazy { ActivityFundsDetailsBinding.inflate(layoutInflater) }
    private var isWithdraw = false
    private var accountId: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        accountId = UserDataUtil.accountCd()
        mPresenter.orderType = intent.extras?.getString("orderType", "") ?: ""
        isWithdraw = TextUtils.equals(mPresenter.orderType, "01")
    }

    override fun initView() {
        super.initView()
        val tvFundMethod = findViewById<TextView>(R.id.tvFundMethod)
        if (isWithdraw) {
            tvFundMethod.text = getString(R.string.withdraw_method)
        }
    }

    override fun initData() {
        super.initData()
        mBinding.mHeaderBar.setTitleText(
            getString(
                if (TextUtils.equals(mPresenter.orderType, "00"))
                    R.string.deposit_details
                else
                    R.string.withdrawal_details
            )
        )

        val orderNo = intent.extras?.getString("orderNo", "") ?: ""
        mPresenter.queryDepositDetails(UserDataUtil.loginToken(), accountId, orderNo, mPresenter.orderType)
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvNext.setOnClickListener(this)
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> {
                CenterActionDialog.Builder(this)
                    .setTitle(getString(R.string.withdrawal_cancellation)) //设置则展示标题，否则不展示
                    .setContent(getString(R.string.please_confirm_that_request)) //设置内容
                    .setStartText(getString(R.string.back))//设置左侧按钮文本
                    .setEndText(getString(R.string.confirm))//设置右侧按钮文本
                    //如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                    .setOnEndListener { textView ->
                        //默认关闭
                        mPresenter.fundCancelWithdrawalOrder()
                    }
                    .build()
                    .showDialog()
            }
        }
    }

    override fun refreshDepositDetails() {
        mPresenter.data?.let { bean ->
            mBinding.tvName.text = bean.userName ?: ""
            mBinding.tvAccount.text = bean.accountId ?: ""
            mBinding.tvAmount.text = bean.amount.numCurrencyFormat()
            mBinding.tvCurrency.text = bean.currency
            if (TextUtils.equals(mPresenter.orderType, "00")) {
                mBinding.tvPaymentMethod.text = bean.payType
                val proccessNote = bean.proccessNote
                mBinding.tvProcessedNotes.text = if (TextUtils.isEmpty(proccessNote)) "--- -- " else proccessNote
                mBinding.tvStatus.text = bean.tranStatus
                mBinding.tvTime.text = bean.createdTime
                mBinding.tvCode.text = bean.orderNo
            } else {
                mBinding.tvPaymentMethod.text = bean.withdrawMethod
                val failReason = bean.failReason
                mBinding.tvProcessedNotes.text = if (TextUtils.isEmpty(failReason)) "-----" else failReason
                mBinding.tvStatus.text = bean.orderStatus
                mBinding.tvTime.text = bean.applyTime
                mBinding.tvCode.text = bean.orderNum
                // 出金状态为 Submitted 或 Accepted 显示"取消出金"按钮
                mBinding.tvNext.isInvisible = ("1" != bean.statusCode && "5" != bean.statusCode)
            }
        }
    }

    override fun fundCancelResult(isSuccess: Boolean, errorMsg: String?) {
        if (isSuccess) {
            // 因为资金列表页在onResume刷新 所以直接关闭就可以达到刷新的效果
            finish()
        } else {
            ToastUtil.showToast(errorMsg)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {}

    override fun onResume() {
        super.onResume()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

}