package cn.com.vau.profile.activity.passkey

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.*
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.BottomSelectPopup
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.account.CheckPassKeyAnd2fa
import cn.com.vau.databinding.*
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.CountDownTextHelper
import cn.com.vau.profile.activity.passkey.viewmodel.PasskeyAuthVerificationViewModel
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAResetActivity
import cn.com.vau.profile.adapter.*
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoWithIconListDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.Runnable

class PasskeyAuthVerificationActivity :
    BaseMvvmActivity<ActivityPasskeyAuthVerificationBinding, PasskeyAuthVerificationViewModel>() {

    private var currentAuthMethod = AUTHENTICATOR_VERIFICATION
    private var tickTime = -1
    private var authVerificationLayout: View? = null
    private var authVerificationLayoutBinding: LayoutPasskeyAuthVerificationBinding? = null

    private var passkeyVerificationLayout: View? = null
    private var passkeyAuthVerificationLayoutBinding: LayoutPasskeyVerficationBinding? = null

    private var switchAdapter: SwitchVerifyAdapter? = null
    private val selectPopup: BottomSelectPopup? by lazy {
        BottomSelectPopup.build(
            this,
            getString(R.string.switch_authentication_method)
        )
    }
    private val tickRunnable by lazy {
        Runnable {
            handleTickTime()
        }
    }

    private val passKeyCredentialHelper: PassKeyCredentialHelper by lazy {
        PassKeyCredentialHelper(this)
    }
    private var checkPassKeyAnd2fa: CheckPassKeyAnd2fa.Obj? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        checkPassKeyAnd2fa = intent.extras?.getSerializable(Constants.PARAM_PASSKEY_CHECK) as? CheckPassKeyAnd2fa.Obj
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun initView() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.authenticator_verification))
            .setEndIconDrawable(getDrawable(AttrResourceUtil.getDrawable(this, R.attr.icon1Cs)))
            .setEndIconClickListener {
                openActivity(HelpCenterActivity::class.java)
            }
        showVerificationUi(checkPassKeyAnd2fa?.passKeyStatus == true)

    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.liveDataVerification.observe(this) {
            openActivity(PasskeyCreatingActivity::class.java)
        }

        mViewModel.liveDataTwoFactorVerification.observe(this) {
            openActivity(PasskeyCreatingActivity::class.java)
        }

        mViewModel.liveDataSendEmailCode.observe(this) {
            tickStart()
            showSoftInput()
        }
    }

    private fun showVerificationUi(hasPassKey: Boolean) {
        if (hasPassKey) {
            showPassKeyVerificationLayout()
        } else {
            showAuthVerificationLayout()
        }
    }

    private fun showAuthVerificationLayout() {
        mBinding.mHeaderBar.setTitleText(getString(R.string.authenticator_verification))
        if (authVerificationLayout == null) {
            authVerificationLayout = mBinding.authVerificationVs.inflate()
            authVerificationLayout?.let {
                authVerificationLayoutBinding = LayoutPasskeyAuthVerificationBinding.bind(it)
                authVerificationLayoutBinding?.let { binding ->
                    binding.switchTv.set(
                        getString(R.string.switch_authentication_method),
                        isShowUnderLine = true
                    ) {
                        showSwitchAuthMethodDialog()
                    }
                    binding.verificationView.setPasswordListener(object : PasswordView.PasswordListener {
                        override fun passwordChange(changeText: String?) {
                        }

                        override fun passwordComplete() {
                            //Google身份验证或者邮箱验证通过后去Passkey创建页
                            startVerification(binding.verificationView.getPassword())
                            binding.verificationView.hiddenSoftInputFromWindow()
                        }

                        override fun keyEnterPress(password: String?, isComplete: Boolean) {
                        }

                    })

                    binding.tvReSendEms.clickNoRepeat {
                        sendEmailCode()
                    }
                }
                showSoftInput()
            }

        } else {
            authVerificationLayout?.isVisible = true
        }
        passkeyVerificationLayout?.isVisible = false

    }

    private fun showSoftInput() {
        lifecycleScope.launchWhenResumed {
            delay(300)
            authVerificationLayoutBinding?.verificationView?.showSoftInput()
        }
    }

    private fun sendEmailCode() {
        if (tickTime == -1) {
            mViewModel.passKeySendEmailCodeApi()
        }
    }

    private fun startVerification(authCode: String?) {
        when (currentAuthMethod) {
            AUTHENTICATOR_VERIFICATION -> {
                mViewModel.twoFactorValidateCodeApi(authCode)
            }

            EMAIL_VERIFICATION -> {
                mViewModel.passKeyValidateEmailCodeApi(authCode)
            }
        }
    }

    private fun showPassKeyVerificationLayout() {
        currentAuthMethod = PASSKEY_VERIFICATION
        mBinding.mHeaderBar.setTitleText(getString(R.string.passkey_verification))
        if (passkeyVerificationLayout == null) {
            passkeyVerificationLayout = mBinding.passkeyVerificationVs.inflate()
            passkeyVerificationLayout?.let {
                passkeyAuthVerificationLayoutBinding = LayoutPasskeyVerficationBinding.bind(it)
                passkeyAuthVerificationLayoutBinding?.let { binding ->
                    binding.switchTv.set(
                        getString(R.string.switch_authentication_method),
                        isShowUnderLine = true
                    ) {
                        showSwitchAuthMethodDialog()
                    }
                    binding.passkeyTapIv.clickNoRepeat {
                        passKeyVerification()
                    }
                }
            }
        } else {
            passkeyVerificationLayout?.isVisible = true
        }
        authVerificationLayout?.isVisible = false
    }

    //passkey 验证
    private fun passKeyVerification() {
        //Passkey验证成功去Passkey创建页
        checkPassKeyAnd2fa?.jsonData?.let {
            lifecycleScope.launch {
                val data = passKeyCredentialHelper.getSavedCredentials(it)
                if (data == null) {
                    ToastUtil.showToast(getString(R.string.data_exception_please_try_again_later))
                    return@launch
                }
                mViewModel.verificationPasskeyApi(data, checkPassKeyAnd2fa?.passKeyId ?: "")
            }
        }
    }

    override fun initListener() {
        super.initListener()
        // 收不到验证码提示文案
        authVerificationLayoutBinding?.tvNotReceiveCodeTips?.setOnClickListener {
            showNotReceiveCodeDialog()
        }
    }

    private fun showSwitchAuthMethodDialog() {
        authVerificationLayoutBinding?.verificationView?.hiddenSoftInputFromWindow()
        switchAdapter = when (currentAuthMethod) {
            AUTHENTICATOR_VERIFICATION -> {
                SwitchVerifyAdapter(
                    arrayListOf<SwitchVerifyData>().apply {
                        add(SwitchVerifyData(getString(R.string.send_otp_via_email), true))
//                        add(SwitchVerifyData(getString(R.string.passkey_verification), true,checkPassKeyAnd2fa?.passKeyStatus == true))
                        add(
                            SwitchVerifyData(
                                getString(R.string.reset_two_factor_authentication),
                                false
                            )
                        )
                    }
                )
            }

            EMAIL_VERIFICATION -> {
                SwitchVerifyAdapter(
                    arrayListOf<SwitchVerifyData>().apply {
                        add(SwitchVerifyData(getString(R.string.authenticator_verification), false, checkPassKeyAnd2fa?.twoFactorUser == true))
//                        add(SwitchVerifyData(getString(R.string.passkey_verification), false,checkPassKeyAnd2fa?.passKeyStatus == true))
                    }
                )
            }

            else -> {
                SwitchVerifyAdapter(
                    arrayListOf<SwitchVerifyData>().apply {
                        add(SwitchVerifyData(getString(R.string.send_otp_via_email), false))
                        add(SwitchVerifyData(getString(R.string.authenticator_verification), false))
                    }
                )
            }
        }
        switchAdapter?.setOnItemClickListener { _, _, position ->
            when (switchAdapter?.data?.getOrNull(position)?.name) {
                getString(R.string.send_otp_via_email) -> {
                    showAuthVerificationLayout()
                    switchToEmailVerification()
                }

                getString(R.string.authenticator_verification) -> {
                    if (checkPassKeyAnd2fa?.twoFactorUser == true) {
                        showAuthVerificationLayout()
                        switchToAuthenticationVerification()
                    }
                }

                getString(R.string.passkey_verification) -> {
                    if (checkPassKeyAnd2fa?.passKeyStatus == true) {
                        showPassKeyVerificationLayout()
                    }
                }

                getString(R.string.reset_two_factor_authentication) -> {
                    TFAResetActivity.open(this)
                }

            }
            selectPopup?.dismiss()
        }
        selectPopup?.setAdapter(switchAdapter)
        selectPopup?.show()
    }

    @SuppressLint("SetTextI18n")
    private fun switchToEmailVerification() {
        currentAuthMethod = EMAIL_VERIFICATION
        authVerificationLayoutBinding?.tvReSendEms?.isVisible = true
        if (tickTime == -1) {
            authVerificationLayoutBinding?.tvReSendEms?.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff))
            authVerificationLayoutBinding?.tvReSendEms?.isEnabled = false
            authVerificationLayoutBinding?.tvReSendEms?.text = getString(R.string.resend_code_in_x_seconds, "--")
        }
        mBinding.mHeaderBar.setTitleText(getString(R.string.verification))
        authVerificationLayoutBinding?.let {
            it.verificationTitleTv.text =
                "${getString(R.string.the_verification_code_has_been_sent_to)}:\n${UserStorage.getEmail()}"
            it.tvPrompt.isVisible = false
            it.verificationView.clearInput()
            if (SpManager.isNeedKYC()) {
                it.tvNotReceiveCodeTips.isVisible = true
            }
        }
        sendEmailCode()
        showSoftInput()
    }

    private fun tickStart() {
        if (tickTime == -1) {
            tickTime = 60
            authVerificationLayoutBinding?.tvReSendEms?.post(tickRunnable)
        }

    }

    private fun handleTickTime() {
        tickTime--
        if (tickTime < 0) {
            authVerificationLayoutBinding?.tvReSendEms?.isEnabled = true
            authVerificationLayoutBinding?.tvReSendEms?.text = getString(R.string.resend)
            authVerificationLayoutBinding?.tvReSendEms?.setTextColor(ce35728)
            authVerificationLayoutBinding?.tvReSendEms?.removeCallbacks(tickRunnable)
        } else {
            val second = tickTime.toString() // 倒计时秒数
            val fullText = getString(R.string.resend_code_in_x_seconds, second)
            authVerificationLayoutBinding?.tvReSendEms?.setTextColor(color_c1e1e1e_cebffffff)
            authVerificationLayoutBinding?.tvReSendEms?.text = countDownTextHelper.updateCountDownText(fullText, second)
            authVerificationLayoutBinding?.tvReSendEms?.isEnabled = false
            authVerificationLayoutBinding?.tvReSendEms?.postDelayed(tickRunnable, 1000)
        }
    }

    private fun switchToAuthenticationVerification() {
        authVerificationLayoutBinding?.tvReSendEms?.isVisible = false
        currentAuthMethod = AUTHENTICATOR_VERIFICATION
        mBinding.mHeaderBar.setTitleText(getString(R.string.authenticator_verification))
        authVerificationLayoutBinding?.let {
            it.verificationTitleTv.text = getString(R.string.authenticator_code)
            it.tvPrompt.isVisible = true
            it.verificationView.clearInput()
            showSoftInput()
            if (SpManager.isNeedKYC()) {
                it.tvNotReceiveCodeTips.isVisible = false
            }
        }

    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        authVerificationLayoutBinding?.verificationView?.hiddenSoftInputFromWindow()
        val data = arrayListOf(
            HintLocalData(
                getString(R.string.double_check_your_email_address),
                getString(R.string.ensure_you_have_it_correctly),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips3)
            ),
            HintLocalData(
                getString(R.string.check_your_spam_junk_folder),
                getString(R.string.sometimes_the_email_by_mistake),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips4)
            ),
            HintLocalData(
                getString(R.string.wait_a_few_minutes),
                getString(R.string.delays_can_happen_occasionally),
                AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips2)
            )
        )
        BottomInfoWithIconListDialog.Builder(this)
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        authVerificationLayoutBinding?.tvReSendEms?.removeCallbacks(tickRunnable)
    }

    companion object {
        const val EMAIL_VERIFICATION = 0
        const val AUTHENTICATOR_VERIFICATION = 1
        const val PASSKEY_VERIFICATION = 2
    }
}