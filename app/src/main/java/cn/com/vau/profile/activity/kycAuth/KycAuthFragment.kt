package cn.com.vau.profile.activity.kycAuth

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.data.account.PrimaryDependOnLevel
import cn.com.vau.databinding.FragmentRecyclerviewBinding
import cn.com.vau.page.user.sumsub.SumsubPromptActivity
import cn.com.vau.profile.activity.kycAuth.KycStandardAdapter.Companion.LEVEL_FACE
import cn.com.vau.util.*

/**
 * Filename: AuthStandardFragment.kt
 * Author: GG
 * Date: 2024/1/11
 * Description:
 */
class KycAuthFragment : BaseMvvmBindingFragment<FragmentRecyclerviewBinding>() {

    private val mViewModel by activityViewModels<KycAuthViewModel>()

    private val type: String by lazy {
        arguments?.getString(KEY_TYPE) ?: KycAuthViewModel.KEY_STANDARD
    }
    private val mAdapter by lazy {
        KycStandardAdapter().apply {
            if (type == KycAuthViewModel.KEY_STANDARD) {
                setList(listOf(PrimaryDependOnLevel(level = KycStandardAdapter.LEVEL_1), PrimaryDependOnLevel(level = KycStandardAdapter.LEVEL_2), PrimaryDependOnLevel(level = KycStandardAdapter.LEVEL_3)))
            } else {
                setList(listOf(PrimaryDependOnLevel(level = KycStandardAdapter.LEVEL_BANK)))
            }
            // 添加稳定 ID 防止布局重绘
            setHasStableIds(true)
            // 关闭默认动画
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
        }
    }

    override fun initView() {
        mBinding.mRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.dp2px(), 30.dp2px()))
        mAdapter.setVerifyButtonCallback { level, showLevelStatus ->
            if (level == LEVEL_FACE) {
                SumsubPromptActivity.openActivity(requireContext(), Constants.SUMSUB_TYPE_FACE)
            } else {
                KycVerifyHelper.showKycDialog(
                    requireActivity(), mapOf(
                        Constants.GoldParam.CODE to Constants.GoldParam.CODE_KYC,
                        Constants.GoldParam.NEXT_LEVEL to if (level.ifNull(0) > 3) level.ifNull(0).toString() else "",
                        Constants.GoldParam.SPECIALLEVEL to level.ifNull(0).toString(),
                    )
                )
            }
        }
    }

    override fun createObserver() {
        super.createObserver()

        mViewModel.statusStandard.observe(this) {
            if (type == KycAuthViewModel.KEY_STANDARD) {
                mAdapter.setList(it)
            }
        }
        mViewModel.statusPlus.observe(this) {
            if (type == KycAuthViewModel.KEY_PLUS) {
                mAdapter.setList(it)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.sensorsTrack(type)
    }

    companion object {

        private const val KEY_TYPE = "type"

        fun newInstance(type: String): KycAuthFragment {
            val args = Bundle()
            args.putString(KEY_TYPE, type)
            val fragment = KycAuthFragment()
            fragment.arguments = args
            return fragment
        }
    }
}