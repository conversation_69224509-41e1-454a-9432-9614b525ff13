package cn.com.vau.profile.activity.inputPWD

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.util.ToastUtil
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.page.WithdrawalBundleBean
import io.reactivex.disposables.Disposable

/**
 * Created by zhy on 2018/11/22.
 */
class InputPWDPresenter : InputPWDContract.Presenter() {

    override fun withdrawal(
        userToken: String?, accountId: String?, currency: String?,
        data: WithdrawalBundleBean,
        fundSafePwd: String?,
        state: Int
    ) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        params["userToken"] = userToken ?: ""
        params["accountId"] = accountId ?: "" //mt4
        params["currency"] = currency ?: "" //出金账户币种
        params["agreeed"] = data.isAgreeed ?: ""//是否阅读同意声明
        params["amount"] = data.amountX1 ?: "" //出金金额
        params["importantNotes"] = data.importantNotesX1 ?: ""//出金备注
        params["fundSafePwd"] = fundSafePwd ?: "" //资金安全密码
        if (state == 1) {
            params["withdrawMethod"] = "1" //出金方式，
            params["cardHolder"] = data.nameOnCardX1 ?: ""//持卡人姓名，
            params["cardPreFourNum"] = data.first4DigitsX1 ?: "" //信用卡前4位
            params["cardAfterThreeNum"] = data.last3DigitsX1 ?: "" //信用卡后3位
            params["cardExpiry"] = data.cardExpiryX1 ?: ""//信用卡过期时间
        } else if (state == 2) {
            params["withdrawMethod"] = "2"
            params["region"] = "Australia"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""//银行名称
            params["bsb"] = data.getbSBX2() ?: ""//不知道啥意思
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: "" //持卡人姓名&&cardHolder一样
            params["swift"] = data.swiftX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
        } else if (state == 3) {
            params["withdrawMethod"] = "2"
            params["region"] = "International"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""
            params["bankAdress"] = data.bankAddressX3 ?: ""
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
            params["cardHolderAdress"] = data.accountHolderAddressX3 ?: ""
            params["swift"] = data.swiftX2 ?: ""
            params["sortCode"] = data.getaBASortCodeX3() ?: ""
        } else if (state == 5) {
            params["withdrawMethod"] = "31"
            params["skillEmail"] = data.skrillNetellerEmailX5 ?: ""
        } else if (state == 7) {
            params["withdrawMethod"] = "32"
            params["skillEmail"] = data.skrillNetellerEmailX5 ?: ""
        } else if (state == 6) {
            val tempBankBranchRegion = data.bankBranchRegionX6?.split(" ".toRegex())?.dropLastWhile { it.isEmpty() }?.toTypedArray()
            params["withdrawMethod"] = "4"
            params["cardHolder"] = data.nameOnCardX1 ?: ""
            params["bankName"] = data.australianBankNameX2 ?: ""
            params["bankCardNo"] = data.bankAccountNumberX2 ?: ""
            params["bankBranchProvince"] = tempBankBranchRegion?.getOrNull(0) ?: ""
            params["bankBranchCity"] = tempBankBranchRegion?.getOrNull(1) ?: ""
            params["bankBranchName"] = data.bankBranchX4 ?: ""
            params["bankBeneficiaryName"] = data.bankBeneficiaryNameX2 ?: ""
        }
        mModel?.withdrawal(params, object : BaseObserver<DataObjStringBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(withdrawalBean: DataObjStringBean) {
                mView?.hideNetDialog()
                if (withdrawalBean.resultCode == "********") {
                    mView?.refreshWithdrawal(withdrawalBean.data?.obj)
                } else {
                    ToastUtil.showToast(withdrawalBean.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }
}
