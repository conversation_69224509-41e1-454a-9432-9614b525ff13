package cn.com.vau.common.mvvm.network

/**
 * 自定义错误信息异常
 */
class AppException : Exception {

    var errorMsg: String? = null //错误消息
    var errorCode: String? = null //错误码
    var throwable: Throwable? = null

    constructor(errCode: String? = null, errMsg: String? = null, throwable: Throwable? = null) : super(errMsg) {
        this.errorMsg = errMsg
        this.errorCode = errCode
        this.throwable = throwable
    }
}