package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PathEffect;
import android.graphics.Typeface;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.view.kchart.interfaces.UnabelFocusedsView;
import cn.com.vau.trade.activity.HKLineChartActivity;
import cn.com.vau.trade.activity.KLineActivity;
import cn.com.vau.util.DistKt;

/**
 * 描述：坐标系背景
 */
public class Coordinates extends ViewContainer<Object> implements UnabelFocusedsView {

    //左边文字画笔
    private Paint mLeftTextPaint = null;
    //右边文字画笔
    private Paint mRightTextPaint = null;
    //底部文字画笔
    private Paint mBottomTextPaint = null;
    //纬线画笔
    private Paint mLongitudeLinePaint = null;
    //经线画笔
    private Paint mLatitudeLinePaint = null;
    //纬线
    private int mLongitudeNums = 0;
    //经线
    private int mLatitudeNums = 0;
    //坐标系刻度适配器
    private CoordinateScaleAdapter mCoordinateScaleAdapter = null;
    //距边距的空隙值
    private float mFixedSpaceWithLeft = 0;
    //固定间隙与经线之间的
    private float mFixedSpaceWithLatitudeLine = 0;
    //刻度所在的位置相对于经线
    private TextGravity mLatitudeTextGravity = TextGravity.ABOVE_LINE;
    //X轴刻度监听器
    private ScaleXAdapter xlistener;
    //分时图纬线缩放dp值
    private float timeChartLongitudeZoomdpVal = 0;
    private Context ctx;
    private int tempPointIndex;
    private Typeface textTypeface;

    /**
     * 坐标系
     *
     * @param context
     * @param coordinateScaleAdapter
     */
    public Coordinates(Context context, CoordinateScaleAdapter coordinateScaleAdapter) {
        super(context);
        this.mCoordinateScaleAdapter = coordinateScaleAdapter;
        //初始化画笔
        initPaint();
    }

    /**
     * 坐标系
     */
    public Coordinates(Context context) {
        super(context);
        this.ctx = context;
        //初始化画笔
        initPaint();
    }

    /**
     * 初始化画笔
     */
    private void initPaint() {
        textTypeface = ResourcesCompat.getFont(ctx, R.font.gilroy_regular);
        //初始化纬线画笔
        this.mLongitudeLinePaint = new Paint();
        mLongitudeLinePaint.setStyle(Paint.Style.STROKE);
        mLongitudeLinePaint.setColor(Color.BLACK);
        mLongitudeLinePaint.setStrokeWidth(1f);
        //初始化经线画笔
        this.mLatitudeLinePaint = new Paint();
        mLatitudeLinePaint.setStyle(Paint.Style.STROKE);
        mLatitudeLinePaint.setColor(Color.BLACK);
        mLatitudeLinePaint.setStrokeWidth(1f);
        //初始化左边文字画笔
        this.mLeftTextPaint = new Paint();
        mLeftTextPaint.setTextSize(getPixelSp(9));
        mLeftTextPaint.setAntiAlias(true);
        mLeftTextPaint.setColor(ContextCompat.getColor(mContext, R.color.tiny_gray));
        //初始化右边文字画笔
        this.mRightTextPaint = new Paint();
        mRightTextPaint.setTextSize(getPixelSp(9));
        mRightTextPaint.setAntiAlias(true);
        mRightTextPaint.setTypeface(textTypeface);
        mRightTextPaint.setColor(ContextCompat.getColor(mContext, R.color.tiny_gray));
        //初始化底部文字画笔
        this.mBottomTextPaint = new Paint();
        mBottomTextPaint.setTextSize(getPixelSp(9));
        mBottomTextPaint.setAntiAlias(true);
        mBottomTextPaint.setColor(ContextCompat.getColor(mContext, R.color.tiny_gray));
        //初始化空隙,该空隙用于文字与左边的距离
        mFixedSpaceWithLeft = getPixelSp(2);
        //初始化间隙,该间隙用于文字与经线之间的距离
        mFixedSpaceWithLatitudeLine = (int) getPixelDp(2);
    }

    @Override
    public void draw(Canvas canvas) {
        //绘制子view
        super.draw(canvas);
        try {
            if (isShow) {
                checkParamter();
                //画纬线,及其刻度
                drawLongitude(canvas);
                //画经线,及其刻度
                drawLatitude(canvas);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //画纬线
    private void drawLongitude(Canvas canvas) {
        //纬线间的宽度  (宽度-左右margin)/(纬线个数-1)
        float longitudeSpace = (mCoordinateWidth - mCoordinateMarginLeft - mCoordinateMarginRight) / (mLongitudeNums - 1);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        Path path = new Path();
        calcZoomdp();
        for (int i = 0; i < mLongitudeNums; i++) {
            String bottomScale = "";//左边刻度文字
            if (mCoordinateScaleAdapter != null) {
                bottomScale = mCoordinateScaleAdapter.getXBottomScaleString(mDataList, mDrawPointIndex, mShownPointNums, i, mLongitudeNums);
            }
            if (bottomScale == null) {
                bottomScale = "";
            }
            float scaleWidth = mBottomTextPaint.measureText(bottomScale);//文字宽度
            mBottomTextPaint.getFontMetrics(fm);
            float scaleHeight = Math.abs(fm.ascent);//文字高度
            if (i == 0) {//第一条纬线
                //画纬线
                path.moveTo(mLongitudeLinePaint.getStrokeWidth() + mCoordinateMarginLeft + DistKt.dp2px(timeChartLongitudeZoomdpVal), 0);
                path.lineTo(mLongitudeLinePaint.getStrokeWidth() + mCoordinateMarginLeft + DistKt.dp2px(timeChartLongitudeZoomdpVal), mCoordinateHeight);
                canvas.drawPath(path, mLongitudeLinePaint);
                path.reset();
                //画纬线刻度
                //                canvas.drawText(bottomScale, mFixedSpaceWithLeft + mCoordinateMarginLeft, mCoordinateHeight + mFixedSpaceWithLatitudeLine + scaleHeight, mBottomTextPaint);
                if (xlistener != null && mDataList != null && mDataList.size() > 0 && mDataList.size() > (mDrawPointIndex + mShownPointNums - 1)) {
//                    if (mLastDrawPointIndexArray[0] != mDrawPointIndex) {
//                    }
                    mLastDrawPointIndexArray[0] = mDrawPointIndex;
                    xlistener.scale(i, mLastDrawPointIndexArray[0], mDataList.size() - mLastDrawPointIndexArray[0], mDataList.get(mLastDrawPointIndexArray[0]));
                }
            } else if (i == mLongitudeNums - 1) {//最后一条纬线
                //画纬线
                path.moveTo(mCoordinateWidth - mLongitudeLinePaint.getStrokeWidth() - mCoordinateMarginRight - DistKt.dp2px(timeChartLongitudeZoomdpVal), 0);
                path.lineTo(mCoordinateWidth - mLongitudeLinePaint.getStrokeWidth() - mCoordinateMarginRight - DistKt.dp2px(timeChartLongitudeZoomdpVal), mCoordinateHeight);
                canvas.drawPath(path, mLongitudeLinePaint);
                path.reset();
                //画纬线刻度
                //                canvas.drawText(bottomScale, mCoordinateWidth - scaleWidth - mFixedSpaceWithLeft, mCoordinateHeight + mFixedSpaceWithLatitudeLine + scaleHeight, mBottomTextPaint);
                if (xlistener != null && mDataList != null && mDataList.size() > 0 && mDataList.size() > (mDrawPointIndex + mShownPointNums - 1)) {
//                    if (mLastDrawPointIndexArray[1] != (mDrawPointIndex + mShownPointNums - 1)) {
//                    }
                    mLastDrawPointIndexArray[1] = mDrawPointIndex + mShownPointNums - 1;
                    xlistener.scale(i, mLastDrawPointIndexArray[1], mDataList.size() - mLastDrawPointIndexArray[1], mDataList.get(mLastDrawPointIndexArray[1]));
                }
            } else {//其中所有的纬线
                //画纬线  i*纬线宽度
                float tempLongitudeSpace = i * longitudeSpace + mCoordinateMarginLeft;//纬线间隙,i此时应从1开始,因为第一个if屏蔽了0
                path.moveTo(tempLongitudeSpace - mLongitudeLinePaint.getStrokeWidth(), 0);
                path.lineTo(tempLongitudeSpace - mLongitudeLinePaint.getStrokeWidth(), mCoordinateHeight);
                canvas.drawPath(path, mLongitudeLinePaint);
                path.reset();
                //画纬线刻度
                //                canvas.drawText(bottomScale, tempLongitudeSpace - scaleWidth / 2, mCoordinateHeight + mFixedSpaceWithLatitudeLine + scaleHeight, mBottomTextPaint);
                if (xlistener != null && mDataList != null && mDataList.size() > 0 && mDataList.size() > (mDrawPointIndex + mShownPointNums - 1)) {
                    tempPointIndex = mDrawPointIndex + (mShownPointNums / 2);
//                    if (tempPointIndex != mLastDrawPointIndexArray[2]) {
//                    }
                    mLastDrawPointIndexArray[2] = tempPointIndex;
                    xlistener.scale(i, mLastDrawPointIndexArray[2], mDataList.size() - mLastDrawPointIndexArray[2], mDataList.get(mLastDrawPointIndexArray[2]));
                }
            }
        }
    }

    //画经线
    private void drawLatitude(Canvas canvas) {
        //经线宽度
        float latitudeSpace = mCoordinateHeight / (mLatitudeNums - 1);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        Path path = new Path();
        Context activity = null;
        if (this.ctx instanceof HKLineChartActivity || this.ctx instanceof KLineActivity) {
            activity = this.ctx;
        }
        for (int i = 0; i < mLatitudeNums; i++) {
            String leftScale = "";//左边刻度文字 (无需显示)
            if (mCoordinateScaleAdapter != null) {
                leftScale = mCoordinateScaleAdapter.getYLeftScaleString(mDataList, mDrawPointIndex, mShownPointNums, i, mLatitudeNums);
            }
            if (leftScale == null) {
                leftScale = "";
            }
            float leftScaleWidth = mLeftTextPaint.measureText(leftScale);//左边文字宽度
            mLeftTextPaint.getFontMetrics(fm);
            float leftScaleHeight = Math.abs(fm.ascent);//左边文字高度

            String rightScale = "";//右边刻度文字
            if (mCoordinateScaleAdapter != null) {
                rightScale = mCoordinateScaleAdapter.getYRightScaleString(mDataList, mDrawPointIndex, mShownPointNums, i, mLatitudeNums);
            }
            if (rightScale == null) {
                rightScale = "";
            }
            float rightScaleWidth = mRightTextPaint.measureText(rightScale);//右边文字宽度
            mRightTextPaint.getFontMetrics(fm);
            float rightScaleHeight = Math.abs(fm.ascent);//右边文字高度

            if (i == 0) {//第一条经线
                path.moveTo(mCoordinateMarginLeft, 0);
                path.lineTo(mCoordinateWidth - mCoordinateMarginRight, 0);
                //画经线
                canvas.drawPath(path, mLatitudeLinePaint);
                path.reset();
                //画经线刻度(左)
                TextGravity textGravity = mLatitudeTextGravity;
                if (textGravity == TextGravity.ABOVE_LINE || textGravity == TextGravity.VERTICAL_CENTER_LINE) {//如果第一条经线的刻度是在经线之上或者之中,我们就将其设置为线之下模式,因为线之上/之中显示不下
                    textGravity = TextGravity.BELOW_LINE;
                }
                if (!TextUtils.isEmpty(leftScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(textGravity, leftScaleHeight);
                    canvas.drawText(leftScale, mFixedSpaceWithLeft, verticalOffset, mLeftTextPaint);
                }
                if (!TextUtils.isEmpty(rightScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(textGravity, rightScaleHeight);
                    //画经线刻度(右)
                    canvas.drawText(rightScale, mCoordinateWidth - rightScaleWidth - mFixedSpaceWithLeft, verticalOffset, mRightTextPaint);
                }
            } else if (i == mLatitudeNums - 1) {//最后一条经线
                path.moveTo(mCoordinateMarginLeft, mCoordinateHeight - 1); //减去1是因为不减就看不到这条线了
                path.lineTo(mCoordinateWidth - mCoordinateMarginRight, mCoordinateHeight - 1); //减去1是因为不减就看不到这条线了
                //画经线
                canvas.drawPath(path, mLatitudeLinePaint);
                path.reset();

                TextGravity textGravity = mLatitudeTextGravity;
                if (textGravity == TextGravity.BELOW_LINE || textGravity == TextGravity.VERTICAL_CENTER_LINE) {
                    textGravity = TextGravity.ABOVE_LINE;
                }

                if (!TextUtils.isEmpty(leftScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(textGravity, leftScaleHeight);
                    //画经线刻度(左)
                    canvas.drawText(leftScale, mFixedSpaceWithLeft, mCoordinateHeight + verticalOffset, mLeftTextPaint);
                }
                if (!TextUtils.isEmpty(rightScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(textGravity, rightScaleHeight);
                    //画经线刻度(右)
                    canvas.drawText(rightScale, mCoordinateWidth - rightScaleWidth - mFixedSpaceWithLeft, mCoordinateHeight + verticalOffset, mRightTextPaint);
                }
            } else {//中间的经线
                //画经线
                float tempLatitudeSpace = i * latitudeSpace;//经线间隙,i此时应从1开始,因为第一个if屏蔽了0
                path.moveTo(mCoordinateMarginLeft, tempLatitudeSpace);
                path.lineTo(mCoordinateWidth - mCoordinateMarginRight, tempLatitudeSpace);
                canvas.drawPath(path, mLatitudeLinePaint);
                path.reset();

                if (!TextUtils.isEmpty(leftScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(mLatitudeTextGravity, leftScaleHeight);
                    //画经线刻度(左)
                    canvas.drawText(leftScale, mFixedSpaceWithLeft, tempLatitudeSpace + verticalOffset, mLeftTextPaint);
                }
                if (!TextUtils.isEmpty(rightScale)) {
                    float verticalOffset = getTextVerticalOffsetByGravity(mLatitudeTextGravity, rightScaleHeight);
                    //画经线刻度(右)
                    if (this.getChartView().isSubChart() && activity != null &&
                            (//副图 MACD 隐藏中经线刻度逻辑
                                    activity instanceof HKLineChartActivity && ((HKLineChartActivity) activity).netBean.getChartSubTypeName().equals("MACD") ||
                                            activity instanceof KLineActivity && ((KLineActivity) activity).getChartFragment() != null && "MACD".equals(((KLineActivity) activity).getChartFragment().getMViewModel().getChartSubTypeName()))
                    ) {
                        // 这种情况不画刻度
                    } else {
                        canvas.drawText(rightScale, mCoordinateWidth - rightScaleWidth - mFixedSpaceWithLeft, tempLatitudeSpace + verticalOffset, mRightTextPaint);
                    }
                }
            }
        }
    }

    private float calcZoomdp() {
        if (this.getChartView().getId() == R.id.chart_time_view) {
            if (mShownPointNums == 70) {
                timeChartLongitudeZoomdpVal = 2f;
            } else if (mShownPointNums < 70 && mShownPointNums >= 39) {
                timeChartLongitudeZoomdpVal = 2f + (float) (70 - mShownPointNums) * (7 - 2) / (70 - 39);
            } else if (mShownPointNums > 70 && mShownPointNums <= 174) {
                timeChartLongitudeZoomdpVal = 2f - (float) (mShownPointNums - 70) * (2 - 1) / (174 - 70);
            }
            return timeChartLongitudeZoomdpVal;
        } else {
            return 0f;
        }
    }

    private float getTextVerticalOffsetByGravity(TextGravity textGravity, float textHeight) {
        switch (textGravity) {
            case ABOVE_LINE: {
                return 0 - mFixedSpaceWithLatitudeLine;
            }
            case VERTICAL_CENTER_LINE: {
                return textHeight / 2;
            }
            case BELOW_LINE: {
                return textHeight + mFixedSpaceWithLatitudeLine;
            }
        }
        return 0;
    }

    private void checkParamter() {
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }

    }

    public void setScaleXAdapter(ScaleXAdapter listener) {
        this.xlistener = listener;
    }

    public CoordinateScaleAdapter getCoordinateScaleAdapter() {
        return mCoordinateScaleAdapter;
    }

    public void setCoordinateScaleAdapter(CoordinateScaleAdapter coordinateScaleAdapter) {
        this.mCoordinateScaleAdapter = coordinateScaleAdapter;
    }

    public void setBottomTextSize(float sp) {
        mBottomTextPaint.setTextSize(getPixelSp(sp));
    }

    public void setLeftTextSize(float sp) {
        mLeftTextPaint.setTextSize(getPixelSp(sp));
    }

    public void setRightTextSize(float sp) {
        mRightTextPaint.setTextSize(getPixelSp(sp));
    }

    public int getLatitudeNums() {
        return mLatitudeNums;
    }

    public void setLatitudeNums(int latitudeNums) {
        this.mLatitudeNums = latitudeNums;
    }

    public int getLongitudeNums() {
        return mLongitudeNums;
    }

    public void setLongitudeNums(int longitudeNums) {
        this.mLongitudeNums = longitudeNums;
    }

    public void setLongitudeLineEffect(PathEffect pathEffect) {
        mLongitudeLinePaint.setPathEffect(pathEffect);
    }

    public void setLatitudeLineEffect(PathEffect pathEffect) {
        mLatitudeLinePaint.setPathEffect(pathEffect);
    }

    public void setLeftTextColor(int color) {
        mLeftTextPaint.setColor(color);
    }

    public void setRightTextColor(int color) {
        mRightTextPaint.setColor(color);
    }

    public void setBottomTextColor(int color) {
        mBottomTextPaint.setColor(color);
    }

    public void setLongitudeLineColor(int color) {
        mLongitudeLinePaint.setColor(color);
    }

    public void setLatitudeLineColor(int color) {
        mLatitudeLinePaint.setColor(color);
    }

    public float getFixedSpaceWithBottom() {
        //乘以2因为上下都需要间隙
        return mFixedSpaceWithLatitudeLine * 2 + Math.abs(mBottomTextPaint.getFontMetrics().ascent);
    }

    public void setLatitudeTextGravity(TextGravity latitudeTextGravity) {
        mLatitudeTextGravity = latitudeTextGravity;
    }

    public void setFixedSpaceWithLeft(float dp) {
        mFixedSpaceWithLeft = getPixelDp(dp);
    }

    public void setFixedSpaceWithLatitudeLine(float dp) {
        mFixedSpaceWithLatitudeLine = getPixelDp(dp);
    }

    @Override
    public void setShownPointNums(int shownPointNums) {
        mShownPointNums = shownPointNums;
    }

    public float getLeftTextWidth(int charCount) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < charCount; i++) {
            sb.append("0");
        }
        return mFixedSpaceWithLeft + mLeftTextPaint.measureText(sb.toString());
    }

    /**
     * 刻度位置
     */
    public enum TextGravity {
        /**
         * 在线之上
         */
        ABOVE_LINE,
        /**
         * 在线之中
         */
        VERTICAL_CENTER_LINE,
        /**
         * 在线之下
         */
        BELOW_LINE
    }

    public interface ScaleXAdapter {

        void scale(int scaleIndex, int listIndex, int indexToEnd, Object bean);

    }

    public interface ScaleYAdapter {

        void scale(int scaleIndex, String value);

    }

    /**
     * 坐标系刻度适配器
     */
    public static abstract class CoordinateScaleAdapter<T> {

        protected ChartView mChartView;

        /**
         * 为了留一点空间，重新计算最小值
         *
         * @param max   最大值
         * @param min   最小值
         * @param yNums 纵轴显示的数量（经线数量）
         * @return
         */
        protected float calYMaxWithSpace(float max, float min, int yNums) {

            return max + Math.abs(max - min) / (yNums - 1) / 5;
        }

        /**
         * 为了留一点空间，重新计算最小值
         *
         * @param max   最大值
         * @param min   最小值
         * @param yNums 纵轴显示的数量（经线数量）
         * @return
         */
        protected float calYMinWithSpace(float max, float min, int yNums) {

            return min + Math.abs(max - min) / (yNums - 1) / 5;
        }

        /**
         * 得到Y轴左边的刻度
         *
         * @param scaleIndex     刻度下标(第几个刻度)
         * @param totalYScaleNum 总下标个数
         */
        public abstract String getYLeftScaleString(List<T> dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalYScaleNum);

        /**
         * 得到Y轴右边的刻度
         */
        public abstract String getYRightScaleString(List<T> dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalYScaleNum);

        /**
         * 得到X轴底部的刻度
         */
        public abstract String getXBottomScaleString(List<T> dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalXScaleNum);

        public void setChartView(ChartView chartView) {

            mChartView = chartView;
        }

    }

}
