package cn.com.vau.common.application

import android.text.TextUtils
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.http.ws.StWsManager
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.StringBean
import cn.com.vau.data.enums.EnumInitStep
import cn.com.vau.data.init.*
import cn.com.vau.data.trade.StTrendBean
import cn.com.vau.data.trade.TrendBean
import cn.com.vau.util.*
import cn.com.vau.util.base64.GZIPUtil
import cn.com.vau.util.opt.AbUtil
import cn.com.vau.util.opt.xhLoge
import com.google.gson.Gson
import com.google.gson.JsonObject
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.pow

/**
 * 初始化接口工具
 */
object InitHelper {

    private val TAG = "ApplicationInit"

    // 初始化重连间隔时长
    private var reconnectInterval = 2 * 1000L

    // 管理器
    private var mRxManager: CompositeDisposable = CompositeDisposable()
    private var mReconnectLaunch: Job? = null
    private var mStepLaunch: Job? = null

    // 代表步骤。 -1:各种接口请求失败，或者WebSocket【主动手动】断开连接（调用breakInit()），  0:初始化流程完成  else 正在链接
    // -1代表各种接口请求失败，这种情况意义不大，外部获取也没有意义，因为当赋值完-1的状态，虽然等待了几秒，但最终还是会赋值为具体的步数
    private var INIT_STATE = -1

    // 初始化执行缓存 -- 当初始化结束时再次执行初始化
    private var hasInitQueue = false

    private var stateChangeCallback = object : LinkStateManager.StateChangeCallback() {
        override fun onProductAndWebsocketSuccess() {
            if (!VauApplication.abOptNetParallel) return
            //Android暂时这么做，因为Android以前接口都是串行的
            //王哥曰：未知其断久矣。若断时已久，且其订单有变（如挂单有变），则前端未得同步此讯。是以，吾需于此重请接口。
            initialize(EnumInitStep.MT4_LOGIN, false)
        }
    }

    init {
        if (VauApplication.abOptNetParallel) {
            LinkStateManager.registerCallback(stateChangeCallback)
        }
    }

    fun start() {
        if (VauApplication.abOptNetParallel) {
            LinkStateManager.dispatchStartInit()
        }
        // 清理需要初始化缓存
        hasInitQueue = false
        // 结束初始化
        finishInit()
        // 断 WebSocket
        WsManager.getInstance().breakSocket()
        StWsManager.getInstance().breakSocket()
        // 开始初始化列表
        initProductList()
        if (VauApplication.abOptNetParallel) {
            startWebSocket()
        }
    }

    //开始链接websocket
    private fun startWebSocket() {
        if (UserDataUtil.isLogin() && UserDataUtil.isStLogin()) {
            StWsManager.getInstance().resetConnect()
        } else {
            WsManager.getInstance().resetConnect()
        }
    }

    /**
     * 0:成功
     * -1:失败
     * else 【正在链接】 【步数】
     */
    @JvmStatic
    fun stepNum(): Int {
        return INIT_STATE
    }

    /**
     * 未成功
     * 【 正在链接 || 失败 】
     */
    @JvmStatic
    fun isNotSuccess(): Boolean {
        return INIT_STATE != 0
    }

    /**
     * state：执行第几步
     * queued：需排队执行【默认排队】
     */
    fun initialize(state: EnumInitStep, queued: Boolean = true) {

        // 排队 && !=成功 && !=失败
        if (queued && INIT_STATE != 0 && INIT_STATE != -1) {
            // 已排队标识
            hasInitQueue = true
            return
        }

        clearRxManager()

        when (state) {
            EnumInitStep.PRODUCT -> initProductList()
            EnumInitStep.MT4_LOGIN -> initTokenCheck()
            EnumInitStep.ORDER -> initPositionList()
            EnumInitStep.SIGNAL_SOURCE -> initStFollowerStrategyList()
            EnumInitStep.USERINFO -> initAccountInfo()
        }

    }

    /**
     * 1 产品列表 压缩【非跟单】
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun initProductList() {

        INIT_STATE = 1

        if (UserDataUtil.isLogin() && UserDataUtil.isStLogin()) {
            initStProductList()
            return
        }

        VAUSdkUtil.collectSymbolList.clear()
        EventBus.getDefault().post(NoticeConstants.Init.APPLICATION_START)

        val startTimeMillisProducts = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("symbol", startTimeMillisProducts)

        val mGson = Gson()
        val jsonMap = HashMap<String, String>()
        if (!UserDataUtil.isLogin()) {
            jsonMap["login"] = ""
            jsonMap["serverId"] = ""
        } else {
            jsonMap["login"] = UserDataUtil.accountCd()
            jsonMap["serverId"] = UserDataUtil.serverId()
        }
        jsonMap["version"] = "3"

        val bodyMap = HashMap<String, String>()
        bodyMap["data"] = mGson.toJson(jsonMap)
        val requestBody =
            mGson.toJson(bodyMap).toRequestBody("application/json".toMediaTypeOrNull())

        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeProductListV2Zip(requestBody),
            object : BaseObserver<StringBean>() {
                override fun onNext(dataBean: StringBean) {
                    LogUtil.i("$TAG --- initProductList --- onNext --- ${dataBean.code} time:${System.currentTimeMillis() - startTimeMillisProducts}")

                    if (dataBean.code != "200") {
                        DealLogUtil.saveFailedDealLog(
                            dataBean.code.toString(), "symbol", startTimeMillisProducts
                        )
                        if (dataBean.code == "********") dataServerTime()
                        reconnectInit()
                        EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_GOODS)
                        if (VauApplication.abOptNetParallel) {
                            LinkStateManager.dispatchProductListError()
                        }
                        return
                    }

                    VAUSdkUtil.shareGoodList().clear()

                    // LogUtil.i("init1111 ---- :", "${GZIPUtil.uncompress(dataBean.data)}")

                    val productsData = GsonUtil.fromJson(
                        GZIPUtil.uncompress(dataBean.data), ProductsData::class.java
                    )

                    VAUSdkUtil.serverTimeMillis =
                        productsData?.currentTime.ifNull("${System.currentTimeMillis()}")

                    productsData?.productList?.let {
                        VAUSdkUtil.shareGoodList().addAll(it)
                    }

                    // 在这开启协程
                    mStepLaunch = GlobalScope.launch {
                        VAUSdkUtil.shareGoodList().forEach { symbolData ->
                            symbolData.symbolList?.forEach {
                                // 问题：K线页切后台放置一段时间后，切回前台时最后一根K线会显示异常
                                // 原因：切回前台时，并在行情没有连接上时，重新进行了初始化，产品数据被重新赋值（originalAsk原始买价 和 originalBid原始卖价 并没有被赋值）导致
                                // 解决方案：产品列表数据返回后 对原始买卖价进行手动赋值
                                it.originalAsk = it.ask
                                it.originalBid = it.bid

                                val pips = it.pips
                                // 计算点差
                                if (pips > 0) {
                                    val pow = 10.0.pow(-it.digits.toDouble())
                                    it.askPips = (pips / 2 + pips % 2) * pow.toFloat()
                                    it.bidPips = pips / 2 * pow.toFloat()
                                    // 因为这里用float计算会有丢失精度的问题，所以使用string 进行计算
                                    it.ask = it.ask.toString().mathAdd(it.askPips.toString()).toFloatCatching()
                                    it.bid = it.bid.toString().mathSub(it.bidPips.toString()).toFloatCatching()
                                }
                            }
                        }
                    }

                    //VAUSdkUtil.updateShareProductUiData() 里的格式化比较耗时每一次循环大概耗时20ms，所以单独放到一个协程执行
                    GlobalScope.launch {
                        VAUSdkUtil.shareGoodList().forEach { symbolData ->
                            symbolData.symbolList?.forEach {
                                VAUSdkUtil.updateShareProductUiData(it)
                            }
                        }
                    }

                    VAUSdkUtil.symbolList().clear()
                    val symbols = mutableListOf<ShareProductData>()
                    for (shareData in VAUSdkUtil.shareGoodList()) {
                        if (shareData.symbolList.isNullOrEmpty()) {
                            continue
                        }
                        symbols.addAll(shareData.symbolList ?: arrayListOf())
                    }
                    VAUSdkUtil.symbolList().addAll(symbols)

                    VAUSdkUtil.initMarketClose()

                    EventBus.getDefault().post(NoticeConstants.Init.DATA_SUCCESS_GOODS)
                    INIT_STATE = 2
                    if (!VauApplication.abOptNetParallel) {
                        WsManager.getInstance().resetConnect()
                    }

                    requestMarketTrendChart()

                    DealLogUtil.saveSuccessDealLog(
                        "count:${VAUSdkUtil.symbolList().size}",
                        "symbol",
                        startTimeMillisProducts
                    )

                    if (VauApplication.abOptNetParallel) {
                        LinkStateManager.dispatchProductListSuccess()
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    LogUtil.i("$TAG --- initProductList --- onError")

                    reconnectInit()
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_GOODS)
                    if (VauApplication.abOptNetParallel) {
                        LinkStateManager.dispatchProductListError()
                    }

                    DealLogUtil.saveFailedDealLog("-1", "symbol", startTimeMillisProducts)

                }
            })
    }

    /**
     * 2 token 校验【非跟单】
     */
    private fun initTokenCheck() {

        INIT_STATE = 3

        if (UserDataUtil.isLogin()) {
            if (UserDataUtil.isStLogin()) {
                initStTokenCheck()
                return
            }
        }

        if (TextUtils.isEmpty(UserDataUtil.accountCd())) {
//            initPositionList()
            finishInit()
            return
        }

        val startTimeMillisLogin = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("login", startTimeMillisLogin)

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("password", UserDataUtil.mt4PWD())
        jsonObject.addProperty("token", UserDataUtil.loginToken())
        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeAccountLogin(requestBody),
            object : BaseObserver<TradeAccountLoginBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(baseBean: TradeAccountLoginBean?) {

                    LogUtil.i("$TAG --- initTokenCheck --- onNext --- ${baseBean?.code} time:${System.currentTimeMillis() - startTimeMillisLogin}")

                    if (baseBean?.code == "********") {
                        EventBus.getDefault().post(NoticeConstants.Init.ACCOUNT_ERROR_OVERDUE)
                        DealLogUtil.saveFailedDealLog(
                            baseBean.code.toString(), "login", startTimeMillisLogin
                        )
                    }

                    // 禁用
                    if (baseBean?.code == "********") {
                        EventBus.getDefault().post(NoticeConstants.Init.ACCOUNT_ERROR_FORBIDDEN)
                    }

                    // demo过期
                    if (baseBean?.code == "********") {
                        EventBus.getDefault().post(NoticeConstants.Init.ACCOUNT_ERROR_DEMO_EXPIRES)
                    }

                    if (baseBean?.code != "200") {
                        DealLogUtil.saveFailedDealLog(
                            baseBean?.code ?: "", "login", startTimeMillisLogin
                        )
                        if (baseBean?.code == "********") dataServerTime()
                        reconnectInit()
                        EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_GOODS)
                        if (VauApplication.abOptNetParallel) {
                            LinkStateManager.dispatchProductListError()
                        }
                        return
                    }

                    DealLogUtil.saveSuccessDealLog("login", startTimeMillisLogin)

                    UserDataUtil.setTradeToken(baseBean.data?.token ?: "")
                    initPositionList()
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    LogUtil.i("$TAG --- mt4Login --- onError")
                    reconnectInit()
                    DealLogUtil.saveFailedDealLog("-1", "login", startTimeMillisLogin)
                }
            })
    }

    /**
     * 3 持仓列表【非跟单】
     */
    private fun initPositionList() {

        INIT_STATE = 4

        EventBus.getDefault().post(NoticeConstants.Init.DATA_REQUEST_ORDER)

        if (UserDataUtil.isLogin() && UserDataUtil.isStLogin()) {
            initStPositionList()
            return
        }

        if (TextUtils.isEmpty(UserDataUtil.accountCd())) {
            finishInit()
            return
        }

        val startTimeMillisOrders = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("check position order", startTimeMillisOrders)

        val mGson = Gson()
        val paramMap = HashMap<String, Any>()
        paramMap["serverId"] = UserDataUtil.serverId()
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["token"] = UserDataUtil.tradeToken()
        val dataObject = JsonObject()
        dataObject.addProperty("data", mGson.toJson(paramMap))
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeOrderPositionListV3(requestBody),
            object : BaseObserver<PositionOrdersBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(baseBean: PositionOrdersBean?) {

                    LogUtil.i("$TAG --- initPositionList --- onNext --- ${baseBean?.code} time:${System.currentTimeMillis() - startTimeMillisOrders}")

                    if (baseBean?.code == "********") {
                        DealLogUtil.saveFailedDealLog(
                            baseBean.code.toString(),
                            "check position order",
                            startTimeMillisOrders
                        )
                        EventBus.getDefault().post(NoticeConstants.Init.ACCOUNT_ERROR_OVERDUE)
                    }

                    if (baseBean?.code != "200") {
                        DealLogUtil.saveFailedDealLog(
                            baseBean?.code ?: "-1", "check position order", startTimeMillisOrders
                        )
                        reconnectInit()
                        EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_ORDER)
                        return
                    }

                    val orderBeanList = baseBean.data?.positionList

                    // 交易日志
                    val ordersSb = StringBuilder()

                    for (orderBean in orderBeanList ?: arrayListOf()) {

                        // 计算手续费
                        orderBean.profitCommission = orderBean.swap
                        orderBean.totalCommission = orderBean.swap.mathAdd(orderBean.commission)
                        // mt4 加 佣金，因为 mt4 平台下单时余额不扣除佣金，mt5平台订单成交时就会扣除佣金
                        if (!UserDataUtil.isMT5()) {
                            orderBean.profitCommission =
                                orderBean.profitCommission.mathAdd(orderBean.commission ?: "0")
                        }

                        ordersSb.append("#${orderBean.order},")

                        VAUSdkUtil.symbolList().firstOrNull {
                            it.symbol == orderBean.symbol
                        }?.apply {
                            orderBean.bid = bid
                            orderBean.ask = ask
                            orderBean.digits = digits
                            orderBean.minvolume = minvolume ?: "0.01"
                            orderBean.priceCurrency = priceCurrency
                        }

                    }

                    VAUSdkUtil.shareOrderList().clear()
                    VAUSdkUtil.shareOrderList().addAll(orderBeanList ?: arrayListOf())
                    VAUSdkUtil.updateOrderProfit()
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_SUCCESS_ORDER)
                    initAccountInfo()

                    var ordersStr = ordersSb.toString()
                    if (ordersStr.isNotEmpty()) {
                        ordersStr = ordersStr.substring(0, ordersStr.length - 1)
                    }

                    DealLogUtil.saveSuccessDealLog(
                        "order:${ordersStr}", "check position order", startTimeMillisOrders
                    )

                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    LogUtil.i("$TAG --- tradeRecords --- onError")
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_ORDER)
                    DealLogUtil.saveFailedDealLog(
                        "-1",
                        "check position order",
                        startTimeMillisOrders
                    )
                    reconnectInit()
                }
            })
    }

    /**
     * 4 账户交易资产信息【非跟单】
     */
    private fun initAccountInfo() {

        if (UserDataUtil.isLogin() && UserDataUtil.isStLogin()) {
            initStAccountInfo()
            return
        }

        INIT_STATE = 5

        if (TextUtils.isEmpty(UserDataUtil.accountCd())) {
            finishInit()
            return
        }

        val startTimeMillisAccountInfo = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("account funds", startTimeMillisAccountInfo)

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        val dataObject = JsonObject()
        dataObject.addProperty("data", jsonObject.toString())
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeAccountInfoV2(requestBody),
            object : BaseObserver<AccountInfoBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(accountBean: AccountInfoBean?) {

                    LogUtil.i("$TAG --- tradeAccountInfo --- onNext --- ${accountBean?.code} time:${System.currentTimeMillis() - startTimeMillisAccountInfo}")

                    if (accountBean?.code != "200") {
                        DealLogUtil.saveFailedDealLog(
                            accountBean?.code.ifNull("-1"),
                            "account funds",
                            startTimeMillisAccountInfo
                        )
                        reconnectInit()
                        return
                    }

                    val infoData = accountBean.data

                    DealLogUtil.saveSuccessDealLog(
                        "credit:${infoData?.credit}  balance:${infoData?.balance}  free margin:${infoData?.marginavailable}",
                        "account funds",
                        startTimeMillisAccountInfo
                    )

                    SpManager.putLeverageTrade(infoData?.leverage.ifNull("1"))

                    // 余额
                    VAUSdkUtil.shareAccountBean().balance = infoData?.balance ?: 0.0
                    // 信用
                    VAUSdkUtil.shareAccountBean().credit = infoData?.credit ?: 0.0
                    // 已用保证金
                    VAUSdkUtil.shareAccountBean().margin = infoData?.marginused ?: 0.0
                    // 警告比例
                    VAUSdkUtil.shareAccountBean().marginCall = infoData?.marginCall.ifNull("80")
                    // 爆仓比例
                    VAUSdkUtil.shareAccountBean().marginStopOut =
                        infoData?.marginStopOut.ifNull("50")

                    // 更新货币类型
                    UserDataUtil.setCurrencyType(infoData?.currency)

                    VAUSdkUtil.updateAccountInfo()

                    finishInit()
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    LogUtil.i("$TAG --- tradeAccountInfo --- onError")
                    DealLogUtil.saveFailedDealLog("-1", "account funds", startTimeMillisAccountInfo)
                    reconnectInit()
                }
            })

    }

    /**
     * 1 产品列表 -- 跟单 (压缩)【跟单】
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun initStProductList() {

        INIT_STATE = 1

        if (AbUtil.isClearAccount()) {
            VAUSdkUtil.collectSymbolList.clear()
        }
        EventBus.getDefault().post(NoticeConstants.Init.APPLICATION_START)

        val startTimeMillisProducts = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("symbol", startTimeMillisProducts)

        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().tradeCompressProductsSimplified(
                UserDataUtil.stAccountId()
            ), object : BaseObserver<StringBean>() {
                override fun onNext(dataBean: StringBean?) {

                    LogUtil.i("$TAG --- initStProductList --- onNext --- ${dataBean?.code} time:${System.currentTimeMillis() - startTimeMillisProducts}")

                    if ("200" != dataBean?.code) {
                        reconnectInit()
                        EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_GOODS)
                        if (VauApplication.abOptNetParallel) {
                            LinkStateManager.dispatchProductListError()
                        }
                        DealLogUtil.saveFailedDealLog(
                            dataBean?.code.toString(), "symbol", startTimeMillisProducts
                        )
                        return
                    }

                    VAUSdkUtil.shareGoodList().clear()

                    // LogUtil.i("init1111 ---- :", "${GZIPUtil.uncompress(dataBean.data)}")

                    val productsData = GsonUtil.fromJson(
                        GZIPUtil.uncompress(dataBean.data), ProductsData::class.java
                    )

                    VAUSdkUtil.serverTimeMillis =
                        productsData?.currentTime.ifNull("${System.currentTimeMillis()}")

                    productsData?.productList?.let {
                        VAUSdkUtil.shareGoodList().addAll(it)
                    }

                    // 跟单长连接有问题，无法全推
                    mStepLaunch = GlobalScope.launch {
                        VAUSdkUtil.shareGoodList().forEach { symbolData ->
                            symbolData.symbolList?.forEach {
                                // 问题：K线页切后台放置一段时间后，切回前台时最后一根K线会显示异常
                                // 原因：切回前台时，并在行情没有连接上时，重新进行了初始化，产品数据被重新赋值（originalAsk原始买价 和 originalBid原始卖价 并没有被赋值）导致
                                // 解决方案：产品列表数据返回后 对原始买卖价进行手动赋值
                                it.originalAsk = it.ask
                                it.originalBid = it.bid

                                val pips = it.pips
                                // 计算点差
                                if (pips > 0) {
                                    val pow = 10.0.pow(-it.digits.toDouble())
                                    it.askPips = (pips / 2 + pips % 2) * pow.toFloat()
                                    it.bidPips = pips / 2 * pow.toFloat()
                                    it.ask += it.askPips
                                    it.bid -= it.bidPips
                                }
                            }
                        }
                    }

                    //VAUSdkUtil.updateShareProductUiData() 里的格式化比较耗时每一次循环大概耗时20ms，所以单独放到一个协程执行
                    GlobalScope.launch {
                        VAUSdkUtil.shareGoodList().forEach { symbolData ->
                            symbolData.symbolList?.forEach {
                                VAUSdkUtil.updateShareProductUiData(it)
                            }
                        }
                    }
                    VAUSdkUtil.symbolList().clear()
                    val symbols = mutableListOf<ShareProductData>()
                    for (good in VAUSdkUtil.shareGoodList()) {
                        if (good.symbolList.isNullOrEmpty()) {
                            continue
                        }
                        symbols.addAll(good?.symbolList ?: arrayListOf())
                    }
                    VAUSdkUtil.symbolList().addAll(symbols)

                    VAUSdkUtil.initMarketClose()

                    EventBus.getDefault().post(NoticeConstants.Init.DATA_SUCCESS_GOODS)
                    INIT_STATE = 2
                    if (!VauApplication.abOptNetParallel) {
                        StWsManager.getInstance().resetConnect()
                    }
                    requestStMarketTrendChart()

                    DealLogUtil.saveSuccessDealLog(
                        "count:${VAUSdkUtil.symbolList().size}",
                        "symbol",
                        startTimeMillisProducts
                    )

                    if (VauApplication.abOptNetParallel) {
                        LinkStateManager.dispatchProductListSuccess()
                    }

                    // 持仓
                    initPositionList()
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    reconnectInit()
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_GOODS)
                    if (VauApplication.abOptNetParallel) {
                        LinkStateManager.dispatchProductListError()
                    }
                    DealLogUtil.saveFailedDealLog("-1", "symbol", startTimeMillisProducts)
                }
            })
    }

    /**
     * 2 token 校验【跟单，其实不需要】
     */
    private fun initStTokenCheck() {
        initStPositionList()
    }

    /**
     * 3 持仓列表 -- 跟单自主【跟单】
     */
    private fun initStPositionList() {

        val startTimeMillisOrders = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("check position order", startTimeMillisOrders)

        HttpUtils.loadData(
            RetrofitHelper.getStHttpService()
                .tradeListOrderV2(
                    "MARKET",
                    UserDataUtil.stMasterPortfolioId()
                ),
            object : BaseObserver<PositionOrdersBean>() {
                override fun onNext(dataBean: PositionOrdersBean?) {

                    LogUtil.i("$TAG --- initStProductList --- onNext --- ${dataBean?.code} time:${System.currentTimeMillis() - startTimeMillisOrders}")

                    if (dataBean?.code != "200") {
                        EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_ORDER)
                        DealLogUtil.saveFailedDealLog(
                            dataBean?.code ?: "-1", "check position order", startTimeMillisOrders
                        )
                        reconnectInit()
                        return
                    }

                    val shareOrderList = VAUSdkUtil.shareOrderList()
                    val stOrderBeanList = dataBean.data?.positionList ?: arrayListOf()
                    // 交易日志
                    val ordersSb = StringBuilder()

                    for (stOrderBean in stOrderBeanList) {

                        // 计算手续费
                        stOrderBean.profitCommission = stOrderBean.swap
                        stOrderBean.totalCommission =
                            stOrderBean.swap.mathAdd(stOrderBean.commission)

                        ordersSb.append("#${stOrderBean.order},")

                        for (shareBean in VAUSdkUtil.symbolList()) {
                            if (stOrderBean.symbol == shareBean.symbol) {
                                stOrderBean.bid = shareBean.bid
                                stOrderBean.ask = shareBean.ask
                                stOrderBean.digits = shareBean.digits
                                stOrderBean.minvolume = shareBean.minvolume.ifNull("0.01")
                                stOrderBean.priceCurrency = shareBean.priceCurrency
                                stOrderBean.closePrice = "${if (OrderUtil.isBuyOfOrder(stOrderBean.cmd)) shareBean.bid else shareBean.ask}"
                                stOrderBean.profit = VAUSdkUtil.getProfitLoss(
                                    shareBean,
                                    stOrderBean.openPrice.ifNull(),
                                    stOrderBean.volume.ifNull(),
                                    stOrderBean.cmd.ifNull()
                                ).toDouble()
                            }
                        }
                    }

                    shareOrderList.clear()
                    shareOrderList.addAll(stOrderBeanList)
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_SUCCESS_ORDER)

                    var ordersStr = ordersSb.toString()
                    if (ordersStr.isNotEmpty()) {
                        ordersStr = ordersStr.substring(0, ordersStr.length - 1)
                    }
                    DealLogUtil.saveSuccessDealLog(
                        "order:${ordersStr}", "check position order", startTimeMillisOrders
                    )

                    initStFollowerStrategyList()
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    EventBus.getDefault().post(NoticeConstants.Init.DATA_ERROR_ORDER)
                    DealLogUtil.saveFailedDealLog(
                        "-1",
                        "check position order",
                        startTimeMillisOrders
                    )
                    reconnectInit()
                }
            })
    }

    /**
     * 4 跟随策略列表 -- 跟单（含跟随订单）【跟单】
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun initStFollowerStrategyList() {

        INIT_STATE = 4

        val startTimeMillisFollower = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("check position copy trader", startTimeMillisFollower)
        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().orderFollowerDetailListV2(
                UserDataUtil.stAccountId()
            ),
            object : BaseObserver<StFollowStrategyBean>() {
                override fun onNext(dataBean: StFollowStrategyBean?) {

                    LogUtil.i("$TAG --- initStFollowerStrategyList --- onNext --- ${dataBean?.code} time:${System.currentTimeMillis() - startTimeMillisFollower}")

                    if ("200" != dataBean?.code) {
                        DealLogUtil.saveFailedDealLog(
                            dataBean?.code ?: "",
                            "check position copy trader", startTimeMillisFollower
                        )
                        reconnectInit()
                        return
                    }

                    val followStrategyList = dataBean.data?.followDetailsList ?: arrayListOf()

                    // 投资金额 （会加会减）
                    var followInvestmentAmount = "0"
                    // 历史收益
                    var followHistoryProfit = 0.0
                    // 已用预付款
                    var followMarginUsed = 0.0
                    // 信用额
                    var followCredit = "0"

                    val stFollowsSb = StringBuilder()
                    val stFollowOrdersSb = StringBuilder()

                    for ((index, followBean) in followStrategyList.withIndex()) {

                        // 投资金额（会加会减）
                        followInvestmentAmount =
                            followInvestmentAmount.mathAdd(followBean.investmentAmount)

                        // 对等信号源 totalHistoryProfit
                        followHistoryProfit += followBean.totalHistoryProfit
                        followMarginUsed += followBean.marginUsed
                        followCredit = followCredit.mathAdd(followBean.investmentCredit)

                        // 日志 对等信号源 signalName
                        stFollowsSb.append("${if (0 == index) "" else " , "}${followBean.strategyName} (")

                        for ((orderIndex, stOrderBean) in (followBean.positions
                            ?: CopyOnWriteArrayList()).withIndex()) {

                            stFollowOrdersSb.append("${if (0 == orderIndex) "order:" else ","}#${stOrderBean.order}")

                            for (shareBean in VAUSdkUtil.symbolList()) {

                                if (shareBean.symbol != stOrderBean.symbol) continue

                                stOrderBean.digits = shareBean.digits
                                stOrderBean.bid = shareBean.bid
                                stOrderBean.ask = shareBean.ask
                                stOrderBean.minvolume = shareBean.minvolume.ifNull("0.01")
                                stOrderBean.priceCurrency = shareBean.priceCurrency
                                stOrderBean.profit = VAUSdkUtil.getProfitLoss(
                                    shareBean,
                                    stOrderBean.openPrice.ifNull(),
                                    stOrderBean.volume.ifNull(),
                                    stOrderBean.cmd.ifNull()
                                ).toDouble()
                            }

                        }

                        stFollowsSb.append("${stFollowOrdersSb})")
                        stFollowOrdersSb.clear()

                    }

                    DealLogUtil.saveSuccessDealLog(
                        "copy trader: $stFollowsSb",
                        "check position copy trader",
                        startTimeMillisFollower
                    )

                    VAUSdkUtil.stShareAccountBean().followInvestmentAmount = followInvestmentAmount

                    VAUSdkUtil.stShareAccountBean().followTotalHistoryProfit = followHistoryProfit
                    VAUSdkUtil.stShareAccountBean().followMarginUsed = followMarginUsed
                    VAUSdkUtil.stShareAccountBean().followCredit = followCredit

                    VAUSdkUtil.stShareStrategyList().clear()
                    VAUSdkUtil.stShareStrategyList().addAll(followStrategyList)
                    // 计算策略数据 （防止下拉刷新时有瞬间数据显示不对）
                    mStepLaunch = GlobalScope.launch {
                        // 子线程
                        VAUSdkUtil.updateStFollowStrategyProfit()
                    }

                    EventBus.getDefault().post(NoticeConstants.Init.DATA_SUCCESS_FOLLOWERS_ORDER_ST)

                    initStAccountInfo()

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    DealLogUtil.saveFailedDealLog(
                        "-1",
                        "check position copy trader",
                        startTimeMillisFollower
                    )
                    reconnectInit()
                }

            }
        )

    }

    /**
     * 5 账户交易资产信息 -- 【跟单】
     */
    private fun initStAccountInfo() {
        INIT_STATE = 5

        val startTimeMillisAccountInfo = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("account funds", startTimeMillisAccountInfo)

        HttpUtils.loadData(
            RetrofitHelper.getStHttpService()
                .accountAccMarginSimplified(UserDataUtil.stToken()),
            object : BaseObserver<StAccMarginSimplifiedBean>() {
                override fun onNext(dataBean: StAccMarginSimplifiedBean?) {

                    LogUtil.i("$TAG --- initStAccountInfo --- onNext --- ${dataBean?.code} time:${System.currentTimeMillis() - startTimeMillisAccountInfo}")

                    if (dataBean?.code != "200") {
                        reconnectInit()
                        DealLogUtil.saveFailedDealLog(
                            dataBean?.code.ifNull("-1"), "account funds", startTimeMillisAccountInfo
                        )
                        return
                    }

                    val stShareAccountBean = VAUSdkUtil.stShareAccountBean()
                    val accountBean = dataBean.data

                    DealLogUtil.saveSuccessDealLog(
                        "credit:${accountBean?.credit}  balance:${accountBean?.balance}  free margin:${accountBean?.freeMargin}",
                        "account funds",
                        startTimeMillisAccountInfo
                    )

                    SpManager.putLeverageTrade(accountBean?.leverage.ifNull("1"))

                    // 只有资金管理页面使用
                    stShareAccountBean.closeProfit = accountBean?.closeProfit.toDoubleCatching()

                    // 自主交易-净值
                    stShareAccountBean.equity = accountBean?.equity.toDoubleCatching()
                    // 自主交易-可用金额
                    stShareAccountBean.balance = accountBean?.balance.toDoubleCatching()
                    // 自主交易-Credit
                    stShareAccountBean.credit = accountBean?.credit.toDoubleCatching()
                    // 自主交易-已用预付款
                    stShareAccountBean.margin = accountBean?.margin.toDoubleCatching()
                    // 可用金额
                    stShareAccountBean.followBalance = accountBean?.followBalance.toDoubleCatching()
                    // 跟单净值
                    stShareAccountBean.followEquity = accountBean?.followEquity.toDoubleCatching()
                    // 浮动盈亏
                    stShareAccountBean.followFloatingPl =
                        accountBean?.followFloatingPl.toDoubleCatching()
                    // 跟單历史收益
                    stShareAccountBean.wholeHistoryProfit = accountBean?.wholeHistoryProfit ?: 0.0
                    // 警告比例
                    stShareAccountBean.marginCall = accountBean?.marginCall.ifNull("80")
                    // 爆仓比例
                    stShareAccountBean.marginStopOut = accountBean?.marginStopOut.ifNull("50")
                    //自主交易是否展示Reset
//                    stShareAccountBean.allowReset = accountBean?.allowReset

                    UserDataUtil.setCurrencyType(accountBean?.currencyType ?: "")

                    VAUSdkUtil.updateAccountInfo()

                    finishInit()

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    LogUtil.i("$TAG --- st --- stAccountAccMargin --- onError")
                    reconnectInit()
                    DealLogUtil.saveFailedDealLog("-1", "account funds", startTimeMillisAccountInfo)
                }

            })
    }

    /**
     * 重连：初始化异常时执行
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun reconnectInit() {
        val stepNum = INIT_STATE
        INIT_STATE = -1
        mReconnectLaunch = GlobalScope.launch {
            delay(reconnectInterval)
            when (stepNum) {
                1 -> initProductList()
                3 -> initTokenCheck()
                4 -> initPositionList()
                5 -> initAccountInfo()
                else -> {
                    WsManager.getInstance().breakSocket()
                    StWsManager.getInstance().breakSocket()
                    initProductList()
                }
            }
        }
    }

    fun breakInit() {
        INIT_STATE = -1
        clearRxManager()
        WsManager.getInstance().breakSocket()
        StWsManager.getInstance().breakSocket()
    }

    private fun finishInit() {

        // application初始化结束
        INIT_STATE = 0

        clearRxManager()

        EventBus.getDefault().post(NoticeConstants.Init.APPLICATION_END)

        if (hasInitQueue) {
            initialize(EnumInitStep.MT4_LOGIN, false)
            hasInitQueue = false
        }

    }

    // 获取服务器时间戳
    fun dataServerTime() {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService().dataServerTime(HashMap<String, Any>()),
            object : BaseObserver<ServerTimeBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(timeData: ServerTimeBean?) {
                    if ("00000000" == timeData?.resultCode) {
                        Constants.offServerTime =
                            System.currentTimeMillis() - timeData.data?.obj?.st.toLongCatching()
                        tradeSeason()
                    }
                }
            })
    }

    private fun tradeSeason() {
        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeSeasonGet(HashMap<String, Any>()),
            object : BaseObserver<SeasonBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(dataBean: SeasonBean?) {
                    if ("200" != dataBean?.code) return
                    Constants.season = dataBean.obj?.season ?: 0
                    VAUSdkUtil.initMarketClose()
                }
            })
    }

    //多品牌请求行情走势图数据
    private fun requestMarketTrendChart() {
        val mGson = Gson()
        val jsonMap = HashMap<String, Any>()
        // 后台同事确认，没有过滤条件
        val symbolStr = VAUSdkUtil.symbolList().map { it.symbol }
        jsonMap["symbols"] = symbolStr
        jsonMap["serverId"] = UserDataUtil.serverId()

        val bodyMap = HashMap<String, String>()
        bodyMap["data"] = mGson.toJson(jsonMap)
        val requestBody =
            mGson.toJson(bodyMap).toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.loadData(
            RetrofitHelper.getHttpService2().tradeProductTrend(requestBody),
            object : BaseObserver<TrendBean>() {
                override fun onNext(dataBean: TrendBean) {

                    if ("200" != dataBean.code) return

                    val objData = dataBean.obj ?: return

                    VAUSdkUtil.shareGoodList().forEach { group ->
                        for (shareData in group.symbolList ?: arrayListOf()) {
                            val trendData = objData[shareData.symbol] ?: continue
                            shareData.trendList = trendData
                        }
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    //这个接口较慢，取消了容易获取不到内容
                    // mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    xhLoge("趋势图 --- error  -----${e?.message}")
                    e?.printStackTrace()
                }
            })
    }

    //跟单请求行情走势图数据
    private fun requestStMarketTrendChart() {
        val mGson = Gson()
        val jsonMap = HashMap<String, Any>()
        // 后台同事确认，没有过滤条件
        val symbolStr = VAUSdkUtil.symbolList().map { it.symbol }
        jsonMap["symbols"] = symbolStr
        jsonMap["server"] = UserDataUtil.serverId()

        val requestBody =
            mGson.toJson(jsonMap).toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.loadData(
            RetrofitHelper.getStHttpService().historyGetRunChart(requestBody),
            object : BaseObserver<StTrendBean>() {
                override fun onNext(dataBean: StTrendBean) {

                    if ("200" != dataBean.code) return
                    val trendList: List<Map<String, ArrayList<String>>>? = dataBean.data?.data?.data
                    if (trendList.isNullOrEmpty()) {
                        return
                    }

                    //将返回的趋势图数据存到产品列表中
                    //1 遍历产品列表
                    VAUSdkUtil.shareGoodList().forEach { group ->
                        val list = group.symbolList
                        list?.forEach { product -> //2 遍历网络返回的趋势图数据
                            trendList.forEach { trendMap -> //3 如果产品列表中包含网络返回的趋势图数据，则赋值给产品列表
                                if (trendMap.containsKey(product.symbol)) {
                                    product.trendList = trendMap[product.symbol]
                                }
                            }
                        }
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    //这个接口较慢，取消了容易获取不到内容
                    //mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    e?.printStackTrace()
                }
            })
    }

    /**
     * 取消所有订阅 || 延迟任务
     */
    fun clearRxManager() {
        mRxManager.clear()
        mReconnectLaunch?.cancel()
        mStepLaunch?.cancel()
    }

}