package cn.com.vau.common.storage

object UserStorage {

    private val storage = StorageUtil.userStorage

    //------账号信息(登录时返回)--------------------------------------------------------------------------------------------
    private const val STORAGE_USERID = "userid"                             // userId
    private const val STORAGE_LOGIN_TOKEN = "login_token"                   // 登录Token
    private const val STORAGE_USER_TEL = "user_tel"                         // 手机号
    private const val STORAGE_COUNTRY_CODE = "country_code"                 // 国家code
    private const val STORAGE_AREA_CODE = "area_code"                       // 地区code
    private const val STORAGE_USER_TYPE = "user_type"                       // 用户类型  1:普通账户  0:IB账户
    private const val STORAGE_FAST_CLOSE_STATE = "fast_close_state"         // 是否快速平仓
    private const val STORAGE_FAST_STOP_COPY = "fast_stop_copy"             // 是否快速停止跟单
    private const val STORAGE_EMAIL = "email"                               // 邮箱
    private const val STORAGE_USER_NICK = "user_nick"                       // 昵称
    private const val STORAGE_USER_PIC = "user_pic"                         // 头像地址
    private const val STORAGE_USER_PASSWORD = "user_password"               // 用户密码(用于Facebook授权后直接登录账户)
    private const val STORAGE_ORDER_CONFIRMATION = "order_confirmation"     // 是否订单确认

    //------账户公用字段-------------------------------------------------------------------------------------------------
    private const val STORAGE_ACCOUNT_CD = "accountCd"                      // 当前登录账户账户号(对应跟单接口字段：serverAccountId   对应多品牌接口字段：accountCd)
    private const val STORAGE_CURRENCY_TYPE = "currency_type"               // 账户货币类型
    private const val STORAGE_SERVERID = "serverId"                         // 服务器id  默认未登录是5
    private const val STORAGE_READONLY_STATE = "readonly_state"             // 账户只读状态
    private const val STORAGE_ACCOUNT_DEAL_TYPE = "account_deal_type"       // 账户类型  0：申请中账户  1：交易账户，2：返佣账户(暂不显示)，3：模拟账户，4:pending账户 (审核状态之一:拒绝/通过/pending)，5：跟单账户，6：虚拟的MT5假Live账户
    private const val STORAGE_PLATFORM = "platform"                         // 账户平台 4:mt4 / 5:mt5 / 2:mts / 7:vts
    private const val STORAGE_KYC_LEVEL = "kyc_level"                       // 用户已完成的KYC等级

    //------多品牌账户字段-------------------------------------------------------------------------------------------------
    private const val STORAGE_ACCOUNT_TYPE = "account_type"                 // 多品牌账户类型（1：Standard STP，2：Raw ECN，6：Islamic STP，13：Hedge STP，14：Hedge ECN）
    private const val STORAGE_TRADE_TOKEN = "trade_token"                   // 交易Token
    private const val STORAGE_MT4_PWD = "mt4_pwd"                           // 交易密码
    private const val STORAGE_MT4_STATE = "mt4_state"                       // 当前账户状态 1:审核中,2:真实账户,3:模拟账号,4:返佣账户
    private const val STORAGE_USER_REAL_NAME = "user_real_name"             // 真实姓名
    private const val STORAGE_OPEN_ACCOUNT_TYPE = "open_account_type"       // 开户类型  1：没有开下来户  2：已开live账户没有开跟单账户  3：已开跟单账户没有开live账户  4：已开live账户和跟单账户
    private const val STORAGE_OPEN_LIVE_ACCO_STATE = "open_live_acco_state" // 是否开通真实账户  0未开通   1已开通

    //------跟单账户字段-------------------------------------------------------------------------------------------------
    private const val STORAGE_ST_ACCOUNT_ID = "st_accountId"                // 跟单账户的accountId
    private const val STORAGE_MASTER_PORTFOLIOID = "master_portfolioId"     // 跟单账户信号源PortfolioId
    private const val STORAGE_IS_ST_PUBLIC_TRADE = "is_st_public_trade"     // 跟单账户是否是信号源
    private const val STORAGE_ST_TOKEN = "st_token"                         // 跟单账户交易Token
    private const val STORAGE_ST_USER_ID = "st_userid"                      // 跟单多策略用户id
    private const val STORAGE_ST_USER_AREA = "st_user_area"                 // 跟单账户地区: 'GS'或'APAC'  GS区域账户跟随指定策略时获取额外收益或亏损兜底


    fun getUserId(): String {
        return storage.decodeString(STORAGE_USERID)
    }
    fun setUserId(userId: String?): Boolean {
        return storage.encode(STORAGE_USERID, userId)
    }

    fun getLoginToken(): String {
        return storage.decodeString(STORAGE_LOGIN_TOKEN)
    }
    fun setLoginToken(loginToken: String?): Boolean {
        return storage.encode(STORAGE_LOGIN_TOKEN, loginToken)
    }

    fun getUserType(): Int {
        return storage.decodeInt(STORAGE_USER_TYPE, -1)
    }
    fun setUserType(userType: Int?): Boolean {
        return storage.encode(STORAGE_USER_TYPE, userType)
    }

    fun getAccountType(): String {
        return storage.decodeString(STORAGE_ACCOUNT_TYPE)
    }
    fun setAccountType(value: String?): Boolean {
        return storage.encode(STORAGE_ACCOUNT_TYPE, value)
    }

    fun getStAccountId(): String {
        return storage.decodeString(STORAGE_ST_ACCOUNT_ID)
    }
    fun setStAccountId(value: String?): Boolean {
        return storage.encode(STORAGE_ST_ACCOUNT_ID, value)
    }

    fun getCurrencyType(): String {
        return storage.decodeString(STORAGE_CURRENCY_TYPE)
    }
    fun setCurrencyType(value: String?): Boolean {
        return storage.encode(STORAGE_CURRENCY_TYPE, value)
    }

    fun getTradeToken(): String {
        return storage.decodeString(STORAGE_TRADE_TOKEN)
    }
    fun setTradeToken(value: String?): Boolean {
        return storage.encode(STORAGE_TRADE_TOKEN, value)
    }

    fun getMt4PWD(): String {
        return storage.decodeString(STORAGE_MT4_PWD)
    }
    fun setMt4PWD(value: String?):Boolean {
        return storage.encode(STORAGE_MT4_PWD, value)
    }

    fun getMt4State(): String {
        return storage.decodeString(STORAGE_MT4_STATE)
    }
    fun setMt4State(value: String?):Boolean {
        return storage.encode(STORAGE_MT4_STATE, value)
    }

    fun getCountryCode(): String {
        return storage.decodeString(STORAGE_COUNTRY_CODE)
    }
    fun setCountryCode(value: String?):Boolean {
        return storage.encode(STORAGE_COUNTRY_CODE, value)
    }

    fun getAreaCode(): String {
        return storage.decodeString(STORAGE_AREA_CODE)
    }
    fun setAreaCode(value: String?):Boolean {
        return storage.encode(STORAGE_AREA_CODE, value)
    }

    fun getServerId(): String {
        return storage.decodeString(STORAGE_SERVERID, "5")
    }
    fun setServerId(value: String?):Boolean {
        return storage.encode(STORAGE_SERVERID, value)
    }

    fun getAccountCd(): String {
        return storage.decodeString(STORAGE_ACCOUNT_CD)
    }
    fun setAccountCd(value: String?):Boolean {
        return storage.encode(STORAGE_ACCOUNT_CD, value)
    }

    fun getMasterPortfolioId(): String {
        return storage.decodeString(STORAGE_MASTER_PORTFOLIOID)
    }
    fun setMasterPortfolioId(value: String?):Boolean {
        return storage.encode(STORAGE_MASTER_PORTFOLIOID, value)
    }

    fun getIsStPublicTrade(): Boolean {
        return storage.decodeBoolean(STORAGE_IS_ST_PUBLIC_TRADE)
    }
    fun setIsStPublicTrade(value: Boolean?):Boolean {
        return storage.encode(STORAGE_IS_ST_PUBLIC_TRADE, value)
    }

    fun getStToken(): String {
        return storage.decodeString(STORAGE_ST_TOKEN)
    }
    fun setStToken(value: String?):Boolean {
        return storage.encode(STORAGE_ST_TOKEN, value)
    }

    fun getUserTel(): String {
        return storage.decodeString(STORAGE_USER_TEL)
    }
    fun setUserTel(value: String?):Boolean {
        return storage.encode(STORAGE_USER_TEL, value)
    }

    fun getEmail(): String {
        return storage.decodeString(STORAGE_EMAIL)
    }
    fun setEmail(value: String?):Boolean {
        return storage.encode(STORAGE_EMAIL, value)
    }

    fun getUserNick(): String {
        return storage.decodeString(STORAGE_USER_NICK)
    }
    fun setUserNick(value: String?): Boolean {
        return storage.encode(STORAGE_USER_NICK, value)
    }

    fun getUserRealName(): String {
        return storage.decodeString(STORAGE_USER_REAL_NAME)
    }
    fun setUserRealName(value: String?): Boolean {
        return storage.encode(STORAGE_USER_REAL_NAME, value)
    }

    fun getUserPic(): String {
        return storage.decodeString(STORAGE_USER_PIC)
    }
    fun setUserPic(value: String?): Boolean {
        return storage.encode(STORAGE_USER_PIC, value)
    }

    fun getPlatForm(): String {
        return storage.decodeString(STORAGE_PLATFORM)
    }
    fun setPlatForm(value: String?): Boolean {
        return storage.encode(STORAGE_PLATFORM, value)
    }

    fun getFastCloseState(): String {
        return storage.decodeString(STORAGE_FAST_CLOSE_STATE)
    }
    fun setFastCloseState(value: String?): Boolean {
        return storage.encode(STORAGE_FAST_CLOSE_STATE, value)
    }

    fun getOpenAccountType(): Int {
        return storage.decodeInt(STORAGE_OPEN_ACCOUNT_TYPE, -1)
    }
    fun setOpenAccountType(value: Int?): Boolean {
        return storage.encode(STORAGE_OPEN_ACCOUNT_TYPE, value)
    }

    fun getOpenLiveAccountState(): String {
        return storage.decodeString(STORAGE_OPEN_LIVE_ACCO_STATE)
    }
    fun setOpenLiveAccountState(value: String?): Boolean {
        return storage.encode(STORAGE_OPEN_LIVE_ACCO_STATE, value)
    }

    fun getUserPassword(): String {
        return storage.decodeString(STORAGE_USER_PASSWORD)
    }
    fun setUserPassword(value: String?): Boolean {
        return storage.encode(STORAGE_USER_PASSWORD, value)
    }

    fun getStUserId(): String {
        return storage.decodeString(STORAGE_ST_USER_ID)
    }
    fun setStUserId(value: String?): Boolean {
        return storage.encode(STORAGE_ST_USER_ID, value)
    }

    fun getReadOnlyState(): Boolean {
        return storage.decodeBoolean(STORAGE_READONLY_STATE)
    }
    fun setReadOnlyState(value: Boolean?): Boolean {
        return storage.encode(STORAGE_READONLY_STATE, value)
    }

    fun getAccountDealType(): String {
        return storage.decodeString(STORAGE_ACCOUNT_DEAL_TYPE)
    }
    fun setAccountDealType(value: String?): Boolean {
        return storage.encode(STORAGE_ACCOUNT_DEAL_TYPE, value)
    }

    // 快速停止跟单
    fun getFastStopCopyState(): String {
        return storage.decodeString(STORAGE_FAST_STOP_COPY)
    }
    fun setFastStopCopyState(value: String?): Boolean {
        return storage.encode(STORAGE_FAST_STOP_COPY, value)
    }

    fun getOrderConfirmationState(): String {
        return storage.decodeString(STORAGE_ORDER_CONFIRMATION)
    }
    fun setOrderConfirmationState(value: String?): Boolean {
        return storage.encode(STORAGE_ORDER_CONFIRMATION, value)
    }

    fun getStUserArea(): String {
        return storage.decodeString(STORAGE_ST_USER_AREA)
    }
    fun setStUserArea(value: String?): Boolean {
        return storage.encode(STORAGE_ST_USER_AREA, value)
    }

    fun getKycLevel(): String {
        return storage.decodeString(STORAGE_KYC_LEVEL)
    }
    fun setKycLevel(value: String?): Boolean {
        return storage.encode(STORAGE_KYC_LEVEL, value)
    }
}