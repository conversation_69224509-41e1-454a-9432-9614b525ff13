package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.viewpager2.widget.ViewPager2
import androidx.viewpager2.widget.ViewPager2.ORIENTATION_HORIZONTAL
import kotlin.math.absoluteValue
import kotlin.math.sign

/**
 * 基于官方代码改造的，可多层嵌套 ViewPager2 的包装控件
 * 解决 ViewPager2 三层嵌套滑动联动问题【暂时有问题】
 */
class MultiNestedScrollableHost : FrameLayout {

    private var touchSlop = 0
    private var initialX = 0f
    private var initialY = 0f

    init {
        touchSlop = ViewConfiguration.get(context).scaledTouchSlop
    }

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    private val parentViewPager: ViewPager2?
        get() {
            var v: View? = parent as? View
            while (v != null && v !is ViewPager2) {
                v = v.parent as? View
            }
            return v as? ViewPager2
        }

    private val parentHost: MultiNestedScrollableHost?
        get() {
            var v: View? = parent as? View
            while (v != null && v !is MultiNestedScrollableHost) {
                v = v.parent as? View
            }
            return v as? MultiNestedScrollableHost
        }

    private var childViewPager: ViewPager2? = null

    private fun findChildViewPager(view: View?) {
        if (view !is ViewGroup) return
        (0 until view.childCount).forEach { index ->
            val childView = view.getChildAt(index)
            if (childView is ViewPager2) {
                childViewPager = childView
                return
            } else {
                findChildViewPager(childView)
            }
        }
    }

    private val child: ViewPager2? get() = if (childCount > 0) getChildAt(0) as ViewPager2? else null

    private fun canChildScroll(orientation: Int, delta: Float): Boolean {
        val direction = -delta.sign.toInt()
        return when (orientation) {
            0 -> child?.canScrollHorizontally(direction) ?: false
            1 -> child?.canScrollVertically(direction) ?: false
            else -> throw IllegalArgumentException()
        }
    }

    override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
        handleInterceptTouchEvent(e)
        return super.onInterceptTouchEvent(e)
    }

    private fun handleInterceptTouchEvent(e: MotionEvent) {
        val orientation = parentViewPager?.orientation ?: return

        if (!canChildScroll(orientation, -1f) && !canChildScroll(orientation, 1f)) {
            return
        }

        if (e.action == MotionEvent.ACTION_DOWN) {
            initialX = e.x
            initialY = e.y
            findChildViewPager(child)
            if (childViewPager != null && parentHost == null && parentViewPager != null) {
                // 识别到还剩最外层ViewPager，设置为不可滑动后，最外层不拦截事件
                parentViewPager?.isUserInputEnabled = false
            }
            parent.requestDisallowInterceptTouchEvent(true)
        } else if (e.action == MotionEvent.ACTION_MOVE) {
            val dx = e.x - initialX
            val dy = e.y - initialY
            val isVpHorizontal = orientation == ORIENTATION_HORIZONTAL

            // assuming ViewPager2 touch-slop is 2x touch-slop of child
            val scaledDx = dx.absoluteValue * if (isVpHorizontal) .5f else 1f
            val scaledDy = dy.absoluteValue * if (isVpHorizontal) 1f else .5f

            if (scaledDx > touchSlop || scaledDy > touchSlop) {
                if (isVpHorizontal == (scaledDy > scaledDx)) {
                    // Gesture is perpendicular, allow all parents to intercept
                    parent.requestDisallowInterceptTouchEvent(false)
                } else {
                    // Gesture is parallel, query child if movement in that direction is possible
                    if (canChildScroll(orientation, if (isVpHorizontal) dx else dy)) {
                        // Child can scroll, disallow all parents to intercept
                        parent.requestDisallowInterceptTouchEvent(true)
                    } else {
                        // Child cannot scroll, allow all parents to intercept
                        parent.requestDisallowInterceptTouchEvent(false)
                        if (childViewPager != null && parentHost == null && parentViewPager != null) {
                            // 识别到还剩最外层ViewPager，并且自己层的viewpager不能再滑动后，设置为外层可以滑动，交给最外层处理事件
                            parentViewPager?.isUserInputEnabled = true
                        }
                    }
                }
            }
        } else if (e.action == MotionEvent.ACTION_UP || e.action == MotionEvent.ACTION_CANCEL) {
            if (parentHost == null && parentViewPager != null) {
                parentViewPager?.isUserInputEnabled = true
            }
        }
    }

}