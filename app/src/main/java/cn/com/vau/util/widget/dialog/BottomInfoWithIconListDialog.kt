package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Build
import android.text.TextPaint
import android.text.TextUtils
import android.view.LayoutInflater
import android.widget.TextView
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.util.screenHeight
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.DialogBottomInfoWithIconListBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.base.fixNavigationBarPadding
import com.lxj.xpopup.core.BottomPopupView

@SuppressLint("ViewConstructor")
@Suppress("unused")
class BottomInfoWithIconListDialog(
    context: Context,
    private var title: CharSequence? = null,
    private val dataList: ArrayList<HintLocalData>,
    private var linkText: CharSequence? = null,
    private var onLinkListener: ((TextView) -> Unit)? = null,
) : BottomPopupView(context), IDialog<DialogBottomInfoWithIconListBinding> {

    private val inflater = LayoutInflater.from(context)

    private var mContentBinding =
        DialogBottomInfoWithIconListBinding.inflate(inflater, bottomPopupContainer, false)

    private var tvTitle: TextView? = null

    private var adapter: BottomInfoWithIconListAdapter? = null

    override fun addInnerContent() {
        bottomPopupContainer.addView(mContentBinding.root)
    }

    override fun getMaxHeight(): Int = (screenHeight * 0.75).toInt()

    override fun onCreate() {
        super.onCreate()
        initView()
        fixNavigationBarPadding()
        processBackPress()
    }

    private fun processBackPress() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && dialog != null) {
            dialog.onBackInvokedDispatcher.registerOnBackInvokedCallback(0) {
                if (onBackPressed()) {
                    return@registerOnBackInvokedCallback
                }
                if (popupInfo.isDismissOnBackPressed &&
                    (popupInfo.xPopupCallback == null || !popupInfo.xPopupCallback.onBackPressed(this))) {
                    dismissOrHideSoftInput()
                }
            }
        }
    }

    private fun initView() {
        setTitleInner(title)
        setRecyclerView()
        setLinkView()
    }

    /**
     * 设置标题
     */
    fun setTitle(title: CharSequence?): BottomInfoWithIconListDialog {
        this.title = title
        setTitleInner(title)
        return this
    }

    private fun setTitleInner(title: CharSequence?) {
        val params = mContentBinding.mRecyclerView.layoutParams as? MarginLayoutParams
        if (!TextUtils.isEmpty(title)) {
            mContentBinding.tvTitle.isVisible = true
            tvTitle = mContentBinding.root.findViewById(R.id.tvTitle)
            tvTitle?.text = title
            params?.topMargin = 8.dp2px()
        } else {
            mContentBinding.tvTitle.isVisible = false
            params?.topMargin = 18.dp2px()
        }
        mContentBinding.mRecyclerView.layoutParams = params
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setDataList(dataList: List<HintLocalData>?): BottomInfoWithIconListDialog {
        if (dataList.isNullOrEmpty()) {
            this.dataList.clear()
        } else {
            this.dataList.clear()
            this.dataList.addAll(dataList)
        }
        adapter?.notifyDataSetChanged()
        return this
    }

    private fun setLinkView() {
        setLinkText()
        setLinkListener()
    }

    private fun setLinkText() {
        val isVisible = !linkText.isNullOrEmpty()
        mContentBinding.tvLink.isVisible = isVisible
        mContentBinding.tvLink.text = this.linkText
        if (isVisible) {
            mContentBinding.tvLink.paintFlags =
                mContentBinding.tvLink.paintFlags or TextPaint.UNDERLINE_TEXT_FLAG
        }
    }

    fun setLinkText(linkText: CharSequence?): BottomInfoWithIconListDialog {
        this.linkText = linkText
        setLinkText()
        return this
    }

    fun setLinkListener(onLinkListener: ((TextView) -> Unit)?): BottomInfoWithIconListDialog {
        this.onLinkListener = onLinkListener
        setLinkListener()
        return this
    }

    private fun setLinkListener() {
        mContentBinding.tvLink.setOnClickListener {
            onLinkListener?.invoke(mContentBinding.tvLink)
            dismissDialog()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setRecyclerView() {
        mContentBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        adapter = BottomInfoWithIconListAdapter(context, dataList)
        mContentBinding.mRecyclerView.adapter = adapter
        adapter?.notifyDataSetChanged()
    }

    override fun getContentViewBinding(): DialogBottomInfoWithIconListBinding {
        return mContentBinding
    }

    override fun isShowDialog(): Boolean {
        return super.isShow()
    }

    override fun showDialog() {
        if (popupInfo == null) {
            return
        }
        super.show()
    }

    override fun dismissDialog() {
        super.dismiss()
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomInfoWithIconListBinding, Builder>(activity) {
        private var dataList: ArrayList<HintLocalData> = ArrayList(0)
        private var title: CharSequence? = null
        private var linkText: CharSequence? = null
        private var onLinkListener: ((TextView) -> Unit)? = null

        /**
         * DataList
         *
         */
        fun setDataList(dataList: List<HintLocalData>?) = apply {
            if (dataList.isNullOrEmpty()) {
                this.dataList.clear()
            } else {
                this.dataList.clear()
                this.dataList.addAll(dataList)
            }
            return this
        }

        /**
         *  设置标题
         */
        fun setTitle(title: CharSequence?): Builder {
            this.title = title
            return this
        }

        fun setLinkText(linkText: CharSequence?) = apply {
            this.linkText = linkText
            return this
        }

        fun setLinkListener(onLinkListener: ((TextView) -> Unit)?) =
            apply {
                this.onLinkListener = onLinkListener
                return this
            }

        override fun build(): BottomInfoWithIconListDialog {
            return super.build() as BottomInfoWithIconListDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomInfoWithIconListBinding> {
            return BottomInfoWithIconListDialog(
                context,
                title,
                dataList,
                linkText,
                onLinkListener
            )
        }
    }
}