package cn.com.vau.util.widget

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.View.MeasureSpec.makeMeasureSpec
import android.view.ViewStub
import android.widget.*
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import cn.com.vau.BuildConfig
import cn.com.vau.R
import cn.com.vau.util.setFontG500
import cn.com.vau.util.setFontG600
import cn.com.vau.util.setMarginLeft

/**
 *  Header Bar封装
 */
class HeaderBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    //是否显示"返回"图标
    private var isShowBack = true

    //右侧默认icon图标
    private var endDrawable: Drawable? = null

    //右侧倒数第二个icon图标
    private var end1Drawable: Drawable? = null
    private var end2Drawable: Drawable? = null
    private var end3Drawable: Drawable? = null

    //是否禁用返回icon点击自动销毁Activity功能
    private var isDisallowClickBackAutoFinish = false

    //Title文字
    private var titleText: String? = ""

    //右侧文字
    private var endText: String? = ""

    //左侧返回icon
    private var mIvStart: ImageView? = null
    private var mIvEndIcon: ImageView? = null
    private var mIvEnd1Icon: ImageView? = null
    private var mIvEnd2Icon: ImageView? = null
    private var mIvEnd3Icon: ImageView? = null
    private var mTvEndText: TextView? = null
    private var mTvTitle: TextView? = null

    private var endIconSize = 0

    init { //获取自定义属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.HeaderBar)

        isShowBack = typedArray.getBoolean(R.styleable.HeaderBar_hb_isShowBack, true)

        endDrawable = typedArray.getDrawable(R.styleable.HeaderBar_hb_endIcon)
        end1Drawable = typedArray.getDrawable(R.styleable.HeaderBar_hb_endIcon1)
        end2Drawable = typedArray.getDrawable(R.styleable.HeaderBar_hb_endIcon2)
        end3Drawable = typedArray.getDrawable(R.styleable.HeaderBar_hb_endIcon3)
        endIconSize = typedArray.getDimensionPixelSize(R.styleable.HeaderBar_hb_endIconSize, -1)

        titleText = typedArray.getString(R.styleable.HeaderBar_hb_titleText)
        endText = typedArray.getString(R.styleable.HeaderBar_hb_endText)

        isDisallowClickBackAutoFinish = typedArray.getBoolean(R.styleable.HeaderBar_hb_backClickAutoFinishDisallow, false)

        initView()
        typedArray.recycle()
    }

    /*
        初始化视图
     */
    @SuppressLint("CutPasteId")
    private fun initView() {
        View.inflate(context, R.layout.layout_header_bar, this)

        //返回按键显示
        initStartIconView()
        titleTextLeftMargin(isShowBack)

        //处理右侧icon显示
        if (endDrawable != null) {
            initEndDrawable()?.run {
                setImageDrawable(endDrawable)
                setEndIconSizeIfConfig(this)
            }
        }

        if (end1Drawable != null) {
            initEnd1Drawable()?.run {
                setImageDrawable(end1Drawable)
                setEndIconSizeIfConfig(this)
            }
        }

        if (end2Drawable != null) {
            initEnd2Drawable()?.run {
                setImageDrawable(end2Drawable)
                setEndIconSizeIfConfig(this)
            }
        }

        if (end3Drawable != null) {
            initEnd3Drawable()?.run {
                setImageDrawable(end3Drawable)
                setEndIconSizeIfConfig(this)
            }
        }

        //右侧text
        if (!endText.isNullOrEmpty()) {
            showEndText(endText)
        }

        //title
        mTvTitle = findViewById<TextView>(R.id.tvTitle).apply {
            setFontG600()
            text = titleText
        }

        //返回按键处理
        setStartBackClick()
    }

    /**
     * Title 的边距调整
     * @param isShowBack 是否显示返回按钮
     */
    private fun titleTextLeftMargin(isShowBack: Boolean) {
        if (mTvTitle == null) {
            return
        }
        if (mTvTitle?.layoutParams !is MarginLayoutParams) {
            return
        }
        if (isShowBack) {
            mTvTitle?.setMarginLeft(0)
        } else {
            mTvTitle?.setMarginLeft(context.resources.getDimensionPixelSize(R.dimen.padding_horizontal_base))
        }
    }

    private fun initStartIconView(): ImageView? {
        if (mIvStart == null) {
            findViewById<ViewStub>(R.id.viewStubIconStart).isVisible = true
            mIvStart = findViewById(R.id.ivLeft)
        }
        return mIvStart
    }

    private fun initEndDrawable(): ImageView? {
        if (mIvEndIcon == null) {
            findViewById<ViewStub>(R.id.viewStubIconEnd).isVisible = true
            mIvEndIcon = findViewById(R.id.mIvIcon)
        }
        return mIvEndIcon
    }

    private fun initEnd1Drawable(): ImageView? {
        if (mIvEnd1Icon == null) {
            checkUseRuleInInflateBefore(1)
            findViewById<ViewStub>(R.id.viewStubIconEnd1).isVisible = true
            mIvEnd1Icon = findViewById(R.id.mIvIcon1)
        }
        return mIvEnd1Icon
    }

    private fun initEnd2Drawable(): ImageView? {
        if (mIvEnd2Icon == null) {
            checkUseRuleInInflateBefore(2)
            findViewById<ViewStub>(R.id.viewStubIconEnd2).isVisible = true
            mIvEnd2Icon = findViewById(R.id.mIvIcon2)
        }
        return mIvEnd2Icon
    }

    private fun initEnd3Drawable(): ImageView? {
        if (mIvEnd3Icon == null) {
            checkUseRuleInInflateBefore(3)
            findViewById<ViewStub>(R.id.viewStubIconEnd3).isVisible = true
            mIvEnd3Icon = findViewById(R.id.mIvIcon3)
        }
        return mIvEnd3Icon
    }

    private fun initRightText(): TextView? {
        if (mTvEndText == null) {
            findViewById<ViewStub>(R.id.viewStubEndText).isVisible = true
            mTvEndText = findViewById<TextView>(R.id.tvRightText).apply {
                setFontG500()
            }
        }
        return mTvEndText
    }

    private fun checkUseRuleInInflateBefore(iconPosition:Int) {
        if (!BuildConfig.DEBUG) {
            return
        }
        if ((iconPosition == 1 && mIvEndIcon == null) || (iconPosition == 2 && mIvEnd1Icon == null) || (iconPosition == 3 && mIvEnd2Icon == null) ) {
            throw RuntimeException("你在配置HeaderBar的右侧icon时，应该按照顺序来配置，例如：应该先使用endIcon 再使用endIcon1  其次使用endIcon2 ...如有特殊需求，请联系eyal")
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        if (heightMode == MeasureSpec.AT_MOST) { //如wrap_content的情况，我们需要确定自身大小
            //默认的高度
            var height = context.resources.getDimension(R.dimen.height_title_bar).toInt()
            if (height > heightSize) { //不能超过父View给定的最大值
                height = heightSize
            }
            val makeMeasureSpecHeight = makeMeasureSpec(height, MeasureSpec.EXACTLY)
            super.onMeasure(widthMeasureSpec, makeMeasureSpecHeight)
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        }
    }

    private fun setEndIconSizeIfConfig(iconView: ImageView) {
        if (endIconSize >= 0) {
            iconView.layoutParams.run {
                width = endIconSize
                height = endIconSize
            }
            iconView.requestLayout()
        }
    }

    //不嫌费劲自己写
    private fun setStartBackClick() { //默认点击返回按键自动关闭Activity
        if (!isDisallowClickBackAutoFinish) {
            mIvStart?.setOnClickListener {
                if (context is Activity) {
                    (context as Activity).finish()
                }
            }
        }
    }

    private fun showEndText(content: String?) { //还没有被显示过
        if (mTvEndText == null) {
            initRightText()?.text = content
        } else {
            mTvEndText?.text = content
        }
    }


    /**********************************TitleText**************************************/
    /**
     * 返回Title的TextView
     */
    @Deprecated("不建议直接获取操作View")
    fun getTitleView(): TextView? {
        return mTvTitle
    }

    fun setTitleText(text: String?): HeaderBar {
        mTvTitle?.text = text
        return this
    }

    fun setTitleTextColor(@ColorInt color: Int) : HeaderBar {
        mTvTitle?.setTextColor(color)
        return this
    }

    fun setTitleTextVisible(isVisible: Boolean): HeaderBar {
        mTvTitle?.isInvisible = !isVisible
        return this
    }

    /**********************************RightText**********************************/

    /**
     * 设置右侧文本内容
     */
    fun setEndText(text: String?): HeaderBar {
        showEndText(text)
        return this
    }

    fun setEndTextVisible(isVisible: Boolean): HeaderBar {
        mTvEndText?.isVisible = isVisible
        return this
    }

    fun setEndTextClickListener(listener: (() -> Unit)? = null): HeaderBar {
        initRightText()?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }

    /**********************************StartIcon**********************************/
    fun setStartBackIconDrawable(drawable: Drawable?): HeaderBar {
        initStartIconView()?.setImageDrawable(drawable)
        return this
    }

    fun setStartBackIconResource(@DrawableRes resId: Int): HeaderBar {
        initStartIconView()?.setImageResource(resId)
        return this
    }

    fun setStartBackIconClickListener(listener: (() -> Unit)? = null): HeaderBar {
        mIvStart?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }

    fun setStartBackIconImageTintList(tint: ColorStateList?) : HeaderBar {
        initStartIconView()?.imageTintList = tint
        return this
    }

    fun setStartBackIconVisible(isVisible: Boolean) {
        mIvStart?.isVisible = isVisible
        titleTextLeftMargin(isVisible)
    }

    fun getStartBackIconVisible(): Boolean {
        return mIvStart?.isVisible ?: false
    }

    /**********************************endIcon**********************************/
    fun setEndIconResource(@DrawableRes resId: Int): HeaderBar {
        initEndDrawable()?.setImageResource(resId)
        return this
    }

    fun setEndIconDrawable(drawable: Drawable?): HeaderBar {
        initEndDrawable()?.setImageDrawable(drawable)
        return this
    }

    fun setEndIconVisible(isVisible: Boolean): HeaderBar {
        mIvEndIcon?.isVisible = isVisible
        return this
    }

    @Deprecated("不建议直接获取操作View")
    fun getEndIconView(): ImageView? {
        return mIvEndIcon
    }

    fun setEndIconClickListener(listener: (() -> Unit)? = null): HeaderBar {
        initEndDrawable()?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }

    /**********************************end1Icon**********************************/
    fun setEndIcon1Resource(@DrawableRes resId: Int): HeaderBar {
        initEnd1Drawable()?.setImageResource(resId)
        return this
    }

    fun setEndIcon1Drawable(drawable: Drawable?): HeaderBar {
        initEnd1Drawable()?.setImageDrawable(drawable)
        return this
    }

    fun setEndIcon1Visible(isVisible: Boolean): HeaderBar {
        mIvEnd1Icon?.isVisible = isVisible
        return this
    }

    @Deprecated("不建议直接获取操作View")
    fun getEndIcon1View(): ImageView? {
        return mIvEnd1Icon
    }

    fun setEndIcon1ClickListener(listener: (() -> Unit)? = null): HeaderBar {
        initEnd1Drawable()?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }

    /**********************************end2con**********************************/
    fun setEndIcon2Resource(@DrawableRes resId: Int): HeaderBar {
        initEnd2Drawable()?.setImageResource(resId)
        return this
    }

    fun setEndIcon2Drawable(drawable: Drawable?): HeaderBar {
        initEnd2Drawable()?.setImageDrawable(drawable)
        return this
    }

    fun setEndIcon2Visible(isVisible: Boolean): HeaderBar {
        mIvEnd2Icon?.isVisible = isVisible
        return this
    }

    fun setEndIcon2ClickListener(listener: (() -> Unit)? = null): HeaderBar {
        initEnd2Drawable()?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }

    /**********************************end3con**********************************/
    fun setEndIcon3Resource(@DrawableRes resId: Int): HeaderBar {
        initEnd3Drawable()?.setImageResource(resId)
        return this
    }

    fun setEndIcon3Drawable(drawable: Drawable?): HeaderBar {
        initEnd3Drawable()?.setImageDrawable(drawable)
        return this
    }

    fun setEndIcon3Visible(isVisible: Boolean): HeaderBar {
        mIvEnd3Icon?.isVisible = isVisible
        return this
    }

    fun setEndIcon3ClickListener(listener: (() -> Unit)? = null): HeaderBar {
        initEnd3Drawable()?.setOnClickListener {
            listener?.invoke()
        }
        return this
    }
}
