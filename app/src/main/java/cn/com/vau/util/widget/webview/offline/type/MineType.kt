package cn.com.vau.util.widget.webview.offline.type

object MineType {
    /**
     * html
     */
    const val html: String = "text/html"
    const val htm: String = "text/html"
    const val txt: String = "text/plain"
    const val css: String = "text/css"
    const val js: String = "application/javascript"

    /**
     * image
     */
    const val jpg: String = "image/jpeg"
    const val jpeg: String = "image/jpeg"
    const val png: String = "image/png"
    const val gif: String = "image/gif"
    const val webp: String = "image/webp"
    const val svg: String = "image/svg+xml"

    /**
     * video
     */
    const val mp4: String = "video/mp4"
    const val flv: String = "audio/x-flv"
    const val mp3: String = "audio/mpeg"
    const val wav: String = "audio/wav"
    const val aac: String = "audio/aac"

    /**
     * data
     */
    const val json: String = "application/json"
    const val xml: String = "application/xml"

    /**
     * font
     */
    const val ttf: String = "application/font-ttf"
    const val woff: String = "application/font-woff"
    const val woff2: String = "application/font-woff2"

    /**
     * document
     */
    const val pdf: String = "application/pdf"
    const val doc: String = "application/msword"
    const val docx: String = "application/msword"
    const val xls: String = "application/vnd.ms-excel"
    const val xlsx: String = "application/vnd.ms-excel"
}
