import{b as e,s}from"./vant-vendor-D8PsFlrJ.js";import{_ as t,s as i}from"./index-M11nEPjl.js";import{J as a,K as d,S as n,F as l,Y as r,j as o,V as c,I as u,M as v,W as p}from"./vue-vendor-DjIN0JG5.js";import{n as m}from"./navbar-DfgFEjpa.js";import{d as y}from"./news-Bq96hVLI.js";import"./vendor-CwRwASPO.js";const h={class:"details"},f={class:"indices-info-detail"},b={class:"item"},k={class:"left"},g={class:"right"},$={key:1};const x={class:"share"},j={class:"shares-detail"},I={class:""},T={class:"shares-detail"},D={class:"shares-detail"},C={class:"shares-detail"};const _={class:"notice-detail"},B={class:"tips"},q={class:"title"},w={class:"symbol"},A={key:0,class:"name"};const L=t({components:{indices:t({props:{list:{type:Array,default:()=>[]}}},[["render",function(s,t,i,u,v,p){const m=e;return d(),a("div",h,[t[0]||(t[0]=n("div",{class:"indices-info"},[n("div",null,"Date"),n("div",null,"Dividend")],-1)),n("div",f,[i.list.length?(d(),a("div",$,[(d(!0),a(l,null,r(i.list,(e=>(d(),a("div",{key:e.exDateProd,class:"indices-box"},[n("div",null,c(e.exDateProd),1),n("div",null,c(e.amountProd),1)])))),128))])):(d(),a(l,{key:0},r(12,(e=>n("div",{class:"skeleton-list",key:e},[n("div",b,[n("div",k,[o(m,{title:""})]),n("div",g,[o(m,{title:""})])])]))),64))])])}],["__scopeId","data-v-a4b94e46"]]),shares:t({props:{detail:{type:Object,default:()=>({})}}},[["render",function(e,s,t,i,l,r){return d(),a("div",x,[n("div",j,[n("span",I,c(e.$t("Ex-date")),1),n("span",null,c(t.detail.exDate),1)]),n("div",T,[n("span",null,c(e.$t("Amount per Share")),1),n("span",null,c(t.detail.amount),1)]),n("div",D,[n("span",null,c(e.$t("Currency")),1),n("span",null,c(t.detail.currency),1)]),n("div",C,[n("span",null,c(e.$t("Tax Rate")),1),n("span",null,c(t.detail.taxRate),1)])])}],["__scopeId","data-v-cbb22992"]]),navBar:m},mixins:[{data(){return{detail:{},symbol:this.$route.query.symbol,productName:this.$route.query.productName,title:this.$route.query.announceContent,dividendType:this.$route.query.dividendType,dividendId:this.$route.query.dividendId,loading:!1}},methods:{handleNavBarClickLeft(){this.$router.go(-1)},getDetail(){this.loading=!0;const e={dividendId:this.dividendId,symbol:this.symbol,dividendType:this.dividendType};y(e).then((e=>{this.loading=!1;const{resultCode:t,data:i,msgInfo:a}=e;"V00000"===t?this.detail=i?i.obj:"":s({message:a,wordBreak:"break-word"})})).catch((e=>{this.loading=!1;const{message:t}=e;"AbortError"!==t&&s({message:this.$t("The system is busy. Please try again later."),wordBreak:"break-word"})}))}},mounted(){i({code:"250",title:this.$t("Announcements")}),this.getDetail()}}]},[["render",function(e,s,t,i,l,r){var m;const y=u("navBar"),h=u("indices"),f=u("shares");return d(),a("div",_,[o(y,{"left-text":e.$t("Announcements"),onClickLeft:e.handleNavBarClickLeft},null,8,["left-text","onClickLeft"]),n("div",B,c(e.title),1),n("div",q,[n("div",w,c(e.symbol),1),5==e.dividendType?(d(),a("div",A,c(e.productName),1)):p("",!0)]),3==e.dividendType?(d(),v(h,{key:0,list:null==(m=e.detail)?void 0:m.productDetailList},null,8,["list"])):5==e.dividendType?(d(),v(f,{key:1,detail:e.detail},null,8,["detail"])):p("",!0)])}],["__scopeId","data-v-7b8cabfc"]]);export{L as default};
