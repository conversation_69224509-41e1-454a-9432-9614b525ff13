import{N as e}from"./vant-vendor-D8PsFlrJ.js";import{_ as t,f as o,h as a,u as r}from"./index-M11nEPjl.js";import{J as s,W as l,K as i,j as n,s as d,L as f,X as c,S as p,y as m,V as v,v as h}from"./vue-vendor-DjIN0JG5.js";const u={setup(){const{paramsStore:e}=r();return{paramsStore:e}},data:()=>({bottomShadow:!1,version:a()}),props:{fixed:{type:Boolean,default:!0},title:{type:String,default:""},leftText:{type:String,default:""},border:{type:Boolean,default:!1},rightContent:{type:Array,default:()=>[]}},methods:{compareVersions:o,handleScroll(){const e=window.scrollY;this.bottomShadow=e>10}},async created(){window.addEventListener("scroll",this.handleScroll,!0),this.version=await a()},beforeDestroy(){window.removeEventListener("scroll",this.handleScroll)}},w={key:0},x={class:"left-text"},S={class:"nav-bar_placeholder"};const b=t(u,[["render",function(t,o,a,r,u,b){const y=e;return b.compareVersions(r.paramsStore.appVersion,u.version)?l("",!0):(i(),s("div",w,[n(y,{class:m(["nav-bar",{"bottom-shadow":u.bottomShadow&&a.fixed}]),"left-arrow":"",fixed:a.fixed,border:a.border,title:a.title,"left-text":a.leftText,"safe-area-inset-top":!0,placeholder:!1,onClickLeft:o[0]||(o[0]=e=>t.$emit("click-left"))},{left:f((()=>[p("div",{class:m([r.paramsStore.isRtl?"icon-arrow_right":"icon-arrow_left","icon"])},null,2),p("div",x,v(a.leftText),1)])),right:f((()=>[c(t.$slots,"default",{},void 0,!0)])),_:3},8,["class","fixed","border","title","left-text"]),d(p("div",S,null,512),[[h,a.fixed]])]))}],["__scopeId","data-v-2fbaa607"]]);export{b as n};
