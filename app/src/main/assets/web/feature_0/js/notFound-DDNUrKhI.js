import{b as a}from"./vant-vendor-D8PsFlrJ.js";import{_ as s}from"./index-M11nEPjl.js";import{J as e,K as t,S as r,j as d,L as o,y as i,V as l,X as n,_ as c,u as p}from"./vue-vendor-DjIN0JG5.js";import{_ as u,a as m}from"./empty_light-BcIdMEkV.js";const v={class:"shadow-card-wrapper"},h={class:"shadow-card-header"},g={class:"shadow-header-l"},w=["src"],f={class:"shadow-card-header-title"},_={class:"shadow-card-header-title-name"},y={class:"shadow-card-header-title-subtitle"},b={class:"shadow-card-main"},S={class:"shadow-card-btn-group"};const j=s({props:{avatar:{type:String,default:""},avatarShape:{type:String,default:"square"},title:{type:String,default:""},subtitle:{type:String,default:""}}},[["render",function(s,c,p,u,m,j){const k=a;return t(),e("div",v,[r("div",h,[r("div",g,[d(k,{avatar:"",loading:!p.avatar,"avatar-shape":p.avatarShape},{default:o((()=>[r("img",{src:p.avatar,alt:"",class:i("square"===p.avatarShape?"square":"round")},null,10,w)])),_:1},8,["loading","avatar-shape"]),d(k,{"row-width":"100",row:1,loading:!p.title},{default:o((()=>[r("div",f,[r("div",_,l(p.title),1),r("div",y,l(p.subtitle),1)])])),_:1},8,["loading"])])]),r("div",b,[n(s.$slots,"main",{},void 0,!0)]),r("div",S,[n(s.$slots,"footer",{},void 0,!0)])])}],["__scopeId","data-v-d734ef58"]]),k={class:"not-found"},q=["src"],$=s({__name:"notFound",setup(a){const s=c(),d=new URL(Object.assign({"/src/packages/au/assets/images/common/empty_dark.webp":m,"/src/packages/au/assets/images/common/empty_light.webp":u})[`/src/packages/au/assets/images/common/empty_${"0"===s.query.theme?"light":"dark"}.webp`],import.meta.url).href;return(a,s)=>(t(),e("div",k,[r("img",{src:p(d),alt:""},null,8,q),r("div",null,l(a.$t("No Records Found")),1)]))}},[["__scopeId","data-v-ee9e2893"]]);export{$ as N,j as R};
