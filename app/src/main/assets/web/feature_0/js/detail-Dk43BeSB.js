import{b as e,s}from"./vant-vendor-D8PsFlrJ.js";import{_ as t,s as a}from"./index-M11nEPjl.js";import{J as i,K as d,S as n,F as l,Y as r,j as o,V as c,I as u,M as v,W as p}from"./vue-vendor-DjIN0JG5.js";import{d as m}from"./news-Bq96hVLI.js";import{n as y}from"./navbar-DfgFEjpa.js";import"./vendor-CwRwASPO.js";const h={class:"details"},f={class:"indices-info-detail"},b={class:"item"},k={class:"left"},$={class:"right"},x={key:1};const g=t({props:{list:{type:Array,default:()=>[]}}},[["render",function(s,t,a,u,v,p){const m=e;return d(),i("div",h,[t[0]||(t[0]=n("div",{class:"indices-info"},[n("div",null,"Date"),n("div",null,"Dividend")],-1)),n("div",f,[a.list.length?(d(),i("div",x,[(d(!0),i(l,null,r(a.list,(e=>(d(),i("div",{key:e.exDateProd,class:"indices-box"},[n("div",null,c(e.exDateProd),1),n("div",null,c(e.amountProd),1)])))),128))])):(d(),i(l,{key:0},r(12,(e=>n("div",{class:"skeleton-list",key:e},[n("div",b,[n("div",k,[o(m,{title:""})]),n("div",$,[o(m,{title:""})])])]))),64))])])}],["__scopeId","data-v-e8a231ad"]]),j={class:"share"},I={class:"shares-detail"},C={class:""},D={class:"shares-detail"},T={class:"shares-detail"},L={class:"shares-detail"};const _=t({props:{detail:{type:Object,default:()=>({})}}},[["render",function(e,s,t,a,l,r){return d(),i("div",j,[n("div",I,[n("span",C,c(e.$t("Ex-date")),1),n("span",null,c(t.detail.exDate),1)]),n("div",D,[n("span",null,c(e.$t("Amount per Share")),1),n("span",null,c(t.detail.amount),1)]),n("div",T,[n("span",null,c(e.$t("Currency")),1),n("span",null,c(t.detail.currency),1)]),n("div",L,[n("span",null,c(e.$t("Tax Rate")),1),n("span",null,c(t.detail.taxRate),1)])])}],["__scopeId","data-v-a54df10e"]]),B={class:"container"},q={class:"tips"},w={class:"title"},A={class:"symbol"},N={class:"name"};const P=t({mixins:[{components:{navBar:y},data(){return{detail:{},symbol:this.$route.query.symbol,productName:this.$route.query.productName,title:this.$route.query.announceContent,dividendType:this.$route.query.dividendType,dividendId:this.$route.query.dividendId}},methods:{handleNavBarClickLeft(){this.$router.go(-1)},getDetail(){const e={dividendId:this.dividendId,symbol:this.symbol,dividendType:this.dividendType};m(e).then((e=>{const{resultCode:t,data:a,msgInfo:i}=e;"V00000"===t?this.detail=(null==a?void 0:a.obj)??"":s({message:i,wordBreak:"break-word"})})).catch((e=>{const{message:t}=e;"AbortError"!==t&&s({message:this.$t("The system is busy. Please try again later."),wordBreak:"break-word"})}))}},mounted(){this.getDetail(),a({code:"250",title:this.$t("Announcements"),iconList:["CLOSE"]})}}],components:{indices:g,shares:_}},[["render",function(e,s,t,a,l,r){var m;const y=u("navBar"),h=u("indices"),f=u("shares");return d(),i("div",B,[o(y,{"left-text":e.$t("Announcements"),onClickLeft:e.handleNavBarClickLeft},null,8,["left-text","onClickLeft"]),n("div",q,c(e.title),1),n("div",w,[n("div",A,c(e.symbol),1),n("div",N,c(e.productName),1)]),3==e.dividendType?(d(),v(h,{key:0,list:null==(m=e.detail)?void 0:m.productDetailList},null,8,["list"])):5==e.dividendType?(d(),v(f,{key:1,detail:e.detail},null,8,["detail"])):p("",!0)])}],["__scopeId","data-v-db897483"]]);export{P as default};
