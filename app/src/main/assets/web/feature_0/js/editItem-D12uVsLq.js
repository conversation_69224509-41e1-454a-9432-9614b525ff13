import{F as e,m as t,B as r,k as i,s}from"./vant-vendor-D8PsFlrJ.js";import{_ as a,s as l}from"./index-M11nEPjl.js";import{n as o}from"./navbar-DfgFEjpa.js";import{s as n}from"./index-CdiGCxuy.js";import{I as d,J as u,K as m,j as p,S as h,L as c,F as y,y as v,V as f,B as $}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const P={class:"edit-item-container"},L={class:"value-limit"},O={class:"field-item"},T={class:"field-item-title"},C={class:"field-item"},g={class:"field-item-title"},w={class:"field-item"},M={class:"field-item-title"},V={class:"button-wrap"};const I=a({components:{Navbar:o},setup:()=>({store:n}),computed:{currentEditType(){return this.editTypeMap[this.$route.query.editType]||{}}},data(){return{editTypeMap:{strategyName:{type:"text",title:this.$t("shadow.Nickname"),maxlength:20,placeholder:this.$t("shadow.EnterCharacters").replace("%s",20),rules:[{required:!0,validator:e=>e.trim().length>3}]},description:{type:"textarea",title:this.$t("shadow.Description"),maxlength:2e3,placeholder:this.$t("shadow.Introduce_your_strategy_to_copiers"),rules:[{required:!1}]},threshold:{title:this.$t("shadow.ThresholdForCopiers")}},defaultMinInvestmentPerCopy:{USD:50,EUR:50,HKD:400,JPY:7e3,USC:4e3,INR:4e3},inputValue:"",minInvestmentPerCopy:"",minLotsPerOrder:"",minLotsMultiplePerOrder:"",isButtonDisabled:!1,accountCurrency:""}},methods:{async save(){if("strategyName"===this.$route.query.editType&&/[^a-zA-Z0-9\s]/.test(this.inputValue))return this.isButtonDisabled=!0,void s(this.$t("shadow.NicknameRule"));await this.$refs.formRef.validate(),"threshold"===this.$route.query.editType?n.setAllState({minInvestmentPerCopy:this.minInvestmentPerCopy,minLotsPerOrder:this.minLotsPerOrder,minLotsMultiplePerOrder:this.minLotsMultiplePerOrder}):n.setAllState({[this.$route.query.editType]:this.inputValue}),this.$router.go(-1)},async goBack(){"textarea"===this.currentEditType.type&&this.inputValue!==n.state.description&&await i({title:this.$t("shadow.Leave_this_page")+"?",message:this.$t("shadow.changeNotSaved"),confirmButtonText:this.$t("shadow.Leave"),cancelButtonText:this.$t("shadow.Stay")}),this.$router.go(-1)},validateForm(e,t){const r=e.target.value,i=(e,t)=>{/^0\d+/.test(r)&&(this[e]=r.replace(/^0+/,"")),r.includes(".")&&r.split(".")[1].length>t&&(this[e]=r.slice(0,r.indexOf(".")+t+1))};"minLotsPerOrder"===t?i("minLotsPerOrder",2):"minLotsMultiplePerOrder"===t&&i("minLotsMultiplePerOrder",1),this.$refs.formRef.validate().then((()=>{this.isButtonDisabled=!1})).catch((()=>{this.isButtonDisabled=!0}))}},mounted(){l({code:"250",title:`${this.$t("shadow.Edit")} ${this.currentEditType.title}`,iconList:["CLOSE"]}),"threshold"===this.$route.query.editType?(this.minInvestmentPerCopy=n.state.minInvestmentPerCopy,this.minLotsPerOrder=n.state.minLotsPerOrder,this.minLotsMultiplePerOrder=n.state.minLotsMultiplePerOrder,this.accountCurrency=this.$route.query.accountCurrency):this.inputValue=n.state[this.$route.query.editType]}},[["render",function(i,s,a,l,o,n){const I=d("navbar"),b=e,x=t,E=r;return m(),u("div",P,[p(I,{onClickLeft:n.goBack,leftText:i.$t("shadow.Edit")+" "+n.currentEditType.title},null,8,["onClickLeft","leftText"]),h("main",null,[p(x,{ref:"formRef","validate-first":!0},{default:c((()=>{var e;return["threshold"!==i.$route.query.editType?(m(),u(y,{key:0},[p(b,{maxlength:n.currentEditType.maxlength,clearable:"",modelValue:o.inputValue,"onUpdate:modelValue":s[0]||(s[0]=e=>o.inputValue=e),type:n.currentEditType.type,class:v(`input-${n.currentEditType.type}`),placeholder:n.currentEditType.placeholder,rules:n.currentEditType.rules,onInput:n.validateForm},null,8,["maxlength","modelValue","type","class","placeholder","rules","onInput"]),h("div",L,f((null==(e=o.inputValue)?void 0:e.length)||0)+"/"+f(n.currentEditType.maxlength),1)],64)):(m(),u(y,{key:1},[h("div",O,[h("div",T,f(i.$t("shadow.MinInvestment")),1),p(b,{type:"digit",maxlength:"9",pattern:"[0-9]*",placeholder:`Min.${o.defaultMinInvestmentPerCopy[o.accountCurrency]||50}`,modelValue:o.minInvestmentPerCopy,"onUpdate:modelValue":s[1]||(s[1]=e=>o.minInvestmentPerCopy=e),rules:[{required:!0,validator:e=>e>=50}],onInput:s[2]||(s[2]=e=>n.validateForm(e,"minInvestmentPerCopy"))},{button:c((()=>[$(f(o.accountCurrency),1)])),_:1},8,["placeholder","modelValue","rules"])]),h("div",C,[h("div",g,f(i.$t("shadow.MinLots")),1),p(b,{type:"number",placeholder:i.$t("shadow.Min2"),modelValue:o.minLotsPerOrder,"onUpdate:modelValue":s[3]||(s[3]=e=>o.minLotsPerOrder=e),onInput:s[4]||(s[4]=e=>n.validateForm(e,"minLotsPerOrder")),rules:[{required:!0,validator:e=>e>=.01&&e<=100}],formatter:e=>e>100?this.minLotsPerOrder:e},{button:c((()=>[$(f(i.$t("shadow.Lots")),1)])),_:1},8,["placeholder","modelValue","rules","formatter"])]),h("div",w,[h("div",M,f(i.$t("shadow.MinMultiples")),1),p(b,{type:"number",placeholder:i.$t("shadow.Min3"),modelValue:o.minLotsMultiplePerOrder,"onUpdate:modelValue":s[5]||(s[5]=e=>o.minLotsMultiplePerOrder=e),onInput:s[6]||(s[6]=e=>n.validateForm(e,"minLotsMultiplePerOrder")),rules:[{required:!0,validator:e=>e>=.1&&e<=50}],formatter:e=>e>50?this.minLotsMultiplePerOrder:e},{button:c((()=>s[7]||(s[7]=[$(" X ")]))),_:1},8,["placeholder","modelValue","rules","formatter"])])],64))]})),_:1},512)]),h("div",V,[p(E,{block:"",round:"",disabled:o.isButtonDisabled,onClick:n.save},{default:c((()=>[$(f(i.$t("shadow.Save")),1)])),_:1},8,["disabled","onClick"])])])}],["__scopeId","data-v-4a69f587"]]);export{I as default};
