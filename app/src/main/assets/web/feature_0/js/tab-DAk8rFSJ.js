import{_ as e,u as t}from"./index-M11nEPjl.js";import{J as a,K as s,S as i,F as n,Y as r,A as d,y as o,V as l,X as h,W as c}from"./vue-vendor-DjIN0JG5.js";const{paramsStore:b}=t(),u={props:{tabs:{type:Array,default:()=>[]},activeBgColor:{type:String,default:""},activeColor:{type:String,default:""},active:{type:Number,default:0},animated:{type:Boolean,default:!1},swipeable:{type:Boolean,default:!1},lazyRender:{type:Boolean,default:!0}},setup:()=>({paramsStore:b}),computed:{istest(e){return this.loadedTabs.includes(e)}},data(){return{activeIndex:this.active,startX:0,endX:0,loadedTabs:[],observer:null,marginValue:0}},methods:{handleTabClick(e){this.activeIndex=e,this.animated&&this.handleChangeTab(e),this.$emit("click",e)},handleChangeTab(e){this.startX=0,this.endX=0;const{isRtl:t}=this.paramsStore,a=this.$refs.conetnt;a.style.transform=`translateX(${t?"+":"-"}${100*e}%)`,a.style.transition="all .3s",this.$emit("change",e)},handleTouchStart(e){this.swipeable&&(this.startX=e.touches[0].clientX)},handleTouchmove(e){this.swipeable&&(this.endX=e.touches[0].clientX)},handleTouchEnd(){if(!this.swipeable)return;const e=this.startX-this.endX;e>100&&this.activeIndex<this.tabs.length-1&&this.endX?(this.activeIndex+=1,this.handleChangeTab(this.activeIndex)):e<-100&&this.activeIndex>0&&this.endX&&(this.activeIndex-=1,this.handleChangeTab(this.activeIndex))},initObserver(){this.observer=new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){-1===this.loadedTabs.indexOf(e.target.getAttribute("data-index"))&&this.loadedTabs.push(e.target.getAttribute("data-index"))}}))}));this.$refs.lazyContainer.forEach((e=>{this.observer.observe(e)}))},disconnectObserver(){this.observer&&this.observer.disconnect()}},beforeDestroy(){this.disconnectObserver()},mounted(){this.lazyRender?this.initObserver():this.loadedTabs=this.tabs.map(((e,t)=>`${t}`));const e=this.$refs.conetnt;this.marginValue=(window.screen.width-e.offsetWidth)/2}},v={class:"tabs"},p={class:"tab-list"},m=["onClick"],f={class:"tab-content",ref:"conetnt"},x=["data-index"];const T=e(u,[["render",function(e,t,b,u,T,g){return s(),a("div",v,[i("div",p,[(s(!0),a(n,null,r(b.tabs,((e,t)=>(s(),a("div",{class:o(["tab",{tab_active:t===T.activeIndex}]),key:t,onClick:e=>g.handleTabClick(t),style:d({"background-color":t===T.activeIndex?b.activeBgColor:"",color:t===T.activeIndex?b.activeColor:""})},l(e),15,m)))),128))]),i("div",f,[(s(!0),a(n,null,r(b.tabs,((i,n)=>(s(),a("div",{ref_for:!0,ref:"lazyContainer",class:o(["tab-pane",[{"tab-pane_animated":b.animated||b.swipeable},{current:n===T.activeIndex}]]),key:n,"data-index":n,onTouchstart:t[0]||(t[0]=(...e)=>g.handleTouchStart&&g.handleTouchStart(...e)),onTouchmove:t[1]||(t[1]=(...e)=>g.handleTouchmove&&g.handleTouchmove(...e)),onTouchend:t[2]||(t[2]=(...e)=>g.handleTouchEnd&&g.handleTouchEnd(...e)),style:d({display:b.animated||b.swipeable||n===T.activeIndex?"block":"none",marginLeft:n===T.activeIndex&&u.paramsStore.isRtl?`${T.marginValue}px`:"",marginRight:n!==T.activeIndex||u.paramsStore.isRtl?"":`${T.marginValue}px`})},[T.loadedTabs.includes(`${n}`)?h(e.$slots,"default",{key:0},void 0,!0):c("",!0)],46,x)))),128))],512)])}],["__scopeId","data-v-5332f99c"]]);export{T as t};
