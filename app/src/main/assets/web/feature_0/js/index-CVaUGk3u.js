import{s,e as t,b as a,E as e,g as o}from"./vant-vendor-D8PsFlrJ.js";import i from"./empty_dark-BVM0wdOH.js";import n from"./empty_light-t-w1hjKQ.js";import{c as l}from"./news-Bq96hVLI.js";import{n as d}from"./navbar-DfgFEjpa.js";import{s as r,r as c,_ as m}from"./index-M11nEPjl.js";import{s as u}from"./search-BMccXSDW.js";import{J as h,K as p,j as v,s as y,v as k,S as b,F as g,Y as f,V as L,I as _,M as N,L as C}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const D={class:"search-symbol"},j={class:"drop-down-list"},I=["onClick"];const w={class:"skeleton-notice"},$={class:"list"};const x={class:"container"},B={key:1,class:"content"},K={key:1},V={class:"sticky"},S={class:"tips"};const q=m({mixins:[{components:{navBar:d},data:()=>({searchKey:"",loading:!0,dataList:{symbolList:[]},isNoData:!1}),methods:{handleNavBarClickLeft(){c(501)},getList(){const t={dividendId:this.$route.query.dividendid};l(t).then((t=>{this.loading=!1;const{resultCode:a,data:e,msgInfo:o}=t;"V00000"===a?(this.dataList=(null==e?void 0:e.obj)??"",this.isNoData=!1):(s({message:o,wordBreak:"break-word"}),this.isNoData=!0)})).catch((t=>{this.loading=!1,this.isNoData=!0;const{message:a}=t;s({message:a,wordBreak:"break-word"})}))},goto(t){const{dividendType:a,dividendId:e,announceContent:o}=this.dataList;a&&e?this.$router.push({path:"announcementDetails",query:{dividendType:a,dividendId:e,announceContent:o,productName:t.productName,symbol:t.symbol}}):s.fail(this.$t("No Records Found"))}},mounted(){this.$route.query.dividendid?this.getList():(this.loading=!1,this.isNoData=!0),r({code:"250",title:this.$t("Announcements")})}}],data:()=>({searchKey:"",loading:!0,dataList:{symbolList:[]},isNoData:!1}),components:{search:m({mixins:[u]},[["render",function(s,a,e,o,i,n){const l=t;return p(),h("div",D,[v(l,{id:"search",modelValue:s.searchKey,"onUpdate:modelValue":a[0]||(a[0]=t=>s.searchKey=t),class:"search-container",shape:"round",clearable:"",placeholder:s.$t("Search")},null,8,["modelValue","placeholder"]),y(b("div",j,[(p(!0),h(g,null,f(s.list,((t,a)=>y((p(),h("div",{key:t.symbol,class:"search-item",onClick:a=>s.goto(t)},L(t.symbol),9,I)),[[k,a<=3]]))),128))],512),[[k,s.show]])])}],["__scopeId","data-v-b20c557b"]]),listSkeleton:m({},[["render",function(s,t){const e=a;return p(),h("div",w,[v(e,{class:"tips"}),t[0]||(t[0]=b("div",{class:"search-bar van-skeleton-paragraph"},null,-1)),b("div",$,[(p(),h(g,null,f(11,(s=>v(e,{key:s,round:"",class:"item",title:""}))),64))])])}],["__scopeId","data-v-1d10ffb4"]])},computed:{noDataImg(){return"dark"===this.$pinia.state.value.params.themeTxt?i:n}}},[["render",function(s,t,a,i,n,l){const d=_("navBar"),r=_("listSkeleton"),c=e,m=_("search"),u=o;return p(),h("div",x,[v(d,{"left-text":s.$t("Announcements"),onClickLeft:s.handleNavBarClickLeft},null,8,["left-text","onClickLeft"]),n.loading?(p(),N(r,{key:0})):(p(),h("div",B,[n.isNoData?(p(),N(c,{key:0,class:"no_data",image:l.noDataImg,description:s.$t("no_records_found"),"image-size":"80"},null,8,["image","description"])):(p(),h("div",K,[b("div",V,[b("div",S,L(n.dataList.announceContent),1),v(m,{"symbol-list":n.dataList.symbolList,onGotoDetail:s.goto},null,8,["symbol-list","onGotoDetail"])]),(p(!0),h(g,null,f(n.dataList.symbolList,(a=>(p(),N(u,{key:a.symbol,title:a.symbol,label:a.productName,"is-link":"",class:"vant-border",onClick:t=>s.goto(a)},{"right-icon":C((()=>t[0]||(t[0]=[b("div",{class:"icon-arrow_right"},null,-1)]))),_:2},1032,["title","label","onClick"])))),128))]))]))])}],["__scopeId","data-v-c166ea20"]]);export{q as default};
