import{E as A,I as t,b as e,s as a}from"./vant-vendor-D8PsFlrJ.js";import{_ as s,a as i}from"./empty_light-BcIdMEkV.js";import{_ as l,s as r,r as o,u as n}from"./index-M11nEPjl.js";import{n as c}from"./navbar-DfgFEjpa.js";import{s as d,C as m}from"./calendar-Cgct8rqI.js";import{g as h}from"./signalStmt-DaX-YCgN.js";import{I as v,J as g,K as u,j as p,S as w,L as B,V as y,M as D,W as S,F as C,Y as f,f as b}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const k={class:"container"},F=["src"],U={class:"content"},I={class:"select-picker"},L={class:"select-picker-item-text"},V=["src"],j={key:1},x={class:"section-container"},Q={class:"header-title"},P={class:"title"},H={class:"card"},R={class:"card-wrapper highlight"},W={class:"text"},E={class:"text right"},M={key:0,class:"card-wrapper"},Y={class:"text"},Z={class:"text right"},T={key:1,class:"card-wrapper"},_={class:"text"},N={class:"text right"},O={class:"card-wrapper"},J={class:"text"},X={class:"text right"},G={class:"section-container"},q={class:"header-title"},K={class:"title"},$={class:"card-wrapper"},z={class:"text"},AA={class:"text right"},tA={class:"card-wrapper"},eA={class:"text"},aA={class:"text right"},sA={class:"card-wrapper"},iA={class:"text"},lA={class:"text right"},rA={class:"card-wrapper highlight"},oA={class:"text"},nA={class:"text right"},cA={class:"action-sheet-content"},dA={class:"content-title"},mA={class:"content-row"},hA={class:"content-row-title"},vA={class:"content-row-txt"},gA={class:"content-row"},uA={class:"content-row-title"},pA={class:"content-row-txt"},wA={class:"content-row"},BA={class:"content-row-title"},yA={class:"content-row-txt"},DA={class:"content-row"};const SA=l({setup(){const{paramsStore:A}=n();return{emptyImgUrl:b((()=>new URL(Object.assign({"../../assets/images/common/empty_dark.webp":i,"../../assets/images/common/empty_light.webp":s})[`../../assets/images/common/empty_${A.themeTxt}.webp`],import.meta.url).href)),questionImgUrl:b((()=>new URL(Object.assign({"./assets/images/question_dark.webp":"data:image/webp;base64,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","./assets/images/question_light.webp":"data:image/webp;base64,UklGRj4FAABXRUJQVlA4WAoAAAAwAAAAPwAAPwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZBTFBIiwEAAA2QVlt7nliPhDjgQQF1QFAAoyBBQQcFoQpoFdAoGFBAxkGvgnlxEA98pXnXur8jYgJQnHZFGgKQLP+mSVC13e7wJg1XTYMUUy2m9ShJ69glqcC0HsUZmKLM1X5jVvp1HGfhGbMz2I2Uc9+okT/DWCosUac5SVfmjHqPi32JM2r26/1nZ9Tt1/tPAmr3i+49z+pw/IvvsIXCvpc3btBoTvtXHjpt0z+jU4Iw5CcOWo3vH2jVoI0ZgIVe43sAQRFcBCw0kwlOFXYXWF3bK6GbaJTBqmtW2khtC6PNUBvx3y9UJtkoy0Jl9/tC2TQ12pLVJqAqAa5bVQm47FRFIJGKRABEp6gDgN4bNSIPObZqIp723iiR9CwPQUmHl31jVUh6hf3JKMhfeFOGk4ID3h7voboOHx6XrrL49wn8xlUVb/jcb1xF8YaSfhmq6f5Q9ng/mSryAcXH4YcVpANmlI0NnCl3E+Ydo3WcIQ8jZpfxl4GFJI6oUlJHv+ZHcr0I6k3jL5pmRUMAkuU+JRQHAFZQOCC8AQAA0A0AnQEqQABAAD4xEohCoiEhGPqttCADBLSAC2pVKPxG6AE2PmLckC9Sd0r/DP2iNSr/gPTxzY/MP/O/rvwAfx3+l/7DrS+hF+uxlQ26AWCIIhMlRfJb0VoTyvmSfyVxj54ifeZxE30mb5ckXCg6J87vgcyQQwAA/v6TB//VM//Gi//6WSYNMys7/t3Kv1CP+oDY/dVBQy82jlfi7Td/22of9zA6WGUzl8XxqUwtgsXj0V8t0Gp52YVD7v3Nn+o0/OfizHA3F3tib02BsbHP11YghIKsSP1rPwdyDpOp/hhlP0o7j1OUr78yZ1gmfB8WkmKtbgspYSj5vEFnmg3bJlt8QZf3LmZ8Ov5lqdeoG0/WymXpCZfP7xVN1sRLvSVH1UeQXlusgfGjl/B0fSbGUAfi4xOKKy5VDYqRdwUkpPR7Z0t7H8XMnP9JtrKfxOYS3LXOsXk3wHTHls81e2CMjs6Vduv9BV6/hJ0/bafVVOY5b7FKdyNrYBq70f9ooRWEfLFxl3IPmsXmDrdy2V5Rh1N+2+IL/7/CsocA4b+XeEb0v/hpt1wOathnnBiN76DWu0PTJGS6t/jAAAAA"})[`./assets/images/question_${A.themeTxt}.webp`],import.meta.url).href))}},data(){return{calendarVisible:!1,selectedDate:this.$pinia.state.value.params.myDate||this.formatDate((new Date).getTime()),endDate:this.$pinia.state.value.params.myDate||this.formatDate((new Date).getTime()),viewSummary:null,loading:!1,modalShow:!1,skeletonRowWidth:["40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%","40%","100%","100%","100%","100%"]}},computed:{viewSummaryDetailListFirst(){var A;return(null==(A=this.viewSummary)?void 0:A.detailList[0])||{}}},components:{navBar:c,Calendar:m,slideActionSheet:d},methods:{handleSummaryModal(){this.modalShow=!0},handleNavBarClickLeft(){this.$router.go(-1)},handleNavBarClickRight(){o("220")},handleOpenDatePicker(){this.calendarVisible=!0},handleCalendarClose(){this.calendarVisible=!1},handleCalendarSelect(A){this.selectedDate=A,this.endDate=A,this.calendarVisible=!1,this.getSummary()},formatDate(A){const t=new Date(A);return`${t.getDate().toString().padStart(2,"0")}/${(t.getMonth()+1).toString().padStart(2,"0")}/${t.getFullYear()}`},getSummary(){this.loading=!0;const{accountId:A,strategyId:t}=this.$route.query;h({accountId:A,strategyId:t,date:this.selectedDate}).then((A=>{"200"===A.code?(this.viewSummary=A.data,this.selectedDate=A.data.settlementStart?A.data.settlementStart:this.selectedDate,this.endDate=A.data.settlementEnd?A.data.settlementEnd:this.selectedDate):(this.viewSummary=null,a(A.msg)),this.loading=!1})).catch((()=>{this.loading=!1}))},handleTouchStart(A){this.startY=A.touches[0].clientY},handleTouchMove(A){this.moveY=A.touches[0].clientY;const t=this.moveY-this.startY;t>0&&(this.translateY=t)},handleTouchEnd(){this.moveY-this.startY>100?this.modalShow=!1:this.$refs.actionSheetRef.$el.style.transform="translateY(0)"},handleModalClose(){this.modalShow=!1},handleLink(){this.modalShow=!1,this.$router.push({path:"profitSharingRules"})}},mounted(){r({code:"250",title:this.$t("profit_sharing_statement"),iconList:["CLOSE"]}),this.getSummary()}},[["render",function(a,s,i,l,r,o){const n=v("nav-bar"),c=v("calendar"),d=A,m=t,h=e,b=v("slide-action-sheet");return u(),g("div",k,[p(n,{"left-text":a.$t("profit_sharing_statement"),onClickLeft:o.handleNavBarClickLeft},{default:B((()=>[w("div",{class:"nav-bar_right",onClick:s[0]||(s[0]=(...A)=>o.handleNavBarClickRight&&o.handleNavBarClickRight(...A))},[w("img",{src:l.questionImgUrl,alt:""},null,8,F)])])),_:1},8,["left-text","onClickLeft"]),w("div",U,[p(c,{visible:r.calendarVisible,onClose:o.handleCalendarClose,onSelected:o.handleCalendarSelect},null,8,["visible","onClose","onSelected"]),w("div",I,[w("div",{class:"select-picker-item",onClick:s[1]||(s[1]=(...A)=>o.handleOpenDatePicker&&o.handleOpenDatePicker(...A))},[w("span",L,y(r.selectedDate),1),s[4]||(s[4]=w("span",{class:"icon icon-arrow_down"},null,-1))])]),p(h,{class:"stmt-skeleton",row:r.skeletonRowWidth.length,loading:r.loading,"row-width":r.skeletonRowWidth},{default:B((()=>{var A;return[r.viewSummary&&(null==(A=r.viewSummary)?void 0:A.detailList)?(u(),g("div",j,[w("div",x,[w("div",Q,[w("div",P,y(a.$t("summary")),1),p(m,{class:"icon",name:"info-o",onClick:o.handleSummaryModal},null,8,["onClick"])]),w("div",H,[w("div",R,[w("div",W,y(a.$t("current_periods_payout")),1),w("div",E,y(o.viewSummaryDetailListFirst.receivedProfit)+" "+y(o.viewSummaryDetailListFirst.currency),1)]),o.viewSummaryDetailListFirst.settlementAmount?(u(),g("div",M,[w("div",Y,y(a.$t("paid_amount")),1),w("div",Z,y(o.viewSummaryDetailListFirst.settlementAmount)+" "+y(o.viewSummaryDetailListFirst.currency),1)])):S("",!0),o.viewSummaryDetailListFirst.pendingProfit?(u(),g("div",T,[w("div",_,y(a.$t("unpaid_amount")),1),w("div",N,y(o.viewSummaryDetailListFirst.pendingProfit)+" "+y(o.viewSummaryDetailListFirst.currency),1)])):S("",!0),w("div",O,[w("div",J,y(a.$t("payment_account")),1),w("div",X,y("-1"!=o.viewSummaryDetailListFirst.receiveAccount&&o.viewSummaryDetailListFirst.receiveAccount?o.viewSummaryDetailListFirst.receiveAccount:a.$t("no_payment_record")),1)])])]),s[5]||(s[5]=w("div",{class:"section-line"},null,-1)),w("div",G,[w("div",q,[w("div",K,y(a.$t("breakdown")),1)]),(u(!0),g(C,null,f(o.viewSummaryDetailListFirst.breakdown,((A,t)=>(u(),g("div",{class:"card",key:t},[w("div",$,[w("div",z,y(a.$t("copiers_amount")),1),w("div",AA,y(A.followerCount||0),1)]),w("div",tA,[w("div",eA,y(a.$t("copiers_floating_profits")),1),w("div",aA,y(A.netProfit||0)+" "+y(o.viewSummaryDetailListFirst.currency),1)]),w("div",sA,[w("div",iA,y(a.$t("profit_sharing_ratio")),1),w("div",lA,y(100*parseFloat(A.profitSharePercentage)||0)+"% ",1)]),w("div",rA,[w("div",oA,y(a.$t("current_periods_payout")),1),w("div",nA,y(A.totalReceivedProfit||0)+" "+y(o.viewSummaryDetailListFirst.currency),1)])])))),128))]),s[6]||(s[6]=w("div",{class:"section-line"},null,-1))])):(u(),D(d,{key:0,class:"empty",description:a.$t("no_records_found"),"image-size":"80"},{image:B((()=>[w("img",{src:l.emptyImgUrl,alt:""},null,8,V)])),_:1},8,["description"]))]})),_:1},8,["row","loading","row-width"]),p(b,{show:r.modalShow,"onUpdate:show":s[3]||(s[3]=A=>r.modalShow=A),onClose:o.handleModalClose},{content:B((()=>[w("div",cA,[w("div",dA,y(a.$t("glossary")),1),w("div",mA,[w("div",hA,y(a.$t("current_periods_payout")),1),w("div",vA,y(a.$t("glossary_current_periods_payout")),1)]),w("div",gA,[w("div",uA,y(a.$t("unpaid_amount")),1),w("div",pA,y(a.$t("glossary_unpaid_amount")),1)]),w("div",wA,[w("div",BA,y(a.$t("payment_account")),1),w("div",yA,y(a.$t("glossary_payment_account")),1)]),w("div",DA,[w("div",{class:"link",onClick:s[2]||(s[2]=(...A)=>o.handleLink&&o.handleLink(...A))},y(a.$t("learn_more")),1)])])])),_:1},8,["show","onClose"])])])}],["__scopeId","data-v-51b5bb30"]]);export{SA as default};
