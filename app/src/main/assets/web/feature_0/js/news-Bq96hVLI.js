import{p as e,c as n}from"./index-M11nEPjl.js";const t="AU".replace(/AU/g,"vau").toLowerCase(),o=e=>n(`/${t}/analyses/detail`,e,{"Content-Type":"application/x-www-form-urlencoded"}),a=n=>e(`/${t}/news/getDetail/${n.id}`,n,{"Content-Type":"application/x-www-form-urlencoded"}),i=n=>e(`/${t}/newsletter/getPage/v1`,n,{"Content-Type":"application/x-www-form-urlencoded"}),d=e=>n(`/${t}/dividendAnnouncement/getList`,e,{"Content-Type":"application/x-www-form-urlencoded"}),p=e=>n(`/${t}/dividendAnnouncement/getDetail`,e,{"Content-Type":"application/x-www-form-urlencoded"});export{o as a,a as b,d as c,p as d,i as g};
