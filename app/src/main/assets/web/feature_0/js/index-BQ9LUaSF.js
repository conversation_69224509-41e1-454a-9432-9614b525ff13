import{m as e,g as t,a as s}from"./index-BVEDVdSS.js";import{n as a}from"./navbar-DfgFEjpa.js";import{_ as r,s as i}from"./index-M11nEPjl.js";import{I as o,D as n,s as u,K as c,J as h,j as p}from"./vue-vendor-DjIN0JG5.js";import"./vant-vendor-D8PsFlrJ.js";import"./vendor-CwRwASPO.js";const d={class:"au-pro-container","loading-img":"au"};const l=r({mixins:[e],components:{Navbar:a},data:()=>({chosen_quiz:0}),methods:{async getProClientCurrentStep(){if(!this.$pinia.state.value.params.userId)return;this.showLoading();const{data:e}=await s({userId:this.$pinia.state.value.params.userId});switch(i({code:"250",iconList:["CLOSE"]}),e.obj.step){case"2":case"0-3":this.getAgreedTC();break;case"1-4":case"0-7":this.$router.replace({path:"success",query:this.$route.query});break;default:this.$router.replace({path:e.obj.step,query:this.$route.query})}},async getAgreedTC(){const{data:e}=await t({userId:this.$pinia.state.value.params.userId,type:1});e.obj.confirm?(this.chosen_quiz=e.obj.choose,this.handleChosenQuiz()):this.$router.replace({path:"1-1",query:this.$route.query})},handleChosenQuiz(){switch(this.chosen_quiz){case 1:this.$router.replace({path:"2",query:this.$route.query});break;case 2:this.$router.replace({path:"0-3",query:this.$route.query})}}},created(){this.getProClientCurrentStep()}},[["render",function(e,t,s,a,r,i){const l=o("Navbar"),m=n("loading");return u((c(),h("div",d,[p(l,{onClickLeft:e.back},null,8,["onClickLeft"])])),[[m,e.loading]])}]]);export{l as default};
