import{b as t,s as e,E as s}from"./vant-vendor-D8PsFlrJ.js";import{n as a}from"./footer-BLTvWGoM.js";import{_ as i,s as o,r as n}from"./index-M11nEPjl.js";import{J as r,K as d,S as l,j as m,F as c,Y as u,I as f,M as h,W as p,V as v}from"./vue-vendor-DjIN0JG5.js";import{n as g}from"./navbar-DfgFEjpa.js";import{b as k}from"./news-Bq96hVLI.js";import{z as w,K as b}from"./vendor-CwRwASPO.js";import{x as y}from"./xssIgnoreConfig-EdhXgEyG.js";import j from"./empty_dark-BVM0wdOH.js";import D from"./empty_light-t-w1hjKQ.js";const x={props:{show:{type:Boolean,default:!1}}},T={class:"skeleton-analysis",style:{width:"100%"}},_={class:"title"},C={class:"desc"},S={class:"intro"},I={class:"left"},N={class:"right"};const B={class:"fx-street container"},M={key:1,class:"contents"},Y={key:1},$={class:"title"},H=["innerHTML"];const L=i({components:{navBar:g,newFooter:a,fxStreetSkeleton:i({mixins:[x]},[["render",function(e,s,a,i,o,n){const f=t;return d(),r("div",T,[l("div",_,[m(f,{title:"",round:""})]),l("div",C,[(d(),r(c,null,u(5,(t=>m(f,{title:"",round:"",key:"skeleton"+t}))),64))]),l("div",S,[l("div",I,[m(f,{title:"",round:"","title-width":100}),m(f,{title:"",round:"","title-width":100})]),l("div",N,[m(f,{title:"",round:"","title-width":100})])])])}],["__scopeId","data-v-8ef78b95"]])},mixins:[{data(){return{detail:{newsTitle:"",newsContent:"",pubTime:"",newsReads:0},loading:!0,id:this.$route.query.id,isNoData:!1}},methods:{goBack(){n("501")},getDetail(){k({id:this.$route.params.id}).then((t=>{this.loading=!1;const{resultCode:e,data:s}=t;if("00000000"===e){this.detail=s;const t=Object.keys(this.detail);this.isNoData=!t.length}else this.isNoData=!0})).catch((t=>{this.loading=!1,this.isNoData=!0;const{message:s}=t;e({message:s,wordBreak:"break-word"})}))},filterXssCode:t=>b(t,y),formatPublishTime:t=>w(t).format("DD/MM/YYYY HH:mm:ss")},mounted(){o({code:"250",title:this.$t("24/7")}),this.getDetail()}}],computed:{noDataImg(){return"dark"===this.$pinia.state.value.params.themeTxt?j:D}}},[["render",function(t,e,a,i,o,n){var c;const u=f("nav-bar"),g=f("fxStreetSkeleton"),k=s,w=f("new-footer");return d(),r("div",B,[m(u,{leftText:"24/7",onClickLeft:t.goBack},null,8,["onClickLeft"]),t.loading?(d(),h(g,{key:0})):(d(),r("div",M,[t.isNoData?(d(),h(k,{key:0,class:"no_data",image:n.noDataImg,description:t.$t("no_records_found"),"image-size":"80"},null,8,["image","description"])):(d(),r("div",Y,[l("h1",$,v(t.detail.newsTitle),1),l("div",{innerHTML:t.filterXssCode(t.detail.newsContent)},null,8,H)])),t.isNoData?p("",!0):(d(),h(w,{key:2,views:t.detail.newsReads,time:t.formatPublishTime(t.detail.pubTime),source:(null==(c=t.detail)?void 0:c.dataSourceStr)||"FX Street"},null,8,["views","time","source"]))]))])}],["__scopeId","data-v-ba0b828e"]]);export{L as default};
