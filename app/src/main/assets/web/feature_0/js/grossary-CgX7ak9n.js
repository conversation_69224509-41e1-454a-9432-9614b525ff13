import{C as t,d as e}from"./vant-vendor-D8PsFlrJ.js";import{_ as i}from"./index-M11nEPjl.js";import{J as o,K as r,j as s,L as n,F as a,Y as h,M as l,S as d,V as c}from"./vue-vendor-DjIN0JG5.js";import"./vendor-CwRwASPO.js";const p={class:"container"};const u=i({name:"grossary",data:()=>({activeNames:"",textArr:[]}),mounted(){this.textArr=[{title:this.$t("Active Copiers"),content:[this.$t("The amount of Copiers who are copying a Signal Provider.")]},{title:this.$t("Copy Asset Under Management (Copy AUM)"),content:[this.$t("The sum of equities of all Copiers that copying a strategy.")]},{title:this.$t("Average Holding Time"),content:[this.$t("The average amount of time a Signal Provider holds their position for.")]},{title:this.$t("Copier"),content:[this.$t("A user who mirrors a Signal Provider's real-time trades, reducing the need to actively observe the market and make trading decisions.")]},{title:this.$t("Copier Review"),content:[this.$t("A system that gives Signal Providers the choice to manually review copy requests or automatically approve them after 72 hours.")]},{title:this.$t("Copy Mode"),content:[this.$t("A Copy Mode determines how a Signal Provider's trades are mirrored onto a Copiers' account. A Copier will configure three main metrics, which are: "),this.$t("1. Equivalent Used Margin"),this.$t("2. Fixed Lots"),this.$t("3. Fixed Multiples")]},{title:this.$t("Copy Opened Trades"),content:[this.$t("A tool that upon activation allows Copiers to copy a Signal Provider's current open positions and future positions."),this.$t("If the Copier deactivates it, they would only copy a Signal Provider's future positions.")]},{title:this.$t("Copy Trading"),content:[this.$t("A form of communal investing. Participants are either Signal Providers who share their trades to be copied, or Copiers who copy those trades.")]},{title:this.$t("Copy Trading Account"),content:[this.$t("An account type that allows users to conduct copy trading activities, such as copy or share their trades.")]},{title:this.$t("Current Floating Profits"),content:[this.$t("A Copiers' current floating profit or loss (PnL) made from copy trading.")]},{title:this.$t("Current Period's Payout"),content:[this.$t("The profit share that will be paid to a Signal Provider, also known as 'Settlement'.")]},{title:this.$t("Eligible Profits for Sharing"),content:[this.$t("The positive difference between a Copier's 'Current Floating Profits' minus 'High Water Mark'. If the difference is negative, there will be no profit sharing.")]},{title:this.$t("Equivalent Used Margin"),content:[this.$t("The volume of a trade copied onto the Copier's account based on margin levels parallel to that of the Signal Provider's.")]},{title:this.$t("Fixed Lots"),content:[this.$t("The volume of a trade copied onto the Copier's account which is identical to the value set by the Signal Provider.")]},{title:this.$t("Fixed Multiples"),content:[this.$t("The volume of a trade copied onto the Copier's account is multiplied from the original order size of the Signal Provider.")]},{title:this.$t("Free Margin"),content:[this.$t("The equity in a user's account that has not been reserved in margin for open positions.")]},{title:this.$t("High Water Mark"),content:[this.$t("A Copiers' highest historical record of 'Current Floating Profits'.")]},{title:this.$t("Investment"),content:[this.$t("The net amount used by Copiers for Copy Trading."),this.$t("i.e. Investment = Initial Amount + Additional Investment Amount - Removed Investment Amount"),this.$t("When 'Removed Investment Amount' exceeds 'Initial Amount' + 'Additional Investment Amount', it will be displayed as a negative value.")]},{title:this.$t("Lot Round-Up"),content:[this.$t("A tool that auto-adjusts a Copier's copy trades to the Signal Provider's minimum lot requirement.")]},{title:this.$t("Minimum Investment per Copy"),content:[this.$t("The minimum investment amount a Signal Provider requests of any Copier who wishes to copy their strategy."),this.$t("If the Copier's currency differs from the Signal Providers', the amount will be converted based on real-time exchange rates.")]},{title:this.$t("Minimum Lots per Order"),content:[this.$t("The minimum lots per copied trade that a Signal Provider requires a Copier to fill, if the Copier chooses 'Fixed Lots' Copy Mode.")]},{title:this.$t("Minimum Multiples per Order"),content:[this.$t("The minimum multiple to one original order size that a Signal Provider requires a Copier to fulfil, if the Copier chooses 'Fixed Multiples' Copy Mode.")]},{title:this.$t("Monthly Return"),content:[this.$t("The difference between a user's net worth at the beginning and end of the month, accounting for any withdrawals or deposits made during that time."),this.$t("Total figures can be calculated by aggregating the current month's returns on an annual basis.")]},{title:this.$t("Open Trades"),content:[this.$t("Trades that have been executed and are still open. When a Signal Provider closes their trade, their Copiers' trades will follow suit and close, too.")]},{title:this.$t("Payment Amount"),content:[this.$t("The Signal Provider's account that will receive the profit share.")]},{title:this.$t("Pending Shareable Profits"),content:[this.$t("A Copier's 'Shareable Profits' to be paid (if any) to a Signal Provider, should they meet the profit sharing requirements.")]},{title:this.$t("Portfolio"),content:[this.$t("A compilation of trades which indicate the instruments that a trader focuses on.")]},{title:this.$t("Position"),content:[this.$t("The direction in which a trade is made."),this.$t("A long position is taken with the confidence that the asset's price will increase."),this.$t("A short position is taken with the belief that the asset's price will decrease.")]},{title:this.$t("Profit Sharing"),content:[this.$t("Profit Sharing is a ratio model that allows Signal Providers to earn a slice of their Copier's success."),this.$t("It is the percentage of 'Eligible Profits for Sharing' made by a Copier that will be paid out to the Signal Provider. The default is set at 0%, adjustable by 5% increments to a maximum of 50%.")]},{title:this.$t("Return"),content:[this.$t("The profit or loss of the copy. If the investment shows 0 or a negative value, then the Rate will be displayed as ∞.")]},{title:this.$t("Risk Band"),content:[this.$t("The level of risk associated with a Signal Provider's trading strategy, ranging from 0 (low risk) to 10 (high risk).")]},{title:this.$t("Risk Management"),content:[this.$t("The practice of managing potential risks of a trade in order to protect the overall portfolio. To prevent overexposure, traders set Stop Loss and Take Profit levels.")]},{title:this.$t("Settlement Frequency"),content:[this.$t("The profit sharing cycle set by the Signal Provider either daily, weekly or monthly.")]},{title:this.$t("Shareable Profits"),content:[this.$t("A Copier's 'Eligible Profits for Sharing' multiplied by 'Profit Sharing Ratio' set by the Signal Provider.")]},{title:this.$t("Signal Provider"),content:[this.$t("A trader who publicly shares their strategies for others to copy, and in return earns from profit shares.")]},{title:this.$t("Source Account"),content:[this.$t("A Live Account where Signal Providers conduct their trade activities, which are then cloned into their Strategy.")]},{title:this.$t("Stop Loss"),content:[this.$t("A tool to automatically close a position to prevent further losses, at a value set by the trader.")]},{title:this.$t("Strategy"),content:[this.$t("A strategy is a portfolio that belongs to a Signal Provider that is readily available for the Copiers to copy.")]},{title:this.$t("Take Profit"),content:[this.$t("A tool to automatically close a position once a profit target is reached to lock in profits.")]},{title:this.$t("Threshold for Copiers"),content:[this.$t("The minimum requirements set by the Signal Providers for a Copier to copy. The requirements include minimum investment per copy, lots per order and multiples per order.")]},{title:this.$t("Total Historical Payout"),content:[this.$t("The total profit shares paid out to a Signal Provider.")]},{title:this.$t("Total Shared Profit"),content:[this.$t("The total profit paid by a Copier to all the Signal Providers they have copied.")]},{title:this.$t("Unpaid Amount"),content:[this.$t("The profit share payout that is on hold due to the lack of a valid Payment Amount.")]}]}},[["render",function(i,u,m,g,f,v){const $=t,y=e;return r(),o("div",p,[s(y,{modelValue:f.activeNames,"onUpdate:modelValue":u[0]||(u[0]=t=>f.activeNames=t),accordion:"",border:!1},{default:n((()=>[(r(!0),o(a,null,h(f.textArr,((t,e)=>(r(),l($,{class:"van-collapse-item--border",size:"large",title:t.title,name:e,key:e},{"right-icon":n((()=>u[1]||(u[1]=[d("div",{class:"icon-arrow_down"},null,-1)]))),default:n((()=>[(r(!0),o(a,null,h(t.content,((t,e)=>(r(),o("div",{key:e},[d("div",null,c(t),1)])))),128))])),_:2},1032,["title","name"])))),128))])),_:1},8,["modelValue"])])}],["__scopeId","data-v-5415053b"]]);export{u as default};
