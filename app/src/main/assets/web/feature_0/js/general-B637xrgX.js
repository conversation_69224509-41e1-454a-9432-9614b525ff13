import{t}from"./tab-DAk8rFSJ.js";import{C as e,d as i}from"./vant-vendor-D8PsFlrJ.js";import{_ as o}from"./index-M11nEPjl.js";import{M as a,K as r,L as n,J as s,F as l,Y as h,A as c,S as d,I as u,j as p,N as g}from"./vue-vendor-DjIN0JG5.js";import{_ as y}from"./image-Bw2isvnQ.js";import"./vendor-CwRwASPO.js";const f=["innerHTML"];const m=o({props:{activeNames:{type:String,default:""},list:{type:Array,default:()=>[]}},data:()=>({name:""}),watch:{activeNames:{immediate:!0,handler(t){this.name=t}}}},[["render",function(t,o,u,p,g,y){const m=e,v=i;return r(),a(v,{modelValue:g.name,"onUpdate:modelValue":o[0]||(o[0]=t=>g.name=t),accordion:"",border:!1},{default:n((()=>[(r(!0),s(l,null,h(u.list,((t,e)=>(r(),a(m,{class:"van-collapse-item--border",size:"large",title:t.title,name:e,key:e},{"right-icon":n((()=>o[1]||(o[1]=[d("div",{class:"icon-arrow_down"},null,-1)]))),default:n((()=>[(r(!0),s(l,null,h(t.content,((e,i)=>(r(),s("div",{key:i,style:c({paddingTop:1===i?"16px":0,paddingBottom:1===i&&i!==t.content.length-1?"16px":0}),innerHTML:e},null,12,f)))),128))])),_:2},1032,["title","name"])))),128))])),_:1},8,["modelValue"])}]]),v={class:"accordion"};const $=o({components:{collapse:m},data(){return{activeNames:"",data:[{title:this.$t("How do I copy a Signal Provider's strategy?"),content:[this.$t("Enter your Copy Trading Account, head to the Discover Page and select a Signal Provider. Tap into their Profile and click on 'Copy'.")]},{title:this.$t("How do I stop copying a Signal Provider's strategy?"),content:[this.$t("Head to the Orders page, click on the Signal Provider you want to stop copying. Click on the 'Manage' button, and tap on 'Stop Copy'.")]},{title:this.$t("How much do I need to start Copy Trading?"),content:[this.$t("The starting amount differs based on the currency of the account. The minimum amount starts from as low as USD 50, EUR 50, HKD 400, JPY 7000, USC 4000 and INR 4000.")]},{title:this.$t("Can I copy more than one strategy at a time?"),content:[this.$t("Yes, you can copy more than one strategy at a time as long as you have sufficient funds available in your wallet.")]},{title:this.$t("Is there a limit to how many strategies I can copy or follow?"),content:[this.$t("No, there is no limit to the number of strategies you can copy or follow.")]},{title:this.$t("Can I copy a Signal Provider's strategy while the market is closed?"),content:[this.$t("Yes, you can. Copiers can start and stop copying a Signal Provider's strategies (at the last available prices) when the market is closed. However, open positions can only be closed after the market is open. Before that, its status will display 'Pending Close'.")]},{title:this.$t("What happens when a copy trade hits the Stop Loss level that you have set for a strategy?"),content:[this.$t("All positions will be forced to close and you will stop following the Signal Provider's strategy.")]},{title:this.$t("What is Profit Sharing Model?"),content:[this.$t("For Copiers, the “Profit Sharing Model” allows you to support your favourite Signal Providers through sharing a portion of your profits.")]},{title:this.$t("What is a Profit Sharing Ratio?"),content:[this.$t("Determined by Signal Providers, it is the percentage of a Copier’s “Eligible Profits for Sharing” that will be paid out to Signal Providers. The default is set to 0%, adjustable by 5%, at a maximum of 50%.")]},{title:this.$t("What is Eligible Profits for Sharing?"),content:[this.$t("For Copiers, it is the positive difference between your “Current Floating Profits” minus “High Water Mark” (if any). If the difference is negative, there will be no profit sharing.")]},{title:this.$t("What are Current Floating Profits?"),content:[this.$t("For Copiers, it is your current “Settlement Cycle” floating profit / loss (PnL) made from copy trading.")]},{title:this.$t("What is a High Water Mark?"),content:[this.$t("For Copiers, it is the highest historical record of your “Current Floating Profits”.")]},{title:this.$t("What is a Settlement Cycle / Settlement Day?"),content:[this.$t("“Settlement Day” is when Signal Providers’ “Pending Profits” and Copiers’ “Pending Shareable Profits” are tabulated. You may pre-set your Settlement Cycle either on a daily, weekly (every Saturday) or monthly basis (first calendar day)."),this.$t("On “Settlement Day”, profit sharing will take effect only when Copiers’ “Current Floating Profits” exceeds their “High Water Mark”.")]},{title:this.$t("What is Total Shared Profit?"),content:[this.$t("It is the “Total Historical Payout” paid to the Signal Provider that you copied.")]},{title:this.$t("What are Shareable Profits and how do I calculate them?"),content:[this.$t("“Shareable Profits” = “Eligible Profits for Sharing” x “Profit Sharing Ratio”")]},{title:this.$t("What are Pending Shareable Profits?"),content:[this.$t("It is the “Shareable Profits” to be paid (if any) to Signal Providers you have copied when “Profit Sharing Requirements” are met.")]},{title:this.$t("What are the Profit Sharing Requirements?"),content:["i."+this.$t("Your “Current Floating Profits” must be higher than “High Water Mark”."),"ii."+this.$t("You must have sufficient balance for the deduction of value under “Shareable Profits”."),"iii."+this.$t("Your margin level must be above 100% after the deduction mentioned in ii.")]}]}}},[["render",function(t,e,i,o,a,n){const l=u("collapse");return r(),s("div",v,[p(l,{activeNames:a.activeNames,list:a.data},null,8,["activeNames","list"])])}]]),P={class:"accordion"};const b=o({components:{collapse:m},data(){return{activeNames:"",data:[{title:this.$t("What is Copy Trading?"),content:[this.$t("Copy Trading is a trading strategy and function that automatically copies the entire portfolio of any other trader. Copy Trading mainly consists of 2 roles, which are:"),this.$t("Signal Provider"),this.$t("Users who publicly share their trades for other users to copy."),this.$t("Copiers"),this.$t("Users who copy the publicly shared trades of Signal Providers.")]},{title:this.$t("What is the difference between Copy Trading and Manual Trading?"),content:[this.$t("Manual Trading enables you to perform regular trading activities while using a Copy Trading account."),this.$t("Copy Trading enables you to copy trades from a Signal Provider‘s Manual Trading.")]},{title:this.$t("Do I need to open a regular Live Account before opening a Copy Trading Account?"),content:[this.$t("No, you may open a Copy Trading Account without having to open a Live Account; however, a regular Live Account is required to withdraw profits from your Copy Trading Account.")]},{title:this.$t("Are there any fees for participating in Copy Trading?"),content:[this.$t("No, there are no management or subscription fees to participate in Copy Trading, however:"),"-"+this.$t("Signal Providers will still be charged spreads on trades and/or transaction fees where applicable."),"-"+this.$t("If requirements are met, profit sharing will take effect for Copiers.")]},{title:this.$t("Can I have multiple Copy Trading Accounts?"),content:[this.$t("No, you may only have ONE (1) Copy Trading Account. Your Copy Trading Account will include both manual and copy trading functions.")]},{title:this.$t("Which markets can I trade on through a Copy Trading Account?"),content:[this.$t("You can access over 1,000 tradeable CFDs ranging from Forex, Crypto, Shares, Indices, Metals and Commodities."),this.$t("Both Signal Providers and Copiers may trade these assets through the Manual Trading tab in the Orders Page.")]},{title:this.$t("How do other traders view / copy my trades?"),content:[this.$t('When you toggle the "Set as public" button on your Profile Page, other traders can view and copy your trades. This action will make your trades visible and you will be recognised as a Signal Provider.')]},{title:this.$t("What are the deposit methods available for Copy Trading accounts?"),content:[this.$t("Deposit methods available for Copy Trading Accounts are:"),this.$t("Bank Transfers"),this.$t("Credit & Debit Cards"),this.$t("Select Cryptocurrencies")]},{title:this.$t("Which currency can I use in Copy Trading Accounts?"),content:[this.$t("Copy Trading Accounts are currently only available in United States Dollar (USD), United States Cents (USC), Euro (EUR), Hong Kong Dollar (HKD), Japanese Yen (JPY), and Indian Rupee (INR), but we are working on adding more currency options in the future.")]},{title:this.$t("How do I open a Copy Trading account?"),content:[this.$t("To sign-up for Copy Trading, head to the Account Management Page in your Profile, and select Open Copy Trading Account.")]},{title:this.$t("Is Copy Trading available on Vantage's web browser?"),content:[this.$t("No, unfortunately. Copy Trading is currently only available on the Vantage App.")]},{title:this.$t("What documents are required to open a Copy Trading account?"),content:[this.$t("No additional documents are required to open a Copy Trading account. However, during your initial sign-up for a Live Account, you will need to submit a photo of your government-issued Identity Card and Proof of Address.")]},{title:this.$t("Can I copy specific trades made by Signal Providers?"),content:[this.$t("Upon copying a strategy, you'll have the choice to copy all open trades or copy specific products within that strategy."),this.$t("If you wish to close or stop copying specific products, simply head into your copy trade order and tap close on the product you wish to stop copying.")]},{title:this.$t("Can I copy trades made by a Signal Provider while the market is closed?"),content:[this.$t("Upon copying a strategy, you'll have the choice to copy all open trades or copy specific products within that strategy."),this.$t("Yes, you can. Copiers can start and stop copying a Signal Provider (at the last available prices) even when the market is closed. However, any open positions can only be closed after the market is open. Until then, its status will be displayed as 'Pending Close'.")]}]}}},[["render",function(t,e,i,o,a,n){const l=u("collapse");return r(),s("div",P,[p(l,{activeNames:a.activeNames,list:a.data},null,8,["activeNames","list"])])}]]),C={class:"accordion"};const S=o({components:{collapse:m},data(){return{activeNames:"",data:[{title:this.$t("How do I become a Signal Provider?"),content:[this.$t("There are two conditions that you need to fulfill in order to become a Signal Provider:"),"1."+this.$t("Make a single, minimum deposit of USD 500 or more."),"2."+this.$t("Possess extensive trading experience, adhere to the best industry practices, and conduct trades with integrity.")]},{title:this.$t("When will I receive my profit share?"),content:[this.$t("Signal Providers will receive pay out within 2 working days after the Settlement Day.")]},{title:this.$t("How do I withdraw profits shared to me?"),content:[this.$t("As a Signal Provider, the shared profit will be transferred to a Payment Account. If you have not set up a Payment Account, you can do so through your Profile - Profit Sharing Summary.")]},{title:this.$t("What should I do if I have not received my shared profits?"),content:[this.$t("If you have not received your shared profits, please contact our friendly 24/5 Customer Support Team.")]},{title:this.$t("Where can I view my profits earned from Copiers?"),content:[this.$t("Head to your Profile page and check out the Total Historical Payout in your Profit Sharing Summary.")]},{title:this.$t("Can I transfer funds from other accounts to my Copy Trading Account?"),content:[this.$t("Yes. Go to your Profile Page and tap on Transfer. You may then select the account you wish to transfer funds from.")]},{title:this.$t("As a Signal Provider, can I copy other Signal Providers' strategies?"),content:[this.$t("Yes. Signal Providers can copy other Signal Providers' strategies.")]},{title:this.$t("What is Profit Sharing Model?"),content:[this.$t("For Signal Providers, the “Profit Sharing Model” allows you to earn from all profitable copy trades.")]},{title:this.$t("What is a Profit Sharing Ratio?"),content:[this.$t("Determined by Signal Providers, it is the percentage of a Copier’s “Eligible Profits for Sharing” that will be paid out to Signal Providers. The default is set to 0%, adjustable by 5%, at a maximum of 50%.")]},{title:this.$t("What is a Settlement Cycle / Settlement Day?"),content:[this.$t("The “Settlement Cycle” occurs within a 7-day period. “Settlement Day” is when Signal Providers’ “Pending Profits” and Copiers’ “Pending Shareable Profits” are tabulated."),this.$t("On “Settlement Day”, profit sharing will take effect only when Copiers’ “Current Floating Profits” exceeds their “High Water Mark”.")]},{title:this.$t("What is Total Historical Payout?"),content:[this.$t("It is the total profits paid out to you.")]},{title:this.$t("What are Current Floating Profits and how do I calculate them?"),content:[this.$t('Current Floating Profits are your Copier\'s "Eligible Profits for Sharing" multiplied by the Profit Sharing Ratio you set.'),this.$t("“Current Floating Profits” = “Eligible Profits for Sharing” x “Profit Sharing Ratio”")]},{title:this.$t("What are Pending Profits?"),content:[this.$t("It is the amount that will be paid out to you if “Profit Sharing Requirements” are met.")]},{title:this.$t("What are Profit Sharing Requirements for Copiers?"),content:["i."+this.$t("Your Copier’s “Current Floating Profits” must be higher than “High Water Mark”."),"ii."+this.$t("Your Copier(s) must have sufficient balance for the deduction of value under “Shareable Profits”."),"iii."+this.$t("Your Copier’s margin level must be above 100% after the deduction mentioned in ii.")]}]}}},[["render",function(t,e,i,o,a,n){const l=u("collapse");return r(),s("div",C,[p(l,{activeNames:a.activeNames,list:a.data},null,8,["activeNames","list"])])}]]),w={class:"container"};const T=o({name:"general",components:{tab:t,generalTab:b,generalCopierTab:$,generalSignalProvidersTab:S},data:()=>({tabList:["General","Copiers","Signal Providers"],tabContentName:b}),methods:{tabChange(t){const e=[b,$,S];this.tabContentName=e[t]}}},[["render",function(t,e,i,o,l,h){const c=u("tab");return r(),s("div",w,[e[0]||(e[0]=d("div",{class:"tab-banner-img"},[d("img",{src:y,alt:""})],-1)),p(c,{tabs:l.tabList,onChange:h.tabChange,swipeable:"",animated:""},{default:n((()=>[(r(),a(g(l.tabContentName)))])),_:1},8,["tabs","onChange"])])}],["__scopeId","data-v-be2108b7"]]);export{T as default};
