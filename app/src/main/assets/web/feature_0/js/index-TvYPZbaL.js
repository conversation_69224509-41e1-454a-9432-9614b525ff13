import{e as s,b as e,s as t,E as a,f as o,g as n}from"./vant-vendor-D8PsFlrJ.js";import{s as i}from"./search-BMccXSDW.js";import{J as l,K as d,j as r,s as c,v as m,S as u,F as p,Y as h,V as v,I as y,M as k,L as b}from"./vue-vendor-DjIN0JG5.js";import{_ as f,s as g,r as L}from"./index-M11nEPjl.js";import{n as _}from"./navbar-DfgFEjpa.js";import{c as C}from"./news-Bq96hVLI.js";import j from"./empty_dark-BVM0wdOH.js";import N from"./empty_light-t-w1hjKQ.js";import"./vendor-CwRwASPO.js";const D={class:"search-symbol"},I={class:"drop-down-list"},$=["onClick"];const w={class:"skeleton-notice"},x={class:"list"};const B={class:"container"},T={key:1,class:"content"},V={key:1},K={class:"tips"};const S=f({components:{navBar:_,search:f({mixins:[i]},[["render",function(e,t,a,o,n,i){const y=s;return d(),l("div",D,[r(y,{id:"search",modelValue:e.searchKey,"onUpdate:modelValue":t[0]||(t[0]=s=>e.searchKey=s),class:"search-container",shape:"round",clearable:"",placeholder:e.$t("Search")},null,8,["modelValue","placeholder"]),c(u("div",I,[(d(!0),l(p,null,h(e.list,((s,t)=>c((d(),l("div",{key:s.symbol,class:"search-item",onClick:t=>e.goto(s)},v(s.symbol),9,$)),[[m,t<=3]]))),128))],512),[[m,e.show]])])}],["__scopeId","data-v-be0b911d"]]),listSkeleton:f({},[["render",function(s,t){const a=e;return d(),l("div",w,[r(a,{class:"tips"}),t[0]||(t[0]=u("div",{class:"search-bar van-skeleton-paragraph"},null,-1)),u("div",x,[(d(),l(p,null,h(11,(s=>r(a,{key:s,round:"",class:"item",title:""}))),64))])])}],["__scopeId","data-v-488a9eb6"]])},mixins:[{data(){return{searchKey:"",loading:!0,dataList:{symbolList:[]},isNoData:!1,dividendType:this.$route.query.dividendType}},methods:{handleNavBarClickLeft(){L(501)},getList(){const s={dividendId:this.$route.params.id};this.loading=!0;const e=this;C(s).then((s=>{e.loading=!1;const{resultCode:a,data:o,msgInfo:n}=s;if("V00000"===a){const{obj:s}=o;e.dataList=s||"",e.isNoData=!(s.symbolList&&s.symbolList.length>0)}else t({message:n,wordBreak:"break-word"}),e.isNoData=!0})).catch((s=>{e.loading=!1,this.isNoData=!0;const{message:a}=s;t({message:a,wordBreak:"break-word"})}))},goBack(){L("501")},goto(s){const{dividendType:e,dividendId:a,announceContent:o}=this.dataList;e&&a?this.$router.push({name:"noticeDetail",query:{dividendType:e,dividendId:a,announceContent:o,productName:s.productName,symbol:s.symbol}}):t.fail(this.$t("No Records Found"))}},mounted(){this.getList(),g({code:"250",title:this.$t("Announcements")})}}],computed:{noDataImg(){return"dark"===this.$pinia.state.value.params.themeTxt?j:N}}},[["render",function(s,e,t,i,c,m){const f=y("navBar"),g=y("listSkeleton"),L=a,_=y("search"),C=o,j=n;return d(),l("div",B,[r(f,{"left-text":s.$t("Announcements"),onClickLeft:s.handleNavBarClickLeft},null,8,["left-text","onClickLeft"]),s.loading?(d(),k(g,{key:0})):(d(),l("div",T,[s.isNoData?(d(),k(L,{key:0,class:"no_data",image:m.noDataImg,description:s.$t("no_records_found"),"image-size":"80"},null,8,["image","description"])):(d(),l("div",V,[r(C,null,{default:b((()=>[u("div",K,v(s.dataList.announceContent),1),r(_,{"symbol-list":s.dataList.symbolList,onGotoDetail:s.goto},null,8,["symbol-list","onGotoDetail"])])),_:1}),(d(!0),l(p,null,h(s.dataList.symbolList,(e=>(d(),k(j,{key:e.symbol,title:e.symbol,label:5==s.dividendType?e.productName:"","is-link":"",onClick:t=>s.goto(e)},null,8,["title","label","onClick"])))),128))]))]))])}],["__scopeId","data-v-525de7ff"]]);export{S as default};
