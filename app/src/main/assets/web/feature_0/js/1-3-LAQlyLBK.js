import{P as e,s as a}from"./vant-vendor-D8PsFlrJ.js";import{_ as t,e as i,r as s}from"./index-M11nEPjl.js";import{m as n,u as o,b as l,a as d,d as r,g as c}from"./index-BVEDVdSS.js";import{J as u}from"./vendor-CwRwASPO.js";import{w as m}from"./wrapperContainer-CL-3pf79.js";import{u as p,s as h}from"./unselected-C8BCzUVZ.js";import{_ as g}from"./remove_icon-CHjeUfRH.js";import{I as f,J as b,K as y,j as v,L as w,S as x,F as _,Y as I,B as V,A as F,V as C,y as k}from"./vue-vendor-DjIN0JG5.js";import"./navbar-DfgFEjpa.js";const M={class:"container"},q={class:"contents upload_statement"},j=["innerHTML"],L={class:"btn2 flex-center btn_upload"},P={id:"pic1",class:"flex-center"},S={class:"upload_files",id:"selectedFiles"},U={class:"row"},$=["id"],R=["onClick"],T={key:1,class:"read read_upload"},z={class:"read_field"},A=["src"],N={class:"selectImageSpan marginBottom0"},B={class:"row"},D={class:"label"},E={class:"w_100"},K={class:"bottom_btn"},W={class:"modal_btn"},Y={class:"modal_btn"},X={class:"modal_btn"};const O=t({components:{WrapperContainer:m},mixins:[n],computed:{imageSrc(){return this.readStatus?this.images.selected2:this.images.unselected}},data:()=>({images:{selected2:h,unselected:p},confirm:!1,readStatus:!1,sizeModalVisible:!1,fileExtensionModalVisible:!1,maxModalVisible:!1,imageFileList:[],uploadTips:['Based on what you have declared on your application, you <span class="text_bold">may be eligible</span> to be categorised as Wholesale client and be offered a PRO account – limited to Wholesale clients only.','You will need to provide a valid <span class="text_bold">(issued in the last two (2) years)</span> certified accountant’s certificate that certifies you meet one of the minimum wealth tests.',"If you do not have an accountant’s certificate, you can print out Vantage FX’s accountant certificate for your qualified accountant to complete. Please find the certificate here"],questionList:['I understand that PUPRIME Group Pty Ltd ("Vantage"), ACN *********, AFSL No. 428901, trading as Vantage is the issuer of the products (Margin FX “FX” and Contracts-for Difference “CFDs”).',"I acknowledge that I have read, understood and agreed to be bound by Vantage’s Wholesale Client Information Statement , Wholesale Client Terms and Conditions(T&C’s), Client Categorisation Notice and Privacy Policy and Vantage FX Pty Ltd’s Privacy Policy.","I agree that I will not be acquiring financial products or services with Vantage in connection with a business.","I agree that Vantage may withdraw my status as a wholesale client at any time in its absolute discretion.","I acknowledge that the financial products and services which Vantage will provide to me as a wholesale client do not necessarily have the same investor protection and disclosure requirements as products made available to retail clients, and I am satisfied that I have the ability to obtain the information needed to make an informed decision.","I acknowledge and agree that upon submitting this application I may be requested to provide Vantage with additional information such as supporting evidence, additional or up to date Know-Your-Customer (KYC) information, as well as close any open position(s) I may currently hold under my existing account(s) before finalising my application.","Vantage has not given me a Product Disclosure Statement.","Vantage has not given me any other document that would be required to be given to me under Chapter 7 of the Corporations Act 2001 if the product or service were provided to me as a retail client.","Vantage does not have any other obligation to me under Chapter 7 of the Corporation Act 2991 that Vantage would have if the product or service were provided to me as a retail client.","If I am an existing Vantage client and have previously been treated as a retail client, I acknowledge that I will now be treated by Vantage as a wholesale clint, and that any previously issued disclosure and engagement documents will no longer correctly reflect my rights and powers and should not be relied on.","I understand and accept the risks associated with trading FX and CFD’s.","I consent to receive electronic communication from Vantage, including but not limited to trade confirmation statements and receipt of funds.","I confirm that the information provided by me and inserted in this form is correct and I acknowledge that I shall be obliged to inform Vantage if any of my information changes.","I agree that if there is any change relating to my wholesale client eligibility, I undertake to inform Vantage as soon as possible, but in any event, within 14 calendar days of such change in circumstances, and agree that Vantage shall not be liable for any losses arising from any delay or failure for me to notify Vantage.","I confirm that I have acted in my name as specified in this application and not on behalf of a third party in respect of all matters related to this client relationship. Accordingly, all funds to be deposited and traded on the account with Vantage are my funds.","I have read, understood, and agreed to be bound by Vantage’s deposits and withdrawals policy."]}),methods:{back(){s("501")},toggleReadStatus(){this.readStatus=!this.readStatus},async getUserAgreedTC(){const{data:e}=await this.useRequest(c,{userId:this.$pinia.state.value.params.userId,type:2});this.confirm=e.obj.confirm},async proclientProcess(){await this.useRequest(r,{userId:this.$pinia.state.value.params.userId,step:"1-3",certificateFilePathList:this.imageFileList,isAgreedDeclaration:!0})},async handleNext(){if(this.confirm){if(0===this.imageFileList.length)return void a("Please upload images");await this.proclientProcess();const{data:e}=await this.useRequest(d,{userId:this.$pinia.state.value.params.userId});switch(e.obj.step){case"1-4":case"0-7":this.$router.push({path:"success",query:this.$route.query});break;default:this.$router.push({path:e.obj.step,query:this.$route.query})}}else{if(!this.readStatus)return void a("Please agree to the terms");await this.useRequest(l,{userId:this.$pinia.state.value.params.userId,choose:2,type:2}),this.confirm=!0}},upload(){this.$refs.inputFileUpload.click()},async imageFileCompressor(e){var a;let t=e.target.files[0].name.split(".").pop();if(e.target.files[0].size/1024>5e3)this.sizeModalVisible=!0;else if("png"!==t&&"jpg"!==t&&"jpeg"!==t)this.fileExtensionModalVisible=!0;else if(this.imageFileList.length>=6)this.maxModalVisible=!0;else{const t=(null==(a=this.$route.query)?void 0:a.accountId)||this.$route.query.userAccount,s=this;new u(e.target.files[0],{quality:.8,maxWidth:1600,maxHeight:1600,success(e){s.$refs.inputFileUpload.value="";let a=new FileReader;a.readAsDataURL(e),a.onloadend=async e=>{const a="TransRec_"+t+i()+".png",{data:n}=await s.uploadFile(e.target.result,a,a);s.imageFileList.push(n.obj.imgFile)}}})}},async uploadFile(e,a,t){return await this.useRequest(o,{token:this.$route.query.token,imgFile:a,imgBase64:e,imgName:t})},clearIndexImage(e){this.imageFileList.splice(e,1)}},created(){this.getUserAgreedTC()}},[["render",function(a,t,i,s,n,o){const l=f("wrapper-container"),d=e;return y(),b("div",M,[v(l,{loading:a.loading},{default:w((()=>[n.confirm?(y(),b(_,{key:0},[x("div",q,[(y(!0),b(_,null,I(n.uploadTips,(e=>(y(),b("div",{class:"text",key:e,innerHTML:e},null,8,j)))),128))]),x("div",L,[x("label",P,[t[10]||(t[10]=V(" Upload Trading Statement ")),x("input",{type:"file",name:"imageUpload",id:"imageUpload",accept:".png, .jpg, .jpeg",ref:"inputFileUpload",onChange:t[0]||(t[0]=(...e)=>o.imageFileCompressor&&o.imageFileCompressor(...e))},null,544),t[11]||(t[11]=x("input",{type:"hidden",id:"imageUploadbase64Name"},null,-1))])]),t[13]||(t[13]=x("div",{class:"contents upload_statement"},[x("div",{class:"text text_bold mb_none"},"File size"),x("div",{class:"text"},[V(" The file exceeds the maximum upload size. "),x("br"),V(" Maximum upload size per file: 5MB ")]),x("div",{class:"text text_bold mb_none"},"Upload quantity"),x("div",{class:"text"}," You may only upload a maximum of 6 files. ")],-1)),x("div",S,[x("div",U,[(y(),b(_,null,I(6,((e,a)=>x("div",{class:"img_box",id:`upload_${a+1}`,key:a},[x("div",{class:"img_container",style:F(n.imageFileList[a]&&`background-image:url(${n.imageFileList[a]})`)},[n.imageFileList[a]?(y(),b("img",{key:1,src:g,class:"test",alt:"",onClick:e=>o.clearIndexImage(a)},null,8,R)):(y(),b("div",{key:0,class:"img_box_text box_upload",onClick:t[1]||(t[1]=(...e)=>o.upload&&o.upload(...e))},t[12]||(t[12]=[x("div",{class:"upload_plus"},null,-1),x("p",null,"Upload",-1)])))],4)],8,$))),64))])])],64)):(y(),b("div",T,[x("div",{class:"flex text read_contents",onClick:t[2]||(t[2]=(...e)=>o.toggleReadStatus&&o.toggleReadStatus(...e))},[x("div",z,[x("img",{src:o.imageSrc,alt:"status",class:"icon"},null,8,A)]),t[14]||(t[14]=x("p",{class:"read_field_label"},"By ticking this box:",-1))]),x("div",N,[(y(!0),b(_,null,I(n.questionList,((e,a)=>(y(),b("div",{class:"text",key:e},[x("div",B,[x("p",D,C(a+1),1),x("p",E,C(e),1)])])))),128))])])),x("div",K,[x("div",{class:k(["btn flex-center btn_next",(n.readStatus||n.imageFileList.length&&n.confirm)&&"active"]),onClick:t[3]||(t[3]=(...e)=>o.handleNext&&o.handleNext(...e))}," Next ",2)])])),_:1},8,["loading"]),v(d,{show:n.sizeModalVisible,"onUpdate:show":t[5]||(t[5]=e=>n.sizeModalVisible=e)},{default:w((()=>[t[15]||(t[15]=x("div",{class:"modal_msg"},[x("p",{class:"mb_2"},"The file exceeds the maximum upload size."),x("p",null,"Maximum upload size per file: 5MB")],-1)),x("div",W,[x("div",{class:"btn flex-center btn_next",onClick:t[4]||(t[4]=e=>n.sizeModalVisible=!1)}," OK ")])])),_:1},8,["show"]),v(d,{show:n.fileExtensionModalVisible,"onUpdate:show":t[7]||(t[7]=e=>n.fileExtensionModalVisible=e)},{default:w((()=>[t[16]||(t[16]=x("div",{class:"modal_msg"},[x("p",{class:"mb_2"},"Please upload .png, .jpg, .jpeg file")],-1)),x("div",Y,[x("div",{class:"btn flex-center btn_next",onClick:t[6]||(t[6]=e=>n.fileExtensionModalVisible=!1)}," OK ")])])),_:1},8,["show"]),v(d,{show:n.maxModalVisible,"onUpdate:show":t[9]||(t[9]=e=>n.maxModalVisible=e)},{default:w((()=>[t[17]||(t[17]=x("div",{class:"modal_msg"},[x("p",null,[V("You may only upload a "),x("br"),V("maximum of 6 files.")])],-1)),x("div",X,[x("div",{class:"btn flex-center btn_next",onClick:t[8]||(t[8]=e=>n.maxModalVisible=!1)}," OK ")])])),_:1},8,["show"])])}],["__scopeId","data-v-d047c7fe"]]);export{O as default};
