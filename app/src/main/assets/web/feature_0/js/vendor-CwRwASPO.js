function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t,n={exports:{}};const r=e((t||(t=1,n.exports=function(){var e=1e3,t=6e4,n=36e5,r="millisecond",i="second",s="minute",o="hour",a="day",c="week",l="month",u="quarter",h="year",d="date",p="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}},_=function(e,t,n){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(n)+e},y={s:_,z:function(e){var t=-e.utcOffset(),n=Math.abs(t),r=Math.floor(n/60),i=n%60;return(t<=0?"+":"-")+_(r,2,"0")+":"+_(i,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var r=12*(n.year()-t.year())+(n.month()-t.month()),i=t.clone().add(r,l),s=n-i<0,o=t.clone().add(r+(s?-1:1),l);return+(-(r+(n-i)/(s?i-o:o-i))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:l,y:h,w:c,d:a,D:d,h:o,m:s,s:i,ms:r,Q:u}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},v="en",b={};b[v]=g;var w="$isDayjsObject",C=function(e){return e instanceof E||!(!e||!e[w])},T=function e(t,n,r){var i;if(!t)return v;if("string"==typeof t){var s=t.toLowerCase();b[s]&&(i=s),n&&(b[s]=n,i=s);var o=t.split("-");if(!i&&o.length>1)return e(o[0])}else{var a=t.name;b[a]=t,i=a}return!r&&i&&(v=i),i||!r&&v},k=function(e,t){if(C(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new E(n)},S=y;S.l=T,S.i=C,S.w=function(e,t){return k(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var E=function(){function g(e){this.$L=T(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[w]=!0}var _=g.prototype;return _.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(S.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(f);if(r){var i=r[2]-1||0,s=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(t)}(e),this.init()},_.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},_.$utils=function(){return S},_.isValid=function(){return!(this.$d.toString()===p)},_.isSame=function(e,t){var n=k(e);return this.startOf(t)<=n&&n<=this.endOf(t)},_.isAfter=function(e,t){return k(e)<this.startOf(t)},_.isBefore=function(e,t){return this.endOf(t)<k(e)},_.$g=function(e,t,n){return S.u(e)?this[t]:this.set(n,e)},_.unix=function(){return Math.floor(this.valueOf()/1e3)},_.valueOf=function(){return this.$d.getTime()},_.startOf=function(e,t){var n=this,r=!!S.u(t)||t,u=S.p(e),p=function(e,t){var i=S.w(n.$u?Date.UTC(n.$y,t,e):new Date(n.$y,t,e),n);return r?i:i.endOf(a)},f=function(e,t){return S.w(n.toDate()[e].apply(n.toDate("s"),(r?[0,0,0,0]:[23,59,59,999]).slice(t)),n)},m=this.$W,g=this.$M,_=this.$D,y="set"+(this.$u?"UTC":"");switch(u){case h:return r?p(1,0):p(31,11);case l:return r?p(1,g):p(0,g+1);case c:var v=this.$locale().weekStart||0,b=(m<v?m+7:m)-v;return p(r?_-b:_+(6-b),g);case a:case d:return f(y+"Hours",0);case o:return f(y+"Minutes",1);case s:return f(y+"Seconds",2);case i:return f(y+"Milliseconds",3);default:return this.clone()}},_.endOf=function(e){return this.startOf(e,!1)},_.$set=function(e,t){var n,c=S.p(e),u="set"+(this.$u?"UTC":""),p=(n={},n[a]=u+"Date",n[d]=u+"Date",n[l]=u+"Month",n[h]=u+"FullYear",n[o]=u+"Hours",n[s]=u+"Minutes",n[i]=u+"Seconds",n[r]=u+"Milliseconds",n)[c],f=c===a?this.$D+(t-this.$W):t;if(c===l||c===h){var m=this.clone().set(d,1);m.$d[p](f),m.init(),this.$d=m.set(d,Math.min(this.$D,m.daysInMonth())).$d}else p&&this.$d[p](f);return this.init(),this},_.set=function(e,t){return this.clone().$set(e,t)},_.get=function(e){return this[S.p(e)]()},_.add=function(r,u){var d,p=this;r=Number(r);var f=S.p(u),m=function(e){var t=k(p);return S.w(t.date(t.date()+Math.round(e*r)),p)};if(f===l)return this.set(l,this.$M+r);if(f===h)return this.set(h,this.$y+r);if(f===a)return m(1);if(f===c)return m(7);var g=(d={},d[s]=t,d[o]=n,d[i]=e,d)[f]||1,_=this.$d.getTime()+r*g;return S.w(_,this)},_.subtract=function(e,t){return this.add(-1*e,t)},_.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||p;var r=e||"YYYY-MM-DDTHH:mm:ssZ",i=S.z(this),s=this.$H,o=this.$m,a=this.$M,c=n.weekdays,l=n.months,u=n.meridiem,h=function(e,n,i,s){return e&&(e[n]||e(t,r))||i[n].slice(0,s)},d=function(e){return S.s(s%12||12,e,"0")},f=u||function(e,t,n){var r=e<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(m,(function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return S.s(t.$y,4,"0");case"M":return a+1;case"MM":return S.s(a+1,2,"0");case"MMM":return h(n.monthsShort,a,l,3);case"MMMM":return h(l,a);case"D":return t.$D;case"DD":return S.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(n.weekdaysMin,t.$W,c,2);case"ddd":return h(n.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(s);case"HH":return S.s(s,2,"0");case"h":return d(1);case"hh":return d(2);case"a":return f(s,o,!0);case"A":return f(s,o,!1);case"m":return String(o);case"mm":return S.s(o,2,"0");case"s":return String(t.$s);case"ss":return S.s(t.$s,2,"0");case"SSS":return S.s(t.$ms,3,"0");case"Z":return i}return null}(e)||i.replace(":","")}))},_.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},_.diff=function(r,d,p){var f,m=this,g=S.p(d),_=k(r),y=(_.utcOffset()-this.utcOffset())*t,v=this-_,b=function(){return S.m(m,_)};switch(g){case h:f=b()/12;break;case l:f=b();break;case u:f=b()/3;break;case c:f=(v-y)/6048e5;break;case a:f=(v-y)/864e5;break;case o:f=v/n;break;case s:f=v/t;break;case i:f=v/e;break;default:f=v}return p?f:S.a(f)},_.daysInMonth=function(){return this.endOf(l).$D},_.$locale=function(){return b[this.$L]},_.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),r=T(e,t,!0);return r&&(n.$L=r),n},_.clone=function(){return S.w(this.$d,this)},_.toDate=function(){return new Date(this.valueOf())},_.toJSON=function(){return this.isValid()?this.toISOString():null},_.toISOString=function(){return this.$d.toISOString()},_.toString=function(){return this.$d.toUTCString()},g}(),I=E.prototype;return k.prototype=I,[["$ms",r],["$s",i],["$m",s],["$H",o],["$W",a],["$M",l],["$y",h],["$D",d]].forEach((function(e){I[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),k.extend=function(e,t){return e.$i||(e(t,E,k),e.$i=!0),k},k.locale=T,k.isDayjs=C,k.unix=function(e){return k(1e3*e)},k.en=b[v],k.Ls=b,k.p={},k}()),n.exports)),i=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"==typeof e&&isFinite(e),o=e=>"[object RegExp]"===C(e),a=e=>T(e)&&0===Object.keys(e).length,c=Object.assign,l=Object.create,u=(e=null)=>l(e);let h;const d=()=>h||(h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:u());function p(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f=Object.prototype.hasOwnProperty;function m(e,t){return f.call(e,t)}const g=Array.isArray,_=e=>"function"==typeof e,y=e=>"string"==typeof e,v=e=>"boolean"==typeof e,b=e=>null!==e&&"object"==typeof e,w=Object.prototype.toString,C=e=>w.call(e),T=e=>"[object Object]"===C(e);function k(e,t){}const S=Object.assign,E=e=>"string"==typeof e;function I(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function x(e,t,n){return{start:e,end:t}}const R=1,A=2,P=3,O=4,N=5,D=6,L=7,M=8,F=9,j=10,U=11,B=12,$=13,q=14;function W(e,t,n={}){const{domain:r,messages:i,args:s}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function H(e){throw e}const z=" ",V="\n",K=String.fromCharCode(8232),G=String.fromCharCode(8233);function Y(e){const t=e;let n=0,r=1,i=1,s=0;const o=e=>"\r"===t[e]&&t[e+1]===V,a=e=>t[e]===G,c=e=>t[e]===K,l=e=>o(e)||(e=>t[e]===V)(e)||a(e)||c(e),u=e=>o(e)||a(e)||c(e)?V:t[e];function h(){return s=0,l(n)&&(r++,i=0),o(n)&&n++,n++,i++,t[n]}return{index:()=>n,line:()=>r,column:()=>i,peekOffset:()=>s,charAt:u,currentChar:()=>u(n),currentPeek:()=>u(n+s),next:h,peek:function(){return o(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,r=1,i=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)h();s=0}}}const J=void 0;function Q(e,t={}){const n=!1!==t.location,r=Y(e),i=()=>r.index(),s=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},o=s(),a=i(),c={currentType:13,offset:a,startLoc:o,endLoc:o,lastType:13,lastOffset:a,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},l=()=>c,{onError:u}=t;function h(e,t,r,...i){const s=l();if(t.column+=r,t.offset+=r,u){const r=W(e,n?x(s.startLoc,t):null,{domain:"tokenizer",args:i});u(r)}}function d(e,t,r){e.endLoc=s(),e.currentType=t;const i={type:t};return n&&(i.loc=x(e.startLoc,e.endLoc)),null!=r&&(i.value=r),i}const p=e=>d(e,13);function f(e,t){return e.currentChar()===t?(e.next(),t):(h(R,s(),0,t),"")}function m(e){let t="";for(;e.currentPeek()===z||e.currentPeek()===V;)t+=e.currentPeek(),e.peek();return t}function g(e){const t=m(e);return e.skipToPeek(),t}function _(e){if(e===J)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function y(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=function(e){if(e===J)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function v(e){m(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function b(e,t=!0){const n=(t=!1,r="")=>{const i=e.currentPeek();return"{"===i?t:"@"!==i&&i?"|"===i?!(r===z||r===V):i===z?(e.peek(),n(!0,z)):i!==V||(e.peek(),n(!0,V)):t},r=n();return t&&e.resetPeek(),r}function w(e,t){const n=e.currentChar();return n===J?J:t(n)?(e.next(),n):null}function C(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function T(e){return w(e,C)}function k(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function S(e){return w(e,k)}function E(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return w(e,E)}function U(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function B(e){return w(e,U)}function $(e){let t="",n="";for(;t=I(e);)n+=t;return n}function q(e){return"'"!==e&&e!==V}function H(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return K(e,t,4);case"U":return K(e,t,6);default:return h(O,s(),0,t),""}}function K(e,t,n){f(e,t);let r="";for(let i=0;i<n;i++){const n=B(e);if(!n){h(N,s(),0,`\\${t}${r}${e.currentChar()}`);break}r+=n}return`\\${t}${r}`}function G(e){return"{"!==e&&"}"!==e&&e!==z&&e!==V}function Q(e){g(e);const t=f(e,"|");return g(e),t}function X(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&h(F,s(),0),e.next(),n=d(t,2,"{"),g(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&h(M,s(),0),e.next(),n=d(t,3,"}"),t.braceNest--,t.braceNest>0&&g(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&h(L,s(),0),n=Z(e,t)||p(t),t.braceNest=0,n;default:{let r=!0,i=!0,o=!0;if(v(e))return t.braceNest>0&&h(L,s(),0),n=d(t,1,Q(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return h(L,s(),0),t.braceNest=0,ee(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t))return n=d(t,4,function(e){g(e);let t="",n="";for(;t=S(e);)n+=t;return e.currentChar()===J&&h(L,s(),0),n}(e)),g(e),n;if(i=y(e,t))return n=d(t,5,function(e){g(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${$(e)}`):t+=$(e),e.currentChar()===J&&h(L,s(),0),t}(e)),g(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;m(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=d(t,6,function(e){g(e),f(e,"'");let t="",n="";for(;t=w(e,q);)n+="\\"===t?H(e):t;const r=e.currentChar();return r===V||r===J?(h(P,s(),0),r===V&&(e.next(),f(e,"'")),n):(f(e,"'"),n)}(e)),g(e),n;if(!r&&!i&&!o)return n=d(t,12,function(e){g(e);let t="",n="";for(;t=w(e,G);)n+=t;return n}(e)),h(A,s(),0,n.value),g(e),n;break}}return n}function Z(e,t){const{currentType:n}=t;let r=null;const i=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||i!==V&&i!==z||h(j,s(),0),i){case"@":return e.next(),r=d(t,7,"@"),t.inLinked=!0,r;case".":return g(e),e.next(),d(t,8,".");case":":return g(e),e.next(),d(t,9,":");default:return v(e)?(r=d(t,1,Q(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(g(e),Z(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;m(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t)?(g(e),d(t,11,function(e){let t="",n="";for(;t=T(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?_(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===z||!t)&&(t===V?(e.peek(),r()):b(e,!1))},i=r();return e.resetPeek(),i}(e,t)?(g(e),"{"===i?X(e,t)||r:d(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===z?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&h(j,s(),0),t.braceNest=0,t.inLinked=!1,ee(e,t))}}function ee(e,t){let n={type:13};if(t.braceNest>0)return X(e,t)||p(t);if(t.inLinked)return Z(e,t)||p(t);switch(e.currentChar()){case"{":return X(e,t)||p(t);case"}":return h(D,s(),0),e.next(),d(t,3,"}");case"@":return Z(e,t)||p(t);default:if(v(e))return n=d(t,1,Q(e)),t.braceNest=0,t.inLinked=!1,n;if(b(e))return d(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===z||n===V)if(b(e))t+=n,e.next();else{if(v(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:o}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=o,c.offset=i(),c.startLoc=s(),r.currentChar()===J?d(c,13):ee(r,c)},currentOffset:i,currentPosition:s,context:l}}const X=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Z(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function ee(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,r,i,s,...o){const a=e.currentPosition();if(a.offset+=s,a.column+=s,n){const e=W(r,t?x(i,a):null,{domain:"parser",args:o});n(e)}}function i(e,n,r){const i={type:e};return t&&(i.start=n,i.end=n,i.loc={start:r,end:r}),i}function s(e,n,r,i){t&&(e.end=n,e.loc&&(e.loc.end=r))}function o(e,t){const n=e.context(),r=i(3,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:o}=n,a=i(5,r,o);return a.index=parseInt(t,10),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function c(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:o}=n,a=i(4,r,o);return a.key=t,e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function l(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:o}=n,a=i(9,r,o);return a.value=t.replace(X,Z),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function u(e){const t=e.context(),n=i(6,t.offset,t.startLoc);let o=e.nextToken();if(8===o.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:o,lastStartLoc:a}=n,c=i(8,o,a);return 11!==t.type?(r(e,B,n.lastStartLoc,0),c.value="",s(c,o,a),{nextConsumeToken:t,node:c}):(null==t.value&&r(e,q,n.lastStartLoc,0,te(t)),c.value=t.value||"",s(c,e.currentOffset(),e.currentPosition()),{node:c})}(e);n.modifier=t.node,o=t.nextConsumeToken||e.nextToken()}switch(9!==o.type&&r(e,q,t.lastStartLoc,0,te(o)),o=e.nextToken(),2===o.type&&(o=e.nextToken()),o.type){case 10:null==o.value&&r(e,q,t.lastStartLoc,0,te(o)),n.key=function(e,t){const n=e.context(),r=i(7,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}(e,o.value||"");break;case 4:null==o.value&&r(e,q,t.lastStartLoc,0,te(o)),n.key=c(e,o.value||"");break;case 5:null==o.value&&r(e,q,t.lastStartLoc,0,te(o)),n.key=a(e,o.value||"");break;case 6:null==o.value&&r(e,q,t.lastStartLoc,0,te(o)),n.key=l(e,o.value||"");break;default:{r(e,$,t.lastStartLoc,0);const a=e.context(),c=i(7,a.offset,a.startLoc);return c.value="",s(c,a.offset,a.startLoc),n.key=c,s(n,a.offset,a.startLoc),{nextConsumeToken:o,node:n}}}return s(n,e.currentOffset(),e.currentPosition()),{node:n}}function h(e){const t=e.context(),n=i(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let h=null;do{const i=h||e.nextToken();switch(h=null,i.type){case 0:null==i.value&&r(e,q,t.lastStartLoc,0,te(i)),n.items.push(o(e,i.value||""));break;case 5:null==i.value&&r(e,q,t.lastStartLoc,0,te(i)),n.items.push(a(e,i.value||""));break;case 4:null==i.value&&r(e,q,t.lastStartLoc,0,te(i)),n.items.push(c(e,i.value||""));break;case 6:null==i.value&&r(e,q,t.lastStartLoc,0,te(i)),n.items.push(l(e,i.value||""));break;case 7:{const t=u(e);n.items.push(t.node),h=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return s(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function d(e){const t=e.context(),{offset:n,startLoc:o}=t,a=h(e);return 13===t.currentType?a:function(e,t,n,o){const a=e.context();let c=0===o.items.length;const l=i(1,t,n);l.cases=[],l.cases.push(o);do{const t=h(e);c||(c=0===t.items.length),l.cases.push(t)}while(13!==a.currentType);return c&&r(e,U,n,0),s(l,e.currentOffset(),e.currentPosition()),l}(e,n,o,a)}return{parse:function(n){const o=Q(n,S({},e)),a=o.context(),c=i(0,a.offset,a.startLoc);return t&&c.loc&&(c.loc.source=n),c.body=d(o),e.onCacheKey&&(c.cacheKey=e.onCacheKey(n)),13!==a.currentType&&r(o,q,a.lastStartLoc,0,n[a.offset]||""),s(c,o.currentOffset(),o.currentPosition()),c}}}function te(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function ne(e,t){for(let n=0;n<e.length;n++)re(e[n],t)}function re(e,t){switch(e.type){case 1:ne(e.cases,t),t.helper("plural");break;case 2:ne(e.items,t);break;case 6:re(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function ie(e,t={}){const n=function(e){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&re(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function se(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=I(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function oe(e){switch(e.t=e.type,e.type){case 0:{const t=e;oe(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)oe(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)oe(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;oe(t.key),t.k=t.key,delete t.key,t.modifier&&(oe(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function ae(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?ae(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const i=t.cases.length;for(let n=0;n<i&&(ae(e,t.cases[n]),n!==i-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const i=t.items.length;for(let s=0;s<i&&(ae(e,t.items[s]),s!==i-1);s++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),ae(e,t.key),t.modifier?(e.push(", "),ae(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function ce(e,t={}){const n=S({},t),r=!!n.jit,i=!!n.minify,s=null==n.optimize||n.optimize,o=ee(n).parse(e);return r?(s&&function(e){const t=e.body;2===t.type?se(t):t.cases.forEach((e=>se(e)))}(o),i&&oe(o),{ast:o,code:""}):(ie(o,n),((e,t={})=>{const n=E(t.mode)?t.mode:"normal",r=E(t.filename)?t.filename:"message.intl";t.sourceMap;const i=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",s=t.needIndent?t.needIndent:"arrow"!==n,o=e.helpers||[],a=function(e,t){const{filename:n,breakLineCode:r,needIndent:i}=t,s=!1!==t.location,o={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:i,indentLevel:0};function a(e,t){o.code+=e}function c(e,t=!0){const n=t?r:"";a(i?n+"  ".repeat(e):n)}return s&&e.loc&&(o.source=e.loc.source),{context:()=>o,push:a,indent:function(e=!0){const t=++o.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--o.indentLevel;e&&c(t)},newline:function(){c(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{filename:r,breakLineCode:i,needIndent:s});a.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(s),o.length>0&&(a.push(`const { ${I(o.map((e=>`${e}: _${e}`)),", ")} } = ctx`),a.newline()),a.push("return "),ae(a,e),a.deindent(s),a.push("}"),delete e.helpers;const{code:c,map:l}=a.context();return{ast:e,code:c,map:l?l.toJSON():void 0}})(o,n))}function le(e){return t=>function(e,t){const n=(r=t,Ce(r,ue));var r;if(null==n)throw Te(0);if(1===_e(n)){const t=function(e){return Ce(e,he,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,de(e,n)]),[]))}return de(e,n)}(t,e)}const ue=["b","body"];const he=["c","cases"];function de(e,t){const n=function(e){return Ce(e,pe)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return Ce(e,fe,[])}(t).reduce(((t,n)=>[...t,me(e,n)]),[]);return e.normalize(n)}}const pe=["s","static"];const fe=["i","items"];function me(e,t){const n=_e(t);switch(n){case 3:case 9:case 7:case 8:return ve(t,n);case 4:{const r=t;if(m(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(m(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw Te(n)}case 5:{const r=t;if(m(r,"i")&&s(r.i))return e.interpolate(e.list(r.i));if(m(r,"index")&&s(r.index))return e.interpolate(e.list(r.index));throw Te(n)}case 6:{const n=t,r=function(e){return Ce(e,be)}(n),i=function(e){const t=Ce(e,we);if(t)return t;throw Te(6)}(n);return e.linked(me(e,i),r?me(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const ge=["t","type"];function _e(e){return Ce(e,ge)}const ye=["v","value"];function ve(e,t){const n=Ce(e,ye);if(n)return n;throw Te(t)}const be=["m","modifier"];const we=["k","key"];function Ce(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(m(e,n)&&null!=e[n])return e[n]}return n}function Te(e){return new Error(`unhandled node type: ${e}`)}const ke=e=>e;let Se=u();function Ee(e){return b(e)&&0===_e(e)&&(m(e,"b")||m(e,"body"))}function Ie(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&y(e)){!v(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||ke)(e),r=Se[n];if(r)return r;const{ast:i,detectError:s}=function(e,t={}){let n=!1;const r=t.onError||H;return t.onError=e=>{n=!0,r(e)},{...ce(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),o=le(i);return s?o:Se[n]=o}{const t=e.cacheKey;if(t){const n=Se[t];return n||(Se[t]=le(e))}return le(e)}}let xe=null;function Re(e){xe=e}const Ae=Pe("function:translate");function Pe(e){return t=>xe&&xe.emit(e,t)}const Oe=17,Ne=18,De=19,Le=21,Me=22,Fe=23,je=24;function Ue(e){return W(e,null,void 0)}function Be(e,t){return null!=t.locale?qe(t.locale):qe(e.locale)}let $e;function qe(e){if(y(e))return e;if(_(e)){if(e.resolvedOnce&&null!=$e)return $e;if("Function"===e.constructor.name){const n=e();if(b(t=n)&&_(t.then)&&_(t.catch))throw Ue(Le);return $e=n}throw Ue(Me)}throw Ue(Fe);var t}function We(e,t,n){return[...new Set([n,...g(t)?t:b(t)?Object.keys(t):y(t)?[t]:[n]])]}function He(e,t,n){const r=y(n)?n:nt,i=e;i.__localeChainCache||(i.__localeChainCache=new Map);let s=i.__localeChainCache.get(r);if(!s){s=[];let e=[n];for(;g(e);)e=ze(s,e,t);const o=g(t)||!T(t)?t:t.default?t.default:null;e=y(o)?[o]:o,g(e)&&ze(s,e,!1),i.__localeChainCache.set(r,s)}return s}function ze(e,t,n){let r=!0;for(let i=0;i<t.length&&v(r);i++){const s=t[i];y(s)&&(r=Ve(e,t[i],n))}return r}function Ve(e,t,n){let r;const i=t.split("-");do{r=Ke(e,i.join("-"),n),i.splice(-1,1)}while(i.length&&!0===r);return r}function Ke(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const i=t.replace(/!/g,"");e.push(i),(g(n)||T(n))&&n[i]&&(r=n[i])}return r}const Ge=[];Ge[0]={w:[0],i:[3,0],"[":[4],o:[7]},Ge[1]={w:[1],".":[2],"[":[4],o:[7]},Ge[2]={w:[2],i:[3,0],0:[3,0]},Ge[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Ge[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Ge[5]={"'":[4,0],o:8,l:[5,0]},Ge[6]={'"':[4,0],o:8,l:[6,0]};const Ye=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Je(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function Qe(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Ye.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const Xe=new Map;function Ze(e,t){return b(e)?e[t]:null}function et(e,t){if(!b(e))return null;let n=Xe.get(t);if(n||(n=function(e){const t=[];let n,r,i,s,o,a,c,l=-1,u=0,h=0;const d=[];function p(){const t=e[l+1];if(5===u&&"'"===t||6===u&&'"'===t)return l++,i="\\"+t,d[0](),!0}for(d[0]=()=>{void 0===r?r=i:r+=i},d[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},d[2]=()=>{d[0](),h++},d[3]=()=>{if(h>0)h--,u=4,d[0]();else{if(h=0,void 0===r)return!1;if(r=Qe(r),!1===r)return!1;d[1]()}};null!==u;)if(l++,n=e[l],"\\"!==n||!p()){if(s=Je(n),c=Ge[u],o=c[s]||c.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(a=d[o[1]],a&&(i=n,!1===a())))return;if(7===u)return t}}(t),n&&Xe.set(t,n)),!n)return null;const r=n.length;let i=e,s=0;for(;s<r;){const e=i[n[s]];if(void 0===e)return null;if(_(i))return null;i=e,s++}return i}const tt=-1,nt="en-US",rt="",it=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let st,ot,at;function ct(e){st=e}function lt(e){ot=e}function ut(e){at=e}let ht=null;const dt=e=>{ht=e},pt=()=>ht;let ft=null;const mt=e=>{ft=e},gt=()=>ft;let _t=0;function yt(e={}){const t=_(e.onWarn)?e.onWarn:k,n=y(e.version)?e.version:"11.1.2",r=y(e.locale)||_(e.locale)?e.locale:nt,i=_(r)?nt:r,s=g(e.fallbackLocale)||T(e.fallbackLocale)||y(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i,a=T(e.messages)?e.messages:vt(i),l=T(e.datetimeFormats)?e.datetimeFormats:vt(i),h=T(e.numberFormats)?e.numberFormats:vt(i),d=c(u(),e.modifiers,{upper:(e,t)=>"text"===t&&y(e)?e.toUpperCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&y(e)?e.toLowerCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&y(e)?it(e):"vnode"===t&&b(e)&&"__v_isVNode"in e?it(e.children):e}),p=e.pluralRules||u(),f=_(e.missing)?e.missing:null,m=!v(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,w=!v(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,C=!!e.fallbackFormat,S=!!e.unresolving,E=_(e.postTranslation)?e.postTranslation:null,I=T(e.processor)?e.processor:null,x=!v(e.warnHtmlMessage)||e.warnHtmlMessage,R=!!e.escapeParameter,A=_(e.messageCompiler)?e.messageCompiler:st,P=_(e.messageResolver)?e.messageResolver:ot||Ze,O=_(e.localeFallbacker)?e.localeFallbacker:at||We,N=b(e.fallbackContext)?e.fallbackContext:void 0,D=e,L=b(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,M=b(D.__numberFormatters)?D.__numberFormatters:new Map,F=b(D.__meta)?D.__meta:{};_t++;const j={version:n,cid:_t,locale:r,fallbackLocale:s,messages:a,modifiers:d,pluralRules:p,missing:f,missingWarn:m,fallbackWarn:w,fallbackFormat:C,unresolving:S,postTranslation:E,processor:I,warnHtmlMessage:x,escapeParameter:R,messageCompiler:A,messageResolver:P,localeFallbacker:O,fallbackContext:N,onWarn:t,__meta:F};return j.datetimeFormats=l,j.numberFormats=h,j.__datetimeFormatters=L,j.__numberFormatters=M,__INTLIFY_PROD_DEVTOOLS__&&function(e,t,n){xe&&xe.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}(j,n,F),j}const vt=e=>({[e]:u()});function bt(e,t,n,r,i){const{missing:s,onWarn:o}=e;if(null!==s){const r=s(e,n,t,i);return y(r)?r:t}return t}function wt(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Ct(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let s=n+1;s<t.length;s++)if(r=e,i=t[s],r!==i&&r.split("-")[0]===i.split("-")[0])return!0;var r,i;return!1}function Tt(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:i,onWarn:s,localeFallbacker:o}=e,{__datetimeFormatters:l}=e,[u,h,d,p]=St(...t);v(d.missingWarn)?d.missingWarn:e.missingWarn;v(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const f=!!d.part,m=Be(e,d),g=o(e,i,m);if(!y(u)||""===u)return new Intl.DateTimeFormat(m,p).format(h);let _,b={},w=null;for(let a=0;a<g.length&&(_=g[a],b=n[_]||{},w=b[u],!T(w));a++)bt(e,u,_,0,"datetime format");if(!T(w)||!y(_))return r?-1:u;let C=`${_}__${u}`;a(p)||(C=`${C}__${JSON.stringify(p)}`);let k=l.get(C);return k||(k=new Intl.DateTimeFormat(_,c({},w,p)),l.set(C,k)),f?k.formatToParts(h):k.format(h)}const kt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function St(...e){const[t,n,r,i]=e,o=u();let a,c=u();if(y(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Ue(De);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();a=new Date(n);try{a.toISOString()}catch{throw Ue(De)}}else if("[object Date]"===C(t)){if(isNaN(t.getTime()))throw Ue(Ne);a=t}else{if(!s(t))throw Ue(Oe);a=t}return y(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{kt.includes(e)?c[e]=n[e]:o[e]=n[e]})),y(r)?o.locale=r:T(r)&&(c=r),T(i)&&(c=i),[o.key||"",a,o,c]}function Et(e,t,n){const r=e;for(const i in n){const e=`${t}__${i}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function It(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:i,onWarn:s,localeFallbacker:o}=e,{__numberFormatters:l}=e,[u,h,d,p]=Rt(...t);v(d.missingWarn)?d.missingWarn:e.missingWarn;v(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn;const f=!!d.part,m=Be(e,d),g=o(e,i,m);if(!y(u)||""===u)return new Intl.NumberFormat(m,p).format(h);let _,b={},w=null;for(let a=0;a<g.length&&(_=g[a],b=n[_]||{},w=b[u],!T(w));a++)bt(e,u,_,0,"number format");if(!T(w)||!y(_))return r?-1:u;let C=`${_}__${u}`;a(p)||(C=`${C}__${JSON.stringify(p)}`);let k=l.get(C);return k||(k=new Intl.NumberFormat(_,c({},w,p)),l.set(C,k)),f?k.formatToParts(h):k.format(h)}const xt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Rt(...e){const[t,n,r,i]=e,o=u();let a=u();if(!s(t))throw Ue(Oe);const c=t;return y(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{xt.includes(e)?a[e]=n[e]:o[e]=n[e]})),y(r)?o.locale=r:T(r)&&(a=r),T(i)&&(a=i),[o.key||"",c,o,a]}function At(e,t,n){const r=e;for(const i in n){const e=`${t}__${i}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const Pt=e=>e,Ot=e=>"",Nt=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}(e),Dt=e=>null==e?"":g(e)||T(e)&&e.toString===w?JSON.stringify(e,null,2):String(e);function Lt(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Mt(e={}){const t=e.locale,n=function(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}(e),r=b(e.pluralRules)&&y(t)&&_(e.pluralRules[t])?e.pluralRules[t]:Lt,i=b(e.pluralRules)&&y(t)&&_(e.pluralRules[t])?Lt:void 0,o=e.list||[],a=e.named||u();s(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function l(t,n){const r=_(e.messages)?e.messages(t,!!n):!!b(e.messages)&&e.messages[t];return r||(e.parent?e.parent.message(t):Ot)}const h=T(e.processor)&&_(e.processor.normalize)?e.processor.normalize:Nt,d=T(e.processor)&&_(e.processor.interpolate)?e.processor.interpolate:Dt,p={list:e=>o[e],named:e=>a[e],plural:e=>e[r(n,e.length,i)],linked:(t,...n)=>{const[r,i]=n;let s="text",o="";1===n.length?b(r)?(o=r.modifier||o,s=r.type||s):y(r)&&(o=r||o):2===n.length&&(y(r)&&(o=r||o),y(i)&&(s=i||s));const a=l(t,!0)(p),c="vnode"===s&&g(a)&&o?a[0]:a;return o?(u=o,e.modifiers?e.modifiers[u]:Pt)(c,s):c;var u},message:l,type:T(e.processor)&&y(e.processor.type)?e.processor.type:"text",interpolate:d,normalize:h,values:c(u(),o,a)};return p}const Ft=()=>"",jt=e=>_(e);function Ut(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:i,messageCompiler:o,fallbackLocale:a,messages:l}=e,[h,d]=qt(...t),f=v(d.missingWarn)?d.missingWarn:e.missingWarn,m=v(d.fallbackWarn)?d.fallbackWarn:e.fallbackWarn,w=v(d.escapeParameter)?d.escapeParameter:e.escapeParameter,C=!!d.resolvedMessage,T=y(d.default)||v(d.default)?v(d.default)?o?h:()=>h:d.default:n?o?h:()=>h:null,k=n||null!=T&&(y(T)||_(T)),S=Be(e,d);w&&function(e){g(e.list)?e.list=e.list.map((e=>y(e)?p(e):e)):b(e.named)&&Object.keys(e.named).forEach((t=>{y(e.named[t])&&(e.named[t]=p(e.named[t]))}))}(d);let[E,I,x]=C?[h,S,l[S]||u()]:Bt(e,h,S,a,m,f),R=E,A=h;if(C||y(R)||Ee(R)||jt(R)||k&&(R=T,A=R),!(C||(y(R)||Ee(R)||jt(R))&&y(I)))return i?-1:h;let P=!1;const O=jt(R)?R:$t(e,h,I,R,A,(()=>{P=!0}));if(P)return R;const N=function(e,t,n,r){const{modifiers:i,pluralRules:o,messageResolver:a,fallbackLocale:c,fallbackWarn:l,missingWarn:u,fallbackContext:h}=e,d=(r,i)=>{let s=a(n,r);if(null==s&&(h||i)){const[,,n]=Bt(h||e,r,t,c,l,u);s=a(n,r)}if(y(s)||Ee(s)){let n=!1;const i=$t(e,r,t,s,r,(()=>{n=!0}));return n?Ft:i}return jt(s)?s:Ft},p={locale:t,modifiers:i,pluralRules:o,messages:d};e.processor&&(p.processor=e.processor);r.list&&(p.list=r.list);r.named&&(p.named=r.named);s(r.plural)&&(p.pluralIndex=r.plural);return p}(e,I,x,d),D=function(e,t,n){const r=t(n);return r}(0,O,Mt(N)),L=r?r(D,h):D;if(__INTLIFY_PROD_DEVTOOLS__){const t={timestamp:Date.now(),key:y(h)?h:jt(R)?R.key:"",locale:I||(jt(R)?R.locale:""),format:y(R)?R:jt(R)?R.source:"",message:L};t.meta=c({},e.__meta,pt()||{}),Ae(t)}return L}function Bt(e,t,n,r,i,s){const{messages:o,onWarn:a,messageResolver:c,localeFallbacker:l}=e,h=l(e,r,n);let d,p=u(),f=null;for(let m=0;m<h.length&&(d=h[m],p=o[d]||u(),null===(f=c(p,t))&&(f=p[t]),!(y(f)||Ee(f)||jt(f)));m++)if(!Ct(d,h)){const n=bt(e,t,d,0,"translate");n!==t&&(f=n)}return[f,d,p]}function $t(e,t,n,r,s,o){const{messageCompiler:a,warnHtmlMessage:c}=e;if(jt(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==a){const e=()=>r;return e.locale=n,e.key=t,e}const l=a(r,function(e,t,n,r,s,o){return{locale:t,key:n,warnHtmlMessage:s,onError:e=>{throw o&&o(e),e},onCacheKey:e=>((e,t,n)=>i({l:e,k:t,s:n}))(t,n,e)}}(0,n,s,0,c,o));return l.locale=n,l.key=t,l.source=r,l}function qt(...e){const[t,n,r]=e,i=u();if(!(y(t)||s(t)||jt(t)||Ee(t)))throw Ue(Oe);const o=s(t)?String(t):(jt(t),t);return s(n)?i.plural=n:y(n)?i.default=n:T(n)&&!a(n)?i.named=n:g(n)&&(i.list=n),s(r)?i.plural=r:y(r)?i.default=r:T(r)&&c(i,r),[o,i]}"boolean"!=typeof __INTLIFY_PROD_DEVTOOLS__&&(d().__INTLIFY_PROD_DEVTOOLS__=!1),"boolean"!=typeof __INTLIFY_DROP_MESSAGE_COMPILER__&&(d().__INTLIFY_DROP_MESSAGE_COMPILER__=!1);var Wt={};const Ht="${JSCORE_VERSION}",zt=function(e,t){if(!e)throw Vt(t)},Vt=function(e){return new Error("Firebase Database ("+Ht+") INTERNAL ASSERT FAILED: "+e)},Kt=function(e){const t=[];let n=0;for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);i<128?t[n++]=i:i<2048?(t[n++]=i>>6|192,t[n++]=63&i|128):55296==(64512&i)&&r+1<e.length&&56320==(64512&e.charCodeAt(r+1))?(i=65536+((1023&i)<<10)+(1023&e.charCodeAt(++r)),t[n++]=i>>18|240,t[n++]=i>>12&63|128,t[n++]=i>>6&63|128,t[n++]=63&i|128):(t[n++]=i>>12|224,t[n++]=i>>6&63|128,t[n++]=63&i|128)}return t},Gt={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let i=0;i<e.length;i+=3){const t=e[i],s=i+1<e.length,o=s?e[i+1]:0,a=i+2<e.length,c=a?e[i+2]:0,l=t>>2,u=(3&t)<<4|o>>4;let h=(15&o)<<2|c>>6,d=63&c;a||(d=64,s||(h=64)),r.push(n[l],n[u],n[h],n[d])}return r.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(Kt(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let n=0,r=0;for(;n<e.length;){const i=e[n++];if(i<128)t[r++]=String.fromCharCode(i);else if(i>191&&i<224){const s=e[n++];t[r++]=String.fromCharCode((31&i)<<6|63&s)}else if(i>239&&i<365){const s=((7&i)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536;t[r++]=String.fromCharCode(55296+(s>>10)),t[r++]=String.fromCharCode(56320+(1023&s))}else{const s=e[n++],o=e[n++];t[r++]=String.fromCharCode((15&i)<<12|(63&s)<<6|63&o)}}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();const n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let i=0;i<e.length;){const t=n[e.charAt(i++)],s=i<e.length?n[e.charAt(i)]:0;++i;const o=i<e.length?n[e.charAt(i)]:64;++i;const a=i<e.length?n[e.charAt(i)]:64;if(++i,null==t||null==s||null==o||null==a)throw new Yt;const c=t<<2|s>>4;if(r.push(c),64!==o){const e=s<<4&240|o>>2;if(r.push(e),64!==a){const e=o<<6&192|a;r.push(e)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class Yt extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const Jt=function(e){const t=Kt(e);return Gt.encodeByteArray(t,!0)},Qt=function(e){return Jt(e).replace(/\./g,"")},Xt=function(e){try{return Gt.decodeString(e,!0)}catch(t){}return null};function Zt(e){return en(void 0,e)}function en(e,t){if(!(t instanceof Object))return t;switch(t.constructor){case Date:return new Date(t.getTime());case Object:void 0===e&&(e={});break;case Array:e=[];break;default:return t}for(const n in t)t.hasOwnProperty(n)&&"__proto__"!==n&&(e[n]=en(e[n],t[n]));return e}const tn=()=>function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}().__FIREBASE_DEFAULTS__,nn=()=>{try{return tn()||(()=>{if("undefined"==typeof process)return;const e=Wt.__FIREBASE_DEFAULTS__;return e?JSON.parse(e):void 0})()||(()=>{if("undefined"==typeof document)return;let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(n){return}const t=e&&Xt(e[1]);return t&&JSON.parse(t)})()}catch(e){return}},rn=e=>{const t=(e=>{var t,n;return null===(n=null===(t=nn())||void 0===t?void 0:t.emulatorHosts)||void 0===n?void 0:n[e]})(e);if(!t)return;const n=t.lastIndexOf(":");if(n<=0||n+1===t.length)throw new Error(`Invalid host ${t} with no separate hostname and port!`);const r=parseInt(t.substring(n+1),10);return"["===t[0]?[t.substring(1,n-1),r]:[t.substring(0,n),r]},sn=()=>{var e;return null===(e=nn())||void 0===e?void 0:e.config};class on{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise(((e,t)=>{this.resolve=e,this.reject=t}))}wrapCallback(e){return(t,n)=>{t?this.reject(t):this.resolve(n),"function"==typeof e&&(this.promise.catch((()=>{})),1===e.length?e(t):e(t,n))}}}function an(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test("undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:"")}function cn(){try{return"object"==typeof indexedDB}catch(e){return!1}}function ln(){return new Promise(((e,t)=>{try{let n=!0;const r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=()=>{i.result.close(),n||self.indexedDB.deleteDatabase(r),e(!0)},i.onupgradeneeded=()=>{n=!1},i.onerror=()=>{var e;t((null===(e=i.error)||void 0===e?void 0:e.message)||"")}}catch(n){t(n)}}))}function un(){return!("undefined"==typeof navigator||!navigator.cookieEnabled)}class hn extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,hn.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,dn.prototype.create)}}class dn{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){const n=t[0]||{},r=`${this.service}/${e}`,i=this.errors[e],s=i?function(e,t){return e.replace(pn,((e,n)=>{const r=t[n];return null!=r?String(r):`<${n}?>`}))}(i,n):"Error",o=`${this.serviceName}: ${s} (${r}).`;return new hn(r,o,n)}}const pn=/\{\$([^}]+)}/g;function fn(e){return JSON.parse(e)}function mn(e){return JSON.stringify(e)}const gn=function(e){let t={},n={},r={},i="";try{const s=e.split(".");t=fn(Xt(s[0])||""),n=fn(Xt(s[1])||""),i=s[2],r=n.d||{},delete n.d}catch(s){}return{header:t,claims:n,data:r,signature:i}};function _n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function yn(e,t){return Object.prototype.hasOwnProperty.call(e,t)?e[t]:void 0}function vn(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}function bn(e,t,n){const r={};for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=t.call(n,e[i],i,e));return r}function wn(e,t){if(e===t)return!0;const n=Object.keys(e),r=Object.keys(t);for(const i of n){if(!r.includes(i))return!1;const n=e[i],s=t[i];if(Cn(n)&&Cn(s)){if(!wn(n,s))return!1}else if(n!==s)return!1}for(const i of r)if(!n.includes(i))return!1;return!0}function Cn(e){return null!==e&&"object"==typeof e}class Tn{constructor(){this.chain_=[],this.buf_=[],this.W_=[],this.pad_=[],this.inbuf_=0,this.total_=0,this.blockSize=64,this.pad_[0]=128;for(let e=1;e<this.blockSize;++e)this.pad_[e]=0;this.reset()}reset(){this.chain_[0]=1732584193,this.chain_[1]=4023233417,this.chain_[2]=2562383102,this.chain_[3]=271733878,this.chain_[4]=3285377520,this.inbuf_=0,this.total_=0}compress_(e,t){t||(t=0);const n=this.W_;if("string"==typeof e)for(let u=0;u<16;u++)n[u]=e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|e.charCodeAt(t+3),t+=4;else for(let u=0;u<16;u++)n[u]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],t+=4;for(let u=16;u<80;u++){const e=n[u-3]^n[u-8]^n[u-14]^n[u-16];n[u]=4294967295&(e<<1|e>>>31)}let r,i,s=this.chain_[0],o=this.chain_[1],a=this.chain_[2],c=this.chain_[3],l=this.chain_[4];for(let u=0;u<80;u++){u<40?u<20?(r=c^o&(a^c),i=1518500249):(r=o^a^c,i=1859775393):u<60?(r=o&a|c&(o|a),i=2400959708):(r=o^a^c,i=3395469782);const e=(s<<5|s>>>27)+r+l+i+n[u]&4294967295;l=c,c=a,a=4294967295&(o<<30|o>>>2),o=s,s=e}this.chain_[0]=this.chain_[0]+s&4294967295,this.chain_[1]=this.chain_[1]+o&4294967295,this.chain_[2]=this.chain_[2]+a&4294967295,this.chain_[3]=this.chain_[3]+c&4294967295,this.chain_[4]=this.chain_[4]+l&4294967295}update(e,t){if(null==e)return;void 0===t&&(t=e.length);const n=t-this.blockSize;let r=0;const i=this.buf_;let s=this.inbuf_;for(;r<t;){if(0===s)for(;r<=n;)this.compress_(e,r),r+=this.blockSize;if("string"==typeof e){for(;r<t;)if(i[s]=e.charCodeAt(r),++s,++r,s===this.blockSize){this.compress_(i),s=0;break}}else for(;r<t;)if(i[s]=e[r],++s,++r,s===this.blockSize){this.compress_(i),s=0;break}}this.inbuf_=s,this.total_+=t}digest(){const e=[];let t=8*this.total_;this.inbuf_<56?this.update(this.pad_,56-this.inbuf_):this.update(this.pad_,this.blockSize-(this.inbuf_-56));for(let r=this.blockSize-1;r>=56;r--)this.buf_[r]=255&t,t/=256;this.compress_(this.buf_);let n=0;for(let r=0;r<5;r++)for(let t=24;t>=0;t-=8)e[n]=this.chain_[r]>>t&255,++n;return e}}const kn=function(e){let t=0;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r<128?t++:r<2048?t+=2:r>=55296&&r<=56319?(t+=4,n++):t+=3}return t};function Sn(e,t=1e3,n=2){const r=t*Math.pow(n,e),i=Math.round(.5*r*(Math.random()-.5)*2);return Math.min(144e5,r+i)}function En(e){return e&&e._delegate?e._delegate:e}class In{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const xn="[DEFAULT]";class Rn{constructor(e,t){this.name=e,this.container=t,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){const t=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(t)){const e=new on;if(this.instancesDeferred.set(t,e),this.isInitialized(t)||this.shouldAutoInitialize())try{const n=this.getOrInitializeService({instanceIdentifier:t});n&&e.resolve(n)}catch(n){}}return this.instancesDeferred.get(t).promise}getImmediate(e){var t;const n=this.normalizeInstanceIdentifier(null==e?void 0:e.identifier),r=null!==(t=null==e?void 0:e.optional)&&void 0!==t&&t;if(!this.isInitialized(n)&&!this.shouldAutoInitialize()){if(r)return null;throw Error(`Service ${this.name} is not available`)}try{return this.getOrInitializeService({instanceIdentifier:n})}catch(i){if(r)return null;throw i}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if(function(e){return"EAGER"===e.instantiationMode}(e))try{this.getOrInitializeService({instanceIdentifier:xn})}catch(t){}for(const[e,n]of this.instancesDeferred.entries()){const r=this.normalizeInstanceIdentifier(e);try{const e=this.getOrInitializeService({instanceIdentifier:r});n.resolve(e)}catch(t){}}}}clearInstance(e=xn){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){const e=Array.from(this.instances.values());await Promise.all([...e.filter((e=>"INTERNAL"in e)).map((e=>e.INTERNAL.delete())),...e.filter((e=>"_delete"in e)).map((e=>e._delete()))])}isComponentSet(){return null!=this.component}isInitialized(e=xn){return this.instances.has(e)}getOptions(e=xn){return this.instancesOptions.get(e)||{}}initialize(e={}){const{options:t={}}=e,n=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(n))throw Error(`${this.name}(${n}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);const r=this.getOrInitializeService({instanceIdentifier:n,options:t});for(const[i,s]of this.instancesDeferred.entries()){n===this.normalizeInstanceIdentifier(i)&&s.resolve(r)}return r}onInit(e,t){var n;const r=this.normalizeInstanceIdentifier(t),i=null!==(n=this.onInitCallbacks.get(r))&&void 0!==n?n:new Set;i.add(e),this.onInitCallbacks.set(r,i);const s=this.instances.get(r);return s&&e(s,r),()=>{i.delete(e)}}invokeOnInitCallbacks(e,t){const n=this.onInitCallbacks.get(t);if(n)for(const i of n)try{i(e,t)}catch(r){}}getOrInitializeService({instanceIdentifier:e,options:t={}}){let n=this.instances.get(e);if(!n&&this.component&&(n=this.component.instanceFactory(this.container,{instanceIdentifier:(r=e,r===xn?void 0:r),options:t}),this.instances.set(e,n),this.instancesOptions.set(e,t),this.invokeOnInitCallbacks(n,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,n)}catch(i){}var r;return n||null}normalizeInstanceIdentifier(e=xn){return this.component?this.component.multipleInstances?e:xn:e}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class An{constructor(e){this.name=e,this.providers=new Map}addComponent(e){const t=this.getProvider(e.name);if(t.isComponentSet())throw new Error(`Component ${e.name} has already been registered with ${this.name}`);t.setComponent(e)}addOrOverwriteComponent(e){this.getProvider(e.name).isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);const t=new Rn(e,this);return this.providers.set(e,t),t}getProviders(){return Array.from(this.providers.values())}}var Pn,On;(On=Pn||(Pn={}))[On.DEBUG=0]="DEBUG",On[On.VERBOSE=1]="VERBOSE",On[On.INFO=2]="INFO",On[On.WARN=3]="WARN",On[On.ERROR=4]="ERROR",On[On.SILENT=5]="SILENT";const Nn={debug:Pn.DEBUG,verbose:Pn.VERBOSE,info:Pn.INFO,warn:Pn.WARN,error:Pn.ERROR,silent:Pn.SILENT},Dn=Pn.INFO,Ln={[Pn.DEBUG]:"log",[Pn.VERBOSE]:"log",[Pn.INFO]:"info",[Pn.WARN]:"warn",[Pn.ERROR]:"error"},Mn=(e,t,...n)=>{if(t<e.logLevel)return;(new Date).toISOString();if(!Ln[t])throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`)};class Fn{constructor(e){this.name=e,this._logLevel=Dn,this._logHandler=Mn,this._userLogHandler=null}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in Pn))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?Nn[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,Pn.DEBUG,...e),this._logHandler(this,Pn.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,Pn.VERBOSE,...e),this._logHandler(this,Pn.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,Pn.INFO,...e),this._logHandler(this,Pn.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,Pn.WARN,...e),this._logHandler(this,Pn.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,Pn.ERROR,...e),this._logHandler(this,Pn.ERROR,...e)}}let jn,Un;const Bn=new WeakMap,$n=new WeakMap,qn=new WeakMap,Wn=new WeakMap,Hn=new WeakMap;let zn={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return $n.get(e);if("objectStoreNames"===t)return e.objectStoreNames||qn.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return Gn(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function Vn(e){return e!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(Un||(Un=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(Yn(this),t),Gn(Bn.get(this))}:function(...t){return Gn(e.apply(Yn(this),t))}:function(t,...n){const r=e.call(Yn(this),t,...n);return qn.set(r,t.sort?t.sort():[t]),Gn(r)}}function Kn(e){return"function"==typeof e?Vn(e):(e instanceof IDBTransaction&&function(e){if($n.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",i),e.removeEventListener("error",s),e.removeEventListener("abort",s)},i=()=>{t(),r()},s=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",i),e.addEventListener("error",s),e.addEventListener("abort",s)}));$n.set(e,t)}(e),t=e,(jn||(jn=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((e=>t instanceof e))?new Proxy(e,zn):e);var t}function Gn(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",i),e.removeEventListener("error",s)},i=()=>{t(Gn(e.result)),r()},s=()=>{n(e.error),r()};e.addEventListener("success",i),e.addEventListener("error",s)}));return t.then((t=>{t instanceof IDBCursor&&Bn.set(t,e)})).catch((()=>{})),Hn.set(t,e),t}(e);if(Wn.has(e))return Wn.get(e);const t=Kn(e);return t!==e&&(Wn.set(e,t),Hn.set(t,e)),t}const Yn=e=>Hn.get(e);function Jn(e,t,{blocked:n,upgrade:r,blocking:i,terminated:s}={}){const o=indexedDB.open(e,t),a=Gn(o);return r&&o.addEventListener("upgradeneeded",(e=>{r(Gn(o.result),e.oldVersion,e.newVersion,Gn(o.transaction),e)})),n&&o.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),a.then((e=>{s&&e.addEventListener("close",(()=>s())),i&&e.addEventListener("versionchange",(e=>i(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),a}const Qn=["get","getKey","getAll","getAllKeys","count"],Xn=["put","add","delete","clear"],Zn=new Map;function er(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(Zn.get(t))return Zn.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,i=Xn.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!i&&!Qn.includes(n))return;const s=async function(e,...t){const s=this.transaction(e,i?"readwrite":"readonly");let o=s.store;return r&&(o=o.index(t.shift())),(await Promise.all([o[n](...t),i&&s.done]))[0]};return Zn.set(t,s),s}zn=(e=>({...e,get:(t,n,r)=>er(t,n)||e.get(t,n,r),has:(t,n)=>!!er(t,n)||e.has(t,n)}))(zn);class tr{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map((e=>{if(function(e){const t=e.getComponent();return"VERSION"===(null==t?void 0:t.type)}(e)){const t=e.getImmediate();return`${t.library}/${t.version}`}return null})).filter((e=>e)).join(" ")}}const nr="@firebase/app",rr="0.11.2",ir=new Fn("@firebase/app"),sr="@firebase/app-compat",or="@firebase/analytics-compat",ar="@firebase/analytics",cr="@firebase/app-check-compat",lr="@firebase/app-check",ur="@firebase/auth",hr="@firebase/auth-compat",dr="@firebase/database",pr="@firebase/data-connect",fr="@firebase/database-compat",mr="@firebase/functions",gr="@firebase/functions-compat",_r="@firebase/installations",yr="@firebase/installations-compat",vr="@firebase/messaging",br="@firebase/messaging-compat",wr="@firebase/performance",Cr="@firebase/performance-compat",Tr="@firebase/remote-config",kr="@firebase/remote-config-compat",Sr="@firebase/storage",Er="@firebase/storage-compat",Ir="@firebase/firestore",xr="@firebase/vertexai",Rr="@firebase/firestore-compat",Ar="firebase",Pr="[DEFAULT]",Or={[nr]:"fire-core",[sr]:"fire-core-compat",[ar]:"fire-analytics",[or]:"fire-analytics-compat",[lr]:"fire-app-check",[cr]:"fire-app-check-compat",[ur]:"fire-auth",[hr]:"fire-auth-compat",[dr]:"fire-rtdb",[pr]:"fire-data-connect",[fr]:"fire-rtdb-compat",[mr]:"fire-fn",[gr]:"fire-fn-compat",[_r]:"fire-iid",[yr]:"fire-iid-compat",[vr]:"fire-fcm",[br]:"fire-fcm-compat",[wr]:"fire-perf",[Cr]:"fire-perf-compat",[Tr]:"fire-rc",[kr]:"fire-rc-compat",[Sr]:"fire-gcs",[Er]:"fire-gcs-compat",[Ir]:"fire-fst",[Rr]:"fire-fst-compat",[xr]:"fire-vertex","fire-js":"fire-js",[Ar]:"fire-js-all"},Nr=new Map,Dr=new Map,Lr=new Map;function Mr(e,t){try{e.container.addComponent(t)}catch(n){ir.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,n)}}function Fr(e){const t=e.name;if(Lr.has(t))return ir.debug(`There were multiple attempts to register component ${t}.`),!1;Lr.set(t,e);for(const n of Nr.values())Mr(n,e);for(const n of Dr.values())Mr(n,e);return!0}function jr(e,t){const n=e.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),e.container.getProvider(t)}const Ur=new dn("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class Br{constructor(e,t,n){this._isDeleted=!1,this._options=Object.assign({},e),this._config=Object.assign({},t),this._name=t.name,this._automaticDataCollectionEnabled=t.automaticDataCollectionEnabled,this._container=n,this.container.addComponent(new In("app",(()=>this),"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw Ur.create("app-deleted",{appName:this._name})}}function $r(e,t={}){let n=e;if("object"!=typeof t){t={name:t}}const r=Object.assign({name:Pr,automaticDataCollectionEnabled:!1},t),i=r.name;if("string"!=typeof i||!i)throw Ur.create("bad-app-name",{appName:String(i)});if(n||(n=sn()),!n)throw Ur.create("no-options");const s=Nr.get(i);if(s){if(wn(n,s.options)&&wn(r,s.config))return s;throw Ur.create("duplicate-app",{appName:i})}const o=new An(i);for(const c of Lr.values())o.addComponent(c);const a=new Br(n,r,o);return Nr.set(i,a),a}function qr(e=Pr){const t=Nr.get(e);if(!t&&e===Pr&&sn())return $r();if(!t)throw Ur.create("no-app",{appName:e});return t}function Wr(e,t,n){var r;let i=null!==(r=Or[e])&&void 0!==r?r:e;n&&(i+=`-${n}`);const s=i.match(/\s|\//),o=t.match(/\s|\//);if(s||o){const e=[`Unable to register library "${i}" with version "${t}":`];return s&&e.push(`library name "${i}" contains illegal characters (whitespace or "/")`),s&&o&&e.push("and"),o&&e.push(`version name "${t}" contains illegal characters (whitespace or "/")`),void ir.warn(e.join(" "))}Fr(new In(`${i}-version`,(()=>({library:i,version:t})),"VERSION"))}const Hr="firebase-heartbeat-store";let zr=null;function Vr(){return zr||(zr=Jn("firebase-heartbeat-database",1,{upgrade:(e,t)=>{if(0===t)try{e.createObjectStore(Hr)}catch(n){}}}).catch((e=>{throw Ur.create("idb-open",{originalErrorMessage:e.message})}))),zr}async function Kr(e,t){try{const n=(await Vr()).transaction(Hr,"readwrite"),r=n.objectStore(Hr);await r.put(t,Gr(e)),await n.done}catch(n){if(n instanceof hn)ir.warn(n.message);else{const e=Ur.create("idb-set",{originalErrorMessage:null==n?void 0:n.message});ir.warn(e.message)}}}function Gr(e){return`${e.name}!${e.options.appId}`}class Yr{constructor(e){this.container=e,this._heartbeatsCache=null;const t=this.container.getProvider("app").getImmediate();this._storage=new Qr(t),this._heartbeatsCachePromise=this._storage.read().then((e=>(this._heartbeatsCache=e,e)))}async triggerHeartbeat(){var e,t;try{const n=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),r=Jr();if(null==(null===(e=this._heartbeatsCache)||void 0===e?void 0:e.heartbeats)&&(this._heartbeatsCache=await this._heartbeatsCachePromise,null==(null===(t=this._heartbeatsCache)||void 0===t?void 0:t.heartbeats)))return;if(this._heartbeatsCache.lastSentHeartbeatDate===r||this._heartbeatsCache.heartbeats.some((e=>e.date===r)))return;if(this._heartbeatsCache.heartbeats.push({date:r,agent:n}),this._heartbeatsCache.heartbeats.length>30){const e=function(e){if(0===e.length)return-1;let t=0,n=e[0].date;for(let r=1;r<e.length;r++)e[r].date<n&&(n=e[r].date,t=r);return t}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(n){ir.warn(n)}}async getHeartbeatsHeader(){var e;try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,null==(null===(e=this._heartbeatsCache)||void 0===e?void 0:e.heartbeats)||0===this._heartbeatsCache.heartbeats.length)return"";const t=Jr(),{heartbeatsToSend:n,unsentEntries:r}=function(e,t=1024){const n=[];let r=e.slice();for(const i of e){const e=n.find((e=>e.agent===i.agent));if(e){if(e.dates.push(i.date),Xr(n)>t){e.dates.pop();break}}else if(n.push({agent:i.agent,dates:[i.date]}),Xr(n)>t){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}(this._heartbeatsCache.heartbeats),i=Qt(JSON.stringify({version:2,heartbeats:n}));return this._heartbeatsCache.lastSentHeartbeatDate=t,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(t){return ir.warn(t),""}}}function Jr(){return(new Date).toISOString().substring(0,10)}class Qr{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!cn()&&ln().then((()=>!0)).catch((()=>!1))}async read(){if(await this._canUseIndexedDBPromise){const e=await async function(e){try{const t=(await Vr()).transaction(Hr),n=await t.objectStore(Hr).get(Gr(e));return await t.done,n}catch(t){if(t instanceof hn)ir.warn(t.message);else{const e=Ur.create("idb-get",{originalErrorMessage:null==t?void 0:t.message});ir.warn(e.message)}}}(this.app);return(null==e?void 0:e.heartbeats)?e:{heartbeats:[]}}return{heartbeats:[]}}async overwrite(e){var t;if(await this._canUseIndexedDBPromise){const n=await this.read();return Kr(this.app,{lastSentHeartbeatDate:null!==(t=e.lastSentHeartbeatDate)&&void 0!==t?t:n.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){var t;if(await this._canUseIndexedDBPromise){const n=await this.read();return Kr(this.app,{lastSentHeartbeatDate:null!==(t=e.lastSentHeartbeatDate)&&void 0!==t?t:n.lastSentHeartbeatDate,heartbeats:[...n.heartbeats,...e.heartbeats]})}}}function Xr(e){return Qt(JSON.stringify({version:2,heartbeats:e})).length}var Zr;Zr="",Fr(new In("platform-logger",(e=>new tr(e)),"PRIVATE")),Fr(new In("heartbeat",(e=>new Yr(e)),"PRIVATE")),Wr(nr,rr,Zr),Wr(nr,rr,"esm2017"),Wr("fire-js","");const ei="@firebase/installations",ti="0.6.13",ni=1e4,ri=`w:${ti}`,ii="FIS_v2",si=36e5,oi=new dn("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function ai(e){return e instanceof hn&&e.code.includes("request-failed")}function ci({projectId:e}){return`https://firebaseinstallations.googleapis.com/v1/projects/${e}/installations`}function li(e){return{token:e.token,requestStatus:2,expiresIn:(t=e.expiresIn,Number(t.replace("s","000"))),creationTime:Date.now()};var t}async function ui(e,t){const n=(await t.json()).error;return oi.create("request-failed",{requestName:e,serverCode:n.code,serverMessage:n.message,serverStatus:n.status})}function hi({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function di(e,{refreshToken:t}){const n=hi(e);return n.append("Authorization",function(e){return`${ii} ${e}`}(t)),n}async function pi(e){const t=await e();return t.status>=500&&t.status<600?e():t}function fi(e){return new Promise((t=>{setTimeout(t,e)}))}const mi=/^[cdef][\w-]{21}$/;function gi(){try{const e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;const t=function(e){const t=(n=e,btoa(String.fromCharCode(...n)).replace(/\+/g,"-").replace(/\//g,"_"));var n;return t.substr(0,22)}(e);return mi.test(t)?t:""}catch(e){return""}}function _i(e){return`${e.appName}!${e.appId}`}const yi=new Map;function vi(e,t){const n=_i(e);bi(n,t),function(e,t){const n=function(){!wi&&"BroadcastChannel"in self&&(wi=new BroadcastChannel("[Firebase] FID Change"),wi.onmessage=e=>{bi(e.data.key,e.data.fid)});return wi}();n&&n.postMessage({key:e,fid:t});0===yi.size&&wi&&(wi.close(),wi=null)}(n,t)}function bi(e,t){const n=yi.get(e);if(n)for(const r of n)r(t)}let wi=null;const Ci="firebase-installations-store";let Ti=null;function ki(){return Ti||(Ti=Jn("firebase-installations-database",1,{upgrade:(e,t)=>{if(0===t)e.createObjectStore(Ci)}})),Ti}async function Si(e,t){const n=_i(e),r=(await ki()).transaction(Ci,"readwrite"),i=r.objectStore(Ci),s=await i.get(n);return await i.put(t,n),await r.done,s&&s.fid===t.fid||vi(e,t.fid),t}async function Ei(e){const t=_i(e),n=(await ki()).transaction(Ci,"readwrite");await n.objectStore(Ci).delete(t),await n.done}async function Ii(e,t){const n=_i(e),r=(await ki()).transaction(Ci,"readwrite"),i=r.objectStore(Ci),s=await i.get(n),o=t(s);return void 0===o?await i.delete(n):await i.put(o,n),await r.done,!o||s&&s.fid===o.fid||vi(e,o.fid),o}async function xi(e){let t;const n=await Ii(e.appConfig,(n=>{const r=function(e){const t=e||{fid:gi(),registrationStatus:0};return Pi(t)}(n),i=function(e,t){if(0===t.registrationStatus){if(!navigator.onLine){return{installationEntry:t,registrationPromise:Promise.reject(oi.create("app-offline"))}}const n={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=async function(e,t){try{const n=await async function({appConfig:e,heartbeatServiceProvider:t},{fid:n}){const r=ci(e),i=hi(e),s=t.getImmediate({optional:!0});if(s){const e=await s.getHeartbeatsHeader();e&&i.append("x-firebase-client",e)}const o={fid:n,authVersion:ii,appId:e.appId,sdkVersion:ri},a={method:"POST",headers:i,body:JSON.stringify(o)},c=await pi((()=>fetch(r,a)));if(c.ok){const e=await c.json();return{fid:e.fid||n,registrationStatus:2,refreshToken:e.refreshToken,authToken:li(e.authToken)}}throw await ui("Create Installation",c)}(e,t);return Si(e.appConfig,n)}catch(n){throw ai(n)&&409===n.customData.serverCode?await Ei(e.appConfig):await Si(e.appConfig,{fid:t.fid,registrationStatus:0}),n}}(e,n);return{installationEntry:n,registrationPromise:r}}return 1===t.registrationStatus?{installationEntry:t,registrationPromise:Ri(e)}:{installationEntry:t}}(e,r);return t=i.registrationPromise,i.installationEntry}));return""===n.fid?{installationEntry:await t}:{installationEntry:n,registrationPromise:t}}async function Ri(e){let t=await Ai(e.appConfig);for(;1===t.registrationStatus;)await fi(100),t=await Ai(e.appConfig);if(0===t.registrationStatus){const{installationEntry:t,registrationPromise:n}=await xi(e);return n||t}return t}function Ai(e){return Ii(e,(e=>{if(!e)throw oi.create("installation-not-found");return Pi(e)}))}function Pi(e){return 1===(t=e).registrationStatus&&t.registrationTime+ni<Date.now()?{fid:e.fid,registrationStatus:0}:e;var t}async function Oi({appConfig:e,heartbeatServiceProvider:t},n){const r=function(e,{fid:t}){return`${ci(e)}/${t}/authTokens:generate`}(e,n),i=di(e,n),s=t.getImmediate({optional:!0});if(s){const e=await s.getHeartbeatsHeader();e&&i.append("x-firebase-client",e)}const o={installation:{sdkVersion:ri,appId:e.appId}},a={method:"POST",headers:i,body:JSON.stringify(o)},c=await pi((()=>fetch(r,a)));if(c.ok){return li(await c.json())}throw await ui("Generate Auth Token",c)}async function Ni(e,t=!1){let n;const r=await Ii(e.appConfig,(r=>{if(!Li(r))throw oi.create("not-registered");const i=r.authToken;if(!t&&function(e){return 2===e.requestStatus&&!function(e){const t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+si}(e)}(i))return r;if(1===i.requestStatus)return n=async function(e,t){let n=await Di(e.appConfig);for(;1===n.authToken.requestStatus;)await fi(100),n=await Di(e.appConfig);const r=n.authToken;return 0===r.requestStatus?Ni(e,t):r}(e,t),r;{if(!navigator.onLine)throw oi.create("app-offline");const t=function(e){const t={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},e),{authToken:t})}(r);return n=async function(e,t){try{const n=await Oi(e,t),r=Object.assign(Object.assign({},t),{authToken:n});return await Si(e.appConfig,r),n}catch(n){if(!ai(n)||401!==n.customData.serverCode&&404!==n.customData.serverCode){const n=Object.assign(Object.assign({},t),{authToken:{requestStatus:0}});await Si(e.appConfig,n)}else await Ei(e.appConfig);throw n}}(e,t),t}}));return n?await n:r.authToken}function Di(e){return Ii(e,(e=>{if(!Li(e))throw oi.create("not-registered");const t=e.authToken;return 1===(n=t).requestStatus&&n.requestTime+ni<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e;var n}))}function Li(e){return void 0!==e&&2===e.registrationStatus}async function Mi(e,t=!1){const n=e;await async function(e){const{registrationPromise:t}=await xi(e);t&&await t}(n);return(await Ni(n,t)).token}function Fi(e){return oi.create("missing-app-config-values",{valueName:e})}const ji="installations",Ui=e=>{const t=jr(e.getProvider("app").getImmediate(),ji).getImmediate();return{getId:()=>async function(e){const t=e,{installationEntry:n,registrationPromise:r}=await xi(t);return r?r.catch(console.error):Ni(t).catch(console.error),n.fid}(t),getToken:e=>Mi(t,e)}};Fr(new In(ji,(e=>{const t=e.getProvider("app").getImmediate(),n=function(e){if(!e||!e.options)throw Fi("App Configuration");if(!e.name)throw Fi("App Name");const t=["projectId","apiKey","appId"];for(const n of t)if(!e.options[n])throw Fi(n);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t);return{app:t,appConfig:n,heartbeatServiceProvider:jr(t,"heartbeat"),_delete:()=>Promise.resolve()}}),"PUBLIC")),Fr(new In("installations-internal",Ui,"PRIVATE")),Wr(ei,ti),Wr(ei,ti,"esm2017");const Bi="analytics",$i="https://www.googletagmanager.com/gtag/js",qi=new Fn("@firebase/analytics"),Wi=new dn("analytics","Analytics",{"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."});function Hi(e){if(!e.startsWith($i)){const t=Wi.create("invalid-gtag-resource",{gtagURL:e});return qi.warn(t.message),""}return e}function zi(e){return Promise.all(e.map((e=>e.catch((e=>e)))))}function Vi(e,t){const n=function(e,t){let n;return window.trustedTypes&&(n=window.trustedTypes.createPolicy(e,t)),n}("firebase-js-sdk-policy",{createScriptURL:Hi}),r=document.createElement("script"),i=`${$i}?l=${e}&id=${t}`;r.src=n?null==n?void 0:n.createScriptURL(i):i,r.async=!0,document.head.appendChild(r)}function Ki(e,t,n,r){return async function(i,...s){try{if("event"===i){const[r,i]=s;await async function(e,t,n,r,i){try{let s=[];if(i&&i.send_to){let e=i.send_to;Array.isArray(e)||(e=[e]);const r=await zi(n);for(const n of e){const e=r.find((e=>e.measurementId===n)),i=e&&t[e.appId];if(!i){s=[];break}s.push(i)}}0===s.length&&(s=Object.values(t)),await Promise.all(s),e("event",r,i||{})}catch(s){qi.error(s)}}(e,t,n,r,i)}else if("config"===i){const[i,o]=s;await async function(e,t,n,r,i,s){const o=r[i];try{if(o)await t[o];else{const e=(await zi(n)).find((e=>e.measurementId===i));e&&await t[e.appId]}}catch(a){qi.error(a)}e("config",i,s)}(e,t,n,r,i,o)}else if("consent"===i){const[t,n]=s;e("consent",t,n)}else if("get"===i){const[t,n,r]=s;e("get",t,n,r)}else if("set"===i){const[t]=s;e("set",t)}else e(i,...s)}catch(o){qi.error(o)}}}const Gi=new class{constructor(e={},t=1e3){this.throttleMetadata=e,this.intervalMillis=t}getThrottleMetadata(e){return this.throttleMetadata[e]}setThrottleMetadata(e,t){this.throttleMetadata[e]=t}deleteThrottleMetadata(e){delete this.throttleMetadata[e]}};function Yi(e){return new Headers({Accept:"application/json","x-goog-api-key":e})}async function Ji(e,t=Gi,n){const{appId:r,apiKey:i,measurementId:s}=e.options;if(!r)throw Wi.create("no-app-id");if(!i){if(s)return{measurementId:s,appId:r};throw Wi.create("no-api-key")}const o=t.getThrottleMetadata(r)||{backoffCount:0,throttleEndTimeMillis:Date.now()},a=new Xi;return setTimeout((async()=>{a.abort()}),6e4),Qi({appId:r,apiKey:i,measurementId:s},o,a,t)}async function Qi(e,{throttleEndTimeMillis:t,backoffCount:n},r,i=Gi){var s;const{appId:o,measurementId:a}=e;try{await function(e,t){return new Promise(((n,r)=>{const i=Math.max(t-Date.now(),0),s=setTimeout(n,i);e.addEventListener((()=>{clearTimeout(s),r(Wi.create("fetch-throttle",{throttleEndTimeMillis:t}))}))}))}(r,t)}catch(c){if(a)return qi.warn(`Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID ${a} provided in the "measurementId" field in the local Firebase config. [${null==c?void 0:c.message}]`),{appId:o,measurementId:a};throw c}try{const t=await async function(e){var t;const{appId:n,apiKey:r}=e,i={method:"GET",headers:Yi(r)},s="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig".replace("{app-id}",n),o=await fetch(s,i);if(200!==o.status&&304!==o.status){let e="";try{const n=await o.json();(null===(t=n.error)||void 0===t?void 0:t.message)&&(e=n.error.message)}catch(a){}throw Wi.create("config-fetch-failed",{httpStatus:o.status,responseMessage:e})}return o.json()}(e);return i.deleteThrottleMetadata(o),t}catch(c){const t=c;if(!function(e){if(!(e instanceof hn&&e.customData))return!1;const t=Number(e.customData.httpStatus);return 429===t||500===t||503===t||504===t}(t)){if(i.deleteThrottleMetadata(o),a)return qi.warn(`Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID ${a} provided in the "measurementId" field in the local Firebase config. [${null==t?void 0:t.message}]`),{appId:o,measurementId:a};throw c}const l=503===Number(null===(s=null==t?void 0:t.customData)||void 0===s?void 0:s.httpStatus)?Sn(n,i.intervalMillis,30):Sn(n,i.intervalMillis),u={throttleEndTimeMillis:Date.now()+l,backoffCount:n+1};return i.setThrottleMetadata(o,u),qi.debug(`Calling attemptFetch again in ${l} millis`),Qi(e,u,r,i)}}class Xi{constructor(){this.listeners=[]}addEventListener(e){this.listeners.push(e)}abort(){this.listeners.forEach((e=>e()))}}async function Zi(e,t,n,r,i,s,o){var a;const c=Ji(e);c.then((t=>{n[t.measurementId]=t.appId,e.options.measurementId&&t.measurementId!==e.options.measurementId&&qi.warn(`The measurement ID in the local Firebase config (${e.options.measurementId}) does not match the measurement ID fetched from the server (${t.measurementId}). To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.`)})).catch((e=>qi.error(e))),t.push(c);const l=async function(){if(!cn())return qi.warn(Wi.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;try{await ln()}catch(e){return qi.warn(Wi.create("indexeddb-unavailable",{errorInfo:null==e?void 0:e.toString()}).message),!1}return!0}().then((e=>e?r.getId():void 0)),[u,h]=await Promise.all([c,l]);(function(e){const t=window.document.getElementsByTagName("script");for(const n of Object.values(t))if(n.src&&n.src.includes($i)&&n.src.includes(e))return n;return null})(s)||Vi(s,u.measurementId),i("js",new Date);const d=null!==(a=null==o?void 0:o.config)&&void 0!==a?a:{};return d.origin="firebase",d.update=!0,null!=h&&(d.firebase_id=h),i("config",u.measurementId,d),u.measurementId}class es{constructor(e){this.app=e}_delete(){return delete ts[this.app.options.appId],Promise.resolve()}}let ts={},ns=[];const rs={};let is,ss,os="dataLayer",as=!1;function cs(){const e=[];if(function(){const e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}()&&e.push("This is a browser extension environment."),un()||e.push("Cookies are not available."),e.length>0){const t=e.map(((e,t)=>`(${t+1}) ${e}`)).join(" "),n=Wi.create("invalid-analytics-context",{errorInfo:t});qi.warn(n.message)}}function ls(e,t,n){cs();const r=e.options.appId;if(!r)throw Wi.create("no-app-id");if(!e.options.apiKey){if(!e.options.measurementId)throw Wi.create("no-api-key");qi.warn(`The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID ${e.options.measurementId} provided in the "measurementId" field in the local Firebase config.`)}if(null!=ts[r])throw Wi.create("already-exists",{id:r});if(!as){!function(e){let t=[];Array.isArray(window[e])?t=window[e]:window[e]=t}(os);const{wrappedGtag:e,gtagCore:t}=function(e,t,n,r,i){let s=function(...e){window[r].push(arguments)};return window[i]&&"function"==typeof window[i]&&(s=window[i]),window[i]=Ki(s,e,t,n),{gtagCore:s,wrappedGtag:window[i]}}(ts,ns,rs,os,"gtag");ss=e,is=t,as=!0}ts[r]=Zi(e,ns,rs,t,is,os,n);return new es(e)}function us(e=qr()){const t=jr(e=En(e),Bi);return t.isInitialized()?t.getImmediate():function(e,t={}){const n=jr(e,Bi);if(n.isInitialized()){const e=n.getImmediate();if(wn(t,n.getOptions()))return e;throw Wi.create("already-initialized")}const r=n.initialize({options:t});return r}(e)}function hs(e,t,n,r){e=En(e),async function(e,t,n,r,i){if(i&&i.global)e("event",n,r);else{const i=await t;e("event",n,Object.assign(Object.assign({},r),{send_to:i}))}}(ss,ts[e.app.options.appId],t,n,r).catch((e=>qi.error(e)))}const ds="@firebase/analytics",ps="0.10.12";Fr(new In(Bi,((e,{options:t})=>ls(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),t)),"PUBLIC")),Fr(new In("analytics-internal",(function(e){try{const t=e.getProvider(Bi).getImmediate();return{logEvent:(e,n,r)=>hs(t,e,n,r)}}catch(t){throw Wi.create("interop-component-reg-failed",{reason:t})}}),"PRIVATE")),Wr(ds,ps),Wr(ds,ps,"esm2017");Wr("firebase","11.4.0","app");var fs={VITE_APP_RUN_ENV:"production",VITE_APP_BASE_URL:"/h5/feature/",VITE_APP_AU_API_URL:"https://app.vttechfx.com:18008",VITE_APP_PU_API_URL:"https://app.apppuprime.com:18008",VITE_APP_MO_API_URL:"https://moneta.app-prod.com:18008",VITE_APP_VJP_API_URL:"https://app.vjpappprotech.com:18008",VITE_APP_GREENID_KEY:"js4-Mu6-V2C-k27",VITE_APP_UM_API_URL:"https://appultimarkets.com:18008",VITE_APP_SOCIAL_TRADING_API_URL:"https://stapp.vttechfx.com:16443/stTradeApp",VITE_APP_SOCIAL_TRADING_API_URL_VJP:"https://stapp-ano.vttechfx.com:16443/stTradeApp/",VITE_APP_API_KEY_AU:"mK9fz2Fhk10N3jP3zo0Q5",VITE_APP_AU_UUID:"f16ae79d-8964-48df-94e2-b8dj372300ae",VITE_APP_API_KEY_PU:"c158c924175d056dfc59c",VITE_APP_PU_UUID:"1ee96572-6275-4c63-a85f-ff3ca4daccf3",VITE_APP_API_KEY_VJP:"a9cf58534fdfbd76264c3",NVM_INC:"/Users/<USER>/.nvm/versions/node/v18.12.0/include/node",npm_package_devDependencies_prettier:"^2.4.1",TERM_PROGRAM:"vscode",NODE:"/opt/homebrew/Cellar/node/22.4.1/bin/node",npm_package_dependencies_vant:"^4",npm_package_dependencies_esno:"4.8.0",INIT_CWD:"/Users/<USER>/hytech/frontend-m-inapp",NVM_CD_FLAGS:"-q",_P9K_TTY:"/dev/ttys001",npm_package_devDependencies_terser_webpack_plugin:"^5.3.10",npm_package_dependencies_axios:"^1.7.2",npm_config_version_git_tag:"true",SHELL:"/bin/zsh",TERM:"xterm-256color",npm_package_devDependencies_vite:"^6.0.11",HOMEBREW_API_DOMAIN:"https://mirrors.aliyun.com/homebrew/homebrew-bottles/api",HOMEBREW_BOTTLE_DOMAIN:"https://mirrors.aliyun.com/homebrew/homebrew-bottles",npm_package_devDependencies_vue_template_compiler:"^2.6.14",TMPDIR:"/var/folders/lk/ntkwtm1154bc1lk_qn52x_qm0000gn/T/",HOMEBREW_REPOSITORY:"/opt/homebrew",npm_package_scripts_analyz:"NODE_ENV=production npm_config_report=true npm run build:prod :prod",npm_config_init_license:"MIT",TERM_PROGRAM_VERSION:"1.98.2",npm_package_devDependencies__vitejs_plugin_vue:"^5.2.1",npm_package_scripts_gen_i18n:"npx esno scripts/gen-i18n.js --brand",MallocNanoZone:"0",ORIGINAL_XDG_CURRENT_DESKTOP:"undefined",ZDOTDIR:"/Users/<USER>",npm_package_devDependencies_unplugin_vue_components:"^28.0.0",npm_package_devDependencies_sass_loader:"^16.0.2",npm_package_devDependencies_webpack_bundle_analyzer:"^4.10.2",npm_package_devDependencies_vconsole:"^3.15.1",npm_package_scripts_i18n:"node ./node_modules/i18n-words/dist/cli.cjs",npm_package_private:"true",npm_config_registry:"https://registry.yarnpkg.com",npm_config_home:"https://mirrors.cloud.tencent.com/npm/",ZSH:"/Users/<USER>/.oh-my-zsh",npm_package_readmeFilename:"README.md",npm_package_dependencies_dayjs:"^1.11.13",USER:"rare",NVM_DIR:"/Users/<USER>/.nvm",npm_package_description:"``` yarn install ```",npm_package_scripts_build_test:"prettier --write . && vite build --mode test -- --brand",LS_COLORS:"di=1;36:ln=35:so=32:pi=33:ex=31:bd=34;46:cd=34;43:su=30;41:sg=30;46:tw=30;42:ow=30;43",npm_package_devDependencies_vite_plugin_dynamic_import:"^1.6.0",COMMAND_MODE:"unix2003",npm_package_dependencies_compressorjs:"^1.2.1",npm_package_devDependencies_vite_plugin_html:"^3.2.2",npm_package_devDependencies__fe9527_i18n_helper:"^1.4.0",npm_package_devDependencies__babel_core:"^7.12.16",npm_package_dependencies_aws_sdk:"^2.1692.0",SSH_AUTH_SOCK:"/private/tmp/com.apple.launchd.P6rgKKU5By/Listeners",npm_package_devDependencies__vue_cli_service:"~5.0.0",__CF_USER_TEXT_ENCODING:"0x1F5:0x19:0x34",VSCODE_PROFILE_INITIALIZED:"1",npm_package_devDependencies_postcss_pxtorem:"^6.1.0",npm_package_devDependencies_eslint:"^7.32.0",npm_package_dependencies_multer_s3:"^3.0.1",HOMEBREW_PIP_INDEX_URL:"http://mirrors.aliyun.com/pypi/simple",npm_execpath:"/opt/homebrew/lib/node_modules/yarn/bin/yarn.js",PAGER:"less",npm_package_devDependencies_unplugin_auto_import:"^19.0.0",npm_package_devDependencies_babel_plugin_import:"^1.13.8",npm_package_dependencies_cors:"^2.8.5",npm_package_dependencies__vant_compat:"^1",npm_package_dependencies__dansmaculotte_vue_zendesk:"^0.4.7",LSCOLORS:"Gxfxcxdxbxegedabagacad",npm_package_devDependencies_eslint_plugin_prettier:"^4.0.0",npm_package_dependencies_zendesk_widget_vue:"^0.0.3",npm_package_devDependencies__grafana_faro_webpack_plugin:"^0.1.1",PATH:"/var/folders/lk/ntkwtm1154bc1lk_qn52x_qm0000gn/T/yarn--1742522591142-0.12426014564305166:/Users/<USER>/hytech/frontend-m-inapp/node_modules/.bin:/Users/<USER>/.config/yarn/link/node_modules/.bin:/Users/<USER>/.yarn/bin:/opt/homebrew/Cellar/node/22.4.1/libexec/lib/node_modules/npm/bin/node-gyp-bin:/opt/homebrew/Cellar/node/22.4.1/lib/node_modules/npm/bin/node-gyp-bin:/opt/homebrew/Cellar/node/22.4.1/bin/node_modules/npm/bin/node-gyp-bin:/opt/homebrew/bin:/Users/<USER>/.nvm/versions/node/v18.12.0/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/opt/homebrew/bin:/Users/<USER>/.nvm/versions/node/v18.12.0/bin:/opt/homebrew/sbin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand",npm_config_argv:'{"remain":[],"cooked":["run","build:prod"],"original":["run","build:prod","au"]}',npm_package_devDependencies_lib_flexible:"^0.3.2",npm_package_devDependencies__vant_auto_import_resolver:"^1.2.1",npm_package_dependencies_multer:"^1.4.5-lts.1",_:"/Users/<USER>/hytech/frontend-m-inapp/node_modules/.bin/vite",npm_package_dependencies_vue:"^3.5.13",__CFBundleIdentifier:"com.microsoft.VSCode",USER_ZDOTDIR:"/Users/<USER>",PWD:"/Users/<USER>/hytech/frontend-m-inapp",npm_package_devDependencies_terser:"^5.38.1",npm_package_dependencies_mongodb:"^6.14.0",VSCODE_NONCE:"4e06c132-5e88-4ea8-8bef-a4e813e18ac6",npm_package_dependencies_core_js:"^3.8.3",P9K_SSH:"0",npm_lifecycle_event:"build:prod",LANG:"zh_CN.UTF-8",P9K_TTY:"old",npm_package_name:"in-apps",npm_package_dependencies_body_parser:"^1.20.3",npm_config_version_commit_hooks:"true",XPC_FLAGS:"0x0",VSCODE_GIT_ASKPASS_EXTRA_ARGS:"",npm_package_devDependencies_rollup_plugin_visualizer:"^5.14.0",npm_package_devDependencies__babel_eslint_parser:"^7.12.16",npm_package_devDependencies_compression_webpack_plugin:"^11.1.0",npm_config_bin_links:"true",npm_config_pytyon:"python2.7",npm_package_engines_node:">=18.12.0",npm_config_ignore_engines:"true",npm_package_devDependencies_eslint_config_prettier:"^8.3.0",npm_package_dependencies_vue_i18n:"^11.0.0-rc.1",npm_package_scripts_build_prod:"prettier --write . && vite build --mode production -- --brand",XPC_SERVICE_NAME:"0",npm_package_version:"0.1.0",VSCODE_INJECTION:"1",npm_package_dependencies_pinia:"^2.3.1",npm_config___crm_prod_547583927517_d_codeartifact_us_east_2_amazonaws_com_npm_npm_store__always_auth:"true",HOME:"/Users/<USER>",SHLVL:"2",UPDATE_ZSH_DAYS:"13",npm_package_devDependencies__vue_cli_plugin_babel:"~5.0.0",VSCODE_GIT_ASKPASS_MAIN:"/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js",npm_package_devDependencies_eslint_plugin_vue:"^8.0.3",npm_package_dependencies_dotenv:"^16.4.7",npm_package_scripts_gen_i18n_excel:"npx esno scripts/gen-i18n-excel.js --brand",npm_package_scripts_serve:"vite --mode development -- --brand",npm_config_save_prefix:"^",npm_config_strict_ssl:"true",HOMEBREW_PREFIX:"/opt/homebrew",npm_config_version_git_message:"v%s",npm_package_browserslist_2:"not dead",npm_package_dependencies_xss:"^1.0.15",npm_package_scripts_build_test2:"prettier --write . && vite build --mode test -- --brand",npm_package_browserslist_1:"last 2 versions",npm_package_dependencies_firebase:"^11.4.0",LOGNAME:"rare",LESS:"-R",YARN_WRAP_OUTPUT:"false",npm_package_browserslist_0:"> 1%",npm_package_scripts_format:"prettier --write .",npm_lifecycle_script:"prettier --write . && vite build --mode production -- --brand",npm_package_devDependencies__vue_cli_plugin_eslint:"~5.0.0",npm_package_dependencies_vite_plugin_commonjs:"^0.10.4",VSCODE_GIT_IPC_HANDLE:"/var/folders/lk/ntkwtm1154bc1lk_qn52x_qm0000gn/T/vscode-git-a61f63909a.sock",NVM_BIN:"/Users/<USER>/.nvm/versions/node/v18.12.0/bin",npm_package_dependencies_sass:"^1.61.0",npm_config_version_git_sign:"",npm_config_ignore_scripts:"",npm_config_user_agent:"yarn/1.22.22 npm/? node/v22.4.1 darwin arm64",HOMEBREW_CELLAR:"/opt/homebrew/Cellar",INFOPATH:"/opt/homebrew/share/info:/opt/homebrew/share/info:",GIT_ASKPASS:"/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh",VSCODE_GIT_ASKPASS_NODE:"/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)",_P9K_SSH_TTY:"/dev/ttys001",npm_package_dependencies_express:"^4.21.2",npm_config_init_version:"1.0.0",npm_config_ignore_optional:"",npm_package_dependencies_vue_router:"^4.0.13",npm_package_devDependencies_i18n_words:"^1.1.4",COLORTERM:"truecolor",npm_node_execpath:"/opt/homebrew/Cellar/node/22.4.1/bin/node",npm_config_version_tag_prefix:"v",NODE_ENV:"production",VITE_APP_PROJECT_NAME:"AU"};const ms="@firebase/database",gs="1.0.13";let _s="";class ys{constructor(e){this.domStorage_=e,this.prefix_="firebase:"}set(e,t){null==t?this.domStorage_.removeItem(this.prefixedName_(e)):this.domStorage_.setItem(this.prefixedName_(e),mn(t))}get(e){const t=this.domStorage_.getItem(this.prefixedName_(e));return null==t?null:fn(t)}remove(e){this.domStorage_.removeItem(this.prefixedName_(e))}prefixedName_(e){return this.prefix_+e}toString(){return this.domStorage_.toString()}}class vs{constructor(){this.cache_={},this.isInMemoryStorage=!0}set(e,t){null==t?delete this.cache_[e]:this.cache_[e]=t}get(e){return _n(this.cache_,e)?this.cache_[e]:null}remove(e){delete this.cache_[e]}}const bs=function(e){try{if("undefined"!=typeof window&&void 0!==window[e]){const t=window[e];return t.setItem("firebase:sentinel","cache"),t.removeItem("firebase:sentinel"),new ys(t)}}catch(t){}return new vs},ws=bs("localStorage"),Cs=bs("sessionStorage"),Ts=new Fn("@firebase/database"),ks=function(){let e=1;return function(){return e++}}(),Ss=function(e){const t=function(e){const t=[];let n=0;for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);if(i>=55296&&i<=56319){const t=i-55296;r++,zt(r<e.length,"Surrogate pair missing trail surrogate."),i=65536+(t<<10)+(e.charCodeAt(r)-56320)}i<128?t[n++]=i:i<2048?(t[n++]=i>>6|192,t[n++]=63&i|128):i<65536?(t[n++]=i>>12|224,t[n++]=i>>6&63|128,t[n++]=63&i|128):(t[n++]=i>>18|240,t[n++]=i>>12&63|128,t[n++]=i>>6&63|128,t[n++]=63&i|128)}return t}(e),n=new Tn;n.update(t);const r=n.digest();return Gt.encodeByteArray(r)},Es=function(...e){let t="";for(let n=0;n<e.length;n++){const r=e[n];Array.isArray(r)||r&&"object"==typeof r&&"number"==typeof r.length?t+=Es.apply(null,r):t+="object"==typeof r?mn(r):r,t+=" "}return t};let Is=null,xs=!0;const Rs=function(...e){if(!0===xs&&(xs=!1,null===Is&&!0===Cs.get("logging_enabled")&&(zt(!0,"Can't turn on custom loggers persistently."),Ts.logLevel=Pn.VERBOSE,Is=Ts.log.bind(Ts))),Is){const t=Es.apply(null,e);Is(t)}},As=function(e){return function(...t){Rs(e,...t)}},Ps=function(...e){const t="FIREBASE INTERNAL ERROR: "+Es(...e);Ts.error(t)},Os=function(...e){const t=`FIREBASE FATAL ERROR: ${Es(...e)}`;throw Ts.error(t),new Error(t)},Ns=function(...e){const t="FIREBASE WARNING: "+Es(...e);Ts.warn(t)},Ds=function(e){return"number"==typeof e&&(e!=e||e===Number.POSITIVE_INFINITY||e===Number.NEGATIVE_INFINITY)},Ls="[MIN_NAME]",Ms="[MAX_NAME]",Fs=function(e,t){if(e===t)return 0;if(e===Ls||t===Ms)return-1;if(t===Ls||e===Ms)return 1;{const n=zs(e),r=zs(t);return null!==n?null!==r?n-r==0?e.length-t.length:n-r:-1:null!==r?1:e<t?-1:1}},js=function(e,t){return e===t?0:e<t?-1:1},Us=function(e,t){if(t&&e in t)return t[e];throw new Error("Missing required key ("+e+") in object: "+mn(t))},Bs=function(e){if("object"!=typeof e||null===e)return mn(e);const t=[];for(const r in e)t.push(r);t.sort();let n="{";for(let r=0;r<t.length;r++)0!==r&&(n+=","),n+=mn(t[r]),n+=":",n+=Bs(e[t[r]]);return n+="}",n},$s=function(e,t){const n=e.length;if(n<=t)return[e];const r=[];for(let i=0;i<n;i+=t)i+t>n?r.push(e.substring(i,n)):r.push(e.substring(i,i+t));return r};function qs(e,t){for(const n in e)e.hasOwnProperty(n)&&t(n,e[n])}const Ws=function(e){zt(!Ds(e),"Invalid JSON number");const t=1023;let n,r,i,s,o;0===e?(r=0,i=0,n=1/e==-1/0?1:0):(n=e<0,(e=Math.abs(e))>=Math.pow(2,-1022)?(s=Math.min(Math.floor(Math.log(e)/Math.LN2),t),r=s+t,i=Math.round(e*Math.pow(2,52-s)-Math.pow(2,52))):(r=0,i=Math.round(e/Math.pow(2,-1074))));const a=[];for(o=52;o;o-=1)a.push(i%2?1:0),i=Math.floor(i/2);for(o=11;o;o-=1)a.push(r%2?1:0),r=Math.floor(r/2);a.push(n?1:0),a.reverse();const c=a.join("");let l="";for(o=0;o<64;o+=8){let e=parseInt(c.substr(o,8),2).toString(16);1===e.length&&(e="0"+e),l+=e}return l.toLowerCase()},Hs=new RegExp("^-?(0*)\\d{1,10}$"),zs=function(e){if(Hs.test(e)){const t=Number(e);if(t>=-**********&&t<=**********)return t}return null},Vs=function(e){try{e()}catch(t){setTimeout((()=>{const e=t.stack||"";throw Ns("Exception was thrown by user callback.",e),t}),Math.floor(0))}},Ks=function(e,t){const n=setTimeout(e,t);return"number"==typeof n&&"undefined"!=typeof Deno&&Deno.unrefTimer?Deno.unrefTimer(n):"object"==typeof n&&n.unref&&n.unref(),n};class Gs{constructor(e,t){var n;this.appCheckProvider=t,this.appName=e.name,null!=(n=e)&&void 0!==n.settings&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.appCheck=null==t?void 0:t.getImmediate({optional:!0}),this.appCheck||null==t||t.get().then((e=>this.appCheck=e))}getToken(e){if(this.serverAppAppCheckToken){if(e)throw new Error("Attempted reuse of `FirebaseServerApp.appCheckToken` after previous usage failed.");return Promise.resolve({token:this.serverAppAppCheckToken})}return this.appCheck?this.appCheck.getToken(e):new Promise(((t,n)=>{setTimeout((()=>{this.appCheck?this.getToken(e).then(t,n):t(null)}),0)}))}addTokenChangeListener(e){var t;null===(t=this.appCheckProvider)||void 0===t||t.get().then((t=>t.addTokenListener(e)))}notifyForInvalidToken(){Ns(`Provided AppCheck credentials for the app named "${this.appName}" are invalid. This usually indicates your app was not initialized correctly.`)}}class Ys{constructor(e,t,n){this.appName_=e,this.firebaseOptions_=t,this.authProvider_=n,this.auth_=null,this.auth_=n.getImmediate({optional:!0}),this.auth_||n.onInit((e=>this.auth_=e))}getToken(e){return this.auth_?this.auth_.getToken(e).catch((e=>e&&"auth/token-not-initialized"===e.code?(Rs("Got auth/token-not-initialized error.  Treating as null token."),null):Promise.reject(e))):new Promise(((t,n)=>{setTimeout((()=>{this.auth_?this.getToken(e).then(t,n):t(null)}),0)}))}addTokenChangeListener(e){this.auth_?this.auth_.addAuthTokenListener(e):this.authProvider_.get().then((t=>t.addAuthTokenListener(e)))}removeTokenChangeListener(e){this.authProvider_.get().then((t=>t.removeAuthTokenListener(e)))}notifyForInvalidToken(){let e='Provided authentication credentials for the app named "'+this.appName_+'" are invalid. This usually indicates your app was not initialized correctly. ';"credential"in this.firebaseOptions_?e+='Make sure the "credential" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':"serviceAccount"in this.firebaseOptions_?e+='Make sure the "serviceAccount" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.':e+='Make sure the "apiKey" and "databaseURL" properties provided to initializeApp() match the values provided for your app at https://console.firebase.google.com/.',Ns(e)}}class Js{constructor(e){this.accessToken=e}getToken(e){return Promise.resolve({accessToken:this.accessToken})}addTokenChangeListener(e){e(this.accessToken)}removeTokenChangeListener(e){}notifyForInvalidToken(){}}Js.OWNER="owner";const Qs=/(console\.firebase|firebase-console-\w+\.corp|firebase\.corp)\.google\.com/,Xs="ac",Zs="websocket",eo="long_polling";class to{constructor(e,t,n,r,i=!1,s="",o=!1,a=!1,c=null){this.secure=t,this.namespace=n,this.webSocketOnly=r,this.nodeAdmin=i,this.persistenceKey=s,this.includeNamespaceInQueryParams=o,this.isUsingEmulator=a,this.emulatorOptions=c,this._host=e.toLowerCase(),this._domain=this._host.substr(this._host.indexOf(".")+1),this.internalHost=ws.get("host:"+e)||this._host}isCacheableHost(){return"s-"===this.internalHost.substr(0,2)}isCustomHost(){return"firebaseio.com"!==this._domain&&"firebaseio-demo.com"!==this._domain}get host(){return this._host}set host(e){e!==this.internalHost&&(this.internalHost=e,this.isCacheableHost()&&ws.set("host:"+this._host,this.internalHost))}toString(){let e=this.toURLString();return this.persistenceKey&&(e+="<"+this.persistenceKey+">"),e}toURLString(){const e=this.secure?"https://":"http://",t=this.includeNamespaceInQueryParams?`?ns=${this.namespace}`:"";return`${e}${this.host}/${t}`}}function no(e,t,n){let r;if(zt("string"==typeof t,"typeof type must == string"),zt("object"==typeof n,"typeof params must == object"),t===Zs)r=(e.secure?"wss://":"ws://")+e.internalHost+"/.ws?";else{if(t!==eo)throw new Error("Unknown connection type: "+t);r=(e.secure?"https://":"http://")+e.internalHost+"/.lp?"}(function(e){return e.host!==e.internalHost||e.isCustomHost()||e.includeNamespaceInQueryParams})(e)&&(n.ns=e.namespace);const i=[];return qs(n,((e,t)=>{i.push(e+"="+t)})),r+i.join("&")}class ro{constructor(){this.counters_={}}incrementCounter(e,t=1){_n(this.counters_,e)||(this.counters_[e]=0),this.counters_[e]+=t}get(){return Zt(this.counters_)}}const io={},so={};function oo(e){const t=e.toString();return io[t]||(io[t]=new ro),io[t]}class ao{constructor(e){this.onMessage_=e,this.pendingResponses=[],this.currentResponseNum=0,this.closeAfterResponse=-1,this.onClose=null}closeAfter(e,t){this.closeAfterResponse=e,this.onClose=t,this.closeAfterResponse<this.currentResponseNum&&(this.onClose(),this.onClose=null)}handleResponse(e,t){for(this.pendingResponses[e]=t;this.pendingResponses[this.currentResponseNum];){const e=this.pendingResponses[this.currentResponseNum];delete this.pendingResponses[this.currentResponseNum];for(let t=0;t<e.length;++t)e[t]&&Vs((()=>{this.onMessage_(e[t])}));if(this.currentResponseNum===this.closeAfterResponse){this.onClose&&(this.onClose(),this.onClose=null);break}this.currentResponseNum++}}}const co="start";class lo{constructor(e,t,n,r,i,s,o){this.connId=e,this.repoInfo=t,this.applicationId=n,this.appCheckToken=r,this.authToken=i,this.transportSessionId=s,this.lastSessionId=o,this.bytesSent=0,this.bytesReceived=0,this.everConnected_=!1,this.log_=As(e),this.stats_=oo(t),this.urlFn=e=>(this.appCheckToken&&(e[Xs]=this.appCheckToken),no(t,eo,e))}open(e,t){this.curSegmentNum=0,this.onDisconnect_=t,this.myPacketOrderer=new ao(e),this.isClosed_=!1,this.connectTimeoutTimer_=setTimeout((()=>{this.log_("Timed out trying to connect."),this.onClosed_(),this.connectTimeoutTimer_=null}),Math.floor(3e4)),function(e){if("complete"===document.readyState)e();else{let t=!1;const n=function(){document.body?t||(t=!0,e()):setTimeout(n,Math.floor(10))};document.addEventListener?(document.addEventListener("DOMContentLoaded",n,!1),window.addEventListener("load",n,!1)):document.attachEvent&&(document.attachEvent("onreadystatechange",(()=>{"complete"===document.readyState&&n()})),window.attachEvent("onload",n))}}((()=>{if(this.isClosed_)return;this.scriptTagHolder=new uo(((...e)=>{const[t,n,r,i,s]=e;if(this.incrementIncomingBytes_(e),this.scriptTagHolder)if(this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null),this.everConnected_=!0,t===co)this.id=n,this.password=r;else{if("close"!==t)throw new Error("Unrecognized command received: "+t);n?(this.scriptTagHolder.sendNewPolls=!1,this.myPacketOrderer.closeAfter(n,(()=>{this.onClosed_()}))):this.onClosed_()}}),((...e)=>{const[t,n]=e;this.incrementIncomingBytes_(e),this.myPacketOrderer.handleResponse(t,n)}),(()=>{this.onClosed_()}),this.urlFn);const e={};e[co]="t",e.ser=Math.floor(1e8*Math.random()),this.scriptTagHolder.uniqueCallbackIdentifier&&(e.cb=this.scriptTagHolder.uniqueCallbackIdentifier),e.v="5",this.transportSessionId&&(e.s=this.transportSessionId),this.lastSessionId&&(e.ls=this.lastSessionId),this.applicationId&&(e.p=this.applicationId),this.appCheckToken&&(e[Xs]=this.appCheckToken),"undefined"!=typeof location&&location.hostname&&Qs.test(location.hostname)&&(e.r="f");const t=this.urlFn(e);this.log_("Connecting via long-poll to "+t),this.scriptTagHolder.addTag(t,(()=>{}))}))}start(){this.scriptTagHolder.startLongPoll(this.id,this.password),this.addDisconnectPingFrame(this.id,this.password)}static forceAllow(){lo.forceAllow_=!0}static forceDisallow(){lo.forceDisallow_=!0}static isAvailable(){return!!lo.forceAllow_||!(lo.forceDisallow_||"undefined"==typeof document||null==document.createElement||"object"==typeof window&&window.chrome&&window.chrome.extension&&!/^chrome/.test(window.location.href)||"object"==typeof Windows&&"object"==typeof Windows.UI)}markConnectionHealthy(){}shutdown_(){this.isClosed_=!0,this.scriptTagHolder&&(this.scriptTagHolder.close(),this.scriptTagHolder=null),this.myDisconnFrame&&(document.body.removeChild(this.myDisconnFrame),this.myDisconnFrame=null),this.connectTimeoutTimer_&&(clearTimeout(this.connectTimeoutTimer_),this.connectTimeoutTimer_=null)}onClosed_(){this.isClosed_||(this.log_("Longpoll is closing itself"),this.shutdown_(),this.onDisconnect_&&(this.onDisconnect_(this.everConnected_),this.onDisconnect_=null))}close(){this.isClosed_||(this.log_("Longpoll is being closed."),this.shutdown_())}send(e){const t=mn(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);const n=Jt(t),r=$s(n,1840);for(let i=0;i<r.length;i++)this.scriptTagHolder.enqueueSegment(this.curSegmentNum,r.length,r[i]),this.curSegmentNum++}addDisconnectPingFrame(e,t){this.myDisconnFrame=document.createElement("iframe");const n={dframe:"t"};n.id=e,n.pw=t,this.myDisconnFrame.src=this.urlFn(n),this.myDisconnFrame.style.display="none",document.body.appendChild(this.myDisconnFrame)}incrementIncomingBytes_(e){const t=mn(e).length;this.bytesReceived+=t,this.stats_.incrementCounter("bytes_received",t)}}class uo{constructor(e,t,n,r){this.onDisconnect=n,this.urlFn=r,this.outstandingRequests=new Set,this.pendingSegs=[],this.currentSerial=Math.floor(1e8*Math.random()),this.sendNewPolls=!0;{this.uniqueCallbackIdentifier=ks(),window["pLPCommand"+this.uniqueCallbackIdentifier]=e,window["pRTLPCB"+this.uniqueCallbackIdentifier]=t,this.myIFrame=uo.createIFrame_();let n="";if(this.myIFrame.src&&"javascript:"===this.myIFrame.src.substr(0,11)){n='<script>document.domain="'+document.domain+'";<\/script>'}const r="<html><body>"+n+"</body></html>";try{this.myIFrame.doc.open(),this.myIFrame.doc.write(r),this.myIFrame.doc.close()}catch(i){Rs("frame writing exception"),i.stack&&Rs(i.stack),Rs(i)}}}static createIFrame_(){const e=document.createElement("iframe");if(e.style.display="none",!document.body)throw"Document body has not initialized. Wait to initialize Firebase until after the document is ready.";document.body.appendChild(e);try{e.contentWindow.document||Rs("No IE domain setting required")}catch(t){const n=document.domain;e.src="javascript:void((function(){document.open();document.domain='"+n+"';document.close();})())"}return e.contentDocument?e.doc=e.contentDocument:e.contentWindow?e.doc=e.contentWindow.document:e.document&&(e.doc=e.document),e}close(){this.alive=!1,this.myIFrame&&(this.myIFrame.doc.body.textContent="",setTimeout((()=>{null!==this.myIFrame&&(document.body.removeChild(this.myIFrame),this.myIFrame=null)}),Math.floor(0)));const e=this.onDisconnect;e&&(this.onDisconnect=null,e())}startLongPoll(e,t){for(this.myID=e,this.myPW=t,this.alive=!0;this.newRequest_(););}newRequest_(){if(this.alive&&this.sendNewPolls&&this.outstandingRequests.size<(this.pendingSegs.length>0?2:1)){this.currentSerial++;const e={};e.id=this.myID,e.pw=this.myPW,e.ser=this.currentSerial;let t=this.urlFn(e),n="",r=0;for(;this.pendingSegs.length>0;){if(!(this.pendingSegs[0].d.length+30+n.length<=1870))break;{const e=this.pendingSegs.shift();n=n+"&seg"+r+"="+e.seg+"&ts"+r+"="+e.ts+"&d"+r+"="+e.d,r++}}return t+=n,this.addLongPollTag_(t,this.currentSerial),!0}return!1}enqueueSegment(e,t,n){this.pendingSegs.push({seg:e,ts:t,d:n}),this.alive&&this.newRequest_()}addLongPollTag_(e,t){this.outstandingRequests.add(t);const n=()=>{this.outstandingRequests.delete(t),this.newRequest_()},r=setTimeout(n,Math.floor(25e3));this.addTag(e,(()=>{clearTimeout(r),n()}))}addTag(e,t){setTimeout((()=>{try{if(!this.sendNewPolls)return;const n=this.myIFrame.doc.createElement("script");n.type="text/javascript",n.async=!0,n.src=e,n.onload=n.onreadystatechange=function(){const e=n.readyState;e&&"loaded"!==e&&"complete"!==e||(n.onload=n.onreadystatechange=null,n.parentNode&&n.parentNode.removeChild(n),t())},n.onerror=()=>{Rs("Long-poll script failed to load: "+e),this.sendNewPolls=!1,this.close()},this.myIFrame.doc.body.appendChild(n)}catch(n){}}),Math.floor(1))}}let ho=null;"undefined"!=typeof MozWebSocket?ho=MozWebSocket:"undefined"!=typeof WebSocket&&(ho=WebSocket);class po{constructor(e,t,n,r,i,s,o){this.connId=e,this.applicationId=n,this.appCheckToken=r,this.authToken=i,this.keepaliveTimer=null,this.frames=null,this.totalFrames=0,this.bytesSent=0,this.bytesReceived=0,this.log_=As(this.connId),this.stats_=oo(t),this.connURL=po.connectionURL_(t,s,o,r,n),this.nodeAdmin=t.nodeAdmin}static connectionURL_(e,t,n,r,i){const s={v:"5"};return"undefined"!=typeof location&&location.hostname&&Qs.test(location.hostname)&&(s.r="f"),t&&(s.s=t),n&&(s.ls=n),r&&(s[Xs]=r),i&&(s.p=i),no(e,Zs,s)}open(e,t){this.onDisconnect=t,this.onMessage=e,this.log_("Websocket connecting to "+this.connURL),this.everConnected_=!1,ws.set("previous_websocket_failure",!0);try{let e;this.mySock=new ho(this.connURL,[],e)}catch(n){this.log_("Error instantiating WebSocket.");const e=n.message||n.data;return e&&this.log_(e),void this.onClosed_()}this.mySock.onopen=()=>{this.log_("Websocket connected."),this.everConnected_=!0},this.mySock.onclose=()=>{this.log_("Websocket connection was disconnected."),this.mySock=null,this.onClosed_()},this.mySock.onmessage=e=>{this.handleIncomingFrame(e)},this.mySock.onerror=e=>{this.log_("WebSocket error.  Closing connection.");const t=e.message||e.data;t&&this.log_(t),this.onClosed_()}}start(){}static forceDisallow(){po.forceDisallow_=!0}static isAvailable(){let e=!1;if("undefined"!=typeof navigator&&navigator.userAgent){const t=/Android ([0-9]{0,}\.[0-9]{0,})/,n=navigator.userAgent.match(t);n&&n.length>1&&parseFloat(n[1])<4.4&&(e=!0)}return!e&&null!==ho&&!po.forceDisallow_}static previouslyFailed(){return ws.isInMemoryStorage||!0===ws.get("previous_websocket_failure")}markConnectionHealthy(){ws.remove("previous_websocket_failure")}appendFrame_(e){if(this.frames.push(e),this.frames.length===this.totalFrames){const e=this.frames.join("");this.frames=null;const t=fn(e);this.onMessage(t)}}handleNewFrameCount_(e){this.totalFrames=e,this.frames=[]}extractFrameCount_(e){if(zt(null===this.frames,"We already have a frame buffer"),e.length<=6){const t=Number(e);if(!isNaN(t))return this.handleNewFrameCount_(t),null}return this.handleNewFrameCount_(1),e}handleIncomingFrame(e){if(null===this.mySock)return;const t=e.data;if(this.bytesReceived+=t.length,this.stats_.incrementCounter("bytes_received",t.length),this.resetKeepAlive(),null!==this.frames)this.appendFrame_(t);else{const e=this.extractFrameCount_(t);null!==e&&this.appendFrame_(e)}}send(e){this.resetKeepAlive();const t=mn(e);this.bytesSent+=t.length,this.stats_.incrementCounter("bytes_sent",t.length);const n=$s(t,16384);n.length>1&&this.sendString_(String(n.length));for(let r=0;r<n.length;r++)this.sendString_(n[r])}shutdown_(){this.isClosed_=!0,this.keepaliveTimer&&(clearInterval(this.keepaliveTimer),this.keepaliveTimer=null),this.mySock&&(this.mySock.close(),this.mySock=null)}onClosed_(){this.isClosed_||(this.log_("WebSocket is closing itself"),this.shutdown_(),this.onDisconnect&&(this.onDisconnect(this.everConnected_),this.onDisconnect=null))}close(){this.isClosed_||(this.log_("WebSocket is being closed"),this.shutdown_())}resetKeepAlive(){clearInterval(this.keepaliveTimer),this.keepaliveTimer=setInterval((()=>{this.mySock&&this.sendString_("0"),this.resetKeepAlive()}),Math.floor(45e3))}sendString_(e){try{this.mySock.send(e)}catch(t){this.log_("Exception thrown from WebSocket.send():",t.message||t.data,"Closing connection."),setTimeout(this.onClosed_.bind(this),0)}}}po.responsesRequiredToBeHealthy=2,po.healthyTimeout=3e4;class fo{static get ALL_TRANSPORTS(){return[lo,po]}static get IS_TRANSPORT_INITIALIZED(){return this.globalTransportInitialized_}constructor(e){this.initTransports_(e)}initTransports_(e){const t=po&&po.isAvailable();let n=t&&!po.previouslyFailed();if(e.webSocketOnly&&(t||Ns("wss:// URL used, but browser isn't known to support websockets.  Trying anyway."),n=!0),n)this.transports_=[po];else{const e=this.transports_=[];for(const t of fo.ALL_TRANSPORTS)t&&t.isAvailable()&&e.push(t);fo.globalTransportInitialized_=!0}}initialTransport(){if(this.transports_.length>0)return this.transports_[0];throw new Error("No transports available")}upgradeTransport(){return this.transports_.length>1?this.transports_[1]:null}}fo.globalTransportInitialized_=!1;class mo{constructor(e,t,n,r,i,s,o,a,c,l){this.id=e,this.repoInfo_=t,this.applicationId_=n,this.appCheckToken_=r,this.authToken_=i,this.onMessage_=s,this.onReady_=o,this.onDisconnect_=a,this.onKill_=c,this.lastSessionId=l,this.connectionCount=0,this.pendingDataMessages=[],this.state_=0,this.log_=As("c:"+this.id+":"),this.transportManager_=new fo(t),this.log_("Connection created"),this.start_()}start_(){const e=this.transportManager_.initialTransport();this.conn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,null,this.lastSessionId),this.primaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;const t=this.connReceiver_(this.conn_),n=this.disconnReceiver_(this.conn_);this.tx_=this.conn_,this.rx_=this.conn_,this.secondaryConn_=null,this.isHealthy_=!1,setTimeout((()=>{this.conn_&&this.conn_.open(t,n)}),Math.floor(0));const r=e.healthyTimeout||0;r>0&&(this.healthyTimeout_=Ks((()=>{this.healthyTimeout_=null,this.isHealthy_||(this.conn_&&this.conn_.bytesReceived>102400?(this.log_("Connection exceeded healthy timeout but has received "+this.conn_.bytesReceived+" bytes.  Marking connection healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()):this.conn_&&this.conn_.bytesSent>10240?this.log_("Connection exceeded healthy timeout but has sent "+this.conn_.bytesSent+" bytes.  Leaving connection alive."):(this.log_("Closing unhealthy connection after timeout."),this.close()))}),Math.floor(r)))}nextTransportId_(){return"c:"+this.id+":"+this.connectionCount++}disconnReceiver_(e){return t=>{e===this.conn_?this.onConnectionLost_(t):e===this.secondaryConn_?(this.log_("Secondary connection lost."),this.onSecondaryConnectionLost_()):this.log_("closing an old connection")}}connReceiver_(e){return t=>{2!==this.state_&&(e===this.rx_?this.onPrimaryMessageReceived_(t):e===this.secondaryConn_?this.onSecondaryMessageReceived_(t):this.log_("message on old connection"))}}sendRequest(e){const t={t:"d",d:e};this.sendData_(t)}tryCleanupConnection(){this.tx_===this.secondaryConn_&&this.rx_===this.secondaryConn_&&(this.log_("cleaning up and promoting a connection: "+this.secondaryConn_.connId),this.conn_=this.secondaryConn_,this.secondaryConn_=null)}onSecondaryControl_(e){if("t"in e){const t=e.t;"a"===t?this.upgradeIfSecondaryHealthy_():"r"===t?(this.log_("Got a reset on secondary, closing it"),this.secondaryConn_.close(),this.tx_!==this.secondaryConn_&&this.rx_!==this.secondaryConn_||this.close()):"o"===t&&(this.log_("got pong on secondary."),this.secondaryResponsesRequired_--,this.upgradeIfSecondaryHealthy_())}}onSecondaryMessageReceived_(e){const t=Us("t",e),n=Us("d",e);if("c"===t)this.onSecondaryControl_(n);else{if("d"!==t)throw new Error("Unknown protocol layer: "+t);this.pendingDataMessages.push(n)}}upgradeIfSecondaryHealthy_(){this.secondaryResponsesRequired_<=0?(this.log_("Secondary connection is healthy."),this.isHealthy_=!0,this.secondaryConn_.markConnectionHealthy(),this.proceedWithUpgrade_()):(this.log_("sending ping on secondary."),this.secondaryConn_.send({t:"c",d:{t:"p",d:{}}}))}proceedWithUpgrade_(){this.secondaryConn_.start(),this.log_("sending client ack on secondary"),this.secondaryConn_.send({t:"c",d:{t:"a",d:{}}}),this.log_("Ending transmission on primary"),this.conn_.send({t:"c",d:{t:"n",d:{}}}),this.tx_=this.secondaryConn_,this.tryCleanupConnection()}onPrimaryMessageReceived_(e){const t=Us("t",e),n=Us("d",e);"c"===t?this.onControl_(n):"d"===t&&this.onDataMessage_(n)}onDataMessage_(e){this.onPrimaryResponse_(),this.onMessage_(e)}onPrimaryResponse_(){this.isHealthy_||(this.primaryResponsesRequired_--,this.primaryResponsesRequired_<=0&&(this.log_("Primary connection is healthy."),this.isHealthy_=!0,this.conn_.markConnectionHealthy()))}onControl_(e){const t=Us("t",e);if("d"in e){const n=e.d;if("h"===t){const e=Object.assign({},n);this.repoInfo_.isUsingEmulator&&(e.h=this.repoInfo_.host),this.onHandshake_(e)}else if("n"===t){this.log_("recvd end transmission on primary"),this.rx_=this.secondaryConn_;for(let e=0;e<this.pendingDataMessages.length;++e)this.onDataMessage_(this.pendingDataMessages[e]);this.pendingDataMessages=[],this.tryCleanupConnection()}else"s"===t?this.onConnectionShutdown_(n):"r"===t?this.onReset_(n):"e"===t?Ps("Server Error: "+n):"o"===t?(this.log_("got pong on primary."),this.onPrimaryResponse_(),this.sendPingOnPrimaryIfNecessary_()):Ps("Unknown control packet command: "+t)}}onHandshake_(e){const t=e.ts,n=e.v,r=e.h;this.sessionId=e.s,this.repoInfo_.host=r,0===this.state_&&(this.conn_.start(),this.onConnectionEstablished_(this.conn_,t),"5"!==n&&Ns("Protocol version mismatch detected"),this.tryStartUpgrade_())}tryStartUpgrade_(){const e=this.transportManager_.upgradeTransport();e&&this.startUpgrade_(e)}startUpgrade_(e){this.secondaryConn_=new e(this.nextTransportId_(),this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,this.sessionId),this.secondaryResponsesRequired_=e.responsesRequiredToBeHealthy||0;const t=this.connReceiver_(this.secondaryConn_),n=this.disconnReceiver_(this.secondaryConn_);this.secondaryConn_.open(t,n),Ks((()=>{this.secondaryConn_&&(this.log_("Timed out trying to upgrade."),this.secondaryConn_.close())}),Math.floor(6e4))}onReset_(e){this.log_("Reset packet received.  New host: "+e),this.repoInfo_.host=e,1===this.state_?this.close():(this.closeConnections_(),this.start_())}onConnectionEstablished_(e,t){this.log_("Realtime connection established."),this.conn_=e,this.state_=1,this.onReady_&&(this.onReady_(t,this.sessionId),this.onReady_=null),0===this.primaryResponsesRequired_?(this.log_("Primary connection is healthy."),this.isHealthy_=!0):Ks((()=>{this.sendPingOnPrimaryIfNecessary_()}),Math.floor(5e3))}sendPingOnPrimaryIfNecessary_(){this.isHealthy_||1!==this.state_||(this.log_("sending ping on primary."),this.sendData_({t:"c",d:{t:"p",d:{}}}))}onSecondaryConnectionLost_(){const e=this.secondaryConn_;this.secondaryConn_=null,this.tx_!==e&&this.rx_!==e||this.close()}onConnectionLost_(e){this.conn_=null,e||0!==this.state_?1===this.state_&&this.log_("Realtime connection lost."):(this.log_("Realtime connection failed."),this.repoInfo_.isCacheableHost()&&(ws.remove("host:"+this.repoInfo_.host),this.repoInfo_.internalHost=this.repoInfo_.host)),this.close()}onConnectionShutdown_(e){this.log_("Connection shutdown command received. Shutting down..."),this.onKill_&&(this.onKill_(e),this.onKill_=null),this.onDisconnect_=null,this.close()}sendData_(e){if(1!==this.state_)throw"Connection is not connected";this.tx_.send(e)}close(){2!==this.state_&&(this.log_("Closing realtime connection."),this.state_=2,this.closeConnections_(),this.onDisconnect_&&(this.onDisconnect_(),this.onDisconnect_=null))}closeConnections_(){this.log_("Shutting down all connections"),this.conn_&&(this.conn_.close(),this.conn_=null),this.secondaryConn_&&(this.secondaryConn_.close(),this.secondaryConn_=null),this.healthyTimeout_&&(clearTimeout(this.healthyTimeout_),this.healthyTimeout_=null)}}class go{put(e,t,n,r){}merge(e,t,n,r){}refreshAuthToken(e){}refreshAppCheckToken(e){}onDisconnectPut(e,t,n){}onDisconnectMerge(e,t,n){}onDisconnectCancel(e,t){}reportStats(e){}}class _o{constructor(e){this.allowedEvents_=e,this.listeners_={},zt(Array.isArray(e)&&e.length>0,"Requires a non-empty array")}trigger(e,...t){if(Array.isArray(this.listeners_[e])){const n=[...this.listeners_[e]];for(let e=0;e<n.length;e++)n[e].callback.apply(n[e].context,t)}}on(e,t,n){this.validateEventType_(e),this.listeners_[e]=this.listeners_[e]||[],this.listeners_[e].push({callback:t,context:n});const r=this.getInitialEvent(e);r&&t.apply(n,r)}off(e,t,n){this.validateEventType_(e);const r=this.listeners_[e]||[];for(let i=0;i<r.length;i++)if(r[i].callback===t&&(!n||n===r[i].context))return void r.splice(i,1)}validateEventType_(e){zt(this.allowedEvents_.find((t=>t===e)),"Unknown event: "+e)}}class yo extends _o{static getInstance(){return new yo}constructor(){super(["online"]),this.online_=!0,"undefined"==typeof window||void 0===window.addEventListener||an()||(window.addEventListener("online",(()=>{this.online_||(this.online_=!0,this.trigger("online",!0))}),!1),window.addEventListener("offline",(()=>{this.online_&&(this.online_=!1,this.trigger("online",!1))}),!1))}getInitialEvent(e){return zt("online"===e,"Unknown event type: "+e),[this.online_]}currentlyOnline(){return this.online_}}class vo{constructor(e,t){if(void 0===t){this.pieces_=e.split("/");let t=0;for(let e=0;e<this.pieces_.length;e++)this.pieces_[e].length>0&&(this.pieces_[t]=this.pieces_[e],t++);this.pieces_.length=t,this.pieceNum_=0}else this.pieces_=e,this.pieceNum_=t}toString(){let e="";for(let t=this.pieceNum_;t<this.pieces_.length;t++)""!==this.pieces_[t]&&(e+="/"+this.pieces_[t]);return e||"/"}}function bo(){return new vo("")}function wo(e){return e.pieceNum_>=e.pieces_.length?null:e.pieces_[e.pieceNum_]}function Co(e){return e.pieces_.length-e.pieceNum_}function To(e){let t=e.pieceNum_;return t<e.pieces_.length&&t++,new vo(e.pieces_,t)}function ko(e){return e.pieceNum_<e.pieces_.length?e.pieces_[e.pieces_.length-1]:null}function So(e,t=0){return e.pieces_.slice(e.pieceNum_+t)}function Eo(e){if(e.pieceNum_>=e.pieces_.length)return null;const t=[];for(let n=e.pieceNum_;n<e.pieces_.length-1;n++)t.push(e.pieces_[n]);return new vo(t,0)}function Io(e,t){const n=[];for(let r=e.pieceNum_;r<e.pieces_.length;r++)n.push(e.pieces_[r]);if(t instanceof vo)for(let r=t.pieceNum_;r<t.pieces_.length;r++)n.push(t.pieces_[r]);else{const e=t.split("/");for(let t=0;t<e.length;t++)e[t].length>0&&n.push(e[t])}return new vo(n,0)}function xo(e){return e.pieceNum_>=e.pieces_.length}function Ro(e,t){const n=wo(e),r=wo(t);if(null===n)return t;if(n===r)return Ro(To(e),To(t));throw new Error("INTERNAL ERROR: innerPath ("+t+") is not within outerPath ("+e+")")}function Ao(e,t){if(Co(e)!==Co(t))return!1;for(let n=e.pieceNum_,r=t.pieceNum_;n<=e.pieces_.length;n++,r++)if(e.pieces_[n]!==t.pieces_[r])return!1;return!0}function Po(e,t){let n=e.pieceNum_,r=t.pieceNum_;if(Co(e)>Co(t))return!1;for(;n<e.pieces_.length;){if(e.pieces_[n]!==t.pieces_[r])return!1;++n,++r}return!0}class Oo{constructor(e,t){this.errorPrefix_=t,this.parts_=So(e,0),this.byteLength_=Math.max(1,this.parts_.length);for(let n=0;n<this.parts_.length;n++)this.byteLength_+=kn(this.parts_[n]);No(this)}}function No(e){if(e.byteLength_>768)throw new Error(e.errorPrefix_+"has a key path longer than 768 bytes ("+e.byteLength_+").");if(e.parts_.length>32)throw new Error(e.errorPrefix_+"path specified exceeds the maximum depth that can be written (32) or object contains a cycle "+Do(e))}function Do(e){return 0===e.parts_.length?"":"in property '"+e.parts_.join(".")+"'"}class Lo extends _o{static getInstance(){return new Lo}constructor(){let e,t;super(["visible"]),"undefined"!=typeof document&&void 0!==document.addEventListener&&(void 0!==document.hidden?(t="visibilitychange",e="hidden"):void 0!==document.mozHidden?(t="mozvisibilitychange",e="mozHidden"):void 0!==document.msHidden?(t="msvisibilitychange",e="msHidden"):void 0!==document.webkitHidden&&(t="webkitvisibilitychange",e="webkitHidden")),this.visible_=!0,t&&document.addEventListener(t,(()=>{const t=!document[e];t!==this.visible_&&(this.visible_=t,this.trigger("visible",t))}),!1)}getInitialEvent(e){return zt("visible"===e,"Unknown event type: "+e),[this.visible_]}}const Mo=1e3;class Fo extends go{constructor(e,t,n,r,i,s,o,a){if(super(),this.repoInfo_=e,this.applicationId_=t,this.onDataUpdate_=n,this.onConnectStatus_=r,this.onServerInfoUpdate_=i,this.authTokenProvider_=s,this.appCheckTokenProvider_=o,this.authOverride_=a,this.id=Fo.nextPersistentConnectionId_++,this.log_=As("p:"+this.id+":"),this.interruptReasons_={},this.listens=new Map,this.outstandingPuts_=[],this.outstandingGets_=[],this.outstandingPutCount_=0,this.outstandingGetCount_=0,this.onDisconnectRequestQueue_=[],this.connected_=!1,this.reconnectDelay_=Mo,this.maxReconnectDelay_=3e5,this.securityDebugCallback_=null,this.lastSessionId=null,this.establishConnectionTimer_=null,this.visible_=!1,this.requestCBHash_={},this.requestNumber_=0,this.realtime_=null,this.authToken_=null,this.appCheckToken_=null,this.forceTokenRefresh_=!1,this.invalidAuthTokenCount_=0,this.invalidAppCheckTokenCount_=0,this.firstConnection_=!0,this.lastConnectionAttemptTime_=null,this.lastConnectionEstablishedTime_=null,a)throw new Error("Auth override specified in options, but not supported on non Node.js platforms");Lo.getInstance().on("visible",this.onVisible_,this),-1===e.host.indexOf("fblocal")&&yo.getInstance().on("online",this.onOnline_,this)}sendRequest(e,t,n){const r=++this.requestNumber_,i={r:r,a:e,b:t};this.log_(mn(i)),zt(this.connected_,"sendRequest call when we're not connected not allowed."),this.realtime_.sendRequest(i),n&&(this.requestCBHash_[r]=n)}get(e){this.initConnection_();const t=new on,n={action:"g",request:{p:e._path.toString(),q:e._queryObject},onComplete:e=>{const n=e.d;"ok"===e.s?t.resolve(n):t.reject(n)}};this.outstandingGets_.push(n),this.outstandingGetCount_++;const r=this.outstandingGets_.length-1;return this.connected_&&this.sendGet_(r),t.promise}listen(e,t,n,r){this.initConnection_();const i=e._queryIdentifier,s=e._path.toString();this.log_("Listen called for "+s+" "+i),this.listens.has(s)||this.listens.set(s,new Map),zt(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"listen() called for non-default but complete query"),zt(!this.listens.get(s).has(i),"listen() called twice for same path/queryId.");const o={onComplete:r,hashFn:t,query:e,tag:n};this.listens.get(s).set(i,o),this.connected_&&this.sendListen_(o)}sendGet_(e){const t=this.outstandingGets_[e];this.sendRequest("g",t.request,(n=>{delete this.outstandingGets_[e],this.outstandingGetCount_--,0===this.outstandingGetCount_&&(this.outstandingGets_=[]),t.onComplete&&t.onComplete(n)}))}sendListen_(e){const t=e.query,n=t._path.toString(),r=t._queryIdentifier;this.log_("Listen on "+n+" for "+r);const i={p:n};e.tag&&(i.q=t._queryObject,i.t=e.tag),i.h=e.hashFn(),this.sendRequest("q",i,(i=>{const s=i.d,o=i.s;Fo.warnOnListenWarnings_(s,t);(this.listens.get(n)&&this.listens.get(n).get(r))===e&&(this.log_("listen response",i),"ok"!==o&&this.removeListen_(n,r),e.onComplete&&e.onComplete(o,s))}))}static warnOnListenWarnings_(e,t){if(e&&"object"==typeof e&&_n(e,"w")){const n=yn(e,"w");if(Array.isArray(n)&&~n.indexOf("no_index")){const e='".indexOn": "'+t._queryParams.getIndex().toString()+'"',n=t._path.toString();Ns(`Using an unspecified index. Your data will be downloaded and filtered on the client. Consider adding ${e} at ${n} to your security rules for better performance.`)}}}refreshAuthToken(e){this.authToken_=e,this.log_("Auth token refreshed"),this.authToken_?this.tryAuth():this.connected_&&this.sendRequest("unauth",{},(()=>{})),this.reduceReconnectDelayIfAdminCredential_(e)}reduceReconnectDelayIfAdminCredential_(e){(e&&40===e.length||function(e){const t=gn(e).claims;return"object"==typeof t&&!0===t.admin}(e))&&(this.log_("Admin auth credential detected.  Reducing max reconnect time."),this.maxReconnectDelay_=3e4)}refreshAppCheckToken(e){this.appCheckToken_=e,this.log_("App check token refreshed"),this.appCheckToken_?this.tryAppCheck():this.connected_&&this.sendRequest("unappeck",{},(()=>{}))}tryAuth(){if(this.connected_&&this.authToken_){const e=this.authToken_,t=function(e){const t=gn(e).claims;return!!t&&"object"==typeof t&&t.hasOwnProperty("iat")}(e)?"auth":"gauth",n={cred:e};null===this.authOverride_?n.noauth=!0:"object"==typeof this.authOverride_&&(n.authvar=this.authOverride_),this.sendRequest(t,n,(t=>{const n=t.s,r=t.d||"error";this.authToken_===e&&("ok"===n?this.invalidAuthTokenCount_=0:this.onAuthRevoked_(n,r))}))}}tryAppCheck(){this.connected_&&this.appCheckToken_&&this.sendRequest("appcheck",{token:this.appCheckToken_},(e=>{const t=e.s,n=e.d||"error";"ok"===t?this.invalidAppCheckTokenCount_=0:this.onAppCheckRevoked_(t,n)}))}unlisten(e,t){const n=e._path.toString(),r=e._queryIdentifier;this.log_("Unlisten called for "+n+" "+r),zt(e._queryParams.isDefault()||!e._queryParams.loadsAllData(),"unlisten() called for non-default but complete query");this.removeListen_(n,r)&&this.connected_&&this.sendUnlisten_(n,r,e._queryObject,t)}sendUnlisten_(e,t,n,r){this.log_("Unlisten on "+e+" for "+t);const i={p:e};r&&(i.q=n,i.t=r),this.sendRequest("n",i)}onDisconnectPut(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("o",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"o",data:t,onComplete:n})}onDisconnectMerge(e,t,n){this.initConnection_(),this.connected_?this.sendOnDisconnect_("om",e,t,n):this.onDisconnectRequestQueue_.push({pathString:e,action:"om",data:t,onComplete:n})}onDisconnectCancel(e,t){this.initConnection_(),this.connected_?this.sendOnDisconnect_("oc",e,null,t):this.onDisconnectRequestQueue_.push({pathString:e,action:"oc",data:null,onComplete:t})}sendOnDisconnect_(e,t,n,r){const i={p:t,d:n};this.log_("onDisconnect "+e,i),this.sendRequest(e,i,(e=>{r&&setTimeout((()=>{r(e.s,e.d)}),Math.floor(0))}))}put(e,t,n,r){this.putInternal("p",e,t,n,r)}merge(e,t,n,r){this.putInternal("m",e,t,n,r)}putInternal(e,t,n,r,i){this.initConnection_();const s={p:t,d:n};void 0!==i&&(s.h=i),this.outstandingPuts_.push({action:e,request:s,onComplete:r}),this.outstandingPutCount_++;const o=this.outstandingPuts_.length-1;this.connected_?this.sendPut_(o):this.log_("Buffering put: "+t)}sendPut_(e){const t=this.outstandingPuts_[e].action,n=this.outstandingPuts_[e].request,r=this.outstandingPuts_[e].onComplete;this.outstandingPuts_[e].queued=this.connected_,this.sendRequest(t,n,(n=>{this.log_(t+" response",n),delete this.outstandingPuts_[e],this.outstandingPutCount_--,0===this.outstandingPutCount_&&(this.outstandingPuts_=[]),r&&r(n.s,n.d)}))}reportStats(e){if(this.connected_){const t={c:e};this.log_("reportStats",t),this.sendRequest("s",t,(e=>{if("ok"!==e.s){const t=e.d;this.log_("reportStats","Error sending stats: "+t)}}))}}onDataMessage_(e){if("r"in e){this.log_("from server: "+mn(e));const t=e.r,n=this.requestCBHash_[t];n&&(delete this.requestCBHash_[t],n(e.b))}else{if("error"in e)throw"A server-side error has occurred: "+e.error;"a"in e&&this.onDataPush_(e.a,e.b)}}onDataPush_(e,t){this.log_("handleServerMessage",e,t),"d"===e?this.onDataUpdate_(t.p,t.d,!1,t.t):"m"===e?this.onDataUpdate_(t.p,t.d,!0,t.t):"c"===e?this.onListenRevoked_(t.p,t.q):"ac"===e?this.onAuthRevoked_(t.s,t.d):"apc"===e?this.onAppCheckRevoked_(t.s,t.d):"sd"===e?this.onSecurityDebugPacket_(t):Ps("Unrecognized action received from server: "+mn(e)+"\nAre you using the latest client?")}onReady_(e,t){this.log_("connection ready"),this.connected_=!0,this.lastConnectionEstablishedTime_=(new Date).getTime(),this.handleTimestamp_(e),this.lastSessionId=t,this.firstConnection_&&this.sendConnectStats_(),this.restoreState_(),this.firstConnection_=!1,this.onConnectStatus_(!0)}scheduleConnect_(e){zt(!this.realtime_,"Scheduling a connect when we're already connected/ing?"),this.establishConnectionTimer_&&clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=setTimeout((()=>{this.establishConnectionTimer_=null,this.establishConnection_()}),Math.floor(e))}initConnection_(){!this.realtime_&&this.firstConnection_&&this.scheduleConnect_(0)}onVisible_(e){e&&!this.visible_&&this.reconnectDelay_===this.maxReconnectDelay_&&(this.log_("Window became visible.  Reducing delay."),this.reconnectDelay_=Mo,this.realtime_||this.scheduleConnect_(0)),this.visible_=e}onOnline_(e){e?(this.log_("Browser went online."),this.reconnectDelay_=Mo,this.realtime_||this.scheduleConnect_(0)):(this.log_("Browser went offline.  Killing connection."),this.realtime_&&this.realtime_.close())}onRealtimeDisconnect_(){if(this.log_("data client disconnected"),this.connected_=!1,this.realtime_=null,this.cancelSentTransactions_(),this.requestCBHash_={},this.shouldReconnect_()){if(this.visible_){if(this.lastConnectionEstablishedTime_){(new Date).getTime()-this.lastConnectionEstablishedTime_>3e4&&(this.reconnectDelay_=Mo),this.lastConnectionEstablishedTime_=null}}else this.log_("Window isn't visible.  Delaying reconnect."),this.reconnectDelay_=this.maxReconnectDelay_,this.lastConnectionAttemptTime_=(new Date).getTime();const e=Math.max(0,(new Date).getTime()-this.lastConnectionAttemptTime_);let t=Math.max(0,this.reconnectDelay_-e);t=Math.random()*t,this.log_("Trying to reconnect in "+t+"ms"),this.scheduleConnect_(t),this.reconnectDelay_=Math.min(this.maxReconnectDelay_,1.3*this.reconnectDelay_)}this.onConnectStatus_(!1)}async establishConnection_(){if(this.shouldReconnect_()){this.log_("Making a connection attempt"),this.lastConnectionAttemptTime_=(new Date).getTime(),this.lastConnectionEstablishedTime_=null;const t=this.onDataMessage_.bind(this),n=this.onReady_.bind(this),r=this.onRealtimeDisconnect_.bind(this),i=this.id+":"+Fo.nextConnectionId_++,s=this.lastSessionId;let o=!1,a=null;const c=function(){a?a.close():(o=!0,r())},l=function(e){zt(a,"sendRequest call when we're not connected not allowed."),a.sendRequest(e)};this.realtime_={close:c,sendRequest:l};const u=this.forceTokenRefresh_;this.forceTokenRefresh_=!1;try{const[e,c]=await Promise.all([this.authTokenProvider_.getToken(u),this.appCheckTokenProvider_.getToken(u)]);o?Rs("getToken() completed but was canceled"):(Rs("getToken() completed. Creating connection."),this.authToken_=e&&e.accessToken,this.appCheckToken_=c&&c.token,a=new mo(i,this.repoInfo_,this.applicationId_,this.appCheckToken_,this.authToken_,t,n,r,(e=>{Ns(e+" ("+this.repoInfo_.toString()+")"),this.interrupt("server_kill")}),s))}catch(e){this.log_("Failed to get token: "+e),o||(this.repoInfo_.nodeAdmin&&Ns(e),c())}}}interrupt(e){Rs("Interrupting connection for reason: "+e),this.interruptReasons_[e]=!0,this.realtime_?this.realtime_.close():(this.establishConnectionTimer_&&(clearTimeout(this.establishConnectionTimer_),this.establishConnectionTimer_=null),this.connected_&&this.onRealtimeDisconnect_())}resume(e){Rs("Resuming connection for reason: "+e),delete this.interruptReasons_[e],vn(this.interruptReasons_)&&(this.reconnectDelay_=Mo,this.realtime_||this.scheduleConnect_(0))}handleTimestamp_(e){const t=e-(new Date).getTime();this.onServerInfoUpdate_({serverTimeOffset:t})}cancelSentTransactions_(){for(let e=0;e<this.outstandingPuts_.length;e++){const t=this.outstandingPuts_[e];t&&"h"in t.request&&t.queued&&(t.onComplete&&t.onComplete("disconnect"),delete this.outstandingPuts_[e],this.outstandingPutCount_--)}0===this.outstandingPutCount_&&(this.outstandingPuts_=[])}onListenRevoked_(e,t){let n;n=t?t.map((e=>Bs(e))).join("$"):"default";const r=this.removeListen_(e,n);r&&r.onComplete&&r.onComplete("permission_denied")}removeListen_(e,t){const n=new vo(e).toString();let r;if(this.listens.has(n)){const e=this.listens.get(n);r=e.get(t),e.delete(t),0===e.size&&this.listens.delete(n)}else r=void 0;return r}onAuthRevoked_(e,t){Rs("Auth token revoked: "+e+"/"+t),this.authToken_=null,this.forceTokenRefresh_=!0,this.realtime_.close(),"invalid_token"!==e&&"permission_denied"!==e||(this.invalidAuthTokenCount_++,this.invalidAuthTokenCount_>=3&&(this.reconnectDelay_=3e4,this.authTokenProvider_.notifyForInvalidToken()))}onAppCheckRevoked_(e,t){Rs("App check token revoked: "+e+"/"+t),this.appCheckToken_=null,this.forceTokenRefresh_=!0,"invalid_token"!==e&&"permission_denied"!==e||(this.invalidAppCheckTokenCount_++,this.invalidAppCheckTokenCount_>=3&&this.appCheckTokenProvider_.notifyForInvalidToken())}onSecurityDebugPacket_(e){this.securityDebugCallback_&&this.securityDebugCallback_(e)}restoreState_(){this.tryAuth(),this.tryAppCheck();for(const e of this.listens.values())for(const t of e.values())this.sendListen_(t);for(let e=0;e<this.outstandingPuts_.length;e++)this.outstandingPuts_[e]&&this.sendPut_(e);for(;this.onDisconnectRequestQueue_.length;){const e=this.onDisconnectRequestQueue_.shift();this.sendOnDisconnect_(e.action,e.pathString,e.data,e.onComplete)}for(let e=0;e<this.outstandingGets_.length;e++)this.outstandingGets_[e]&&this.sendGet_(e)}sendConnectStats_(){const e={};e["sdk.js."+_s.replace(/\./g,"-")]=1,an()?e["framework.cordova"]=1:"object"==typeof navigator&&"ReactNative"===navigator.product&&(e["framework.reactnative"]=1),this.reportStats(e)}shouldReconnect_(){const e=yo.getInstance().currentlyOnline();return vn(this.interruptReasons_)&&e}}Fo.nextPersistentConnectionId_=0,Fo.nextConnectionId_=0;class jo{constructor(e,t){this.name=e,this.node=t}static Wrap(e,t){return new jo(e,t)}}class Uo{getCompare(){return this.compare.bind(this)}indexedValueChanged(e,t){const n=new jo(Ls,e),r=new jo(Ls,t);return 0!==this.compare(n,r)}minPost(){return jo.MIN}}let Bo;class $o extends Uo{static get __EMPTY_NODE(){return Bo}static set __EMPTY_NODE(e){Bo=e}compare(e,t){return Fs(e.name,t.name)}isDefinedOn(e){throw Vt("KeyIndex.isDefinedOn not expected to be called.")}indexedValueChanged(e,t){return!1}minPost(){return jo.MIN}maxPost(){return new jo(Ms,Bo)}makePost(e,t){return zt("string"==typeof e,"KeyIndex indexValue must always be a string."),new jo(e,Bo)}toString(){return".key"}}const qo=new $o;class Wo{constructor(e,t,n,r,i=null){this.isReverse_=r,this.resultGenerator_=i,this.nodeStack_=[];let s=1;for(;!e.isEmpty();)if(s=t?n(e.key,t):1,r&&(s*=-1),s<0)e=this.isReverse_?e.left:e.right;else{if(0===s){this.nodeStack_.push(e);break}this.nodeStack_.push(e),e=this.isReverse_?e.right:e.left}}getNext(){if(0===this.nodeStack_.length)return null;let e,t=this.nodeStack_.pop();if(e=this.resultGenerator_?this.resultGenerator_(t.key,t.value):{key:t.key,value:t.value},this.isReverse_)for(t=t.left;!t.isEmpty();)this.nodeStack_.push(t),t=t.right;else for(t=t.right;!t.isEmpty();)this.nodeStack_.push(t),t=t.left;return e}hasNext(){return this.nodeStack_.length>0}peek(){if(0===this.nodeStack_.length)return null;const e=this.nodeStack_[this.nodeStack_.length-1];return this.resultGenerator_?this.resultGenerator_(e.key,e.value):{key:e.key,value:e.value}}}class Ho{constructor(e,t,n,r,i){this.key=e,this.value=t,this.color=null!=n?n:Ho.RED,this.left=null!=r?r:zo.EMPTY_NODE,this.right=null!=i?i:zo.EMPTY_NODE}copy(e,t,n,r,i){return new Ho(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=r?r:this.left,null!=i?i:this.right)}count(){return this.left.count()+1+this.right.count()}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||!!e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min_(){return this.left.isEmpty()?this:this.left.min_()}minKey(){return this.min_().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let r=this;const i=n(e,r.key);return r=i<0?r.copy(null,null,null,r.left.insert(e,t,n),null):0===i?r.copy(null,t,null,null,null):r.copy(null,null,null,null,r.right.insert(e,t,n)),r.fixUp_()}removeMin_(){if(this.left.isEmpty())return zo.EMPTY_NODE;let e=this;return e.left.isRed_()||e.left.left.isRed_()||(e=e.moveRedLeft_()),e=e.copy(null,null,null,e.left.removeMin_(),null),e.fixUp_()}remove(e,t){let n,r;if(n=this,t(e,n.key)<0)n.left.isEmpty()||n.left.isRed_()||n.left.left.isRed_()||(n=n.moveRedLeft_()),n=n.copy(null,null,null,n.left.remove(e,t),null);else{if(n.left.isRed_()&&(n=n.rotateRight_()),n.right.isEmpty()||n.right.isRed_()||n.right.left.isRed_()||(n=n.moveRedRight_()),0===t(e,n.key)){if(n.right.isEmpty())return zo.EMPTY_NODE;r=n.right.min_(),n=n.copy(r.key,r.value,null,null,n.right.removeMin_())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp_()}isRed_(){return this.color}fixUp_(){let e=this;return e.right.isRed_()&&!e.left.isRed_()&&(e=e.rotateLeft_()),e.left.isRed_()&&e.left.left.isRed_()&&(e=e.rotateRight_()),e.left.isRed_()&&e.right.isRed_()&&(e=e.colorFlip_()),e}moveRedLeft_(){let e=this.colorFlip_();return e.right.left.isRed_()&&(e=e.copy(null,null,null,null,e.right.rotateRight_()),e=e.rotateLeft_(),e=e.colorFlip_()),e}moveRedRight_(){let e=this.colorFlip_();return e.left.left.isRed_()&&(e=e.rotateRight_(),e=e.colorFlip_()),e}rotateLeft_(){const e=this.copy(null,null,Ho.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight_(){const e=this.copy(null,null,Ho.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip_(){const e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth_(){const e=this.check_();return Math.pow(2,e)<=this.count()+1}check_(){if(this.isRed_()&&this.left.isRed_())throw new Error("Red node has red child("+this.key+","+this.value+")");if(this.right.isRed_())throw new Error("Right child of ("+this.key+","+this.value+") is red");const e=this.left.check_();if(e!==this.right.check_())throw new Error("Black depths differ");return e+(this.isRed_()?0:1)}}Ho.RED=!0,Ho.BLACK=!1;class zo{constructor(e,t=zo.EMPTY_NODE){this.comparator_=e,this.root_=t}insert(e,t){return new zo(this.comparator_,this.root_.insert(e,t,this.comparator_).copy(null,null,Ho.BLACK,null,null))}remove(e){return new zo(this.comparator_,this.root_.remove(e,this.comparator_).copy(null,null,Ho.BLACK,null,null))}get(e){let t,n=this.root_;for(;!n.isEmpty();){if(t=this.comparator_(e,n.key),0===t)return n.value;t<0?n=n.left:t>0&&(n=n.right)}return null}getPredecessorKey(e){let t,n=this.root_,r=null;for(;!n.isEmpty();){if(t=this.comparator_(e,n.key),0===t){if(n.left.isEmpty())return r?r.key:null;for(n=n.left;!n.right.isEmpty();)n=n.right;return n.key}t<0?n=n.left:t>0&&(r=n,n=n.right)}throw new Error("Attempted to find predecessor key for a nonexistent key.  What gives?")}isEmpty(){return this.root_.isEmpty()}count(){return this.root_.count()}minKey(){return this.root_.minKey()}maxKey(){return this.root_.maxKey()}inorderTraversal(e){return this.root_.inorderTraversal(e)}reverseTraversal(e){return this.root_.reverseTraversal(e)}getIterator(e){return new Wo(this.root_,null,this.comparator_,!1,e)}getIteratorFrom(e,t){return new Wo(this.root_,e,this.comparator_,!1,t)}getReverseIteratorFrom(e,t){return new Wo(this.root_,e,this.comparator_,!0,t)}getReverseIterator(e){return new Wo(this.root_,null,this.comparator_,!0,e)}}function Vo(e,t){return Fs(e.name,t.name)}function Ko(e,t){return Fs(e,t)}let Go;zo.EMPTY_NODE=new class{copy(e,t,n,r,i){return this}insert(e,t,n){return new Ho(e,t,null)}remove(e,t){return this}count(){return 0}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}check_(){return 0}isRed_(){return!1}};const Yo=function(e){return"number"==typeof e?"number:"+Ws(e):"string:"+e},Jo=function(e){if(e.isLeafNode()){const t=e.val();zt("string"==typeof t||"number"==typeof t||"object"==typeof t&&_n(t,".sv"),"Priority must be a string or number.")}else zt(e===Go||e.isEmpty(),"priority of unexpected type.");zt(e===Go||e.getPriority().isEmpty(),"Priority nodes can't have a priority of their own.")};let Qo,Xo,Zo;class ea{static set __childrenNodeConstructor(e){Qo=e}static get __childrenNodeConstructor(){return Qo}constructor(e,t=ea.__childrenNodeConstructor.EMPTY_NODE){this.value_=e,this.priorityNode_=t,this.lazyHash_=null,zt(void 0!==this.value_&&null!==this.value_,"LeafNode shouldn't be created with null/undefined value."),Jo(this.priorityNode_)}isLeafNode(){return!0}getPriority(){return this.priorityNode_}updatePriority(e){return new ea(this.value_,e)}getImmediateChild(e){return".priority"===e?this.priorityNode_:ea.__childrenNodeConstructor.EMPTY_NODE}getChild(e){return xo(e)?this:".priority"===wo(e)?this.priorityNode_:ea.__childrenNodeConstructor.EMPTY_NODE}hasChild(){return!1}getPredecessorChildName(e,t){return null}updateImmediateChild(e,t){return".priority"===e?this.updatePriority(t):t.isEmpty()&&".priority"!==e?this:ea.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(e,t).updatePriority(this.priorityNode_)}updateChild(e,t){const n=wo(e);return null===n?t:t.isEmpty()&&".priority"!==n?this:(zt(".priority"!==n||1===Co(e),".priority must be the last token in a path"),this.updateImmediateChild(n,ea.__childrenNodeConstructor.EMPTY_NODE.updateChild(To(e),t)))}isEmpty(){return!1}numChildren(){return 0}forEachChild(e,t){return!1}val(e){return e&&!this.getPriority().isEmpty()?{".value":this.getValue(),".priority":this.getPriority().val()}:this.getValue()}hash(){if(null===this.lazyHash_){let e="";this.priorityNode_.isEmpty()||(e+="priority:"+Yo(this.priorityNode_.val())+":");const t=typeof this.value_;e+=t+":",e+="number"===t?Ws(this.value_):this.value_,this.lazyHash_=Ss(e)}return this.lazyHash_}getValue(){return this.value_}compareTo(e){return e===ea.__childrenNodeConstructor.EMPTY_NODE?1:e instanceof ea.__childrenNodeConstructor?-1:(zt(e.isLeafNode(),"Unknown node type"),this.compareToLeafNode_(e))}compareToLeafNode_(e){const t=typeof e.value_,n=typeof this.value_,r=ea.VALUE_TYPE_ORDER.indexOf(t),i=ea.VALUE_TYPE_ORDER.indexOf(n);return zt(r>=0,"Unknown leaf type: "+t),zt(i>=0,"Unknown leaf type: "+n),r===i?"object"===n?0:this.value_<e.value_?-1:this.value_===e.value_?0:1:i-r}withIndex(){return this}isIndexed(){return!0}equals(e){if(e===this)return!0;if(e.isLeafNode()){const t=e;return this.value_===t.value_&&this.priorityNode_.equals(t.priorityNode_)}return!1}}ea.VALUE_TYPE_ORDER=["object","boolean","number","string"];const ta=new class extends Uo{compare(e,t){const n=e.node.getPriority(),r=t.node.getPriority(),i=n.compareTo(r);return 0===i?Fs(e.name,t.name):i}isDefinedOn(e){return!e.getPriority().isEmpty()}indexedValueChanged(e,t){return!e.getPriority().equals(t.getPriority())}minPost(){return jo.MIN}maxPost(){return new jo(Ms,new ea("[PRIORITY-POST]",Zo))}makePost(e,t){const n=Xo(e);return new jo(t,new ea("[PRIORITY-POST]",n))}toString(){return".priority"}},na=Math.log(2);class ra{constructor(e){var t;this.count=(t=e+1,parseInt(Math.log(t)/na,10)),this.current_=this.count-1;const n=(r=this.count,parseInt(Array(r+1).join("1"),2));var r;this.bits_=e+1&n}nextBitIsOne(){const e=!(this.bits_&1<<this.current_);return this.current_--,e}}const ia=function(e,t,n,r){e.sort(t);const i=function(t,r){const s=r-t;let o,a;if(0===s)return null;if(1===s)return o=e[t],a=n?n(o):o,new Ho(a,o.node,Ho.BLACK,null,null);{const c=parseInt(s/2,10)+t,l=i(t,c),u=i(c+1,r);return o=e[c],a=n?n(o):o,new Ho(a,o.node,Ho.BLACK,l,u)}},s=function(t){let r=null,s=null,o=e.length;const a=function(t,r){const s=o-t,a=o;o-=t;const l=i(s+1,a),u=e[s],h=n?n(u):u;c(new Ho(h,u.node,r,null,l))},c=function(e){r?(r.left=e,r=e):(s=e,r=e)};for(let e=0;e<t.count;++e){const n=t.nextBitIsOne(),r=Math.pow(2,t.count-(e+1));n?a(r,Ho.BLACK):(a(r,Ho.BLACK),a(r,Ho.RED))}return s}(new ra(e.length));return new zo(r||t,s)};let sa;const oa={};class aa{static get Default(){return zt(oa&&ta,"ChildrenNode.ts has not been loaded"),sa=sa||new aa({".priority":oa},{".priority":ta}),sa}constructor(e,t){this.indexes_=e,this.indexSet_=t}get(e){const t=yn(this.indexes_,e);if(!t)throw new Error("No index defined for "+e);return t instanceof zo?t:null}hasIndex(e){return _n(this.indexSet_,e.toString())}addIndex(e,t){zt(e!==qo,"KeyIndex always exists and isn't meant to be added to the IndexMap.");const n=[];let r=!1;const i=t.getIterator(jo.Wrap);let s,o=i.getNext();for(;o;)r=r||e.isDefinedOn(o.node),n.push(o),o=i.getNext();s=r?ia(n,e.getCompare()):oa;const a=e.toString(),c=Object.assign({},this.indexSet_);c[a]=e;const l=Object.assign({},this.indexes_);return l[a]=s,new aa(l,c)}addToIndexes(e,t){const n=bn(this.indexes_,((n,r)=>{const i=yn(this.indexSet_,r);if(zt(i,"Missing index implementation for "+r),n===oa){if(i.isDefinedOn(e.node)){const n=[],r=t.getIterator(jo.Wrap);let s=r.getNext();for(;s;)s.name!==e.name&&n.push(s),s=r.getNext();return n.push(e),ia(n,i.getCompare())}return oa}{const r=t.get(e.name);let i=n;return r&&(i=i.remove(new jo(e.name,r))),i.insert(e,e.node)}}));return new aa(n,this.indexSet_)}removeFromIndexes(e,t){const n=bn(this.indexes_,(n=>{if(n===oa)return n;{const r=t.get(e.name);return r?n.remove(new jo(e.name,r)):n}}));return new aa(n,this.indexSet_)}}let ca;class la{static get EMPTY_NODE(){return ca||(ca=new la(new zo(Ko),null,aa.Default))}constructor(e,t,n){this.children_=e,this.priorityNode_=t,this.indexMap_=n,this.lazyHash_=null,this.priorityNode_&&Jo(this.priorityNode_),this.children_.isEmpty()&&zt(!this.priorityNode_||this.priorityNode_.isEmpty(),"An empty node cannot have a priority")}isLeafNode(){return!1}getPriority(){return this.priorityNode_||ca}updatePriority(e){return this.children_.isEmpty()?this:new la(this.children_,e,this.indexMap_)}getImmediateChild(e){if(".priority"===e)return this.getPriority();{const t=this.children_.get(e);return null===t?ca:t}}getChild(e){const t=wo(e);return null===t?this:this.getImmediateChild(t).getChild(To(e))}hasChild(e){return null!==this.children_.get(e)}updateImmediateChild(e,t){if(zt(t,"We should always be passing snapshot nodes"),".priority"===e)return this.updatePriority(t);{const n=new jo(e,t);let r,i;t.isEmpty()?(r=this.children_.remove(e),i=this.indexMap_.removeFromIndexes(n,this.children_)):(r=this.children_.insert(e,t),i=this.indexMap_.addToIndexes(n,this.children_));const s=r.isEmpty()?ca:this.priorityNode_;return new la(r,s,i)}}updateChild(e,t){const n=wo(e);if(null===n)return t;{zt(".priority"!==wo(e)||1===Co(e),".priority must be the last token in a path");const r=this.getImmediateChild(n).updateChild(To(e),t);return this.updateImmediateChild(n,r)}}isEmpty(){return this.children_.isEmpty()}numChildren(){return this.children_.count()}val(e){if(this.isEmpty())return null;const t={};let n=0,r=0,i=!0;if(this.forEachChild(ta,((s,o)=>{t[s]=o.val(e),n++,i&&la.INTEGER_REGEXP_.test(s)?r=Math.max(r,Number(s)):i=!1})),!e&&i&&r<2*n){const e=[];for(const n in t)e[n]=t[n];return e}return e&&!this.getPriority().isEmpty()&&(t[".priority"]=this.getPriority().val()),t}hash(){if(null===this.lazyHash_){let e="";this.getPriority().isEmpty()||(e+="priority:"+Yo(this.getPriority().val())+":"),this.forEachChild(ta,((t,n)=>{const r=n.hash();""!==r&&(e+=":"+t+":"+r)})),this.lazyHash_=""===e?"":Ss(e)}return this.lazyHash_}getPredecessorChildName(e,t,n){const r=this.resolveIndex_(n);if(r){const n=r.getPredecessorKey(new jo(e,t));return n?n.name:null}return this.children_.getPredecessorKey(e)}getFirstChildName(e){const t=this.resolveIndex_(e);if(t){const e=t.minKey();return e&&e.name}return this.children_.minKey()}getFirstChild(e){const t=this.getFirstChildName(e);return t?new jo(t,this.children_.get(t)):null}getLastChildName(e){const t=this.resolveIndex_(e);if(t){const e=t.maxKey();return e&&e.name}return this.children_.maxKey()}getLastChild(e){const t=this.getLastChildName(e);return t?new jo(t,this.children_.get(t)):null}forEachChild(e,t){const n=this.resolveIndex_(e);return n?n.inorderTraversal((e=>t(e.name,e.node))):this.children_.inorderTraversal(t)}getIterator(e){return this.getIteratorFrom(e.minPost(),e)}getIteratorFrom(e,t){const n=this.resolveIndex_(t);if(n)return n.getIteratorFrom(e,(e=>e));{const n=this.children_.getIteratorFrom(e.name,jo.Wrap);let r=n.peek();for(;null!=r&&t.compare(r,e)<0;)n.getNext(),r=n.peek();return n}}getReverseIterator(e){return this.getReverseIteratorFrom(e.maxPost(),e)}getReverseIteratorFrom(e,t){const n=this.resolveIndex_(t);if(n)return n.getReverseIteratorFrom(e,(e=>e));{const n=this.children_.getReverseIteratorFrom(e.name,jo.Wrap);let r=n.peek();for(;null!=r&&t.compare(r,e)>0;)n.getNext(),r=n.peek();return n}}compareTo(e){return this.isEmpty()?e.isEmpty()?0:-1:e.isLeafNode()||e.isEmpty()?1:e===ua?-1:0}withIndex(e){if(e===qo||this.indexMap_.hasIndex(e))return this;{const t=this.indexMap_.addIndex(e,this.children_);return new la(this.children_,this.priorityNode_,t)}}isIndexed(e){return e===qo||this.indexMap_.hasIndex(e)}equals(e){if(e===this)return!0;if(e.isLeafNode())return!1;{const t=e;if(this.getPriority().equals(t.getPriority())){if(this.children_.count()===t.children_.count()){const e=this.getIterator(ta),n=t.getIterator(ta);let r=e.getNext(),i=n.getNext();for(;r&&i;){if(r.name!==i.name||!r.node.equals(i.node))return!1;r=e.getNext(),i=n.getNext()}return null===r&&null===i}return!1}return!1}}resolveIndex_(e){return e===qo?null:this.indexMap_.get(e.toString())}}la.INTEGER_REGEXP_=/^(0|[1-9]\d*)$/;const ua=new class extends la{constructor(){super(new zo(Ko),la.EMPTY_NODE,aa.Default)}compareTo(e){return e===this?0:1}equals(e){return e===this}getPriority(){return this}getImmediateChild(e){return la.EMPTY_NODE}isEmpty(){return!1}};Object.defineProperties(jo,{MIN:{value:new jo(Ls,la.EMPTY_NODE)},MAX:{value:new jo(Ms,ua)}}),$o.__EMPTY_NODE=la.EMPTY_NODE,ea.__childrenNodeConstructor=la,Go=ua,function(e){Zo=e}(ua);function ha(e,t=null){if(null===e)return la.EMPTY_NODE;if("object"==typeof e&&".priority"in e&&(t=e[".priority"]),zt(null===t||"string"==typeof t||"number"==typeof t||"object"==typeof t&&".sv"in t,"Invalid priority type found: "+typeof t),"object"==typeof e&&".value"in e&&null!==e[".value"]&&(e=e[".value"]),"object"!=typeof e||".sv"in e){return new ea(e,ha(t))}if(e instanceof Array){let n=la.EMPTY_NODE;return qs(e,((t,r)=>{if(_n(e,t)&&"."!==t.substring(0,1)){const e=ha(r);!e.isLeafNode()&&e.isEmpty()||(n=n.updateImmediateChild(t,e))}})),n.updatePriority(ha(t))}{const n=[];let r=!1;if(qs(e,((e,t)=>{if("."!==e.substring(0,1)){const i=ha(t);i.isEmpty()||(r=r||!i.getPriority().isEmpty(),n.push(new jo(e,i)))}})),0===n.length)return la.EMPTY_NODE;const i=ia(n,Vo,(e=>e.name),Ko);if(r){const e=ia(n,ta.getCompare());return new la(i,ha(t),new aa({".priority":e},{".priority":ta}))}return new la(i,ha(t),aa.Default)}}!function(e){Xo=e}(ha);class da extends Uo{constructor(e){super(),this.indexPath_=e,zt(!xo(e)&&".priority"!==wo(e),"Can't create PathIndex with empty path or .priority key")}extractChild(e){return e.getChild(this.indexPath_)}isDefinedOn(e){return!e.getChild(this.indexPath_).isEmpty()}compare(e,t){const n=this.extractChild(e.node),r=this.extractChild(t.node),i=n.compareTo(r);return 0===i?Fs(e.name,t.name):i}makePost(e,t){const n=ha(e),r=la.EMPTY_NODE.updateChild(this.indexPath_,n);return new jo(t,r)}maxPost(){const e=la.EMPTY_NODE.updateChild(this.indexPath_,ua);return new jo(Ms,e)}toString(){return So(this.indexPath_,0).join("/")}}const pa=new class extends Uo{compare(e,t){const n=e.node.compareTo(t.node);return 0===n?Fs(e.name,t.name):n}isDefinedOn(e){return!0}indexedValueChanged(e,t){return!e.equals(t)}minPost(){return jo.MIN}maxPost(){return jo.MAX}makePost(e,t){const n=ha(e);return new jo(t,n)}toString(){return".value"}};function fa(e,t,n){return{type:"child_changed",snapshotNode:t,childName:e,oldSnap:n}}class ma{constructor(){this.limitSet_=!1,this.startSet_=!1,this.startNameSet_=!1,this.startAfterSet_=!1,this.endSet_=!1,this.endNameSet_=!1,this.endBeforeSet_=!1,this.limit_=0,this.viewFrom_="",this.indexStartValue_=null,this.indexStartName_="",this.indexEndValue_=null,this.indexEndName_="",this.index_=ta}hasStart(){return this.startSet_}isViewFromLeft(){return""===this.viewFrom_?this.startSet_:"l"===this.viewFrom_}getIndexStartValue(){return zt(this.startSet_,"Only valid if start has been set"),this.indexStartValue_}getIndexStartName(){return zt(this.startSet_,"Only valid if start has been set"),this.startNameSet_?this.indexStartName_:Ls}hasEnd(){return this.endSet_}getIndexEndValue(){return zt(this.endSet_,"Only valid if end has been set"),this.indexEndValue_}getIndexEndName(){return zt(this.endSet_,"Only valid if end has been set"),this.endNameSet_?this.indexEndName_:Ms}hasLimit(){return this.limitSet_}hasAnchoredLimit(){return this.limitSet_&&""!==this.viewFrom_}getLimit(){return zt(this.limitSet_,"Only valid if limit has been set"),this.limit_}getIndex(){return this.index_}loadsAllData(){return!(this.startSet_||this.endSet_||this.limitSet_)}isDefault(){return this.loadsAllData()&&this.index_===ta}copy(){const e=new ma;return e.limitSet_=this.limitSet_,e.limit_=this.limit_,e.startSet_=this.startSet_,e.startAfterSet_=this.startAfterSet_,e.indexStartValue_=this.indexStartValue_,e.startNameSet_=this.startNameSet_,e.indexStartName_=this.indexStartName_,e.endSet_=this.endSet_,e.endBeforeSet_=this.endBeforeSet_,e.indexEndValue_=this.indexEndValue_,e.endNameSet_=this.endNameSet_,e.indexEndName_=this.indexEndName_,e.index_=this.index_,e.viewFrom_=this.viewFrom_,e}}function ga(e){const t={};if(e.isDefault())return t;let n;if(e.index_===ta?n="$priority":e.index_===pa?n="$value":e.index_===qo?n="$key":(zt(e.index_ instanceof da,"Unrecognized index type!"),n=e.index_.toString()),t.orderBy=mn(n),e.startSet_){const n=e.startAfterSet_?"startAfter":"startAt";t[n]=mn(e.indexStartValue_),e.startNameSet_&&(t[n]+=","+mn(e.indexStartName_))}if(e.endSet_){const n=e.endBeforeSet_?"endBefore":"endAt";t[n]=mn(e.indexEndValue_),e.endNameSet_&&(t[n]+=","+mn(e.indexEndName_))}return e.limitSet_&&(e.isViewFromLeft()?t.limitToFirst=e.limit_:t.limitToLast=e.limit_),t}function _a(e){const t={};if(e.startSet_&&(t.sp=e.indexStartValue_,e.startNameSet_&&(t.sn=e.indexStartName_),t.sin=!e.startAfterSet_),e.endSet_&&(t.ep=e.indexEndValue_,e.endNameSet_&&(t.en=e.indexEndName_),t.ein=!e.endBeforeSet_),e.limitSet_){t.l=e.limit_;let n=e.viewFrom_;""===n&&(n=e.isViewFromLeft()?"l":"r"),t.vf=n}return e.index_!==ta&&(t.i=e.index_.toString()),t}class ya extends go{reportStats(e){throw new Error("Method not implemented.")}static getListenId_(e,t){return void 0!==t?"tag$"+t:(zt(e._queryParams.isDefault(),"should have a tag if it's not a default query."),e._path.toString())}constructor(e,t,n,r){super(),this.repoInfo_=e,this.onDataUpdate_=t,this.authTokenProvider_=n,this.appCheckTokenProvider_=r,this.log_=As("p:rest:"),this.listens_={}}listen(e,t,n,r){const i=e._path.toString();this.log_("Listen called for "+i+" "+e._queryIdentifier);const s=ya.getListenId_(e,n),o={};this.listens_[s]=o;const a=ga(e._queryParams);this.restRequest_(i+".json",a,((e,t)=>{let a=t;if(404===e&&(a=null,e=null),null===e&&this.onDataUpdate_(i,a,!1,n),yn(this.listens_,s)===o){let t;t=e?401===e?"permission_denied":"rest_error:"+e:"ok",r(t,null)}}))}unlisten(e,t){const n=ya.getListenId_(e,t);delete this.listens_[n]}get(e){const t=ga(e._queryParams),n=e._path.toString(),r=new on;return this.restRequest_(n+".json",t,((e,t)=>{let i=t;404===e&&(i=null,e=null),null===e?(this.onDataUpdate_(n,i,!1,null),r.resolve(i)):r.reject(new Error(i))})),r.promise}refreshAuthToken(e){}restRequest_(e,t={},n){return t.format="export",Promise.all([this.authTokenProvider_.getToken(!1),this.appCheckTokenProvider_.getToken(!1)]).then((([r,i])=>{r&&r.accessToken&&(t.auth=r.accessToken),i&&i.token&&(t.ac=i.token);const s=(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host+e+"?ns="+this.repoInfo_.namespace+function(e){const t=[];for(const[n,r]of Object.entries(e))Array.isArray(r)?r.forEach((e=>{t.push(encodeURIComponent(n)+"="+encodeURIComponent(e))})):t.push(encodeURIComponent(n)+"="+encodeURIComponent(r));return t.length?"&"+t.join("&"):""}(t);this.log_("Sending REST request for "+s);const o=new XMLHttpRequest;o.onreadystatechange=()=>{if(n&&4===o.readyState){this.log_("REST Response for "+s+" received. status:",o.status,"response:",o.responseText);let t=null;if(o.status>=200&&o.status<300){try{t=fn(o.responseText)}catch(e){Ns("Failed to parse JSON response for "+s+": "+o.responseText)}n(null,t)}else 401!==o.status&&404!==o.status&&Ns("Got unsuccessful REST response for "+s+" Status: "+o.status),n(o.status);n=null}},o.open("GET",s,!0),o.send()}))}}class va{constructor(){this.rootNode_=la.EMPTY_NODE}getNode(e){return this.rootNode_.getChild(e)}updateSnapshot(e,t){this.rootNode_=this.rootNode_.updateChild(e,t)}}function ba(){return{value:null,children:new Map}}function wa(e,t,n){if(xo(t))e.value=n,e.children.clear();else if(null!==e.value)e.value=e.value.updateChild(t,n);else{const r=wo(t);e.children.has(r)||e.children.set(r,ba());wa(e.children.get(r),t=To(t),n)}}function Ca(e,t,n){null!==e.value?n(t,e.value):function(e,t){e.children.forEach(((e,n)=>{t(n,e)}))}(e,((e,r)=>{Ca(r,new vo(t.toString()+"/"+e),n)}))}class Ta{constructor(e){this.collection_=e,this.last_=null}get(){const e=this.collection_.get(),t=Object.assign({},e);return this.last_&&qs(this.last_,((e,n)=>{t[e]=t[e]-n})),this.last_=e,t}}class ka{constructor(e,t){this.server_=t,this.statsToReport_={},this.statsListener_=new Ta(e);const n=1e4+2e4*Math.random();Ks(this.reportStats_.bind(this),Math.floor(n))}reportStats_(){const e=this.statsListener_.get(),t={};let n=!1;qs(e,((e,r)=>{r>0&&_n(this.statsToReport_,e)&&(t[e]=r,n=!0)})),n&&this.server_.reportStats(t),Ks(this.reportStats_.bind(this),Math.floor(2*Math.random()*3e5))}}var Sa,Ea;function Ia(e){return{fromUser:!1,fromServer:!0,queryId:e,tagged:!0}}(Ea=Sa||(Sa={}))[Ea.OVERWRITE=0]="OVERWRITE",Ea[Ea.MERGE=1]="MERGE",Ea[Ea.ACK_USER_WRITE=2]="ACK_USER_WRITE",Ea[Ea.LISTEN_COMPLETE=3]="LISTEN_COMPLETE";class xa{constructor(e,t,n){this.path=e,this.affectedTree=t,this.revert=n,this.type=Sa.ACK_USER_WRITE,this.source={fromUser:!0,fromServer:!1,queryId:null,tagged:!1}}operationForChild(e){if(xo(this.path)){if(null!=this.affectedTree.value)return zt(this.affectedTree.children.isEmpty(),"affectedTree should not have overlapping affected paths."),this;{const t=this.affectedTree.subtree(new vo(e));return new xa(bo(),t,this.revert)}}return zt(wo(this.path)===e,"operationForChild called for unrelated child."),new xa(To(this.path),this.affectedTree,this.revert)}}class Ra{constructor(e,t,n){this.source=e,this.path=t,this.snap=n,this.type=Sa.OVERWRITE}operationForChild(e){return xo(this.path)?new Ra(this.source,bo(),this.snap.getImmediateChild(e)):new Ra(this.source,To(this.path),this.snap)}}class Aa{constructor(e,t,n){this.source=e,this.path=t,this.children=n,this.type=Sa.MERGE}operationForChild(e){if(xo(this.path)){const t=this.children.subtree(new vo(e));return t.isEmpty()?null:t.value?new Ra(this.source,bo(),t.value):new Aa(this.source,bo(),t)}return zt(wo(this.path)===e,"Can't get a merge for a child not on the path of the operation"),new Aa(this.source,To(this.path),this.children)}toString(){return"Operation("+this.path+": "+this.source.toString()+" merge: "+this.children.toString()+")"}}class Pa{constructor(e,t,n){this.node_=e,this.fullyInitialized_=t,this.filtered_=n}isFullyInitialized(){return this.fullyInitialized_}isFiltered(){return this.filtered_}isCompleteForPath(e){if(xo(e))return this.isFullyInitialized()&&!this.filtered_;const t=wo(e);return this.isCompleteForChild(t)}isCompleteForChild(e){return this.isFullyInitialized()&&!this.filtered_||this.node_.hasChild(e)}getNode(){return this.node_}}function Oa(e,t,n,r,i,s){const o=r.filter((e=>e.type===n));o.sort(((t,n)=>function(e,t,n){if(null==t.childName||null==n.childName)throw Vt("Should only compare child_ events.");const r=new jo(t.childName,t.snapshotNode),i=new jo(n.childName,n.snapshotNode);return e.index_.compare(r,i)}(e,t,n))),o.forEach((n=>{const r=function(e,t,n){return"value"===t.type||"child_removed"===t.type||(t.prevName=n.getPredecessorChildName(t.childName,t.snapshotNode,e.index_)),t}(e,n,s);i.forEach((i=>{i.respondsTo(n.type)&&t.push(i.createEvent(r,e.query_))}))}))}function Na(e,t){return{eventCache:e,serverCache:t}}function Da(e,t,n,r){return Na(new Pa(t,n,r),e.serverCache)}function La(e,t,n,r){return Na(e.eventCache,new Pa(t,n,r))}function Ma(e){return e.eventCache.isFullyInitialized()?e.eventCache.getNode():null}function Fa(e){return e.serverCache.isFullyInitialized()?e.serverCache.getNode():null}let ja;class Ua{static fromObject(e){let t=new Ua(null);return qs(e,((e,n)=>{t=t.set(new vo(e),n)})),t}constructor(e,t=(()=>(ja||(ja=new zo(js)),ja))()){this.value=e,this.children=t}isEmpty(){return null===this.value&&this.children.isEmpty()}findRootMostMatchingPathAndValue(e,t){if(null!=this.value&&t(this.value))return{path:bo(),value:this.value};if(xo(e))return null;{const n=wo(e),r=this.children.get(n);if(null!==r){const i=r.findRootMostMatchingPathAndValue(To(e),t);if(null!=i){return{path:Io(new vo(n),i.path),value:i.value}}return null}return null}}findRootMostValueAndPath(e){return this.findRootMostMatchingPathAndValue(e,(()=>!0))}subtree(e){if(xo(e))return this;{const t=wo(e),n=this.children.get(t);return null!==n?n.subtree(To(e)):new Ua(null)}}set(e,t){if(xo(e))return new Ua(t,this.children);{const n=wo(e),r=(this.children.get(n)||new Ua(null)).set(To(e),t),i=this.children.insert(n,r);return new Ua(this.value,i)}}remove(e){if(xo(e))return this.children.isEmpty()?new Ua(null):new Ua(null,this.children);{const t=wo(e),n=this.children.get(t);if(n){const r=n.remove(To(e));let i;return i=r.isEmpty()?this.children.remove(t):this.children.insert(t,r),null===this.value&&i.isEmpty()?new Ua(null):new Ua(this.value,i)}return this}}get(e){if(xo(e))return this.value;{const t=wo(e),n=this.children.get(t);return n?n.get(To(e)):null}}setTree(e,t){if(xo(e))return t;{const n=wo(e),r=(this.children.get(n)||new Ua(null)).setTree(To(e),t);let i;return i=r.isEmpty()?this.children.remove(n):this.children.insert(n,r),new Ua(this.value,i)}}fold(e){return this.fold_(bo(),e)}fold_(e,t){const n={};return this.children.inorderTraversal(((r,i)=>{n[r]=i.fold_(Io(e,r),t)})),t(e,this.value,n)}findOnPath(e,t){return this.findOnPath_(e,bo(),t)}findOnPath_(e,t,n){const r=!!this.value&&n(t,this.value);if(r)return r;if(xo(e))return null;{const r=wo(e),i=this.children.get(r);return i?i.findOnPath_(To(e),Io(t,r),n):null}}foreachOnPath(e,t){return this.foreachOnPath_(e,bo(),t)}foreachOnPath_(e,t,n){if(xo(e))return this;{this.value&&n(t,this.value);const r=wo(e),i=this.children.get(r);return i?i.foreachOnPath_(To(e),Io(t,r),n):new Ua(null)}}foreach(e){this.foreach_(bo(),e)}foreach_(e,t){this.children.inorderTraversal(((n,r)=>{r.foreach_(Io(e,n),t)})),this.value&&t(e,this.value)}foreachChild(e){this.children.inorderTraversal(((t,n)=>{n.value&&e(t,n.value)}))}}class Ba{constructor(e){this.writeTree_=e}static empty(){return new Ba(new Ua(null))}}function $a(e,t,n){if(xo(t))return new Ba(new Ua(n));{const r=e.writeTree_.findRootMostValueAndPath(t);if(null!=r){const i=r.path;let s=r.value;const o=Ro(i,t);return s=s.updateChild(o,n),new Ba(e.writeTree_.set(i,s))}{const r=new Ua(n),i=e.writeTree_.setTree(t,r);return new Ba(i)}}}function qa(e,t,n){let r=e;return qs(n,((e,n)=>{r=$a(r,Io(t,e),n)})),r}function Wa(e,t){if(xo(t))return Ba.empty();{const n=e.writeTree_.setTree(t,new Ua(null));return new Ba(n)}}function Ha(e,t){return null!=za(e,t)}function za(e,t){const n=e.writeTree_.findRootMostValueAndPath(t);return null!=n?e.writeTree_.get(n.path).getChild(Ro(n.path,t)):null}function Va(e){const t=[],n=e.writeTree_.value;return null!=n?n.isLeafNode()||n.forEachChild(ta,((e,n)=>{t.push(new jo(e,n))})):e.writeTree_.children.inorderTraversal(((e,n)=>{null!=n.value&&t.push(new jo(e,n.value))})),t}function Ka(e,t){if(xo(t))return e;{const n=za(e,t);return new Ba(null!=n?new Ua(n):e.writeTree_.subtree(t))}}function Ga(e){return e.writeTree_.isEmpty()}function Ya(e,t){return Ja(bo(),e.writeTree_,t)}function Ja(e,t,n){if(null!=t.value)return n.updateChild(e,t.value);{let r=null;return t.children.inorderTraversal(((t,i)=>{".priority"===t?(zt(null!==i.value,"Priority writes must always be leaf nodes"),r=i.value):n=Ja(Io(e,t),i,n)})),n.getChild(e).isEmpty()||null===r||(n=n.updateChild(Io(e,".priority"),r)),n}}function Qa(e,t){return uc(t,e)}function Xa(e,t){const n=e.allWrites.findIndex((e=>e.writeId===t));zt(n>=0,"removeWrite called with nonexistent writeId.");const r=e.allWrites[n];e.allWrites.splice(n,1);let i=r.visible,s=!1,o=e.allWrites.length-1;for(;i&&o>=0;){const t=e.allWrites[o];t.visible&&(o>=n&&Za(t,r.path)?i=!1:Po(r.path,t.path)&&(s=!0)),o--}if(i){if(s)return function(e){e.visibleWrites=tc(e.allWrites,ec,bo()),e.allWrites.length>0?e.lastWriteId=e.allWrites[e.allWrites.length-1].writeId:e.lastWriteId=-1}(e),!0;if(r.snap)e.visibleWrites=Wa(e.visibleWrites,r.path);else{qs(r.children,(t=>{e.visibleWrites=Wa(e.visibleWrites,Io(r.path,t))}))}return!0}return!1}function Za(e,t){if(e.snap)return Po(e.path,t);for(const n in e.children)if(e.children.hasOwnProperty(n)&&Po(Io(e.path,n),t))return!0;return!1}function ec(e){return e.visible}function tc(e,t,n){let r=Ba.empty();for(let i=0;i<e.length;++i){const s=e[i];if(t(s)){const e=s.path;let t;if(s.snap)Po(n,e)?(t=Ro(n,e),r=$a(r,t,s.snap)):Po(e,n)&&(t=Ro(e,n),r=$a(r,bo(),s.snap.getChild(t)));else{if(!s.children)throw Vt("WriteRecord should have .snap or .children");if(Po(n,e))t=Ro(n,e),r=qa(r,t,s.children);else if(Po(e,n))if(t=Ro(e,n),xo(t))r=qa(r,bo(),s.children);else{const e=yn(s.children,wo(t));if(e){const n=e.getChild(To(t));r=$a(r,bo(),n)}}}}}return r}function nc(e,t,n,r,i){if(r||i){const s=Ka(e.visibleWrites,t);if(!i&&Ga(s))return n;if(i||null!=n||Ha(s,bo())){const s=function(e){return(e.visible||i)&&(!r||!~r.indexOf(e.writeId))&&(Po(e.path,t)||Po(t,e.path))};return Ya(tc(e.allWrites,s,t),n||la.EMPTY_NODE)}return null}{const r=za(e.visibleWrites,t);if(null!=r)return r;{const r=Ka(e.visibleWrites,t);if(Ga(r))return n;if(null!=n||Ha(r,bo())){return Ya(r,n||la.EMPTY_NODE)}return null}}}function rc(e,t,n,r){return nc(e.writeTree,e.treePath,t,n,r)}function ic(e,t){return function(e,t,n){let r=la.EMPTY_NODE;const i=za(e.visibleWrites,t);if(i)return i.isLeafNode()||i.forEachChild(ta,((e,t)=>{r=r.updateImmediateChild(e,t)})),r;if(n){const i=Ka(e.visibleWrites,t);return n.forEachChild(ta,((e,t)=>{const n=Ya(Ka(i,new vo(e)),t);r=r.updateImmediateChild(e,n)})),Va(i).forEach((e=>{r=r.updateImmediateChild(e.name,e.node)})),r}return Va(Ka(e.visibleWrites,t)).forEach((e=>{r=r.updateImmediateChild(e.name,e.node)})),r}(e.writeTree,e.treePath,t)}function sc(e,t,n,r){return function(e,t,n,r,i){zt(r||i,"Either existingEventSnap or existingServerSnap must exist");const s=Io(t,n);if(Ha(e.visibleWrites,s))return null;{const t=Ka(e.visibleWrites,s);return Ga(t)?i.getChild(n):Ya(t,i.getChild(n))}}(e.writeTree,e.treePath,t,n,r)}function oc(e,t){return function(e,t){return za(e.visibleWrites,t)}(e.writeTree,Io(e.treePath,t))}function ac(e,t,n,r,i,s){return function(e,t,n,r,i,s,o){let a;const c=Ka(e.visibleWrites,t),l=za(c,bo());if(null!=l)a=l;else{if(null==n)return[];a=Ya(c,n)}if(a=a.withIndex(o),a.isEmpty()||a.isLeafNode())return[];{const e=[],t=o.getCompare(),n=s?a.getReverseIteratorFrom(r,o):a.getIteratorFrom(r,o);let c=n.getNext();for(;c&&e.length<i;)0!==t(c,r)&&e.push(c),c=n.getNext();return e}}(e.writeTree,e.treePath,t,n,r,i,s)}function cc(e,t,n){return function(e,t,n,r){const i=Io(t,n),s=za(e.visibleWrites,i);if(null!=s)return s;if(r.isCompleteForChild(n))return Ya(Ka(e.visibleWrites,i),r.getNode().getImmediateChild(n));return null}(e.writeTree,e.treePath,t,n)}function lc(e,t){return uc(Io(e.treePath,t),e.writeTree)}function uc(e,t){return{treePath:e,writeTree:t}}class hc{constructor(){this.changeMap=new Map}trackChildChange(e){const t=e.type,n=e.childName;zt("child_added"===t||"child_changed"===t||"child_removed"===t,"Only child changes supported for tracking"),zt(".priority"!==n,"Only non-priority child changes can be tracked.");const r=this.changeMap.get(n);if(r){const s=r.type;if("child_added"===t&&"child_removed"===s)this.changeMap.set(n,fa(n,e.snapshotNode,r.snapshotNode));else if("child_removed"===t&&"child_added"===s)this.changeMap.delete(n);else if("child_removed"===t&&"child_changed"===s)this.changeMap.set(n,(i=n,{type:"child_removed",snapshotNode:r.oldSnap,childName:i}));else if("child_changed"===t&&"child_added"===s)this.changeMap.set(n,function(e,t){return{type:"child_added",snapshotNode:t,childName:e}}(n,e.snapshotNode));else{if("child_changed"!==t||"child_changed"!==s)throw Vt("Illegal combination of changes: "+e+" occurred after "+r);this.changeMap.set(n,fa(n,e.snapshotNode,r.oldSnap))}}else this.changeMap.set(n,e);var i}getChanges(){return Array.from(this.changeMap.values())}}const dc=new class{getCompleteChild(e){return null}getChildAfterChild(e,t,n){return null}};class pc{constructor(e,t,n=null){this.writes_=e,this.viewCache_=t,this.optCompleteServerCache_=n}getCompleteChild(e){const t=this.viewCache_.eventCache;if(t.isCompleteForChild(e))return t.getNode().getImmediateChild(e);{const t=null!=this.optCompleteServerCache_?new Pa(this.optCompleteServerCache_,!0,!1):this.viewCache_.serverCache;return cc(this.writes_,e,t)}}getChildAfterChild(e,t,n){const r=null!=this.optCompleteServerCache_?this.optCompleteServerCache_:Fa(this.viewCache_),i=ac(this.writes_,r,t,1,n,e);return 0===i.length?null:i[0]}}function fc(e,t,n,r,i){const s=new hc;let o,a;if(n.type===Sa.OVERWRITE){const c=n;c.source.fromUser?o=_c(e,t,c.path,c.snap,r,i,s):(zt(c.source.fromServer,"Unknown source."),a=c.source.tagged||t.serverCache.isFiltered()&&!xo(c.path),o=gc(e,t,c.path,c.snap,r,i,a,s))}else if(n.type===Sa.MERGE){const c=n;c.source.fromUser?o=function(e,t,n,r,i,s,o){let a=t;return r.foreach(((r,c)=>{const l=Io(n,r);yc(t,wo(l))&&(a=_c(e,a,l,c,i,s,o))})),r.foreach(((r,c)=>{const l=Io(n,r);yc(t,wo(l))||(a=_c(e,a,l,c,i,s,o))})),a}(e,t,c.path,c.children,r,i,s):(zt(c.source.fromServer,"Unknown source."),a=c.source.tagged||t.serverCache.isFiltered(),o=bc(e,t,c.path,c.children,r,i,a,s))}else if(n.type===Sa.ACK_USER_WRITE){const a=n;o=a.revert?function(e,t,n,r,i,s){let o;if(null!=oc(r,n))return t;{const a=new pc(r,t,i),c=t.eventCache.getNode();let l;if(xo(n)||".priority"===wo(n)){let n;if(t.serverCache.isFullyInitialized())n=rc(r,Fa(t));else{const e=t.serverCache.getNode();zt(e instanceof la,"serverChildren would be complete if leaf node"),n=ic(r,e)}l=e.filter.updateFullNode(c,n,s)}else{const i=wo(n);let u=cc(r,i,t.serverCache);null==u&&t.serverCache.isCompleteForChild(i)&&(u=c.getImmediateChild(i)),l=null!=u?e.filter.updateChild(c,i,u,To(n),a,s):t.eventCache.getNode().hasChild(i)?e.filter.updateChild(c,i,la.EMPTY_NODE,To(n),a,s):c,l.isEmpty()&&t.serverCache.isFullyInitialized()&&(o=rc(r,Fa(t)),o.isLeafNode()&&(l=e.filter.updateFullNode(l,o,s)))}return o=t.serverCache.isFullyInitialized()||null!=oc(r,bo()),Da(t,l,o,e.filter.filtersNodes())}}(e,t,a.path,r,i,s):function(e,t,n,r,i,s,o){if(null!=oc(i,n))return t;const a=t.serverCache.isFiltered(),c=t.serverCache;if(null!=r.value){if(xo(n)&&c.isFullyInitialized()||c.isCompleteForPath(n))return gc(e,t,n,c.getNode().getChild(n),i,s,a,o);if(xo(n)){let r=new Ua(null);return c.getNode().forEachChild(qo,((e,t)=>{r=r.set(new vo(e),t)})),bc(e,t,n,r,i,s,a,o)}return t}{let l=new Ua(null);return r.foreach(((e,t)=>{const r=Io(n,e);c.isCompleteForPath(r)&&(l=l.set(e,c.getNode().getChild(r)))})),bc(e,t,n,l,i,s,a,o)}}(e,t,a.path,a.affectedTree,r,i,s)}else{if(n.type!==Sa.LISTEN_COMPLETE)throw Vt("Unknown operation type: "+n.type);o=function(e,t,n,r,i){const s=t.serverCache,o=La(t,s.getNode(),s.isFullyInitialized()||xo(n),s.isFiltered());return mc(e,o,n,r,dc,i)}(e,t,n.path,r,s)}const c=s.getChanges();return function(e,t,n){const r=t.eventCache;if(r.isFullyInitialized()){const i=r.getNode().isLeafNode()||r.getNode().isEmpty(),s=Ma(e);(n.length>0||!e.eventCache.isFullyInitialized()||i&&!r.getNode().equals(s)||!r.getNode().getPriority().equals(s.getPriority()))&&n.push({type:"value",snapshotNode:Ma(t)})}}(t,o,c),{viewCache:o,changes:c}}function mc(e,t,n,r,i,s){const o=t.eventCache;if(null!=oc(r,n))return t;{let a,c;if(xo(n))if(zt(t.serverCache.isFullyInitialized(),"If change path is empty, we must have complete server data"),t.serverCache.isFiltered()){const n=Fa(t),i=ic(r,n instanceof la?n:la.EMPTY_NODE);a=e.filter.updateFullNode(t.eventCache.getNode(),i,s)}else{const n=rc(r,Fa(t));a=e.filter.updateFullNode(t.eventCache.getNode(),n,s)}else{const l=wo(n);if(".priority"===l){zt(1===Co(n),"Can't have a priority with additional path components");const i=o.getNode();c=t.serverCache.getNode();const s=sc(r,n,i,c);a=null!=s?e.filter.updatePriority(i,s):o.getNode()}else{const u=To(n);let h;if(o.isCompleteForChild(l)){c=t.serverCache.getNode();const e=sc(r,n,o.getNode(),c);h=null!=e?o.getNode().getImmediateChild(l).updateChild(u,e):o.getNode().getImmediateChild(l)}else h=cc(r,l,t.serverCache);a=null!=h?e.filter.updateChild(o.getNode(),l,h,u,i,s):o.getNode()}}return Da(t,a,o.isFullyInitialized()||xo(n),e.filter.filtersNodes())}}function gc(e,t,n,r,i,s,o,a){const c=t.serverCache;let l;const u=o?e.filter:e.filter.getIndexedFilter();if(xo(n))l=u.updateFullNode(c.getNode(),r,null);else if(u.filtersNodes()&&!c.isFiltered()){const e=c.getNode().updateChild(n,r);l=u.updateFullNode(c.getNode(),e,null)}else{const e=wo(n);if(!c.isCompleteForPath(n)&&Co(n)>1)return t;const i=To(n),s=c.getNode().getImmediateChild(e).updateChild(i,r);l=".priority"===e?u.updatePriority(c.getNode(),s):u.updateChild(c.getNode(),e,s,i,dc,null)}const h=La(t,l,c.isFullyInitialized()||xo(n),u.filtersNodes());return mc(e,h,n,i,new pc(i,h,s),a)}function _c(e,t,n,r,i,s,o){const a=t.eventCache;let c,l;const u=new pc(i,t,s);if(xo(n))l=e.filter.updateFullNode(t.eventCache.getNode(),r,o),c=Da(t,l,!0,e.filter.filtersNodes());else{const i=wo(n);if(".priority"===i)l=e.filter.updatePriority(t.eventCache.getNode(),r),c=Da(t,l,a.isFullyInitialized(),a.isFiltered());else{const s=To(n),l=a.getNode().getImmediateChild(i);let h;if(xo(s))h=r;else{const e=u.getCompleteChild(i);h=null!=e?".priority"===ko(s)&&e.getChild(Eo(s)).isEmpty()?e:e.updateChild(s,r):la.EMPTY_NODE}if(l.equals(h))c=t;else{c=Da(t,e.filter.updateChild(a.getNode(),i,h,s,u,o),a.isFullyInitialized(),e.filter.filtersNodes())}}}return c}function yc(e,t){return e.eventCache.isCompleteForChild(t)}function vc(e,t,n){return n.foreach(((e,n)=>{t=t.updateChild(e,n)})),t}function bc(e,t,n,r,i,s,o,a){if(t.serverCache.getNode().isEmpty()&&!t.serverCache.isFullyInitialized())return t;let c,l=t;c=xo(n)?r:new Ua(null).setTree(n,r);const u=t.serverCache.getNode();return c.children.inorderTraversal(((n,r)=>{if(u.hasChild(n)){const c=vc(0,t.serverCache.getNode().getImmediateChild(n),r);l=gc(e,l,new vo(n),c,i,s,o,a)}})),c.children.inorderTraversal(((n,r)=>{const c=!t.serverCache.isCompleteForChild(n)&&null===r.value;if(!u.hasChild(n)&&!c){const c=vc(0,t.serverCache.getNode().getImmediateChild(n),r);l=gc(e,l,new vo(n),c,i,s,o,a)}})),l}function wc(e,t){const n=Fa(e.viewCache_);return n&&(e.query._queryParams.loadsAllData()||!xo(t)&&!n.getImmediateChild(wo(t)).isEmpty())?n.getChild(t):null}function Cc(e,t,n,r){t.type===Sa.MERGE&&null!==t.source.queryId&&(zt(Fa(e.viewCache_),"We should always have a full cache before handling merges"),zt(Ma(e.viewCache_),"Missing event cache, even though we have a server cache"));const i=e.viewCache_,s=fc(e.processor_,i,t,n,r);var o,a;return o=e.processor_,a=s.viewCache,zt(a.eventCache.getNode().isIndexed(o.filter.getIndex()),"Event snap not indexed"),zt(a.serverCache.getNode().isIndexed(o.filter.getIndex()),"Server snap not indexed"),zt(s.viewCache.serverCache.isFullyInitialized()||!i.serverCache.isFullyInitialized(),"Once a server snap is complete, it should never go back"),e.viewCache_=s.viewCache,function(e,t,n){const r=e.eventRegistrations_;return function(e,t,n,r){const i=[],s=[];return t.forEach((t=>{var n;"child_changed"===t.type&&e.index_.indexedValueChanged(t.oldSnap,t.snapshotNode)&&s.push((n=t.childName,{type:"child_moved",snapshotNode:t.snapshotNode,childName:n}))})),Oa(e,i,"child_removed",t,r,n),Oa(e,i,"child_added",t,r,n),Oa(e,i,"child_moved",s,r,n),Oa(e,i,"child_changed",t,r,n),Oa(e,i,"value",t,r,n),i}(e.eventGenerator_,t,n,r)}(e,s.changes,s.viewCache.eventCache.getNode())}let Tc,kc;function Sc(e,t,n,r){const i=t.source.queryId;if(null!==i){const s=e.views.get(i);return zt(null!=s,"SyncTree gave us an op for an invalid query."),Cc(s,t,n,r)}{let i=[];for(const s of e.views.values())i=i.concat(Cc(s,t,n,r));return i}}function Ec(e,t){let n=null;for(const r of e.views.values())n=n||wc(r,t);return n}class Ic{constructor(e){this.listenProvider_=e,this.syncPointTree_=new Ua(null),this.pendingWriteTree_={visibleWrites:Ba.empty(),allWrites:[],lastWriteId:-1},this.tagToQueryMap=new Map,this.queryToTagMap=new Map}}function xc(e,t,n,r,i){return function(e,t,n,r,i){zt(r>e.lastWriteId,"Stacking an older write on top of newer ones"),void 0===i&&(i=!0),e.allWrites.push({path:t,snap:n,writeId:r,visible:i}),i&&(e.visibleWrites=$a(e.visibleWrites,t,n)),e.lastWriteId=r}(e.pendingWriteTree_,t,n,r,i),i?Oc(e,new Ra({fromUser:!0,fromServer:!1,queryId:null,tagged:!1},t,n)):[]}function Rc(e,t,n=!1){const r=function(e,t){for(let n=0;n<e.allWrites.length;n++){const r=e.allWrites[n];if(r.writeId===t)return r}return null}(e.pendingWriteTree_,t);if(Xa(e.pendingWriteTree_,t)){let t=new Ua(null);return null!=r.snap?t=t.set(bo(),!0):qs(r.children,(e=>{t=t.set(new vo(e),!0)})),Oc(e,new xa(r.path,t,n))}return[]}function Ac(e,t,n){return Oc(e,new Ra({fromUser:!1,fromServer:!0,queryId:null,tagged:!1},t,n))}function Pc(e,t,n){const r=e.pendingWriteTree_,i=e.syncPointTree_.findOnPath(t,((e,n)=>{const r=Ec(n,Ro(e,t));if(r)return r}));return nc(r,t,i,n,!0)}function Oc(e,t){return Nc(t,e.syncPointTree_,null,Qa(e.pendingWriteTree_,bo()))}function Nc(e,t,n,r){if(xo(e.path))return Dc(e,t,n,r);{const i=t.get(bo());null==n&&null!=i&&(n=Ec(i,bo()));let s=[];const o=wo(e.path),a=e.operationForChild(o),c=t.children.get(o);if(c&&a){const e=n?n.getImmediateChild(o):null,t=lc(r,o);s=s.concat(Nc(a,c,e,t))}return i&&(s=s.concat(Sc(i,e,r,n))),s}}function Dc(e,t,n,r){const i=t.get(bo());null==n&&null!=i&&(n=Ec(i,bo()));let s=[];return t.children.inorderTraversal(((t,i)=>{const o=n?n.getImmediateChild(t):null,a=lc(r,t),c=e.operationForChild(t);c&&(s=s.concat(Dc(c,i,o,a)))})),i&&(s=s.concat(Sc(i,e,r,n))),s}function Lc(e,t){return e.tagToQueryMap.get(t)}function Mc(e){const t=e.indexOf("$");return zt(-1!==t&&t<e.length-1,"Bad queryKey."),{queryId:e.substr(t+1),path:new vo(e.substr(0,t))}}function Fc(e,t,n){const r=e.syncPointTree_.get(t);zt(r,"Missing sync point for query tag that we're tracking");return Sc(r,n,Qa(e.pendingWriteTree_,t),null)}class jc{constructor(e){this.node_=e}getImmediateChild(e){const t=this.node_.getImmediateChild(e);return new jc(t)}node(){return this.node_}}class Uc{constructor(e,t){this.syncTree_=e,this.path_=t}getImmediateChild(e){const t=Io(this.path_,e);return new Uc(this.syncTree_,t)}node(){return Pc(this.syncTree_,this.path_)}}const Bc=function(e,t,n){return e&&"object"==typeof e?(zt(".sv"in e,"Unexpected leaf node or priority contents"),"string"==typeof e[".sv"]?$c(e[".sv"],t,n):"object"==typeof e[".sv"]?qc(e[".sv"],t):void zt(!1,"Unexpected server value: "+JSON.stringify(e,null,2))):e},$c=function(e,t,n){if("timestamp"===e)return n.timestamp;zt(!1,"Unexpected server value: "+e)},qc=function(e,t,n){e.hasOwnProperty("increment")||zt(!1,"Unexpected server value: "+JSON.stringify(e,null,2));const r=e.increment;"number"!=typeof r&&zt(!1,"Unexpected increment value: "+r);const i=t.node();if(zt(null!=i,"Expected ChildrenNode.EMPTY_NODE for nulls"),!i.isLeafNode())return r;const s=i.getValue();return"number"!=typeof s?r:s+r},Wc=function(e,t,n){return Hc(e,new jc(t),n)};function Hc(e,t,n){const r=e.getPriority().val(),i=Bc(r,t.getImmediateChild(".priority"),n);let s;if(e.isLeafNode()){const r=e,s=Bc(r.getValue(),t,n);return s!==r.getValue()||i!==r.getPriority().val()?new ea(s,ha(i)):e}{const r=e;return s=r,i!==r.getPriority().val()&&(s=s.updatePriority(new ea(i))),r.forEachChild(ta,((e,r)=>{const i=Hc(r,t.getImmediateChild(e),n);i!==r&&(s=s.updateImmediateChild(e,i))})),s}}class zc{constructor(e="",t=null,n={children:{},childCount:0}){this.name=e,this.parent=t,this.node=n}}function Vc(e,t){let n=t instanceof vo?t:new vo(t),r=e,i=wo(n);for(;null!==i;){const e=yn(r.node.children,i)||{children:{},childCount:0};r=new zc(i,r,e),n=To(n),i=wo(n)}return r}function Kc(e){return e.node.value}function Gc(e,t){e.node.value=t,Zc(e)}function Yc(e){return e.node.childCount>0}function Jc(e,t){qs(e.node.children,((n,r)=>{t(new zc(n,e,r))}))}function Qc(e,t,n,r){n&&t(e),Jc(e,(e=>{Qc(e,t,!0)}))}function Xc(e){return new vo(null===e.parent?e.name:Xc(e.parent)+"/"+e.name)}function Zc(e){null!==e.parent&&function(e,t,n){const r=function(e){return void 0===Kc(e)&&!Yc(e)}(n),i=_n(e.node.children,t);r&&i?(delete e.node.children[t],e.node.childCount--,Zc(e)):r||i||(e.node.children[t]=n.node,e.node.childCount++,Zc(e))}(e.parent,e.name,e)}const el=/[\[\].#$\/\u0000-\u001F\u007F]/,tl=/[\[\].#$\u0000-\u001F\u007F]/,nl=10485760,rl=function(e){return"string"==typeof e&&0!==e.length&&!el.test(e)},il=function(e){return e&&(e=e.replace(/^\/*\.info(\/|$)/,"/")),function(e){return"string"==typeof e&&0!==e.length&&!tl.test(e)}(e)},sl=function(e,t,n){const r=n instanceof vo?new Oo(n,e):n;if(void 0===t)throw new Error(e+"contains undefined "+Do(r));if("function"==typeof t)throw new Error(e+"contains a function "+Do(r)+" with contents = "+t.toString());if(Ds(t))throw new Error(e+"contains "+t.toString()+" "+Do(r));if("string"==typeof t&&t.length>nl/3&&kn(t)>nl)throw new Error(e+"contains a string greater than "+nl+" utf8 bytes "+Do(r)+" ('"+t.substring(0,50)+"...')");if(t&&"object"==typeof t){let n=!1,i=!1;if(qs(t,((t,s)=>{if(".value"===t)n=!0;else if(".priority"!==t&&".sv"!==t&&(i=!0,!rl(t)))throw new Error(e+" contains an invalid key ("+t+") "+Do(r)+'.  Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"');var o,a;a=t,(o=r).parts_.length>0&&(o.byteLength_+=1),o.parts_.push(a),o.byteLength_+=kn(a),No(o),sl(e,s,r),function(e){const t=e.parts_.pop();e.byteLength_-=kn(t),e.parts_.length>0&&(e.byteLength_-=1)}(r)})),n&&i)throw new Error(e+' contains ".value" child '+Do(r)+" in addition to actual children.")}};class ol{constructor(){this.eventLists_=[],this.recursionDepth_=0}}function al(e,t,n){!function(e,t){let n=null;for(let r=0;r<t.length;r++){const i=t[r],s=i.getPath();null===n||Ao(s,n.path)||(e.eventLists_.push(n),n=null),null===n&&(n={events:[],path:s}),n.events.push(i)}n&&e.eventLists_.push(n)}(e,n),function(e,t){e.recursionDepth_++;let n=!0;for(let r=0;r<e.eventLists_.length;r++){const i=e.eventLists_[r];if(i){t(i.path)?(cl(e.eventLists_[r]),e.eventLists_[r]=null):n=!1}}n&&(e.eventLists_=[]);e.recursionDepth_--}(e,(e=>Po(e,t)||Po(t,e)))}function cl(e){for(let t=0;t<e.events.length;t++){const n=e.events[t];if(null!==n){e.events[t]=null;const r=n.getEventRunner();Is&&Rs("event: "+n.toString()),Vs(r)}}}class ll{constructor(e,t,n,r){this.repoInfo_=e,this.forceRestClient_=t,this.authTokenProvider_=n,this.appCheckProvider_=r,this.dataUpdateCount=0,this.statsListener_=null,this.eventQueue_=new ol,this.nextWriteId_=1,this.interceptServerDataCallback_=null,this.onDisconnect_=ba(),this.transactionQueueTree_=new zc,this.persistentConnection_=null,this.key=this.repoInfo_.toURLString()}toString(){return(this.repoInfo_.secure?"https://":"http://")+this.repoInfo_.host}}function ul(e,t,n){if(e.stats_=oo(e.repoInfo_),e.forceRestClient_||("object"==typeof window&&window.navigator&&window.navigator.userAgent||"").search(/googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i)>=0)e.server_=new ya(e.repoInfo_,((t,n,r,i)=>{pl(e,t,n,r,i)}),e.authTokenProvider_,e.appCheckProvider_),setTimeout((()=>fl(e,!0)),0);else{if(null!=n){if("object"!=typeof n)throw new Error("Only objects are supported for option databaseAuthVariableOverride");try{mn(n)}catch(r){throw new Error("Invalid authOverride provided: "+r)}}e.persistentConnection_=new Fo(e.repoInfo_,t,((t,n,r,i)=>{pl(e,t,n,r,i)}),(t=>{fl(e,t)}),(t=>{!function(e,t){qs(t,((t,n)=>{ml(e,t,n)}))}(e,t)}),e.authTokenProvider_,e.appCheckProvider_,n),e.server_=e.persistentConnection_}e.authTokenProvider_.addTokenChangeListener((t=>{e.server_.refreshAuthToken(t)})),e.appCheckProvider_.addTokenChangeListener((t=>{e.server_.refreshAppCheckToken(t.token)})),e.statsReporter_=function(e,t){const n=e.toString();return so[n]||(so[n]=t()),so[n]}(e.repoInfo_,(()=>new ka(e.stats_,e.server_))),e.infoData_=new va,e.infoSyncTree_=new Ic({startListening:(t,n,r,i)=>{let s=[];const o=e.infoData_.getNode(t._path);return o.isEmpty()||(s=Ac(e.infoSyncTree_,t._path,o),setTimeout((()=>{i("ok")}),0)),s},stopListening:()=>{}}),ml(e,"connected",!1),e.serverSyncTree_=new Ic({startListening:(t,n,r,i)=>(e.server_.listen(t,r,n,((n,r)=>{const s=i(n,r);al(e.eventQueue_,t._path,s)})),[]),stopListening:(t,n)=>{e.server_.unlisten(t,n)}})}function hl(e){const t=e.infoData_.getNode(new vo(".info/serverTimeOffset")).val()||0;return(new Date).getTime()+t}function dl(e){return(t=(t={timestamp:hl(e)})||{}).timestamp=t.timestamp||(new Date).getTime(),t;var t}function pl(e,t,n,r,i){e.dataUpdateCount++;const s=new vo(t);n=e.interceptServerDataCallback_?e.interceptServerDataCallback_(t,n):n;let o=[];if(i)if(r){const t=bn(n,(e=>ha(e)));o=function(e,t,n,r){const i=Lc(e,r);if(i){const r=Mc(i),s=r.path,o=r.queryId,a=Ro(s,t),c=Ua.fromObject(n);return Fc(e,s,new Aa(Ia(o),a,c))}return[]}(e.serverSyncTree_,s,t,i)}else{const t=ha(n);o=function(e,t,n,r){const i=Lc(e,r);if(null!=i){const r=Mc(i),s=r.path,o=r.queryId,a=Ro(s,t);return Fc(e,s,new Ra(Ia(o),a,n))}return[]}(e.serverSyncTree_,s,t,i)}else if(r){const t=bn(n,(e=>ha(e)));o=function(e,t,n){const r=Ua.fromObject(n);return Oc(e,new Aa({fromUser:!1,fromServer:!0,queryId:null,tagged:!1},t,r))}(e.serverSyncTree_,s,t)}else{const t=ha(n);o=Ac(e.serverSyncTree_,s,t)}let a=s;o.length>0&&(a=bl(e,s)),al(e.eventQueue_,a,o)}function fl(e,t){ml(e,"connected",t),!1===t&&function(e){_l(e,"onDisconnectEvents");const t=dl(e),n=ba();Ca(e.onDisconnect_,bo(),((r,i)=>{const s=function(e,t,n,r){return Hc(t,new Uc(n,e),r)}(r,i,e.serverSyncTree_,t);wa(n,r,s)}));let r=[];Ca(n,bo(),((t,n)=>{r=r.concat(Ac(e.serverSyncTree_,t,n));const i=function(e,t){const n=Xc(wl(e,t)),r=Vc(e.transactionQueueTree_,t);return function(e,t){let n=e.parent;for(;null!==n;){if(t(n))return!0;n=n.parent}}(r,(t=>{Sl(e,t)})),Sl(e,r),Qc(r,(t=>{Sl(e,t)})),n}(e,t);bl(e,i)})),e.onDisconnect_=ba(),al(e.eventQueue_,bo(),r)}(e)}function ml(e,t,n){const r=new vo("/.info/"+t),i=ha(n);e.infoData_.updateSnapshot(r,i);const s=Ac(e.infoSyncTree_,r,i);al(e.eventQueue_,r,s)}function gl(e){return e.nextWriteId_++}function _l(e,...t){let n="";e.persistentConnection_&&(n=e.persistentConnection_.id+":"),Rs(n,...t)}function yl(e,t,n){return Pc(e.serverSyncTree_,t,n)||la.EMPTY_NODE}function vl(e,t=e.transactionQueueTree_){if(t||kl(e,t),Kc(t)){const n=Cl(e,t);zt(n.length>0,"Sending zero length transaction queue");n.every((e=>0===e.status))&&function(e,t,n){const r=n.map((e=>e.currentWriteId)),i=yl(e,t,r);let s=i;const o=i.hash();for(let l=0;l<n.length;l++){const e=n[l];zt(0===e.status,"tryToSendTransactionQueue_: items in queue should all be run."),e.status=1,e.retryCount++;const r=Ro(t,e.path);s=s.updateChild(r,e.currentOutputSnapshotRaw)}const a=s.val(!0),c=t;e.server_.put(c.toString(),a,(r=>{_l(e,"transaction put response",{path:c.toString(),status:r});let i=[];if("ok"===r){const r=[];for(let t=0;t<n.length;t++)n[t].status=2,i=i.concat(Rc(e.serverSyncTree_,n[t].currentWriteId)),n[t].onComplete&&r.push((()=>n[t].onComplete(null,!0,n[t].currentOutputSnapshotResolved))),n[t].unwatcher();kl(e,Vc(e.transactionQueueTree_,t)),vl(e,e.transactionQueueTree_),al(e.eventQueue_,t,i);for(let e=0;e<r.length;e++)Vs(r[e])}else{if("datastale"===r)for(let e=0;e<n.length;e++)3===n[e].status?n[e].status=4:n[e].status=0;else{Ns("transaction at "+c.toString()+" failed: "+r);for(let e=0;e<n.length;e++)n[e].status=4,n[e].abortReason=r}bl(e,t)}}),o)}(e,Xc(t),n)}else Yc(t)&&Jc(t,(t=>{vl(e,t)}))}function bl(e,t){const n=wl(e,t),r=Xc(n);return function(e,t,n){if(0===t.length)return;const r=[];let i=[];const s=t.filter((e=>0===e.status)),o=s.map((e=>e.currentWriteId));for(let c=0;c<t.length;c++){const s=t[c],l=Ro(n,s.path);let u,h=!1;if(zt(null!==l,"rerunTransactionsUnderNode_: relativePath should not be null."),4===s.status)h=!0,u=s.abortReason,i=i.concat(Rc(e.serverSyncTree_,s.currentWriteId,!0));else if(0===s.status)if(s.retryCount>=25)h=!0,u="maxretry",i=i.concat(Rc(e.serverSyncTree_,s.currentWriteId,!0));else{const n=yl(e,s.path,o);s.currentInputSnapshot=n;const r=t[c].update(n.val());if(void 0!==r){sl("transaction failed: Data returned ",r,s.path);let t=ha(r);"object"==typeof r&&null!=r&&_n(r,".priority")||(t=t.updatePriority(n.getPriority()));const a=s.currentWriteId,c=dl(e),l=Wc(t,n,c);s.currentOutputSnapshotRaw=t,s.currentOutputSnapshotResolved=l,s.currentWriteId=gl(e),o.splice(o.indexOf(a),1),i=i.concat(xc(e.serverSyncTree_,s.path,l,s.currentWriteId,s.applyLocally)),i=i.concat(Rc(e.serverSyncTree_,a,!0))}else h=!0,u="nodata",i=i.concat(Rc(e.serverSyncTree_,s.currentWriteId,!0))}al(e.eventQueue_,n,i),i=[],h&&(t[c].status=2,a=t[c].unwatcher,setTimeout(a,Math.floor(0)),t[c].onComplete&&("nodata"===u?r.push((()=>t[c].onComplete(null,!1,t[c].currentInputSnapshot))):r.push((()=>t[c].onComplete(new Error(u),!1,null)))))}var a;kl(e,e.transactionQueueTree_);for(let c=0;c<r.length;c++)Vs(r[c]);vl(e,e.transactionQueueTree_)}(e,Cl(e,n),r),r}function wl(e,t){let n,r=e.transactionQueueTree_;for(n=wo(t);null!==n&&void 0===Kc(r);)r=Vc(r,n),n=wo(t=To(t));return r}function Cl(e,t){const n=[];return Tl(e,t,n),n.sort(((e,t)=>e.order-t.order)),n}function Tl(e,t,n){const r=Kc(t);if(r)for(let i=0;i<r.length;i++)n.push(r[i]);Jc(t,(t=>{Tl(e,t,n)}))}function kl(e,t){const n=Kc(t);if(n){let e=0;for(let t=0;t<n.length;t++)2!==n[t].status&&(n[e]=n[t],e++);n.length=e,Gc(t,n.length>0?n:void 0)}Jc(t,(t=>{kl(e,t)}))}function Sl(e,t){const n=Kc(t);if(n){const r=[];let i=[],s=-1;for(let t=0;t<n.length;t++)3===n[t].status||(1===n[t].status?(zt(s===t-1,"All SENT items should be at beginning of queue."),s=t,n[t].status=3,n[t].abortReason="set"):(zt(0===n[t].status,"Unexpected transaction status in abort"),n[t].unwatcher(),i=i.concat(Rc(e.serverSyncTree_,n[t].currentWriteId,!0)),n[t].onComplete&&r.push(n[t].onComplete.bind(null,new Error("set"),!1,null))));-1===s?Gc(t,void 0):n.length=s+1,al(e.eventQueue_,Xc(t),i);for(let e=0;e<r.length;e++)Vs(r[e])}}const El=function(e,t){const n=Il(e),r=n.namespace;"firebase.com"===n.domain&&Os(n.host+" is no longer supported. Please use <YOUR FIREBASE>.firebaseio.com instead"),r&&"undefined"!==r||"localhost"===n.domain||Os("Cannot parse Firebase url. Please use https://<YOUR FIREBASE>.firebaseio.com"),n.secure||"undefined"!=typeof window&&window.location&&window.location.protocol&&-1!==window.location.protocol.indexOf("https:")&&Ns("Insecure Firebase access from a secure page. Please use https in calls to new Firebase().");const i="ws"===n.scheme||"wss"===n.scheme;return{repoInfo:new to(n.host,n.secure,r,i,t,"",r!==n.subdomain),path:new vo(n.pathString)}},Il=function(e){let t="",n="",r="",i="",s="",o=!0,a="https",c=443;if("string"==typeof e){let l=e.indexOf("//");l>=0&&(a=e.substring(0,l-1),e=e.substring(l+2));let u=e.indexOf("/");-1===u&&(u=e.length);let h=e.indexOf("?");-1===h&&(h=e.length),t=e.substring(0,Math.min(u,h)),u<h&&(i=function(e){let t="";const n=e.split("/");for(let i=0;i<n.length;i++)if(n[i].length>0){let e=n[i];try{e=decodeURIComponent(e.replace(/\+/g," "))}catch(r){}t+="/"+e}return t}(e.substring(u,h)));const d=function(e){const t={};"?"===e.charAt(0)&&(e=e.substring(1));for(const n of e.split("&")){if(0===n.length)continue;const r=n.split("=");2===r.length?t[decodeURIComponent(r[0])]=decodeURIComponent(r[1]):Ns(`Invalid query segment '${n}' in query '${e}'`)}return t}(e.substring(Math.min(e.length,h)));l=t.indexOf(":"),l>=0?(o="https"===a||"wss"===a,c=parseInt(t.substring(l+1),10)):l=t.length;const p=t.slice(0,l);if("localhost"===p.toLowerCase())n="localhost";else if(p.split(".").length<=2)n=p;else{const e=t.indexOf(".");r=t.substring(0,e).toLowerCase(),n=t.substring(e+1),s=r}"ns"in d&&(s=d.ns)}return{host:t,port:c,domain:n,subdomain:r,secure:o,scheme:a,pathString:i,namespace:s}};class xl{constructor(e,t,n,r){this._repo=e,this._path=t,this._queryParams=n,this._orderByCalled=r}get key(){return xo(this._path)?null:ko(this._path)}get ref(){return new Rl(this._repo,this._path)}get _queryIdentifier(){const e=_a(this._queryParams),t=Bs(e);return"{}"===t?"default":t}get _queryObject(){return _a(this._queryParams)}isEqual(e){if(!((e=En(e))instanceof xl))return!1;const t=this._repo===e._repo,n=Ao(this._path,e._path),r=this._queryIdentifier===e._queryIdentifier;return t&&n&&r}toJSON(){return this.toString()}toString(){return this._repo.toString()+function(e){let t="";for(let n=e.pieceNum_;n<e.pieces_.length;n++)""!==e.pieces_[n]&&(t+="/"+encodeURIComponent(String(e.pieces_[n])));return t||"/"}(this._path)}}class Rl extends xl{constructor(e,t){super(e,t,new ma,!1)}get parent(){const e=Eo(this._path);return null===e?null:new Rl(this._repo,e)}get root(){let e=this;for(;null!==e.parent;)e=e.parent;return e}}!function(e){zt(!Tc,"__referenceConstructor has already been defined"),Tc=e}(Rl),function(e){zt(!kc,"__referenceConstructor has already been defined"),kc=e}(Rl);const Al={};let Pl=!1;function Ol(e,t,n,r,i){let s=r||e.options.databaseURL;void 0===s&&(e.options.projectId||Os("Can't determine Firebase Database URL. Be sure to include  a Project ID when calling firebase.initializeApp()."),Rs("Using default host for project ",e.options.projectId),s=`${e.options.projectId}-default-rtdb.firebaseio.com`);let o,a=El(s,i),c=a.repoInfo;"undefined"!=typeof process&&fs&&(o=fs.FIREBASE_DATABASE_EMULATOR_HOST),o?(s=`http://${o}?ns=${c.namespace}`,a=El(s,i),c=a.repoInfo):a.repoInfo.secure;const l=new Ys(e.name,e.options,t);!function(e,t){const n=t.path.toString();if("string"!=typeof t.repoInfo.host||0===t.repoInfo.host.length||!rl(t.repoInfo.namespace)&&"localhost"!==t.repoInfo.host.split(":")[0]||0!==n.length&&!il(n))throw new Error(function(e){return`${e} failed: ${"url"} argument `}(e)+'must be a valid firebase URL and the path can\'t contain ".", "#", "$", "[", or "]".')}("Invalid Firebase Database URL",a),xo(a.path)||Os("Database URL must point to the root of a Firebase Database (not including a child path).");const u=function(e,t,n,r){let i=Al[t.name];i||(i={},Al[t.name]=i);let s=i[e.toURLString()];s&&Os("Database initialized multiple times. Please make sure the format of the database URL matches with each database() call.");return s=new ll(e,Pl,n,r),i[e.toURLString()]=s,s}(c,e,l,new Gs(e,n));return new Nl(u,e)}class Nl{constructor(e,t){this._repoInternal=e,this.app=t,this.type="database",this._instanceStarted=!1}get _repo(){return this._instanceStarted||(ul(this._repoInternal,this.app.options.appId,this.app.options.databaseAuthVariableOverride),this._instanceStarted=!0),this._repoInternal}get _root(){return this._rootInternal||(this._rootInternal=new Rl(this._repo,bo())),this._rootInternal}_delete(){return null!==this._rootInternal&&(!function(e,t){const n=Al[t];n&&n[e.key]===e||Os(`Database ${t}(${e.repoInfo_}) has already been deleted.`),function(e){e.persistentConnection_&&e.persistentConnection_.interrupt("repo_interrupt")}(e),delete n[e.key]}(this._repo,this.app.name),this._repoInternal=null,this._rootInternal=null),Promise.resolve()}_checkNotDeleted(e){null===this._rootInternal&&Os("Cannot call "+e+" on a deleted database.")}}function Dl(e=qr(),t){const n=jr(e,"database").getImmediate({identifier:t});if(!n._instanceStarted){const e=rn("database");e&&function(e,t,n,r={}){e=En(e),e._checkNotDeleted("useEmulator");const i=`${t}:${n}`,s=e._repoInternal;if(e._instanceStarted){if(i===e._repoInternal.repoInfo_.host&&wn(r,s.repoInfo_.emulatorOptions))return;Os("connectDatabaseEmulator() cannot initialize or alter the emulator configuration after the database instance has started.")}let o;if(s.repoInfo_.nodeAdmin)r.mockUserToken&&Os('mockUserToken is not supported by the Admin SDK. For client access with mock users, please use the "firebase" package instead of "firebase-admin".'),o=new Js(Js.OWNER);else if(r.mockUserToken){const t="string"==typeof r.mockUserToken?r.mockUserToken:function(e,t){if(e.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');const n=t||"demo-project",r=e.iat||0,i=e.sub||e.user_id;if(!i)throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");const s=Object.assign({iss:`https://securetoken.google.com/${n}`,aud:n,iat:r,exp:r+3600,auth_time:r,sub:i,user_id:i,firebase:{sign_in_provider:"custom",identities:{}}},e);return[Qt(JSON.stringify({alg:"none",type:"JWT"})),Qt(JSON.stringify(s)),""].join(".")}(r.mockUserToken,e.app.options.projectId);o=new Js(t)}!function(e,t,n,r){e.repoInfo_=new to(t,!1,e.repoInfo_.namespace,e.repoInfo_.webSocketOnly,e.repoInfo_.nodeAdmin,e.repoInfo_.persistenceKey,e.repoInfo_.includeNamespaceInQueryParams,!0,n),r&&(e.authTokenProvider_=r)}(s,i,r,o)}(n,...e)}return n}Fo.prototype.simpleListen=function(e,t){this.sendRequest("q",{p:e},t)},Fo.prototype.echo=function(e,t){this.sendRequest("echo",{d:e},t)},function(e){_s="11.4.0",Fr(new In("database",((e,{instanceIdentifier:t})=>Ol(e.getProvider("app").getImmediate(),e.getProvider("auth-internal"),e.getProvider("app-check-internal"),t)),"PUBLIC").setMultipleInstances(!0)),Wr(ms,gs,e),Wr(ms,gs,"esm2017")}();var Ll,Ml,Fl=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},jl=function(e){if("loading"===document.readyState)return"loading";var t=Fl();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},Ul=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},Bl=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var r=e,i=r.id?"#"+r.id:Ul(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?"."+r.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+i.length>(t||100)-1)return n||i;if(n=n?i+">"+n:i,r.id)break;e=r.parentNode}}catch(s){}return n},$l=-1,ql=function(e){addEventListener("pageshow",(function(t){t.persisted&&($l=t.timeStamp,e(t))}),!0)},Wl=function(){var e=Fl();return e&&e.activationStart||0},Hl=function(e,t){var n=Fl(),r="navigate";return $l>=0?r="back-forward-cache":n&&(document.prerendering||Wl()>0?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},zl=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(i){}},Vl=function(e,t,n,r){var i,s;return function(o){var a,c;t.value>=0&&(o||r)&&((s=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=s,t.rating=(a=t.value)>(c=n)[1]?"poor":a>c[0]?"needs-improvement":"good",e(t))}},Kl=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},Gl=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},Yl=function(e){var t=!1;return function(){t||(e(),t=!0)}},Jl=-1,Ql=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},Xl=function(e){"hidden"===document.visibilityState&&Jl>-1&&(Jl="visibilitychange"===e.type?e.timeStamp:0,eu())},Zl=function(){addEventListener("visibilitychange",Xl,!0),addEventListener("prerenderingchange",Xl,!0)},eu=function(){removeEventListener("visibilitychange",Xl,!0),removeEventListener("prerenderingchange",Xl,!0)},tu=function(){return Jl<0&&(Jl=Ql(),Zl(),ql((function(){setTimeout((function(){Jl=Ql(),Zl()}),0)}))),{get firstHiddenTime(){return Jl}}},nu=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},ru=[1800,3e3],iu=[.1,.25],su=function(e,t){var n,r;n=function(t){var n=function(e){var t,n={};if(e.entries.length){var r=e.entries.reduce((function(e,t){return e&&e.value>t.value?e:t}));if(r&&r.sources&&r.sources.length){var i=(t=r.sources).find((function(e){return e.node&&1===e.node.nodeType}))||t[0];i&&(n={largestShiftTarget:Bl(i.node),largestShiftTime:r.startTime,largestShiftValue:r.value,largestShiftSource:i,largestShiftEntry:r,loadState:jl(r.startTime)})}}return Object.assign(e,{attribution:n})}(t);e(n)},r=(r=t)||{},function(e,t){t=t||{},nu((function(){var n,r=tu(),i=Hl("FCP"),s=zl("paint",(function(e){e.forEach((function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-Wl(),0),i.entries.push(e),n(!0)))}))}));s&&(n=Vl(e,i,ru,t.reportAllChanges),ql((function(r){i=Hl("FCP"),n=Vl(e,i,ru,t.reportAllChanges),Kl((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))}(Yl((function(){var e,t=Hl("CLS",0),i=0,s=[],o=function(n){n.forEach((function(e){if(!e.hadRecentInput){var t=s[0],n=s[s.length-1];i&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,s.push(e)):(i=e.value,s=[e])}})),i>t.value&&(t.value=i,t.entries=s,e())},a=zl("layout-shift",o);a&&(e=Vl(n,t,iu,r.reportAllChanges),Gl((function(){o(a.takeRecords()),e(!0)})),ql((function(){i=0,t=Hl("CLS",0),e=Vl(n,t,iu,r.reportAllChanges),Kl((function(){return e()}))})),setTimeout(e,0))})))},ou=0,au=1/0,cu=0,lu=function(e){e.forEach((function(e){e.interactionId&&(au=Math.min(au,e.interactionId),cu=Math.max(cu,e.interactionId),ou=cu?(cu-au)/7+1:0)}))},uu=function(){return Ll?ou:performance.interactionCount||0},hu=[],du=new Map,pu=0,fu=[],mu=function(e){if(fu.forEach((function(t){return t(e)})),e.interactionId||"first-input"===e.entryType){var t=hu[hu.length-1],n=du.get(e.interactionId);if(n||hu.length<10||e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0].startTime&&n.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};du.set(r.id,r),hu.push(r)}hu.sort((function(e,t){return t.latency-e.latency})),hu.length>10&&hu.splice(10).forEach((function(e){return du.delete(e.id)}))}}},gu=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=Yl(e),"hidden"===document.visibilityState?e():(n=t(e),Gl(e)),n},_u=[200,500],yu=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},nu((function(){var n;"interactionCount"in performance||Ll||(Ll=zl("event",lu,{type:"event",buffered:!0,durationThreshold:0}));var r,i=Hl("INP"),s=function(e){gu((function(){e.forEach(mu);var t=function(){var e=Math.min(hu.length-1,Math.floor((uu()-pu)/50));return hu[e]}();t&&t.latency!==i.value&&(i.value=t.latency,i.entries=t.entries,r())}))},o=zl("event",s,{durationThreshold:null!==(n=t.durationThreshold)&&void 0!==n?n:40});r=Vl(e,i,_u,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),Gl((function(){s(o.takeRecords()),r(!0)})),ql((function(){pu=uu(),hu.length=0,du.clear(),i=Hl("INP"),r=Vl(e,i,_u,t.reportAllChanges)})))})))},vu=[],bu=[],wu=0,Cu=new WeakMap,Tu=new Map,ku=-1,Su=function(e){vu=vu.concat(e),Eu()},Eu=function(){ku<0&&(ku=gu(Iu))},Iu=function(){Tu.size>10&&Tu.forEach((function(e,t){du.has(t)||Tu.delete(t)}));var e=hu.map((function(e){return Cu.get(e.entries[0])})),t=bu.length-50;bu=bu.filter((function(n,r){return r>=t||e.includes(n)}));for(var n=new Set,r=0;r<bu.length;r++){var i=bu[r];xu(i.startTime,i.processingEnd).forEach((function(e){n.add(e)}))}var s=vu.length-1-50;vu=vu.filter((function(e,t){return e.startTime>wu&&t>s||n.has(e)})),ku=-1};fu.push((function(e){e.interactionId&&e.target&&!Tu.has(e.interactionId)&&Tu.set(e.interactionId,e.target)}),(function(e){var t,n=e.startTime+e.duration;wu=Math.max(wu,e.processingEnd);for(var r=bu.length-1;r>=0;r--){var i=bu[r];if(Math.abs(n-i.renderTime)<=8){(t=i).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},bu.push(t)),(e.interactionId||"first-input"===e.entryType)&&Cu.set(e,t),Eu()}));var xu=function(e,t){for(var n,r=[],i=0;n=vu[i];i++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;r.push(n)}return r},Ru=function(e,t){Ml||(Ml=zl("long-animation-frame",Su)),yu((function(t){var n,r,i,s,o,a,c,l,u,h,d,p,f=(r=(n=t).entries[0],i=Cu.get(r),s=r.processingStart,o=i.processingEnd,a=i.entries.sort((function(e,t){return e.processingStart-t.processingStart})),c=xu(r.startTime,o),l=n.entries.find((function(e){return e.target})),u=l&&l.target||Tu.get(r.interactionId),h=[r.startTime+r.duration,o].concat(c.map((function(e){return e.startTime+e.duration}))),d=Math.max.apply(Math,h),p={interactionTarget:Bl(u),interactionTargetElement:u,interactionType:r.name.startsWith("key")?"keyboard":"pointer",interactionTime:r.startTime,nextPaintTime:d,processedEventEntries:a,longAnimationFrameEntries:c,inputDelay:s-r.startTime,processingDuration:o-s,presentationDelay:Math.max(d-o,0),loadState:jl(r.startTime)},Object.assign(n,{attribution:p}));e(f)}),t)},Au=[2500,4e3],Pu={},Ou=function(e,t){var n,r;n=function(t){var n=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var n=Fl();if(n){var r=n.activationStart||0,i=e.entries[e.entries.length-1],s=i.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===i.url}))[0],o=Math.max(0,n.responseStart-r),a=Math.max(o,s?(s.requestStart||s.startTime)-r:0),c=Math.max(a,s?s.responseEnd-r:0),l=Math.max(c,i.startTime-r);t={element:Bl(i.element),timeToFirstByte:o,resourceLoadDelay:a-o,resourceLoadDuration:c-a,elementRenderDelay:l-c,navigationEntry:n,lcpEntry:i},i.url&&(t.url=i.url),s&&(t.lcpResourceEntry=s)}}return Object.assign(e,{attribution:t})}(t);e(n)},r=(r=t)||{},nu((function(){var e,t=tu(),i=Hl("LCP"),s=function(n){r.reportAllChanges||(n=n.slice(-1)),n.forEach((function(n){n.startTime<t.firstHiddenTime&&(i.value=Math.max(n.startTime-Wl(),0),i.entries=[n],e())}))},o=zl("largest-contentful-paint",s);if(o){e=Vl(n,i,Au,r.reportAllChanges);var a=Yl((function(){Pu[i.id]||(s(o.takeRecords()),o.disconnect(),Pu[i.id]=!0,e(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return gu(a)}),{once:!0,capture:!0})})),Gl(a),ql((function(t){i=Hl("LCP"),e=Vl(n,i,Au,r.reportAllChanges),Kl((function(){i.value=performance.now()-t.timeStamp,Pu[i.id]=!0,e(!0)}))}))}}))};const Nu="@firebase/performance",Du="0.7.1",Lu=Du,Mu="FB-PERF-TRACE-MEASURE",Fu="_wt_",ju="_fcp",Uu="_fid",Bu="_lcp",$u="_inp",qu="_cls",Wu="@firebase/performance/config",Hu="@firebase/performance/configexpire",zu="Performance",Vu=new dn("performance",zu,{"trace started":"Trace {$traceName} was started before.","trace stopped":"Trace {$traceName} is not running.","nonpositive trace startTime":"Trace {$traceName} startTime should be positive.","nonpositive trace duration":"Trace {$traceName} duration should be positive.","no window":"Window is not available.","no app id":"App id is not available.","no project id":"Project id is not available.","no api key":"Api key is not available.","invalid cc log":"Attempted to queue invalid cc event","FB not default":"Performance can only start when Firebase app instance is the default one.","RC response not ok":"RC response is not ok","invalid attribute name":"Attribute name {$attributeName} is invalid.","invalid attribute value":"Attribute value {$attributeValue} is invalid.","invalid custom metric name":"Custom metric name {$customMetricName} is invalid","invalid String merger input":"Input for String merger is invalid, contact support team to resolve.","already initialized":"initializePerformance() has already been called with different options. To avoid this error, call initializePerformance() with the same options as when it was originally called, or call getPerformance() to return the already initialized instance."}),Ku=new Fn(zu);let Gu,Yu,Ju,Qu;Ku.logLevel=Pn.INFO;class Xu{constructor(e){if(this.window=e,!e)throw Vu.create("no window");this.performance=e.performance,this.PerformanceObserver=e.PerformanceObserver,this.windowLocation=e.location,this.navigator=e.navigator,this.document=e.document,this.navigator&&this.navigator.cookieEnabled&&(this.localStorage=e.localStorage),e.perfMetrics&&e.perfMetrics.onFirstInputDelay&&(this.onFirstInputDelay=e.perfMetrics.onFirstInputDelay),this.onLCP=Ou,this.onINP=Ru,this.onCLS=su}getUrl(){return this.windowLocation.href.split("?")[0]}mark(e){this.performance&&this.performance.mark&&this.performance.mark(e)}measure(e,t,n){this.performance&&this.performance.measure&&this.performance.measure(e,t,n)}getEntriesByType(e){return this.performance&&this.performance.getEntriesByType?this.performance.getEntriesByType(e):[]}getEntriesByName(e){return this.performance&&this.performance.getEntriesByName?this.performance.getEntriesByName(e):[]}getTimeOrigin(){return this.performance&&(this.performance.timeOrigin||this.performance.timing.navigationStart)}requiredApisAvailable(){return fetch&&Promise&&un()?!!cn()||(Ku.info("IndexedDB is not supported by current browser"),!1):(Ku.info("Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled."),!1)}setupObserver(e,t){if(!this.PerformanceObserver)return;new this.PerformanceObserver((e=>{for(const n of e.getEntries())t(n)})).observe({entryTypes:[e]})}static getInstance(){return void 0===Gu&&(Gu=new Xu(Yu)),Gu}}function Zu(){return Ju}function eh(e,t){const n=e.length-t.length;if(n<0||n>1)throw Vu.create("invalid String merger input");const r=[];for(let i=0;i<e.length;i++)r.push(e.charAt(i)),t.length>i&&r.push(t.charAt(i));return r.join("")}class th{constructor(){this.instrumentationEnabled=!0,this.dataCollectionEnabled=!0,this.loggingEnabled=!1,this.tracesSamplingRate=1,this.networkRequestsSamplingRate=1,this.logEndPointUrl="https://firebaselogging.googleapis.com/v0cc/log?format=json_proto",this.flTransportEndpointUrl=eh("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),this.transportKey=eh("AzSC8r6ReiGqFMyfvgow","Iayx0u-XT3vksVM-pIV"),this.logSource=462,this.logTraceAfterSampling=!1,this.logNetworkAfterSampling=!1,this.configTimeToLive=12}getFlTransportFullUrl(){return this.flTransportEndpointUrl.concat("?key=",this.transportKey)}static getInstance(){return void 0===Qu&&(Qu=new th),Qu}}var nh,rh;(rh=nh||(nh={}))[rh.UNKNOWN=0]="UNKNOWN",rh[rh.VISIBLE=1]="VISIBLE",rh[rh.HIDDEN=2]="HIDDEN";const ih=["firebase_","google_","ga_"],sh=new RegExp("^[a-zA-Z]\\w*$");function oh(){const e=Xu.getInstance().navigator;return(null==e?void 0:e.serviceWorker)?e.serviceWorker.controller?2:3:1}function ah(){switch(Xu.getInstance().document.visibilityState){case"visible":return nh.VISIBLE;case"hidden":return nh.HIDDEN;default:return nh.UNKNOWN}}function ch(){const e=Xu.getInstance().navigator.connection;switch(e&&e.effectiveType){case"slow-2g":return 1;case"2g":return 2;case"3g":return 3;case"4g":return 4;default:return 0}}function lh(e){var t;const n=null===(t=e.options)||void 0===t?void 0:t.appId;if(!n)throw Vu.create("no app id");return n}const uh="0.0.1",hh={loggingEnabled:!0},dh="FIREBASE_INSTALLATIONS_AUTH";function ph(e,t){const n=function(){const e=Xu.getInstance().localStorage;if(!e)return;const t=e.getItem(Hu);if(!(t&&(n=t,Number(n)>Date.now())))return;var n;const r=e.getItem(Wu);if(!r)return;try{return JSON.parse(r)}catch(i){return}}();return n?(mh(n),Promise.resolve()):function(e,t){return function(e){const t=e.getToken();return t.then((e=>{})),t}(e.installations).then((n=>{const r=function(e){var t;const n=null===(t=e.options)||void 0===t?void 0:t.projectId;if(!n)throw Vu.create("no project id");return n}(e.app),i=function(e){var t;const n=null===(t=e.options)||void 0===t?void 0:t.apiKey;if(!n)throw Vu.create("no api key");return n}(e.app),s=new Request(`https://firebaseremoteconfig.googleapis.com/v1/projects/${r}/namespaces/fireperf:fetch?key=${i}`,{method:"POST",headers:{Authorization:`${dh} ${n}`},body:JSON.stringify({app_instance_id:t,app_instance_id_token:n,app_id:lh(e.app),app_version:Lu,sdk_version:uh})});return fetch(s).then((e=>{if(e.ok)return e.json();throw Vu.create("RC response not ok")}))})).catch((()=>{Ku.info(fh)}))}(e,t).then(mh).then((e=>function(e){const t=Xu.getInstance().localStorage;if(!e||!t)return;t.setItem(Wu,JSON.stringify(e)),t.setItem(Hu,String(Date.now()+60*th.getInstance().configTimeToLive*60*1e3))}(e)),(()=>{}))}const fh="Could not fetch config, will use default configs";function mh(e){if(!e)return e;const t=th.getInstance(),n=e.entries||{};return void 0!==n.fpr_enabled?t.loggingEnabled="true"===String(n.fpr_enabled):t.loggingEnabled=hh.loggingEnabled,n.fpr_log_source?t.logSource=Number(n.fpr_log_source):hh.logSource&&(t.logSource=hh.logSource),n.fpr_log_endpoint_url?t.logEndPointUrl=n.fpr_log_endpoint_url:hh.logEndPointUrl&&(t.logEndPointUrl=hh.logEndPointUrl),n.fpr_log_transport_key?t.transportKey=n.fpr_log_transport_key:hh.transportKey&&(t.transportKey=hh.transportKey),void 0!==n.fpr_vc_network_request_sampling_rate?t.networkRequestsSamplingRate=Number(n.fpr_vc_network_request_sampling_rate):void 0!==hh.networkRequestsSamplingRate&&(t.networkRequestsSamplingRate=hh.networkRequestsSamplingRate),void 0!==n.fpr_vc_trace_sampling_rate?t.tracesSamplingRate=Number(n.fpr_vc_trace_sampling_rate):void 0!==hh.tracesSamplingRate&&(t.tracesSamplingRate=hh.tracesSamplingRate),t.logTraceAfterSampling=gh(t.tracesSamplingRate),t.logNetworkAfterSampling=gh(t.networkRequestsSamplingRate),e}function gh(e){return Math.random()<=e}let _h,yh=1;function vh(e){return yh=2,_h=_h||function(e){return function(){const e=Xu.getInstance().document;return new Promise((t=>{if(e&&"complete"!==e.readyState){const n=()=>{"complete"===e.readyState&&(e.removeEventListener("readystatechange",n),t())};e.addEventListener("readystatechange",n)}else t()}))}().then((()=>function(e){const t=e.getId();return t.then((e=>{Ju=e})),t}(e.installations))).then((t=>ph(e,t))).then((()=>bh()),(()=>bh()))}(e),_h}function bh(){yh=3}let wh,Ch=3,Th=[],kh=!1;function Sh(e){setTimeout((()=>{0!==Ch&&(Th.length>0&&Eh(),Sh(1e4))}),e)}function Eh(){const e=Th.splice(0,1e3),t=e.map((e=>({source_extension_json_proto3:e.message,event_time_ms:String(e.eventTime)})));(function(e){const t=th.getInstance().getFlTransportFullUrl(),n=JSON.stringify(e);return navigator.sendBeacon&&navigator.sendBeacon(t,n)?Promise.resolve():fetch(t,{method:"POST",body:n,keepalive:!0}).then()})({request_time_ms:String(Date.now()),client_info:{client_type:1,js_client_info:{}},log_source:th.getInstance().logSource,log_event:t}).then((()=>{Ch=3})).catch((()=>{Th=[...e,...Th],Ch--,Ku.info(`Tries left: ${Ch}.`),Sh(1e4)}))}function Ih(e){return(...t)=>{!function(e){if(!e.eventTime||!e.message)throw Vu.create("invalid cc log");Th=[...Th,e]}({message:e(...t),eventTime:Date.now()})}}function xh(){for(;Th.length>0;)Eh()}function Rh(e,t){wh||(wh={send:Ih(Oh),flush:xh}),wh.send(e,t)}function Ah(e){const t=th.getInstance();!t.instrumentationEnabled&&e.isAuto||(t.dataCollectionEnabled||e.isAuto)&&Xu.getInstance().requiredApisAvailable()&&(3===yh?Ph(e):vh(e.performanceController).then((()=>Ph(e)),(()=>Ph(e))))}function Ph(e){if(!Zu())return;const t=th.getInstance();t.loggingEnabled&&t.logTraceAfterSampling&&Rh(e,1)}function Oh(e,t){return 0===t?function(e){const t={url:e.url,http_method:e.httpMethod||0,http_response_code:200,response_payload_bytes:e.responsePayloadBytes,client_start_time_us:e.startTimeUs,time_to_response_initiated_us:e.timeToResponseInitiatedUs,time_to_response_completed_us:e.timeToResponseCompletedUs},n={application_info:Nh(e.performanceController.app),network_request_metric:t};return JSON.stringify(n)}(e):function(e){const t={name:e.name,is_auto:e.isAuto,client_start_time_us:e.startTimeUs,duration_us:e.durationUs};0!==Object.keys(e.counters).length&&(t.counters=e.counters);const n=e.getAttributes();0!==Object.keys(n).length&&(t.custom_attributes=n);const r={application_info:Nh(e.performanceController.app),trace_metric:t};return JSON.stringify(r)}(e)}function Nh(e){return{google_app_id:lh(e),app_instance_id:Zu(),web_app_info:{sdk_version:Lu,page_url:Xu.getInstance().getUrl(),service_worker_status:oh(),visibility_state:ah(),effective_connection_type:ch()},application_process_state:0}}function Dh(e,t){const n=t;if(!n||void 0===n.responseStart)return;const r=Xu.getInstance().getTimeOrigin(),i=Math.floor(1e3*(n.startTime+r)),s=n.responseStart?Math.floor(1e3*(n.responseStart-n.startTime)):void 0,o=Math.floor(1e3*(n.responseEnd-n.startTime));!function(e){const t=th.getInstance();if(!t.instrumentationEnabled)return;const n=e.url,r=t.logEndPointUrl.split("?")[0],i=t.flTransportEndpointUrl.split("?")[0];n!==r&&n!==i&&t.loggingEnabled&&t.logNetworkAfterSampling&&Rh(e,0)}({performanceController:e,url:n.name&&n.name.split("?")[0],responsePayloadBytes:n.transferSize,startTimeUs:i,timeToResponseInitiatedUs:s,timeToResponseCompletedUs:o})}const Lh=["_fp",ju,Uu,Bu,qu,$u];class Mh{constructor(e,t,n=!1,r){this.performanceController=e,this.name=t,this.isAuto=n,this.state=1,this.customAttributes={},this.counters={},this.api=Xu.getInstance(),this.randomId=Math.floor(1e6*Math.random()),this.isAuto||(this.traceStartMark=`FB-PERF-TRACE-START-${this.randomId}-${this.name}`,this.traceStopMark=`FB-PERF-TRACE-STOP-${this.randomId}-${this.name}`,this.traceMeasure=r||`${Mu}-${this.randomId}-${this.name}`,r&&this.calculateTraceMetrics())}start(){if(1!==this.state)throw Vu.create("trace started",{traceName:this.name});this.api.mark(this.traceStartMark),this.state=2}stop(){if(2!==this.state)throw Vu.create("trace stopped",{traceName:this.name});this.state=3,this.api.mark(this.traceStopMark),this.api.measure(this.traceMeasure,this.traceStartMark,this.traceStopMark),this.calculateTraceMetrics(),Ah(this)}record(e,t,n){if(e<=0)throw Vu.create("nonpositive trace startTime",{traceName:this.name});if(t<=0)throw Vu.create("nonpositive trace duration",{traceName:this.name});if(this.durationUs=Math.floor(1e3*t),this.startTimeUs=Math.floor(1e3*e),n&&n.attributes&&(this.customAttributes=Object.assign({},n.attributes)),n&&n.metrics)for(const r of Object.keys(n.metrics))isNaN(Number(n.metrics[r]))||(this.counters[r]=Math.floor(Number(n.metrics[r])));Ah(this)}incrementMetric(e,t=1){void 0===this.counters[e]?this.putMetric(e,t):this.putMetric(e,this.counters[e]+t)}putMetric(e,t){if(n=e,r=this.name,0===n.length||n.length>100||!(r&&r.startsWith(Fu)&&Lh.indexOf(n)>-1)&&n.startsWith("_"))throw Vu.create("invalid custom metric name",{customMetricName:e});var n,r;this.counters[e]=function(e){const t=Math.floor(e);return t<e&&Ku.info(`Metric value should be an Integer, setting the value as : ${t}.`),t}(null!=t?t:0)}getMetric(e){return this.counters[e]||0}putAttribute(e,t){const n=!(0===(r=e).length||r.length>40||ih.some((e=>r.startsWith(e)))||!r.match(sh));var r;const i=function(e){return 0!==e.length&&e.length<=100}(t);if(n&&i)this.customAttributes[e]=t;else{if(!n)throw Vu.create("invalid attribute name",{attributeName:e});if(!i)throw Vu.create("invalid attribute value",{attributeValue:t})}}getAttribute(e){return this.customAttributes[e]}removeAttribute(e){void 0!==this.customAttributes[e]&&delete this.customAttributes[e]}getAttributes(){return Object.assign({},this.customAttributes)}setStartTime(e){this.startTimeUs=e}setDuration(e){this.durationUs=e}calculateTraceMetrics(){const e=this.api.getEntriesByName(this.traceMeasure),t=e&&e[0];t&&(this.durationUs=Math.floor(1e3*t.duration),this.startTimeUs=Math.floor(1e3*(t.startTime+this.api.getTimeOrigin())))}static createOobTrace(e,t,n,r,i){const s=Xu.getInstance().getUrl();if(!s)return;const o=new Mh(e,Fu+s,!0),a=Math.floor(1e3*Xu.getInstance().getTimeOrigin());o.setStartTime(a),t&&t[0]&&(o.setDuration(Math.floor(1e3*t[0].duration)),o.putMetric("domInteractive",Math.floor(1e3*t[0].domInteractive)),o.putMetric("domContentLoadedEventEnd",Math.floor(1e3*t[0].domContentLoadedEventEnd)),o.putMetric("loadEventEnd",Math.floor(1e3*t[0].loadEventEnd)));if(n){const e=n.find((e=>"first-paint"===e.name));e&&e.startTime&&o.putMetric("_fp",Math.floor(1e3*e.startTime));const t=n.find((e=>"first-contentful-paint"===e.name));t&&t.startTime&&o.putMetric(ju,Math.floor(1e3*t.startTime)),i&&o.putMetric(Uu,Math.floor(1e3*i))}this.addWebVitalMetric(o,Bu,"lcp_element",r.lcp),this.addWebVitalMetric(o,qu,"cls_largestShiftTarget",r.cls),this.addWebVitalMetric(o,$u,"inp_interactionTarget",r.inp),Ah(o),wh&&wh.flush()}static addWebVitalMetric(e,t,n,r){r&&(e.putMetric(t,Math.floor(1e3*r.value)),r.elementAttribution&&e.putAttribute(n,r.elementAttribution))}static createUserTimingTrace(e,t){Ah(new Mh(e,t,!1,t))}}let Fh,jh={},Uh=!1;function Bh(e){Zu()&&(setTimeout((()=>function(e){const t=Xu.getInstance();"onpagehide"in window?t.document.addEventListener("pagehide",(()=>qh(e))):t.document.addEventListener("unload",(()=>qh(e)));t.document.addEventListener("visibilitychange",(()=>{"hidden"===t.document.visibilityState&&qh(e)})),t.onFirstInputDelay&&t.onFirstInputDelay((e=>{Fh=e}));t.onLCP((e=>{var t;jh.lcp={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.element}})),t.onCLS((e=>{var t;jh.cls={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.largestShiftTarget}})),t.onINP((e=>{var t;jh.inp={value:e.value,elementAttribution:null===(t=e.attribution)||void 0===t?void 0:t.interactionTarget}}))}(e)),0),setTimeout((()=>function(e){const t=Xu.getInstance(),n=t.getEntriesByType("resource");for(const r of n)Dh(e,r);t.setupObserver("resource",(t=>Dh(e,t)))}(e)),0),setTimeout((()=>function(e){const t=Xu.getInstance(),n=t.getEntriesByType("measure");for(const r of n)$h(e,r);t.setupObserver("measure",(t=>$h(e,t)))}(e)),0))}function $h(e,t){const n=t.name;n.substring(0,21)!==Mu&&Mh.createUserTimingTrace(e,n)}function qh(e){if(!Uh){Uh=!0;const t=Xu.getInstance(),n=t.getEntriesByType("navigation"),r=t.getEntriesByType("paint");setTimeout((()=>{Mh.createOobTrace(e,n,r,jh,Fh)}),0)}}class Wh{constructor(e,t){this.app=e,this.installations=t,this.initialized=!1}_init(e){this.initialized||(void 0!==(null==e?void 0:e.dataCollectionEnabled)&&(this.dataCollectionEnabled=e.dataCollectionEnabled),void 0!==(null==e?void 0:e.instrumentationEnabled)&&(this.instrumentationEnabled=e.instrumentationEnabled),Xu.getInstance().requiredApisAvailable()?ln().then((e=>{e&&(kh||(Sh(5500),kh=!0),vh(this).then((()=>Bh(this)),(()=>Bh(this))),this.initialized=!0)})).catch((e=>{Ku.info(`Environment doesn't support IndexedDB: ${e}`)})):Ku.info('Firebase Performance cannot start if the browser does not support "Fetch" and "Promise", or cookies are disabled.'))}set instrumentationEnabled(e){th.getInstance().instrumentationEnabled=e}get instrumentationEnabled(){return th.getInstance().instrumentationEnabled}set dataCollectionEnabled(e){th.getInstance().dataCollectionEnabled=e}get dataCollectionEnabled(){return th.getInstance().dataCollectionEnabled}}function Hh(e=qr()){return jr(e=En(e),"performance").getImmediate()}function zh(e,t){return e=En(e),new Mh(e,t)}const Vh=(e,{options:t})=>{const n=e.getProvider("app").getImmediate(),r=e.getProvider("installations-internal").getImmediate();if("[DEFAULT]"!==n.name)throw Vu.create("FB not default");if("undefined"==typeof window)throw Vu.create("no window");var i;i=window,Yu=i;const s=new Wh(n,r);return s._init(t),s};function Kh(e,t){return function(){return e.apply(t,arguments)}}Fr(new In("performance",Vh,"PUBLIC")),Wr(Nu,Du),Wr(Nu,Du,"esm2017");const{toString:Gh}=Object.prototype,{getPrototypeOf:Yh}=Object,Jh=(e=>t=>{const n=Gh.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Qh=e=>(e=e.toLowerCase(),t=>Jh(t)===e),Xh=e=>t=>typeof t===e,{isArray:Zh}=Array,ed=Xh("undefined");const td=Qh("ArrayBuffer");const nd=Xh("string"),rd=Xh("function"),id=Xh("number"),sd=e=>null!==e&&"object"==typeof e,od=e=>{if("object"!==Jh(e))return!1;const t=Yh(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},ad=Qh("Date"),cd=Qh("File"),ld=Qh("Blob"),ud=Qh("FileList"),hd=Qh("URLSearchParams"),[dd,pd,fd,md]=["ReadableStream","Request","Response","Headers"].map(Qh);function gd(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,i;if("object"!=typeof e&&(e=[e]),Zh(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;let o;for(r=0;r<s;r++)o=i[r],t.call(null,e[o],o,e)}}function _d(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const yd="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,vd=e=>!ed(e)&&e!==yd;const bd=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&Yh(Uint8Array)),wd=Qh("HTMLFormElement"),Cd=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Td=Qh("RegExp"),kd=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};gd(n,((n,i)=>{let s;!1!==(s=t(n,i,e))&&(r[i]=s||n)})),Object.defineProperties(e,r)},Sd="abcdefghijklmnopqrstuvwxyz",Ed="0123456789",Id={DIGIT:Ed,ALPHA:Sd,ALPHA_DIGIT:Sd+Sd.toUpperCase()+Ed};const xd=Qh("AsyncFunction"),Rd=(Ad="function"==typeof setImmediate,Pd=rd(yd.postMessage),Ad?setImmediate:Pd?(Od=`axios@${Math.random()}`,Nd=[],yd.addEventListener("message",(({source:e,data:t})=>{e===yd&&t===Od&&Nd.length&&Nd.shift()()}),!1),e=>{Nd.push(e),yd.postMessage(Od,"*")}):e=>setTimeout(e));var Ad,Pd,Od,Nd;const Dd="undefined"!=typeof queueMicrotask?queueMicrotask.bind(yd):"undefined"!=typeof process&&process.nextTick||Rd,Ld={isArray:Zh,isArrayBuffer:td,isBuffer:function(e){return null!==e&&!ed(e)&&null!==e.constructor&&!ed(e.constructor)&&rd(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||rd(e.append)&&("formdata"===(t=Jh(e))||"object"===t&&rd(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&td(e.buffer),t},isString:nd,isNumber:id,isBoolean:e=>!0===e||!1===e,isObject:sd,isPlainObject:od,isReadableStream:dd,isRequest:pd,isResponse:fd,isHeaders:md,isUndefined:ed,isDate:ad,isFile:cd,isBlob:ld,isRegExp:Td,isFunction:rd,isStream:e=>sd(e)&&rd(e.pipe),isURLSearchParams:hd,isTypedArray:bd,isFileList:ud,forEach:gd,merge:function e(){const{caseless:t}=vd(this)&&this||{},n={},r=(r,i)=>{const s=t&&_d(n,i)||i;od(n[s])&&od(r)?n[s]=e(n[s],r):od(r)?n[s]=e({},r):Zh(r)?n[s]=r.slice():n[s]=r};for(let i=0,s=arguments.length;i<s;i++)arguments[i]&&gd(arguments[i],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(gd(t,((t,r)=>{n&&rd(t)?e[r]=Kh(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,s,o;const a={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)o=i[s],r&&!r(o,e,t)||a[o]||(t[o]=e[o],a[o]=!0);e=!1!==n&&Yh(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Jh,kindOfTest:Qh,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Zh(e))return e;let t=e.length;if(!id(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:wd,hasOwnProperty:Cd,hasOwnProp:Cd,reduceDescriptors:kd,freezeMethods:e=>{kd(e,((t,n)=>{if(rd(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];rd(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Zh(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:_d,global:yd,isContextDefined:vd,ALPHABET:Id,generateString:(e=16,t=Id.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&rd(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(sd(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const i=Zh(e)?[]:{};return gd(e,((e,t)=>{const s=n(e,r+1);!ed(s)&&(i[t]=s)})),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:xd,isThenable:e=>e&&(sd(e)||rd(e))&&rd(e.then)&&rd(e.catch),setImmediate:Rd,asap:Dd};function Md(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}Ld.inherits(Md,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Ld.toJSONObject(this.config),code:this.code,status:this.status}}});const Fd=Md.prototype,jd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{jd[e]={value:e}})),Object.defineProperties(Md,jd),Object.defineProperty(Fd,"isAxiosError",{value:!0}),Md.from=(e,t,n,r,i,s)=>{const o=Object.create(Fd);return Ld.toFlatObject(e,o,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Md.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};function Ud(e){return Ld.isPlainObject(e)||Ld.isArray(e)}function Bd(e){return Ld.endsWith(e,"[]")?e.slice(0,-2):e}function $d(e,t,n){return e?e.concat(t).map((function(e,t){return e=Bd(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const qd=Ld.toFlatObject(Ld,{},null,(function(e){return/^is[A-Z]/.test(e)}));function Wd(e,t,n){if(!Ld.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Ld.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Ld.isUndefined(t[e])}))).metaTokens,i=n.visitor||l,s=n.dots,o=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&Ld.isSpecCompliantForm(t);if(!Ld.isFunction(i))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(Ld.isDate(e))return e.toISOString();if(!a&&Ld.isBlob(e))throw new Md("Blob is not supported. Use a Buffer instead.");return Ld.isArrayBuffer(e)||Ld.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,i){let a=e;if(e&&!i&&"object"==typeof e)if(Ld.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Ld.isArray(e)&&function(e){return Ld.isArray(e)&&!e.some(Ud)}(e)||(Ld.isFileList(e)||Ld.endsWith(n,"[]"))&&(a=Ld.toArray(e)))return n=Bd(n),a.forEach((function(e,r){!Ld.isUndefined(e)&&null!==e&&t.append(!0===o?$d([n],r,s):null===o?n:n+"[]",c(e))})),!1;return!!Ud(e)||(t.append($d(i,n,s),c(e)),!1)}const u=[],h=Object.assign(qd,{defaultVisitor:l,convertValue:c,isVisitable:Ud});if(!Ld.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Ld.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),Ld.forEach(n,(function(n,s){!0===(!(Ld.isUndefined(n)||null===n)&&i.call(t,n,Ld.isString(s)?s.trim():s,r,h))&&e(n,r?r.concat(s):[s])})),u.pop()}}(e),t}function Hd(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function zd(e,t){this._pairs=[],e&&Wd(e,this,t)}const Vd=zd.prototype;function Kd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Gd(e,t,n){if(!t)return e;const r=n&&n.encode||Kd,i=n&&n.serialize;let s;if(s=i?i(t,n):Ld.isURLSearchParams(t)?t.toString():new zd(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}Vd.append=function(e,t){this._pairs.push([e,t])},Vd.toString=function(e){const t=e?function(t){return e.call(this,t,Hd)}:Hd;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class Yd{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Ld.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const Jd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Qd={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:zd,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Xd="undefined"!=typeof window&&"undefined"!=typeof document,Zd="object"==typeof navigator&&navigator||void 0,ep=Xd&&(!Zd||["ReactNative","NativeScript","NS"].indexOf(Zd.product)<0),tp="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,np=Xd&&window.location.href||"http://localhost",rp={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Xd,hasStandardBrowserEnv:ep,hasStandardBrowserWebWorkerEnv:tp,navigator:Zd,origin:np},Symbol.toStringTag,{value:"Module"})),...Qd};function ip(e){function t(e,n,r,i){let s=e[i++];if("__proto__"===s)return!0;const o=Number.isFinite(+s),a=i>=e.length;if(s=!s&&Ld.isArray(r)?r.length:s,a)return Ld.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!o;r[s]&&Ld.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],i)&&Ld.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}(r[s])),!o}if(Ld.isFormData(e)&&Ld.isFunction(e.entries)){const n={};return Ld.forEachEntry(e,((e,r)=>{t(function(e){return Ld.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null}const sp={transitional:Jd,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=Ld.isObject(e);i&&Ld.isHTMLForm(e)&&(e=new FormData(e));if(Ld.isFormData(e))return r?JSON.stringify(ip(e)):e;if(Ld.isArrayBuffer(e)||Ld.isBuffer(e)||Ld.isStream(e)||Ld.isFile(e)||Ld.isBlob(e)||Ld.isReadableStream(e))return e;if(Ld.isArrayBufferView(e))return e.buffer;if(Ld.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Wd(e,new rp.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return rp.isNode&&Ld.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=Ld.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Wd(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(Ld.isString(e))try{return(t||JSON.parse)(e),Ld.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||sp.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Ld.isResponse(e)||Ld.isReadableStream(e))return e;if(e&&Ld.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(i){if(n){if("SyntaxError"===i.name)throw Md.from(i,Md.ERR_BAD_RESPONSE,this,null,this.response);throw i}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rp.classes.FormData,Blob:rp.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Ld.forEach(["delete","get","head","post","put","patch"],(e=>{sp.headers[e]={}}));const op=Ld.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ap=Symbol("internals");function cp(e){return e&&String(e).trim().toLowerCase()}function lp(e){return!1===e||null==e?e:Ld.isArray(e)?e.map(lp):String(e)}function up(e,t,n,r,i){return Ld.isFunction(r)?r.call(this,t,n):(i&&(t=n),Ld.isString(t)?Ld.isString(r)?-1!==t.indexOf(r):Ld.isRegExp(r)?r.test(t):void 0:void 0)}let hp=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=cp(t);if(!i)throw new Error("header name must be a non-empty string");const s=Ld.findKey(r,i);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=lp(e))}const s=(e,t)=>Ld.forEach(e,((e,n)=>i(e,n,t)));if(Ld.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(Ld.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&op[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Ld.isHeaders(e))for(const[o,a]of e.entries())i(a,o,n);else null!=e&&i(t,e,n);return this}get(e,t){if(e=cp(e)){const n=Ld.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Ld.isFunction(t))return t.call(this,e,n);if(Ld.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=cp(e)){const n=Ld.findKey(this,e);return!(!n||void 0===this[n]||t&&!up(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=cp(e)){const i=Ld.findKey(n,e);!i||t&&!up(0,n[i],i,t)||(delete n[i],r=!0)}}return Ld.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!up(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return Ld.forEach(this,((r,i)=>{const s=Ld.findKey(n,i);if(s)return t[s]=lp(r),void delete t[i];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(i):String(i).trim();o!==i&&delete t[i],t[o]=lp(r),n[o]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return Ld.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Ld.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[ap]=this[ap]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=cp(e);t[r]||(!function(e,t){const n=Ld.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})}))}(n,e),t[r]=!0)}return Ld.isArray(e)?e.forEach(r):r(e),this}};function dp(e,t){const n=this||sp,r=t||n,i=hp.from(r.headers);let s=r.data;return Ld.forEach(e,(function(e){s=e.call(n,s,i.normalize(),t?t.status:void 0)})),i.normalize(),s}function pp(e){return!(!e||!e.__CANCEL__)}function fp(e,t,n){Md.call(this,null==e?"canceled":e,Md.ERR_CANCELED,t,n),this.name="CanceledError"}function mp(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Md("Request failed with status code "+n.status,[Md.ERR_BAD_REQUEST,Md.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}hp.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Ld.reduceDescriptors(hp.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),Ld.freezeMethods(hp),Ld.inherits(fp,Md,{__CANCEL__:!0});const gp=(e,t,n=3)=>{let r=0;const i=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,s=0,o=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[o];i||(i=c),n[s]=a,r[s]=c;let u=o,h=0;for(;u!==s;)h+=n[u++],u%=e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-i<t)return;const d=l&&c-l;return d?Math.round(1e3*h/d):void 0}}(50,250);return function(e,t){let n,r,i=0,s=1e3/t;const o=(t,s=Date.now())=>{i=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-i;a>=s?o(e,t):(n=e,r||(r=setTimeout((()=>{r=null,o(n)}),s-a)))},()=>n&&o(n)]}((n=>{const s=n.loaded,o=n.lengthComputable?n.total:void 0,a=s-r,c=i(a);r=s;e({loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:c||void 0,estimated:c&&o&&s<=o?(o-s)/c:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})}),n)},_p=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},yp=e=>(...t)=>Ld.asap((()=>e(...t))),vp=rp.hasStandardBrowserEnv?function(){const e=rp.navigator&&/(msie|trident)/i.test(rp.navigator.userAgent),t=document.createElement("a");let n;function r(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=r(window.location.href),function(e){const t=Ld.isString(e)?r(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return function(){return!0}}(),bp=rp.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const o=[e+"="+encodeURIComponent(t)];Ld.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),Ld.isString(r)&&o.push("path="+r),Ld.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function wp(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Cp=e=>e instanceof hp?{...e}:e;function Tp(e,t){t=t||{};const n={};function r(e,t,n){return Ld.isPlainObject(e)&&Ld.isPlainObject(t)?Ld.merge.call({caseless:n},e,t):Ld.isPlainObject(t)?Ld.merge({},t):Ld.isArray(t)?t.slice():t}function i(e,t,n){return Ld.isUndefined(t)?Ld.isUndefined(e)?void 0:r(void 0,e,n):r(e,t,n)}function s(e,t){if(!Ld.isUndefined(t))return r(void 0,t)}function o(e,t){return Ld.isUndefined(t)?Ld.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,i,s){return s in t?r(n,i):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(e,t)=>i(Cp(e),Cp(t),!0)};return Ld.forEach(Object.keys(Object.assign({},e,t)),(function(r){const s=c[r]||i,o=s(e[r],t[r],r);Ld.isUndefined(o)&&s!==a||(n[r]=o)})),n}const kp=e=>{const t=Tp({},e);let n,{data:r,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:c}=t;if(t.headers=a=hp.from(a),t.url=Gd(wp(t.baseURL,t.url),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),Ld.isFormData(r))if(rp.hasStandardBrowserEnv||rp.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(rp.hasStandardBrowserEnv&&(i&&Ld.isFunction(i)&&(i=i(t)),i||!1!==i&&vp(t.url))){const e=s&&o&&bp.read(o);e&&a.set(s,e)}return t},Sp="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=kp(e);let i=r.data;const s=hp.from(r.headers).normalize();let o,a,c,l,u,{responseType:h,onUploadProgress:d,onDownloadProgress:p}=r;function f(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=hp.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());mp((function(e){t(e),f()}),(function(e){n(e),f()}),{data:h&&"text"!==h&&"json"!==h?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Md("Request aborted",Md.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Md("Network Error",Md.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||Jd;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Md(t,i.clarifyTimeoutError?Md.ETIMEDOUT:Md.ECONNABORTED,e,m)),m=null},void 0===i&&s.setContentType(null),"setRequestHeader"in m&&Ld.forEach(s.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),Ld.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),h&&"json"!==h&&(m.responseType=r.responseType),p&&([c,u]=gp(p,!0),m.addEventListener("progress",c)),d&&m.upload&&([a,l]=gp(d),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(o=t=>{m&&(n(!t||t.type?new fp(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const _=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);_&&-1===rp.protocols.indexOf(_)?n(new Md("Unsupported protocol "+_+":",Md.ERR_BAD_REQUEST,e)):m.send(i||null)}))},Ep=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Md?t:new fp(t instanceof Error?t.message:t))}};let s=t&&setTimeout((()=>{s=null,i(new Md(`timeout ${t} of ms exceeded`,Md.ETIMEDOUT))}),t);const o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)})),e=null)};e.forEach((e=>e.addEventListener("abort",i)));const{signal:a}=r;return a.unsubscribe=()=>Ld.asap(o),a}},Ip=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},xp=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Rp=(e,t,n,r)=>{const i=async function*(e,t){for await(const n of xp(e))yield*Ip(n,t)}(e,t);let s,o=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=o+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},Ap="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Pp=Ap&&"function"==typeof ReadableStream,Op=Ap&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Np=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},Dp=Pp&&Np((()=>{let e=!1;const t=new Request(rp.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Lp=Pp&&Np((()=>Ld.isReadableStream(new Response("").body))),Mp={stream:Lp&&(e=>e.body)};var Fp;Ap&&(Fp=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Mp[e]&&(Mp[e]=Ld.isFunction(Fp[e])?t=>t[e]():(t,n)=>{throw new Md(`Response type '${e}' is not supported`,Md.ERR_NOT_SUPPORT,n)})})));const jp=async(e,t)=>{const n=Ld.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Ld.isBlob(e))return e.size;if(Ld.isSpecCompliantForm(e)){const t=new Request(rp.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Ld.isArrayBufferView(e)||Ld.isArrayBuffer(e)?e.byteLength:(Ld.isURLSearchParams(e)&&(e+=""),Ld.isString(e)?(await Op(e)).byteLength:void 0)})(t):n},Up={http:null,xhr:Sp,fetch:Ap&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:o,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:h="same-origin",fetchOptions:d}=kp(e);l=l?(l+"").toLowerCase():"text";let p,f=Ep([i,s&&s.toAbortSignal()],o);const m=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let g;try{if(c&&Dp&&"get"!==n&&"head"!==n&&0!==(g=await jp(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Ld.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=_p(g,gp(yp(c)));r=Rp(n.body,65536,e,t)}}Ld.isString(h)||(h=h?"include":"omit");const i="credentials"in Request.prototype;p=new Request(t,{...d,signal:f,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:i?h:void 0});let s=await fetch(p);const o=Lp&&("stream"===l||"response"===l);if(Lp&&(a||o&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=s[t]}));const t=Ld.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&_p(t,gp(yp(a),!0))||[];s=new Response(Rp(s.body,65536,n,(()=>{r&&r(),m&&m()})),e)}l=l||"text";let _=await Mp[Ld.findKey(Mp,l)||"text"](s,e);return!o&&m&&m(),await new Promise(((t,n)=>{mp(t,n,{data:_,headers:hp.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:p})}))}catch(_){if(m&&m(),_&&"TypeError"===_.name&&/fetch/i.test(_.message))throw Object.assign(new Md("Network Error",Md.ERR_NETWORK,e,p),{cause:_.cause||_});throw Md.from(_,_&&_.code,e,p)}})};Ld.forEach(Up,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}}));const Bp=e=>`- ${e}`,$p=e=>Ld.isFunction(e)||null===e||!1===e,qp=e=>{e=Ld.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!$p(n)&&(r=Up[(t=String(n)).toLowerCase()],void 0===r))throw new Md(`Unknown adapter '${t}'`);if(r)break;i[t||"#"+s]=r}if(!r){const e=Object.entries(i).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new Md("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Bp).join("\n"):" "+Bp(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function Wp(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new fp(null,e)}function Hp(e){Wp(e),e.headers=hp.from(e.headers),e.data=dp.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return qp(e.adapter||sp.adapter)(e).then((function(t){return Wp(e),t.data=dp.call(e,e.transformResponse,t),t.headers=hp.from(t.headers),t}),(function(t){return pp(t)||(Wp(e),t&&t.response&&(t.response.data=dp.call(e,e.transformResponse,t.response),t.response.headers=hp.from(t.response.headers))),Promise.reject(t)}))}const zp="1.7.7",Vp={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Vp[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Kp={};Vp.transitional=function(e,t,n){return(r,i,s)=>{if(!1===e)throw new Md(function(e,t){return"[Axios v1.7.7] Transitional option '"+e+"'"+t+(n?". "+n:"")}(i," has been removed"+(t?" in "+t:"")),Md.ERR_DEPRECATED);return t&&!Kp[i]&&(Kp[i]=!0),!e||e(r,i,s)}};const Gp={assertOptions:function(e,t,n){if("object"!=typeof e)throw new Md("options must be an object",Md.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const s=r[i],o=t[s];if(o){const t=e[s],n=void 0===t||o(t,s,e);if(!0!==n)throw new Md("option "+s+" must be "+n,Md.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Md("Unknown option "+s,Md.ERR_BAD_OPTION)}},validators:Vp},Yp=Gp.validators;let Jp=class{constructor(e){this.defaults=e,this.interceptors={request:new Yd,response:new Yd}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e;Error.captureStackTrace?Error.captureStackTrace(e={}):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Tp(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&Gp.assertOptions(n,{silentJSONParsing:Yp.transitional(Yp.boolean),forcedJSONParsing:Yp.transitional(Yp.boolean),clarifyTimeoutError:Yp.transitional(Yp.boolean)},!1),null!=r&&(Ld.isFunction(r)?t.paramsSerializer={serialize:r}:Gp.assertOptions(r,{encode:Yp.function,serialize:Yp.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=i&&Ld.merge(i.common,i[t.method]);i&&Ld.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),t.headers=hp.concat(s,i);const o=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,o.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,h=0;if(!a){const e=[Hp.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);h<u;)l=l.then(e[h++],e[h++]);return l}u=o.length;let d=t;for(h=0;h<u;){const e=o[h++],t=o[h++];try{d=e(d)}catch(p){t.call(this,p);break}}try{l=Hp.call(this,d)}catch(p){return Promise.reject(p)}for(h=0,u=c.length;h<u;)l=l.then(c[h++],c[h++]);return l}getUri(e){return Gd(wp((e=Tp(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}};Ld.forEach(["delete","get","head","options"],(function(e){Jp.prototype[e]=function(t,n){return this.request(Tp(n||{},{method:e,url:t,data:(n||{}).data}))}})),Ld.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,i){return this.request(Tp(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Jp.prototype[e]=t(),Jp.prototype[e+"Form"]=t(!0)}));const Qp={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Qp).forEach((([e,t])=>{Qp[t]=e}));const Xp=function e(t){const n=new Jp(t),r=Kh(Jp.prototype.request,n);return Ld.extend(r,Jp.prototype,n,{allOwnKeys:!0}),Ld.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Tp(t,n))},r}(sp);Xp.Axios=Jp,Xp.CanceledError=fp,Xp.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,i){n.reason||(n.reason=new fp(e,r,i),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e((function(e){t=e})),cancel:t}}},Xp.isCancel=pp,Xp.VERSION=zp,Xp.toFormData=Wd,Xp.AxiosError=Md,Xp.Cancel=Xp.CanceledError,Xp.all=function(e){return Promise.all(e)},Xp.spread=function(e){return function(t){return e.apply(null,t)}},Xp.isAxiosError=function(e){return Ld.isObject(e)&&!0===e.isAxiosError},Xp.mergeConfig=Tp,Xp.AxiosHeaders=hp,Xp.formToJSON=e=>ip(Ld.isHTMLForm(e)?new FormData(e):e),Xp.getAdapter=qp,Xp.HttpStatusCode=Qp,Xp.default=Xp;const{Axios:Zp,AxiosError:ef,CanceledError:tf,isCancel:nf,CancelToken:rf,VERSION:sf,all:of,Cancel:af,isAxiosError:cf,spread:lf,toFormData:uf,AxiosHeaders:hf,HttpStatusCode:df,formToJSON:pf,getAdapter:ff,mergeConfig:mf}=Xp;function gf(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gf(Object(n),!0).forEach((function(t){vf(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gf(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function yf(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,wf(r.key),r)}}function vf(e,t,n){return(t=wf(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bf(){return bf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bf.apply(this,arguments)}function wf(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:String(t)}!function(e,t){var n,r=e.document,i=r.documentElement,s=r.querySelector('meta[name="viewport"]'),o=r.querySelector('meta[name="flexible"]'),a=0,c=0,l=t.flexible||(t.flexible={});if(s){var u=s.getAttribute("content").match(/initial\-scale=([\d\.]+)/);u&&(c=parseFloat(u[1]),a=parseInt(1/c))}else if(o){var h=o.getAttribute("content");if(h){var d=h.match(/initial\-dpr=([\d\.]+)/),p=h.match(/maximum\-dpr=([\d\.]+)/);d&&(a=parseFloat(d[1]),c=parseFloat((1/a).toFixed(2))),p&&(a=parseFloat(p[1]),c=parseFloat((1/a).toFixed(2)))}}if(!a&&!c){e.navigator.appVersion.match(/android/gi);var f=e.navigator.appVersion.match(/iphone/gi),m=e.devicePixelRatio;c=1/(a=f?m>=3&&(!a||a>=3)?3:m>=2&&(!a||a>=2)?2:1:1)}if(i.setAttribute("data-dpr",a),!s)if((s=r.createElement("meta")).setAttribute("name","viewport"),s.setAttribute("content","initial-scale="+c+", maximum-scale="+c+", minimum-scale="+c+", user-scalable=no"),i.firstElementChild)i.firstElementChild.appendChild(s);else{var g=r.createElement("div");g.appendChild(s),r.write(g.innerHTML)}function _(){var t=i.getBoundingClientRect().width;t/a>540&&(t=540*a);var n=t/10;i.style.fontSize=n+"px",l.rem=e.rem=n}e.addEventListener("resize",(function(){clearTimeout(n),n=setTimeout(_,300)}),!1),e.addEventListener("pageshow",(function(e){e.persisted&&(clearTimeout(n),n=setTimeout(_,300))}),!1),"complete"===r.readyState?r.body.style.fontSize=12*a+"px":r.addEventListener("DOMContentLoaded",(function(e){r.body.style.fontSize=12*a+"px"}),!1),_(),l.dpr=e.dpr=a,l.refreshRem=_,l.rem2px=function(e){var t=parseFloat(e)*this.rem;return"string"==typeof e&&e.match(/rem$/)&&(t+="px"),t},l.px2rem=function(e){var t=parseFloat(e)/this.rem;return"string"==typeof e&&e.match(/px$/)&&(t+="rem"),t}}(window,window.lib||(window.lib={}));var Cf,Tf,kf,Sf,Ef,If,xf,Rf,Af={exports:{}};Cf=Af,"undefined"!=typeof window&&(Tf=window,kf=Tf.HTMLCanvasElement&&Tf.HTMLCanvasElement.prototype,Sf=Tf.Blob&&function(){try{return Boolean(new Blob)}catch(e){return!1}}(),Ef=Sf&&Tf.Uint8Array&&function(){try{return 100===new Blob([new Uint8Array(100)]).size}catch(e){return!1}}(),If=Tf.BlobBuilder||Tf.WebKitBlobBuilder||Tf.MozBlobBuilder||Tf.MSBlobBuilder,xf=/^data:((.*?)(;charset=.*?)?)(;base64)?,/,Rf=(Sf||If)&&Tf.atob&&Tf.ArrayBuffer&&Tf.Uint8Array&&function(e){var t,n,r,i,s,o,a,c,l;if(!(t=e.match(xf)))throw new Error("invalid data URI");for(n=t[2]?t[1]:"text/plain"+(t[3]||";charset=US-ASCII"),r=!!t[4],i=e.slice(t[0].length),s=r?atob(i):decodeURIComponent(i),o=new ArrayBuffer(s.length),a=new Uint8Array(o),c=0;c<s.length;c+=1)a[c]=s.charCodeAt(c);return Sf?new Blob([Ef?a:o],{type:n}):((l=new If).append(o),l.getBlob(n))},Tf.HTMLCanvasElement&&!kf.toBlob&&(kf.mozGetAsFile?kf.toBlob=function(e,t,n){var r=this;setTimeout((function(){n&&kf.toDataURL&&Rf?e(Rf(r.toDataURL(t,n))):e(r.mozGetAsFile("blob",t))}))}:kf.toDataURL&&Rf&&(kf.msToBlob?kf.toBlob=function(e,t,n){var r=this;setTimeout((function(){(t&&"image/png"!==t||n)&&kf.toDataURL&&Rf?e(Rf(r.toDataURL(t,n))):e(r.msToBlob(t))}))}:kf.toBlob=function(e,t,n){var r=this;setTimeout((function(){e(Rf(r.toDataURL(t,n)))}))})),Cf.exports?Cf.exports=Rf:Tf.dataURLtoBlob=Rf);var Pf=Af.exports,Of={strict:!0,checkOrientation:!0,retainExif:!1,maxWidth:1/0,maxHeight:1/0,minWidth:0,minHeight:0,width:void 0,height:void 0,resize:"none",quality:.8,mimeType:"auto",convertTypes:["image/png"],convertSize:5e6,beforeDraw:null,drew:null,success:null,error:null},Nf="undefined"!=typeof window&&void 0!==window.document?window:{},Df=function(e){return e>0&&e<1/0},Lf=Array.prototype.slice;function Mf(e){return Array.from?Array.from(e):Lf.call(e)}var Ff=/^image\/.+$/;function jf(e){return Ff.test(e)}var Uf=String.fromCharCode;var Bf=Nf.btoa;function $f(e,t){for(var n=[],r=new Uint8Array(e);r.length>0;)n.push(Uf.apply(null,Mf(r.subarray(0,8192)))),r=r.subarray(8192);return"data:".concat(t,";base64,").concat(Bf(n.join("")))}function qf(e){var t,n=new DataView(e);try{var r,i,s;if(255===n.getUint8(0)&&216===n.getUint8(1))for(var o=n.byteLength,a=2;a+1<o;){if(255===n.getUint8(a)&&225===n.getUint8(a+1)){i=a;break}a+=1}if(i){var c=i+10;if("Exif"===function(e,t,n){var r,i="";for(n+=t,r=t;r<n;r+=1)i+=Uf(e.getUint8(r));return i}(n,i+4,4)){var l=n.getUint16(c);if(((r=18761===l)||19789===l)&&42===n.getUint16(c+2,r)){var u=n.getUint32(c+4,r);u>=8&&(s=c+u)}}}if(s){var h,d,p=n.getUint16(s,r);for(d=0;d<p;d+=1)if(h=s+12*d+2,274===n.getUint16(h,r)){h+=8,t=n.getUint16(h,r),n.setUint16(h,1,r);break}}}catch(f){t=1}return t}var Wf=/\.\d*(?:0|9){12}\d*$/;function Hf(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return Wf.test(e)?Math.round(e*t)/t:e}function zf(e){var t=e.aspectRatio,n=e.height,r=e.width,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"none",s=Df(r),o=Df(n);if(s&&o){var a=n*t;("contain"===i||"none"===i)&&a>r||"cover"===i&&a<r?n=r/t:r=n*t}else s?n=r/t:o&&(r=n*t);return{width:r,height:n}}var Vf,Kf,Gf,Yf,Jf,Qf,Xf,Zf,em,tm,nm,rm=Nf.ArrayBuffer,im=Nf.FileReader,sm=Nf.URL||Nf.webkitURL,om=/\.\w+$/,am=Nf.Compressor,cm=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.file=t,this.exif=[],this.image=new Image,this.options=_f(_f({},Of),n),this.aborted=!1,this.result=null,this.init()}var t,n,r;return t=e,n=[{key:"init",value:function(){var e=this,t=this.file,n=this.options;if(r=t,"undefined"!=typeof Blob&&(r instanceof Blob||"[object Blob]"===Object.prototype.toString.call(r))){var r,i=t.type;if(jf(i))if(sm&&im){rm||(n.checkOrientation=!1,n.retainExif=!1);var s="image/jpeg"===i,o=s&&n.checkOrientation,a=s&&n.retainExif;if(!sm||o||a){var c=new im;this.reader=c,c.onload=function(n){var r=n.target.result,s={},c=1;o&&(c=qf(r))>1&&bf(s,function(e){var t=0,n=1,r=1;switch(e){case 2:n=-1;break;case 3:t=-180;break;case 4:r=-1;break;case 5:t=90,r=-1;break;case 6:t=90;break;case 7:t=90,n=-1;break;case 8:t=-90}return{rotate:t,scaleX:n,scaleY:r}}(c)),a&&(e.exif=function(e){for(var t=Mf(new Uint8Array(e)),n=t.length,r=[],i=0;i+3<n;){var s=t[i],o=t[i+1];if(255===s&&218===o)break;if(255===s&&216===o)i+=2;else{var a=i+(256*t[i+2]+t[i+3])+2,c=t.slice(i,a);r.push(c),i=a}}return r.reduce((function(e,t){return 255===t[0]&&225===t[1]?e.concat(t):e}),[])}(r)),s.url=o||a?!sm||c>1?$f(r,i):sm.createObjectURL(t):r,e.load(s)},c.onabort=function(){e.fail(new Error("Aborted to read the image with FileReader."))},c.onerror=function(){e.fail(new Error("Failed to read the image with FileReader."))},c.onloadend=function(){e.reader=null},o||a?c.readAsArrayBuffer(t):c.readAsDataURL(t)}else this.load({url:sm.createObjectURL(t)})}else this.fail(new Error("The current browser does not support image compression."));else this.fail(new Error("The first argument must be an image File or Blob object."))}else this.fail(new Error("The first argument must be a File or Blob object."))}},{key:"load",value:function(e){var t=this,n=this.file,r=this.image;r.onload=function(){t.draw(_f(_f({},e),{},{naturalWidth:r.naturalWidth,naturalHeight:r.naturalHeight}))},r.onabort=function(){t.fail(new Error("Aborted to load the image."))},r.onerror=function(){t.fail(new Error("Failed to load the image."))},Nf.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(Nf.navigator.userAgent)&&(r.crossOrigin="anonymous"),r.alt=n.name,r.src=e.url}},{key:"draw",value:function(e){var t=this,n=e.naturalWidth,r=e.naturalHeight,i=e.rotate,s=void 0===i?0:i,o=e.scaleX,a=void 0===o?1:o,c=e.scaleY,l=void 0===c?1:c,u=this.file,h=this.image,d=this.options,p=document.createElement("canvas"),f=p.getContext("2d"),m=Math.abs(s)%180==90,g=("contain"===d.resize||"cover"===d.resize)&&Df(d.width)&&Df(d.height),_=Math.max(d.maxWidth,0)||1/0,y=Math.max(d.maxHeight,0)||1/0,v=Math.max(d.minWidth,0)||0,b=Math.max(d.minHeight,0)||0,w=n/r,C=d.width,T=d.height;if(m){var k=[y,_];_=k[0],y=k[1];var S=[b,v];v=S[0],b=S[1];var E=[T,C];C=E[0],T=E[1]}g&&(w=C/T);var I=zf({aspectRatio:w,width:_,height:y},"contain");_=I.width,y=I.height;var x=zf({aspectRatio:w,width:v,height:b},"cover");if(v=x.width,b=x.height,g){var R=zf({aspectRatio:w,width:C,height:T},d.resize);C=R.width,T=R.height}else{var A=zf({aspectRatio:w,width:C,height:T}),P=A.width;C=void 0===P?n:P;var O=A.height;T=void 0===O?r:O}var N=-(C=Math.floor(Hf(Math.min(Math.max(C,v),_))))/2,D=-(T=Math.floor(Hf(Math.min(Math.max(T,b),y))))/2,L=C,M=T,F=[];if(g){var j,U,B,$,q=zf({aspectRatio:w,width:n,height:r},{contain:"cover",cover:"contain"}[d.resize]);B=q.width,$=q.height,j=(n-B)/2,U=(r-$)/2,F.push(j,U,B,$)}if(F.push(N,D,L,M),m){var W=[T,C];C=W[0],T=W[1]}p.width=C,p.height=T,jf(d.mimeType)||(d.mimeType=u.type);var H="transparent";u.size>d.convertSize&&d.convertTypes.indexOf(d.mimeType)>=0&&(d.mimeType="image/jpeg");var z="image/jpeg"===d.mimeType;if(z&&(H="#fff"),f.fillStyle=H,f.fillRect(0,0,C,T),d.beforeDraw&&d.beforeDraw.call(this,f,p),!this.aborted&&(f.save(),f.translate(C/2,T/2),f.rotate(s*Math.PI/180),f.scale(a,l),f.drawImage.apply(f,[h].concat(F)),f.restore(),d.drew&&d.drew.call(this,f,p),!this.aborted)){var V=function(e){if(!t.aborted){var i=function(e){return t.done({naturalWidth:n,naturalHeight:r,result:e})};if(e&&z&&d.retainExif&&t.exif&&t.exif.length>0){var s=function(e){return i(Pf($f(function(e,t){var n=Mf(new Uint8Array(e));if(255!==n[2]||224!==n[3])return e;var r=256*n[4]+n[5],i=[255,216].concat(t,n.slice(4+r));return new Uint8Array(i)}(e,t.exif),d.mimeType)))};if(e.arrayBuffer)e.arrayBuffer().then(s).catch((function(){t.fail(new Error("Failed to read the compressed image with Blob.arrayBuffer()."))}));else{var o=new im;t.reader=o,o.onload=function(e){var t=e.target;s(t.result)},o.onabort=function(){t.fail(new Error("Aborted to read the compressed image with FileReader."))},o.onerror=function(){t.fail(new Error("Failed to read the compressed image with FileReader."))},o.onloadend=function(){t.reader=null},o.readAsArrayBuffer(e)}}else i(e)}};p.toBlob?p.toBlob(V,d.mimeType,d.quality):V(Pf(p.toDataURL(d.mimeType,d.quality)))}}},{key:"done",value:function(e){var t,n,r=e.naturalWidth,i=e.naturalHeight,s=e.result,o=this.file,a=this.image,c=this.options;if(sm&&0===a.src.indexOf("blob:")&&sm.revokeObjectURL(a.src),s)if(c.strict&&!c.retainExif&&s.size>o.size&&c.mimeType===o.type&&!(c.width>r||c.height>i||c.minWidth>r||c.minHeight>i||c.maxWidth<r||c.maxHeight<i))s=o;else{var l=new Date;s.lastModified=l.getTime(),s.lastModifiedDate=l,s.name=o.name,s.name&&s.type!==o.type&&(s.name=s.name.replace(om,(t=s.type,"jpeg"===(n=jf(t)?t.substr(6):"")&&(n="jpg"),".".concat(n))))}else s=o;this.result=s,c.success&&c.success.call(this,s)}},{key:"fail",value:function(e){var t=this.options;if(!t.error)throw e;t.error.call(this,e)}},{key:"abort",value:function(){this.aborted||(this.aborted=!0,this.reader?this.reader.abort():this.image.complete?this.fail(new Error("The compression process has been aborted.")):(this.image.onload=null,this.image.onabort()))}}],r=[{key:"noConflict",value:function(){return window.Compressor=am,e}},{key:"setDefaults",value:function(e){bf(Of,e)}}],n&&yf(t.prototype,n),r&&yf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),lm={exports:{}},um={},hm={exports:{}},dm={};function pm(){if(Vf)return dm;function e(){var e={"align-content":!1,"align-items":!1,"align-self":!1,"alignment-adjust":!1,"alignment-baseline":!1,all:!1,"anchor-point":!1,animation:!1,"animation-delay":!1,"animation-direction":!1,"animation-duration":!1,"animation-fill-mode":!1,"animation-iteration-count":!1,"animation-name":!1,"animation-play-state":!1,"animation-timing-function":!1,azimuth:!1,"backface-visibility":!1,background:!0,"background-attachment":!0,"background-clip":!0,"background-color":!0,"background-image":!0,"background-origin":!0,"background-position":!0,"background-repeat":!0,"background-size":!0,"baseline-shift":!1,binding:!1,bleed:!1,"bookmark-label":!1,"bookmark-level":!1,"bookmark-state":!1,border:!0,"border-bottom":!0,"border-bottom-color":!0,"border-bottom-left-radius":!0,"border-bottom-right-radius":!0,"border-bottom-style":!0,"border-bottom-width":!0,"border-collapse":!0,"border-color":!0,"border-image":!0,"border-image-outset":!0,"border-image-repeat":!0,"border-image-slice":!0,"border-image-source":!0,"border-image-width":!0,"border-left":!0,"border-left-color":!0,"border-left-style":!0,"border-left-width":!0,"border-radius":!0,"border-right":!0,"border-right-color":!0,"border-right-style":!0,"border-right-width":!0,"border-spacing":!0,"border-style":!0,"border-top":!0,"border-top-color":!0,"border-top-left-radius":!0,"border-top-right-radius":!0,"border-top-style":!0,"border-top-width":!0,"border-width":!0,bottom:!1,"box-decoration-break":!0,"box-shadow":!0,"box-sizing":!0,"box-snap":!0,"box-suppress":!0,"break-after":!0,"break-before":!0,"break-inside":!0,"caption-side":!1,chains:!1,clear:!0,clip:!1,"clip-path":!1,"clip-rule":!1,color:!0,"color-interpolation-filters":!0,"column-count":!1,"column-fill":!1,"column-gap":!1,"column-rule":!1,"column-rule-color":!1,"column-rule-style":!1,"column-rule-width":!1,"column-span":!1,"column-width":!1,columns:!1,contain:!1,content:!1,"counter-increment":!1,"counter-reset":!1,"counter-set":!1,crop:!1,cue:!1,"cue-after":!1,"cue-before":!1,cursor:!1,direction:!1,display:!0,"display-inside":!0,"display-list":!0,"display-outside":!0,"dominant-baseline":!1,elevation:!1,"empty-cells":!1,filter:!1,flex:!1,"flex-basis":!1,"flex-direction":!1,"flex-flow":!1,"flex-grow":!1,"flex-shrink":!1,"flex-wrap":!1,float:!1,"float-offset":!1,"flood-color":!1,"flood-opacity":!1,"flow-from":!1,"flow-into":!1,font:!0,"font-family":!0,"font-feature-settings":!0,"font-kerning":!0,"font-language-override":!0,"font-size":!0,"font-size-adjust":!0,"font-stretch":!0,"font-style":!0,"font-synthesis":!0,"font-variant":!0,"font-variant-alternates":!0,"font-variant-caps":!0,"font-variant-east-asian":!0,"font-variant-ligatures":!0,"font-variant-numeric":!0,"font-variant-position":!0,"font-weight":!0,grid:!1,"grid-area":!1,"grid-auto-columns":!1,"grid-auto-flow":!1,"grid-auto-rows":!1,"grid-column":!1,"grid-column-end":!1,"grid-column-start":!1,"grid-row":!1,"grid-row-end":!1,"grid-row-start":!1,"grid-template":!1,"grid-template-areas":!1,"grid-template-columns":!1,"grid-template-rows":!1,"hanging-punctuation":!1,height:!0,hyphens:!1,icon:!1,"image-orientation":!1,"image-resolution":!1,"ime-mode":!1,"initial-letters":!1,"inline-box-align":!1,"justify-content":!1,"justify-items":!1,"justify-self":!1,left:!1,"letter-spacing":!0,"lighting-color":!0,"line-box-contain":!1,"line-break":!1,"line-grid":!1,"line-height":!1,"line-snap":!1,"line-stacking":!1,"line-stacking-ruby":!1,"line-stacking-shift":!1,"line-stacking-strategy":!1,"list-style":!0,"list-style-image":!0,"list-style-position":!0,"list-style-type":!0,margin:!0,"margin-bottom":!0,"margin-left":!0,"margin-right":!0,"margin-top":!0,"marker-offset":!1,"marker-side":!1,marks:!1,mask:!1,"mask-box":!1,"mask-box-outset":!1,"mask-box-repeat":!1,"mask-box-slice":!1,"mask-box-source":!1,"mask-box-width":!1,"mask-clip":!1,"mask-image":!1,"mask-origin":!1,"mask-position":!1,"mask-repeat":!1,"mask-size":!1,"mask-source-type":!1,"mask-type":!1,"max-height":!0,"max-lines":!1,"max-width":!0,"min-height":!0,"min-width":!0,"move-to":!1,"nav-down":!1,"nav-index":!1,"nav-left":!1,"nav-right":!1,"nav-up":!1,"object-fit":!1,"object-position":!1,opacity:!1,order:!1,orphans:!1,outline:!1,"outline-color":!1,"outline-offset":!1,"outline-style":!1,"outline-width":!1,overflow:!1,"overflow-wrap":!1,"overflow-x":!1,"overflow-y":!1,padding:!0,"padding-bottom":!0,"padding-left":!0,"padding-right":!0,"padding-top":!0,page:!1,"page-break-after":!1,"page-break-before":!1,"page-break-inside":!1,"page-policy":!1,pause:!1,"pause-after":!1,"pause-before":!1,perspective:!1,"perspective-origin":!1,pitch:!1,"pitch-range":!1,"play-during":!1,position:!1,"presentation-level":!1,quotes:!1,"region-fragment":!1,resize:!1,rest:!1,"rest-after":!1,"rest-before":!1,richness:!1,right:!1,rotation:!1,"rotation-point":!1,"ruby-align":!1,"ruby-merge":!1,"ruby-position":!1,"shape-image-threshold":!1,"shape-outside":!1,"shape-margin":!1,size:!1,speak:!1,"speak-as":!1,"speak-header":!1,"speak-numeral":!1,"speak-punctuation":!1,"speech-rate":!1,stress:!1,"string-set":!1,"tab-size":!1,"table-layout":!1,"text-align":!0,"text-align-last":!0,"text-combine-upright":!0,"text-decoration":!0,"text-decoration-color":!0,"text-decoration-line":!0,"text-decoration-skip":!0,"text-decoration-style":!0,"text-emphasis":!0,"text-emphasis-color":!0,"text-emphasis-position":!0,"text-emphasis-style":!0,"text-height":!0,"text-indent":!0,"text-justify":!0,"text-orientation":!0,"text-overflow":!0,"text-shadow":!0,"text-space-collapse":!0,"text-transform":!0,"text-underline-position":!0,"text-wrap":!0,top:!1,transform:!1,"transform-origin":!1,"transform-style":!1,transition:!1,"transition-delay":!1,"transition-duration":!1,"transition-property":!1,"transition-timing-function":!1,"unicode-bidi":!1,"vertical-align":!1,visibility:!1,"voice-balance":!1,"voice-duration":!1,"voice-family":!1,"voice-pitch":!1,"voice-range":!1,"voice-rate":!1,"voice-stress":!1,"voice-volume":!1,volume:!1,"white-space":!1,widows:!1,width:!0,"will-change":!1,"word-break":!0,"word-spacing":!0,"word-wrap":!0,"wrap-flow":!1,"wrap-through":!1,"writing-mode":!1,"z-index":!1};return e}Vf=1;var t=/javascript\s*\:/gim;return dm.whiteList=e(),dm.getDefaultWhiteList=e,dm.onAttr=function(e,t,n){},dm.onIgnoreAttr=function(e,t,n){},dm.safeAttrValue=function(e,n){return t.test(n)?"":n},dm}function fm(){return Gf?Kf:(Gf=1,Kf={indexOf:function(e,t){var n,r;if(Array.prototype.indexOf)return e.indexOf(t);for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},forEach:function(e,t,n){var r,i;if(Array.prototype.forEach)return e.forEach(t,n);for(r=0,i=e.length;r<i;r++)t.call(n,e[r],r,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(e){return String.prototype.trimRight?e.trimRight():e.replace(/(\s*$)/g,"")}})}function mm(){if(Xf)return Qf;Xf=1;var e=pm(),t=function(){if(Jf)return Yf;Jf=1;var e=fm();return Yf=function(t,n){";"!==(t=e.trimRight(t))[t.length-1]&&(t+=";");var r=t.length,i=!1,s=0,o=0,a="";function c(){if(!i){var r=e.trim(t.slice(s,o)),c=r.indexOf(":");if(-1!==c){var l=e.trim(r.slice(0,c)),u=e.trim(r.slice(c+1));if(l){var h=n(s,a.length,l,u,r);h&&(a+=h+"; ")}}}s=o+1}for(;o<r;o++){var l=t[o];if("/"===l&&"*"===t[o+1]){var u=t.indexOf("*/",o+2);if(-1===u)break;s=(o=u+1)+1,i=!1}else"("===l?i=!0:")"===l?i=!1:";"===l?i||c():"\n"===l&&c()}return e.trim(a)}}();function n(e){return null==e}function r(t){(t=function(e){var t={};for(var n in e)t[n]=e[n];return t}(t||{})).whiteList=t.whiteList||e.whiteList,t.onAttr=t.onAttr||e.onAttr,t.onIgnoreAttr=t.onIgnoreAttr||e.onIgnoreAttr,t.safeAttrValue=t.safeAttrValue||e.safeAttrValue,this.options=t}return fm(),r.prototype.process=function(e){if(!(e=(e=e||"").toString()))return"";var r=this.options,i=r.whiteList,s=r.onAttr,o=r.onIgnoreAttr,a=r.safeAttrValue;return t(e,(function(e,t,r,c,l){var u=i[r],h=!1;if(!0===u?h=u:"function"==typeof u?h=u(c):u instanceof RegExp&&(h=u.test(c)),!0!==h&&(h=!1),c=a(r,c)){var d,p={position:t,sourcePosition:e,source:l,isWhite:h};return h?n(d=s(r,c,p))?r+":"+c:d:n(d=o(r,c,p))?void 0:d}}))},Qf=r}function gm(){return Zf||(Zf=1,function(e,t){var n=pm(),r=mm();for(var i in(t=e.exports=function(e,t){return new r(t).process(e)}).FilterCSS=r,n)t[i]=n[i];"undefined"!=typeof window&&(window.filterCSS=e.exports)}(hm,hm.exports)),hm.exports}function _m(){return tm?em:(tm=1,em={indexOf:function(e,t){var n,r;if(Array.prototype.indexOf)return e.indexOf(t);for(n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},forEach:function(e,t,n){var r,i;if(Array.prototype.forEach)return e.forEach(t,n);for(r=0,i=e.length;r<i;r++)t.call(n,e[r],r,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(e){var t=/\s|\n|\t/.exec(e);return t?t.index:-1}})}function ym(){if(nm)return um;nm=1;var e=gm().FilterCSS,t=gm().getDefaultWhiteList,n=_m();function r(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var i=new e;function s(e){return e.replace(o,"&lt;").replace(a,"&gt;")}var o=/</g,a=/>/g,c=/"/g,l=/&quot;/g,u=/&#([a-zA-Z0-9]*);?/gim,h=/&colon;?/gim,d=/&newline;?/gim,p=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,f=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,m=/u\s*r\s*l\s*\(.*/gi;function g(e){return e.replace(c,"&quot;")}function _(e){return e.replace(l,'"')}function y(e){return e.replace(u,(function(e,t){return"x"===t[0]||"X"===t[0]?String.fromCharCode(parseInt(t.substr(1),16)):String.fromCharCode(parseInt(t,10))}))}function v(e){return e.replace(h,":").replace(d," ")}function b(e){for(var t="",r=0,i=e.length;r<i;r++)t+=e.charCodeAt(r)<32?" ":e.charAt(r);return n.trim(t)}function w(e){return e=b(e=v(e=y(e=_(e))))}function C(e){return e=s(e=g(e))}return um.whiteList={a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]},um.getDefaultWhiteList=r,um.onTag=function(e,t,n){},um.onIgnoreTag=function(e,t,n){},um.onTagAttr=function(e,t,n){},um.onIgnoreTagAttr=function(e,t,n){},um.safeAttrValue=function(e,t,r,s){if(r=w(r),"href"===t||"src"===t){if("#"===(r=n.trim(r)))return"#";if("http://"!==r.substr(0,7)&&"https://"!==r.substr(0,8)&&"mailto:"!==r.substr(0,7)&&"tel:"!==r.substr(0,4)&&"data:image/"!==r.substr(0,11)&&"ftp://"!==r.substr(0,6)&&"./"!==r.substr(0,2)&&"../"!==r.substr(0,3)&&"#"!==r[0]&&"/"!==r[0])return""}else if("background"===t){if(p.lastIndex=0,p.test(r))return""}else if("style"===t){if(f.lastIndex=0,f.test(r))return"";if(m.lastIndex=0,m.test(r)&&(p.lastIndex=0,p.test(r)))return"";!1!==s&&(r=(s=s||i).process(r))}return r=C(r)},um.escapeHtml=s,um.escapeQuote=g,um.unescapeQuote=_,um.escapeHtmlEntities=y,um.escapeDangerHtml5Entities=v,um.clearNonPrintableCharacter=b,um.friendlyAttrValue=w,um.escapeAttrValue=C,um.onIgnoreTagStripAll=function(){return""},um.StripTagBody=function(e,t){"function"!=typeof t&&(t=function(){});var r=!Array.isArray(e),i=[],s=!1;return{onIgnoreTag:function(o,a,c){if(function(t){return!!r||-1!==n.indexOf(e,t)}(o)){if(c.isClosing){var l="[/removed]",u=c.position+10;return i.push([!1!==s?s:c.position,u]),s=!1,l}return s||(s=c.position),"[removed]"}return t(o,a,c)},remove:function(e){var t="",r=0;return n.forEach(i,(function(n){t+=e.slice(r,n[0]),r=n[1]})),t+=e.slice(r)}}},um.stripCommentTag=function(e){for(var t="",n=0;n<e.length;){var r=e.indexOf("\x3c!--",n);if(-1===r){t+=e.slice(n);break}t+=e.slice(n,r);var i=e.indexOf("--\x3e",r);if(-1===i)break;n=i+3}return t},um.stripBlankChar=function(e){var t=e.split("");return(t=t.filter((function(e){var t=e.charCodeAt(0);return 127!==t&&(!(t<=31)||(10===t||13===t))}))).join("")},um.attributeWrapSign='"',um.cssFilter=i,um.getDefaultCSSWhiteList=t,um}var vm,bm,wm,Cm,Tm={};function km(){if(vm)return Tm;vm=1;var e=_m();function t(t){var n,r=e.spaceIndex(t);return n=-1===r?t.slice(1,-1):t.slice(1,r+1),"/"===(n=e.trim(n).toLowerCase()).slice(0,1)&&(n=n.slice(1)),"/"===n.slice(-1)&&(n=n.slice(0,-1)),n}function n(e){return"</"===e.slice(0,2)}var r=/[^a-zA-Z0-9\\_:.-]/gim;function i(e,t){for(;t<e.length;t++){var n=e[t];if(" "!==n)return"="===n?t:-1}}function s(e,t){for(;t<e.length;t++){var n=e[t];if(" "!==n)return"'"===n||'"'===n?t:-1}}function o(e,t){for(;t>0;t--){var n=e[t];if(" "!==n)return"="===n?t:-1}}function a(e){return function(e){return'"'===e[0]&&'"'===e[e.length-1]||"'"===e[0]&&"'"===e[e.length-1]}(e)?e.substr(1,e.length-2):e}return Tm.parseTag=function(e,r,i){var s="",o=0,a=!1,c=!1,l=0,u=e.length,h="",d="";e:for(l=0;l<u;l++){var p=e.charAt(l);if(!1===a){if("<"===p){a=l;continue}}else if(!1===c){if("<"===p){s+=i(e.slice(o,l)),a=l,o=l;continue}if(">"===p||l===u-1){s+=i(e.slice(o,a)),h=t(d=e.slice(a,l+1)),s+=r(a,s.length,h,d,n(d)),o=l+1,a=!1;continue}if('"'===p||"'"===p)for(var f=1,m=e.charAt(l-f);""===m.trim()||"="===m;){if("="===m){c=p;continue e}m=e.charAt(l-++f)}}else if(p===c){c=!1;continue}}return o<u&&(s+=i(e.substr(o))),s},Tm.parseAttr=function(t,n){var c=0,l=0,u=[],h=!1,d=t.length;function p(t,i){if(!((t=(t=e.trim(t)).replace(r,"").toLowerCase()).length<1)){var s=n(t,i||"");s&&u.push(s)}}for(var f=0;f<d;f++){var m,g=t.charAt(f);if(!1!==h||"="!==g)if(!1===h||f!==l)if(/\s|\n|\t/.test(g)){if(t=t.replace(/\s|\n|\t/g," "),!1===h){if(-1===(m=i(t,f))){p(e.trim(t.slice(c,f))),h=!1,c=f+1;continue}f=m-1;continue}if(-1===(m=o(t,f-1))){p(h,a(e.trim(t.slice(c,f)))),h=!1,c=f+1;continue}}else;else{if(-1===(m=t.indexOf(g,f+1)))break;p(h,e.trim(t.slice(l+1,m))),h=!1,c=(f=m)+1}else h=t.slice(c,f),c=f+1,l='"'===t.charAt(c)||"'"===t.charAt(c)?c:s(t,f+1)}return c<t.length&&(!1===h?p(t.slice(c)):p(h,a(e.trim(t.slice(c))))),e.trim(u.join(" "))},Tm}function Sm(){if(wm)return bm;wm=1;var e=gm().FilterCSS,t=ym(),n=km(),r=n.parseTag,i=n.parseAttr,s=_m();function o(e){return null==e}function a(n){(n=function(e){var t={};for(var n in e)t[n]=e[n];return t}(n||{})).stripIgnoreTag&&(n.onIgnoreTag,n.onIgnoreTag=t.onIgnoreTagStripAll),n.whiteList||n.allowList?n.whiteList=function(e){var t={};for(var n in e)Array.isArray(e[n])?t[n.toLowerCase()]=e[n].map((function(e){return e.toLowerCase()})):t[n.toLowerCase()]=e[n];return t}(n.whiteList||n.allowList):n.whiteList=t.whiteList,this.attributeWrapSign=!0===n.singleQuotedAttributeValue?"'":t.attributeWrapSign,n.onTag=n.onTag||t.onTag,n.onTagAttr=n.onTagAttr||t.onTagAttr,n.onIgnoreTag=n.onIgnoreTag||t.onIgnoreTag,n.onIgnoreTagAttr=n.onIgnoreTagAttr||t.onIgnoreTagAttr,n.safeAttrValue=n.safeAttrValue||t.safeAttrValue,n.escapeHtml=n.escapeHtml||t.escapeHtml,this.options=n,!1===n.css?this.cssFilter=!1:(n.css=n.css||{},this.cssFilter=new e(n.css))}return a.prototype.process=function(e){if(!(e=(e=e||"").toString()))return"";var n=this,a=n.options,c=a.whiteList,l=a.onTag,u=a.onIgnoreTag,h=a.onTagAttr,d=a.onIgnoreTagAttr,p=a.safeAttrValue,f=a.escapeHtml,m=n.attributeWrapSign,g=n.cssFilter;a.stripBlankChar&&(e=t.stripBlankChar(e)),a.allowCommentTag||(e=t.stripCommentTag(e));var _=!1;a.stripIgnoreTagBody&&(_=t.StripTagBody(a.stripIgnoreTagBody,u),u=_.onIgnoreTag);var y=r(e,(function(e,t,n,r,a){var _={sourcePosition:e,position:t,isClosing:a,isWhite:Object.prototype.hasOwnProperty.call(c,n)},y=l(n,r,_);if(!o(y))return y;if(_.isWhite){if(_.isClosing)return"</"+n+">";var v=function(e){var t=s.spaceIndex(e);if(-1===t)return{html:"",closing:"/"===e[e.length-2]};var n="/"===(e=s.trim(e.slice(t+1,-1)))[e.length-1];return n&&(e=s.trim(e.slice(0,-1))),{html:e,closing:n}}(r),b=c[n],w=i(v.html,(function(e,t){var r=-1!==s.indexOf(b,e),i=h(n,e,t,r);return o(i)?r?(t=p(n,e,t,g))?e+"="+m+t+m:e:o(i=d(n,e,t,r))?void 0:i:i}));return r="<"+n,w&&(r+=" "+w),v.closing&&(r+=" /"),r+=">"}return o(y=u(n,r,_))?f(r):y}),f);return _&&(y=_.remove(y)),y},bm=a}var Em=(Cm||(Cm=1,function(e,t){var n=ym(),r=km(),i=Sm();function s(e,t){return new i(t).process(e)}(t=e.exports=s).filterXSS=s,t.FilterXSS=i,function(){for(var e in n)t[e]=n[e];for(var i in r)t[i]=r[i]}(),"undefined"!=typeof window&&(window.filterXSS=e.exports),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=e.exports)}(lm,lm.exports)),lm.exports);const Im=e(Em);export{$r as A,us as B,je as C,nt as D,Dl as E,Hh as F,zh as G,Xp as H,hs as I,cm as J,Im as K,rt as M,tt as N,yt as a,Et as b,W as c,At as d,mt as e,He as f,gt as g,St as h,Tt as i,Rt as j,Ee as k,jt as l,xt as m,It as n,kt as o,qt as p,lt as q,ct as r,dt as s,Ut as t,wt as u,ut as v,Re as w,Ie as x,et as y,r as z};
