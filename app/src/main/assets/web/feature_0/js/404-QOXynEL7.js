import{n as a}from"./navbar-DfgFEjpa.js";import{_ as s,r as e}from"./index-M11nEPjl.js";import{I as n,J as t,K as o,j as r,S as c,V as l}from"./vue-vendor-DjIN0JG5.js";import"./vant-vendor-D8PsFlrJ.js";import"./vendor-CwRwASPO.js";const d={class:"error-page"},i={class:"content"};const p=s({components:{navBar:a},methods:{handleNavBarClickLeft(){e("501")}}},[["render",function(a,s,e,p,v,f){const m=n("nav-bar");return o(),t("div",d,[r(m,{onClickLeft:f.handleNavBarClickLeft},null,8,["onClickLeft"]),s[0]||(s[0]=c("p",{class:"title"},"404",-1)),c("p",i,l(a.$t("Page not found, please contact sales.")),1)])}],["__scopeId","data-v-0c9e2093"]]);export{p as default};
