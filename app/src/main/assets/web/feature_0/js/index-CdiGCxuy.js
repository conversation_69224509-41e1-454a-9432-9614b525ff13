import{a1 as e,c as t}from"./vue-vendor-DjIN0JG5.js";const r=t({strategyId:"",strategyNo:"",stUserId:"",avatar:"",strategyName:"",sourceAccount:"",description:"",paymentAccount:null,paymentAccountServerId:"",paymentAccountPlatform:"",paymentAccountCurrency:"",profitShareRatio:"",settlementFrequency:0,currentSettlementFrequency:0,nextSettlementTime:"",loginAccountCurrency:"",copierReview:!1,reviewType:0,minInvestmentPerCopy:0,minLotsPerOrder:0,minLotsMultiplePerOrder:0,info:{},paymentAccountList:[],settlementFrequencyList:[]}),n={state:e(r),setState:(e,t)=>{r.hasOwnProperty(e)&&(r[e]=t)},setAllState:e=>{Object.keys(e).forEach((t=>{r.hasOwnProperty(t)&&(r[t]=e[t])}))},clearState:()=>{const e={strategyId:"",strategyNo:"",stUserId:"",avatar:"",strategyName:"",sourceAccount:"",description:"",paymentAccount:null,paymentAccountServerId:"",paymentAccountPlatform:"",paymentAccountCurrency:"",profitShareRatio:"",settlementFrequency:0,currentSettlementFrequency:0,nextSettlementTime:"",loginAccountCurrency:"",copierReview:!1,reviewType:0,minInvestmentPerCopy:0,minLotsPerOrder:0,minLotsMultiplePerOrder:0,info:{},paymentAccountList:[],settlementFrequencyList:[]};Object.keys(r).forEach((t=>{r[t]=e[t]}))}};export{n as s};
