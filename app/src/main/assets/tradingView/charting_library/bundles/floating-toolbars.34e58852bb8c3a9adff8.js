(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2306],{66783:t=>{"use strict";var e=Object.prototype.hasOwnProperty;function o(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}t.exports=function(t,i){if(o(t,i))return!0;if("object"!=typeof t||null===t||"object"!=typeof i||null===i)return!1;var n=Object.keys(t),r=Object.keys(i);if(n.length!==r.length)return!1;for(var s=0;s<n.length;s++)if(!e.call(i,n[s])||!o(t[n[s]],i[n[s]]))return!1;return!0}},26006:t=>{t.exports={}},51338:t=>{t.exports={}},1414:t=>{t.exports={button:"button-D4RPB3ZC",content:"content-D4RPB3ZC","icon-only":"icon-only-D4RPB3ZC",link:"link-D4RPB3ZC","color-brand":"color-brand-D4RPB3ZC","variant-primary":"variant-primary-D4RPB3ZC","variant-secondary":"variant-secondary-D4RPB3ZC","color-gray":"color-gray-D4RPB3ZC","color-green":"color-green-D4RPB3ZC","color-red":"color-red-D4RPB3ZC","color-black":"color-black-D4RPB3ZC","size-xsmall":"size-xsmall-D4RPB3ZC","start-icon-wrap":"start-icon-wrap-D4RPB3ZC","end-icon-wrap":"end-icon-wrap-D4RPB3ZC","with-start-icon":"with-start-icon-D4RPB3ZC","with-end-icon":"with-end-icon-D4RPB3ZC","size-small":"size-small-D4RPB3ZC","size-medium":"size-medium-D4RPB3ZC","size-large":"size-large-D4RPB3ZC","size-xlarge":"size-xlarge-D4RPB3ZC",animated:"animated-D4RPB3ZC",stretch:"stretch-D4RPB3ZC",grouped:"grouped-D4RPB3ZC","adjust-position":"adjust-position-D4RPB3ZC","first-row":"first-row-D4RPB3ZC","first-col":"first-col-D4RPB3ZC","no-corner-top-left":"no-corner-top-left-D4RPB3ZC","no-corner-top-right":"no-corner-top-right-D4RPB3ZC","no-corner-bottom-right":"no-corner-bottom-right-D4RPB3ZC","no-corner-bottom-left":"no-corner-bottom-left-D4RPB3ZC","text-wrap":"text-wrap-D4RPB3ZC","multiline-content":"multiline-content-D4RPB3ZC","secondary-text":"secondary-text-D4RPB3ZC","primary-text":"primary-text-D4RPB3ZC"}},70132:t=>{t.exports={}},19619:t=>{t.exports={}},65719:t=>{t.exports={}},12005:t=>{t.exports={wrap:"wrap-Nn3SCuEL",icon:"icon-Nn3SCuEL",colorBg:"colorBg-Nn3SCuEL",color:"color-Nn3SCuEL",multicolor:"multicolor-Nn3SCuEL",white:"white-Nn3SCuEL"}},31188:t=>{t.exports={button:"button-BuUjli6L"}},20835:t=>{t.exports={item:"item-KdWj36gM",withIcon:"withIcon-KdWj36gM",icon:"icon-KdWj36gM",labelRow:"labelRow-KdWj36gM",multiWidth:"multiWidth-KdWj36gM",buttonWrap:"buttonWrap-KdWj36gM",buttonLabel:"buttonLabel-KdWj36gM"}},93402:t=>{t.exports={container:"container-mdcOkvbj",sectionTitle:"sectionTitle-mdcOkvbj",separator:"separator-mdcOkvbj",customButton:"customButton-mdcOkvbj",accessible:"accessible-mdcOkvbj"}},80679:t=>{t.exports={container:"container-iiEYaqPD",form:"form-iiEYaqPD",swatch:"swatch-iiEYaqPD",inputWrap:"inputWrap-iiEYaqPD",inputHash:"inputHash-iiEYaqPD",input:"input-iiEYaqPD",buttonWrap:"buttonWrap-iiEYaqPD",hueSaturationWrap:"hueSaturationWrap-iiEYaqPD",saturation:"saturation-iiEYaqPD",hue:"hue-iiEYaqPD"}},1369:t=>{t.exports={hue:"hue-r4uo5Wn6",pointer:"pointer-r4uo5Wn6",pointerContainer:"pointerContainer-r4uo5Wn6"}},30099:t=>{t.exports={opacity:"opacity-EnWts7Xu",
opacitySlider:"opacitySlider-EnWts7Xu",opacitySliderGradient:"opacitySliderGradient-EnWts7Xu",pointer:"pointer-EnWts7Xu",dragged:"dragged-EnWts7Xu",opacityPointerWrap:"opacityPointerWrap-EnWts7Xu",opacityInputWrap:"opacityInputWrap-EnWts7Xu",opacityInput:"opacityInput-EnWts7Xu",opacityInputPercent:"opacityInputPercent-EnWts7Xu",accessible:"accessible-EnWts7Xu"}},35257:t=>{t.exports={saturation:"saturation-NFNfqP2w",pointer:"pointer-NFNfqP2w"}},87466:t=>{t.exports={swatches:"swatches-sfn7Lezv",swatch:"swatch-sfn7Lezv",hover:"hover-sfn7Lezv",empty:"empty-sfn7Lezv",white:"white-sfn7Lezv",selected:"selected-sfn7Lezv",contextItem:"contextItem-sfn7Lezv",row:"row-sfn7Lezv"}},94720:(t,e,o)=>{"use strict";o.d(e,{Button:()=>y});var i=o(50959),n=o(97754),r=o(95604),s=o(9745),a=o(1414),l=o.n(a);function c(t){const{color:e="brand",size:o="medium",variant:i="primary",stretch:s=!1,icon:a,startIcon:c,endIcon:d,iconOnly:h=!1,className:u,isGrouped:p,cellState:g,disablePositionAdjustment:m=!1,primaryText:_,secondaryText:v,isAnchor:f=!1}=t,y=function(t){let e="";return 0!==t&&(1&t&&(e=n(e,l()["no-corner-top-left"])),2&t&&(e=n(e,l()["no-corner-top-right"])),4&t&&(e=n(e,l()["no-corner-bottom-right"])),8&t&&(e=n(e,l()["no-corner-bottom-left"]))),e}((0,r.getGroupCellRemoveRoundBorders)(g));return n(u,l().button,l()[`size-${o}`],l()[`color-${e}`],l()[`variant-${i}`],s&&l().stretch,(a||c)&&l()["with-start-icon"],d&&l()["with-end-icon"],h&&l()["icon-only"],y,p&&l().grouped,p&&!m&&l()["adjust-position"],p&&g.isTop&&l()["first-row"],p&&g.isLeft&&l()["first-col"],_&&v&&l()["multiline-content"],f&&l().link)}function d(t){const{startIcon:e,icon:o,iconOnly:n,children:r,endIcon:a,primaryText:c,secondaryText:d}=t,h=null!=e?e:o,u=!(e||o||a||n)&&!r&&c&&d;return i.createElement(i.Fragment,null,h&&i.createElement(s.Icon,{icon:h,className:l()["start-icon-wrap"]}),r&&i.createElement("span",{className:l().content},r),a&&!n&&i.createElement(s.Icon,{icon:a,className:l()["end-icon-wrap"]}),u&&function(t){return t.primaryText&&t.secondaryText&&i.createElement("div",{className:l()["text-wrap"]},i.createElement("span",{className:l()["primary-text"]}," ",t.primaryText," "),"string"==typeof t.secondaryText?i.createElement("span",{className:l()["secondary-text"]}," ",t.secondaryText," "):i.createElement("span",{className:l()["secondary-text"]},i.createElement("span",null,t.secondaryText.firstLine),i.createElement("span",null,t.secondaryText.secondLine)))}(t))}var h=o(86332),u=o(90186);function p(t){const{className:e,color:o,variant:i,size:n,stretch:r,animated:s,icon:a,iconOnly:l,startIcon:c,endIcon:d,primaryText:h,secondaryText:p,...g}=t;return{...g,...(0,u.filterDataProps)(t),...(0,u.filterAriaProps)(t)}}function g(t){const{reference:e,...o}=t,{isGrouped:n,cellState:r,disablePositionAdjustment:s}=(0,i.useContext)(h.ControlGroupContext),a=c({...o,isGrouped:n,cellState:r,disablePositionAdjustment:s});return i.createElement("button",{...p(o),className:a,ref:e},i.createElement(d,{...o}))}function m(t="default"){switch(t){case"default":return"primary";case"stroke":
return"secondary"}}function _(t="primary"){switch(t){case"primary":return"brand";case"success":return"green";case"default":return"gray";case"danger":return"red"}}function v(t="m"){switch(t){case"s":return"xsmall";case"m":return"small";case"l":return"large"}}function f(t){const{intent:e,size:o,appearance:i,useFullWidth:n,icon:r,...s}=t;return{...s,color:_(e),size:v(o),variant:m(i),stretch:n,startIcon:r}}function y(t){return i.createElement(g,{...f(t)})}},86332:(t,e,o)=>{"use strict";o.d(e,{ControlGroupContext:()=>i});const i=o(50959).createContext({isGrouped:!1,cellState:{isTop:!0,isRight:!0,isBottom:!0,isLeft:!0}})},95604:(t,e,o)=>{"use strict";function i(t){let e=0;return t.isTop&&t.isLeft||(e+=1),t.isTop&&t.isRight||(e+=2),t.isBottom&&t.isLeft||(e+=8),t.isBottom&&t.isRight||(e+=4),e}o.d(e,{getGroupCellRemoveRoundBorders:()=>i})},56073:(t,e,o)=>{"use strict";function i(t,e=!1){const o=getComputedStyle(t),i=[o.height];return"border-box"!==o.boxSizing&&i.push(o.paddingTop,o.paddingBottom,o.borderTopWidth,o.borderBottomWidth),e&&i.push(o.marginTop,o.marginBottom),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}function n(t,e=!1){const o=getComputedStyle(t),i=[o.width];return"border-box"!==o.boxSizing&&i.push(o.paddingLeft,o.paddingRight,o.borderLeftWidth,o.borderRightWidth),e&&i.push(o.marginLeft,o.marginRight),i.reduce(((t,e)=>t+(parseFloat(e)||0)),0)}o.d(e,{outerHeight:()=>i,outerWidth:()=>n})},70114:(t,e,o)=>{"use strict";o.d(e,{ColorPickerButton:()=>v});var i=o(50959),n=o(97754),r=o.n(n),s=o(50151),a=o(9745),l=o(24377),c=o(87095),d=o(56512),h=o(34381),u=o(6914),p=o(8087),g=o(43982),m=o(12811),_=o(12005);function v(t){const{property:e,icon:o,propertyApplier:n,title:v,undoText:f,isToolbarFixed:y,className:b}=t,w=(0,g.useProperty)(e),C=(0,i.useRef)(null),T=w?(0,l.parseRgba)(w)[3]:void 0,x=""===w,S=String(L()).toLowerCase()===u.white,[P,E,W]=(0,d.useCustomColors)();return i.createElement(p.ToolWidgetMenu,{className:b,verticalDropDirection:y?m.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:y?m.HorizontalDropDirection.FromLeftToRight:void 0,horizontalAttachEdge:y?m.HorizontalAttachEdge.Left:void 0,verticalAttachEdge:y?m.VerticalAttachEdge.Top:void 0,content:i.createElement("div",{className:_.wrap},i.createElement(a.Icon,{className:_.icon,icon:o}),i.createElement("div",{className:_.colorBg},i.createElement("div",{className:r()(_.color,x&&_.multicolor,S&&_.white),style:x?void 0:{backgroundColor:w}}))),arrow:!1,title:v,ref:C,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(h.ColorPicker,{color:L(),opacity:T,onColorChange:function(t,e){const o=w?(0,c.alphaToTransparency)((0,l.parseRgba)(w)[3]):0;B((0,c.generateColor)(String(t),o,true)),e||(0,s.ensureNotNull)(C.current).close()},onOpacityChange:function(t){B((0,c.generateColor)(w,(0,c.alphaToTransparency)(t),!0))},selectOpacity:void 0!==T,selectCustom:!0,customColors:P,onAddColor:function(t){E(t),(0,s.ensureNotNull)(C.current).close()},onRemoveCustomColor:W}));function L(){return w?(0,l.rgbToHexString)((0,l.parseRgb)(w)):null}
function B(t){n.setProperty(e,t,f)}}},61259:(t,e,o)=>{"use strict";o.d(e,{LineWidthButton:()=>b});var i=o(50959),n=o(97754),r=o(50151),s=o(9745),a=o(8087),l=o(43982),c=o(16396),d=o(40173),h=o(12811),u=o(22978),p=o(14631),g=o(6096),m=o(6483),_=o(66611),v=o(20835);const f=(0,d.mergeThemes)(c.DEFAULT_POPUP_MENU_ITEM_THEME,v),y=[{value:1,icon:u},{value:2,icon:p},{value:3,icon:g},{value:4,icon:m}];function b(t){const{multipleProperty:e,title:o,undoText:d,propertyApplier:u,isToolbarFixed:p,className:g,isSmallScreen:m}=t,b=(0,l.useProperty)((0,r.ensureDefined)(e)),w="mixed"===b||!b,C=function(t){const e=y.find((e=>e.value===t));if(!e)return _;return e.icon}(b);return i.createElement(a.ToolWidgetMenu,{className:g,arrow:!1,title:o,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:p?h.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:p?h.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:p?h.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:p?h.VerticalAttachEdge.Top:void 0,content:i.createElement("div",null,w?i.createElement("div",{className:v.multiWidth},i.createElement(s.Icon,{icon:_})):i.createElement("div",{className:v.buttonWrap},!m&&i.createElement(s.Icon,{icon:C}),i.createElement("div",{className:n(!m&&v.buttonLabel)},`${b}px`)))},y.map((({value:t,icon:e})=>i.createElement(c.PopupMenuItem,{key:t,theme:f,label:`${t}px`,icon:e,isActive:t===b,onClick:T,onClickArg:t}))));function T(t){t&&e&&(u.beginUndoMacro(d),e.setValue(t,void 0,{applyValue:(t,e)=>{u.setProperty(t,e,d)}}),u.endUndoMacro())}}},43982:(t,e,o)=>{"use strict";o.d(e,{useProperty:()=>n});var i=o(50959);const n=t=>{const[e,o]=(0,i.useState)(t.value());return(0,i.useEffect)((()=>{const e=t=>{o(t.value())};e(t);const i={};return t.subscribe(i,e),()=>t.unsubscribe(i,e)}),[t]),e}},34381:(t,e,o)=>{"use strict";o.d(e,{ColorPicker:()=>Y});var i=o(50959),n=o(97754),r=o.n(n),s=o(44352),a=o(16838),l=o(50151),c=o(68335),d=o(71468);const h=[37,39,38,40];function u(t){const e=(0,i.useRef)(null);return(0,i.useLayoutEffect)((()=>{if(!a.PLATFORM_ACCESSIBILITY_ENABLED)return;const t=(0,l.ensureNotNull)(e.current),o=()=>{const o=(0,a.queryTabbableElements)(t).sort(a.navigationOrderComparator);if(0===o.length||o[0].parentElement&&!m(o[0].parentElement,(0,l.ensureNotNull)(e.current))){const i=function(t){const o=g(t).sort(a.navigationOrderComparator).find((t=>m(t,(0,l.ensureNotNull)(e.current))));if(!o)return null;const i=Array.from(o.children);if(!i.length)return null;return i[0]}(t);if(null===i)return;if((0,d.becomeMainElement)(i),o.length>0)for(const t of o)(0,d.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",o),o(),()=>window.removeEventListener("keyboard-navigation-activation",o)}),[]),[e,function(e){if(!a.PLATFORM_ACCESSIBILITY_ENABLED)return;if(e.defaultPrevented)return;const o=(0,c.hashFromEvent)(e);if(!h.includes(o))return;const i=document.activeElement;if(!(i instanceof HTMLElement))return;const n=e.currentTarget;let r,s;if(t){const t=i.parentElement
;r=t?Array.from(t.children):[],s=r.indexOf(i)}else r=(l=n,Array.from(l.querySelectorAll("button:not([disabled], [aria-disabled])")).filter((0,a.createScopedVisibleElementFilter)(l))).sort(a.navigationOrderComparator),s=r.indexOf(i);var l;if(0===r.length||-1===s)return;const d=o=>{if(!document.activeElement)return;const i=g(n),r=document.activeElement.parentElement;if(!r)return;const s=Array.from(r.children).indexOf(document.activeElement);if(-1===s)return;const a=i["down"===o?i.indexOf(r)+1:i.indexOf(r)-1];if(!a)return;e.preventDefault();const l=Array.from(a.children);l.length&&(!t&&s<=l.length-1?_(l[s]):_(l[0]))};switch(o){case 37:if(e.preventDefault(),!t&&0===s)break;_(p(r,s,-1));break;case 39:if(e.preventDefault(),!t&&s===r.length-1)break;_(p(r,s,1));break;case 38:d("up");break;case 40:d("down")}}]}function p(t,e,o){return t[(e+t.length+o)%t.length]}function g(t){return Array.from(t.querySelectorAll('[data-role="row"]')).filter((0,a.createScopedVisibleElementFilter)(t))}function m(t,e){const o=(0,l.ensureNotNull)(t.parentElement).offsetTop,i=o+(0,l.ensureNotNull)(t.parentElement).clientHeight,n=e.scrollTop,r=n+e.clientHeight;return o>=n&&i<=r}function _(t){document.activeElement&&(0,d.becomeSecondaryElement)(document.activeElement),(0,d.becomeMainElement)(t),t.focus()}var v=o(43688),f=o(93532),y=o(45582),b=Math.ceil,w=Math.max;const C=function(t,e,o){e=(o?(0,f.default)(t,e,o):void 0===e)?1:w((0,y.default)(e),0);var i=null==t?0:t.length;if(!i||e<1)return[];for(var n=0,r=0,s=Array(b(i/e));n<i;)s[r++]=(0,v.default)(t,n,n+=e);return s};var T=o(24377),x=o(49483),S=o(20520),P=o(16396);const E=i.createContext(void 0);var W=o(6914),L=o(50238),B=o(35149),D=o(87466);function k(t){const{index:e,color:r,selected:c,onSelect:d}=t,[h,u]=(0,i.useState)(!1),p=(0,i.useContext)(E),[g,m]=(0,L.useRovingTabindexElement)(null),_=Boolean(p)&&!x.CheckMobile.any();return i.createElement(i.Fragment,null,i.createElement("button",{ref:g,style:r?{color:r}:void 0,className:n(D.swatch,a.PLATFORM_ACCESSIBILITY_ENABLED&&D.accessible,h&&D.hover,c&&D.selected,!r&&D.empty,String(r).toLowerCase()===W.white&&D.white),onClick:function(){d(r)},onContextMenu:_?v:void 0,tabIndex:m,"data-role":"swatch"}),_&&i.createElement(S.PopupMenu,{isOpened:h,onClose:v,position:function(){const t=(0,l.ensureNotNull)(g.current).getBoundingClientRect();return{x:t.left,y:t.top+t.height+4}},onClickOutside:v},i.createElement(P.PopupMenuItem,{className:D.contextItem,label:s.t(null,void 0,o(54336)),icon:B,onClick:function(){v(),(0,l.ensureDefined)(p)(e)},dontClosePopup:!0})));function v(){u(!h)}}function N(t){const{colors:e,color:o,children:n,onSelect:r}=t;if(!e)return null;const s=o?(0,T.parseRgb)(String(o)):void 0,a=C(e,10);return i.createElement("div",{className:D.swatches},a.map(((t,e)=>i.createElement("div",{className:D.row,"data-role":"row",key:e},t.map(((t,e)=>i.createElement(k,{key:String(t)+e,index:e,color:t,selected:s&&(0,T.areEqualRgb)(s,(0,T.parseRgb)(String(t))),onSelect:l})))))),n);function l(t){r&&r(t)}}var I=o(54368),A=o(94720);function M(t){
const e=`Invalid RGB color: ${t}`;if(null===t)throw new Error(e);const o=t.match(/^#?([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/i);if(null===o)throw new Error(e);const[,i,n,r]=o;if(!i||!n||!r)throw new Error(e);const s=parseInt(i,16)/255,a=parseInt(n,16)/255,l=parseInt(r,16)/255,c=Math.max(s,a,l),d=Math.min(s,a,l);let h;const u=c,p=c-d,g=0===c?0:p/c;if(c===d)h=0;else{switch(c){case s:h=(a-l)/p+(a<l?6:0);break;case a:h=(l-s)/p+2;break;case l:h=(s-a)/p+4;break;default:h=0}h/=6}return{h,s:g,v:u}}var R=o(43370),F=o(35257);class V extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{this._container=t},this._handlePosition=t=>{const{hsv:{h:e},onChange:o}=this.props;if(!o)return;const i=(0,l.ensureNotNull)(this._container).getBoundingClientRect(),n=t.clientX-i.left,r=t.clientY-i.top;let s=n/i.width;s<0?s=0:s>1&&(s=1);let a=1-r/i.height;a<0?a=0:a>1&&(a=1),o({h:e,s,v:a})},this._mouseDown=t=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,R.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e,s:o,v:n}}=this.props,s=`hsl(${360*e}, 100%, 50%)`;return i.createElement("div",{className:r()(F.saturation,t),style:{backgroundColor:s},ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:F.pointer,style:{left:100*o+"%",top:100*(1-n)+"%"}}))}}var z=o(1369);class O extends i.PureComponent{constructor(){super(...arguments),this._container=null,this._refContainer=t=>{this._container=t},this._handlePosition=t=>{const{hsv:{s:e,v:o},onChange:i}=this.props;if(!i)return;const n=(0,l.ensureNotNull)(this._container).getBoundingClientRect();let r=(t.clientY-n.top)/n.height;r<0?r=0:r>1&&(r=1),i({h:r,s:e,v:o})},this._mouseDown=t=>{window.addEventListener("mouseup",this._mouseUp),window.addEventListener("mousemove",this._mouseMove)},this._mouseUp=t=>{window.removeEventListener("mousemove",this._mouseMove),window.removeEventListener("mouseup",this._mouseUp),this._handlePosition(t)},this._mouseMove=(0,R.default)(this._handlePosition,100),this._handleTouch=t=>{this._handlePosition(t.nativeEvent.touches[0])}}render(){const{className:t,hsv:{h:e}}=this.props;return i.createElement("div",{className:r()(z.hue,t)},i.createElement("div",{className:z.pointerContainer,ref:this._refContainer,onMouseDown:this._mouseDown,onTouchStart:this._handleTouch,onTouchMove:this._handleTouch},i.createElement("div",{className:z.pointer,style:{top:100*e+"%"}})))}}var H=o(80679);const U="#000000",j=s.t(null,{context:"Color Picker"},o(40276));class Z extends i.PureComponent{constructor(t){super(t),this._inputRef=i.createRef(),this._handleHSV=t=>{const e=function(t){const{h:e,s:o,v:i}=t;let n,r,s
;const a=Math.floor(6*e),l=6*e-a,c=i*(1-o),d=i*(1-l*o),h=i*(1-(1-l)*o);switch(a%6){case 0:n=i,r=h,s=c;break;case 1:n=d,r=i,s=c;break;case 2:n=c,r=i,s=h;break;case 3:n=c,r=d,s=i;break;case 4:n=h,r=c,s=i;break;case 5:n=i,r=c,s=d;break;default:n=0,r=0,s=0}return"#"+[255*n,255*r,255*s].map((t=>("0"+Math.round(t).toString(16)).replace(/.+?([a-f0-9]{2})$/i,"$1"))).join("")}(t)||U;this.setState({color:e,inputColor:e.replace(/^#/,""),hsv:t}),this.props.onSelect(e)},this._handleInput=t=>{const e=t.currentTarget.value;try{const t=M(e),o=`#${e}`;this.setState({color:o,inputColor:e,hsv:t}),this.props.onSelect(o)}catch(t){this.setState({inputColor:e})}},this._handleAddColor=()=>this.props.onAdd(this.state.color);const e=t.color||U;this.state={color:e,inputColor:e.replace(/^#/,""),hsv:M(e)}}componentDidMount(){var t;a.PLATFORM_ACCESSIBILITY_ENABLED&&!x.CheckMobile.any()&&(null===(t=this._inputRef.current)||void 0===t||t.focus())}render(){const{color:t,hsv:e,inputColor:o}=this.state;return i.createElement("div",{className:H.container},i.createElement("div",{className:H.form},i.createElement("div",{className:H.swatch,style:{backgroundColor:t}}),i.createElement("div",{className:H.inputWrap},i.createElement("span",{className:H.inputHash},"#"),i.createElement("input",{ref:this._inputRef,type:"text",className:H.input,value:o,onChange:this._handleInput})),i.createElement("div",{className:H.buttonWrap},i.createElement(A.Button,{size:"s",onClick:this._handleAddColor},j))),i.createElement("div",{className:H.hueSaturationWrap},i.createElement(V,{className:H.saturation,hsv:e,onChange:this._handleHSV}),i.createElement(O,{className:H.hue,hsv:e,onChange:this._handleHSV})))}}var $=o(93402);const G=s.t(null,{context:"Color Picker"},o(53585)),q=s.t(null,{context:"Color Picker"},o(81865));function Y(t){const{color:e,opacity:o,selectCustom:n,selectOpacity:s,customColors:l,onRemoveCustomColor:c,onToggleCustom:d,onOpacityChange:h,menu:p}=t,[g,m]=(0,i.useState)(!1),_="number"==typeof o?o:1,[v,f]=u();return(0,i.useLayoutEffect)((()=>{p&&p.update()}),[s,p]),g?i.createElement(Z,{color:e,onSelect:y,onAdd:function(e){m(!1),null==d||d(!1);const{onAddColor:o}=t;o&&o(e)}}):i.createElement("div",{className:$.container},i.createElement("div",{ref:v,onKeyDown:f},i.createElement(N,{colors:W.basic,color:e,onSelect:y}),i.createElement(N,{colors:W.extended,color:e,onSelect:y}),i.createElement("div",{className:$.separator}),i.createElement(E.Provider,{value:c},i.createElement(N,{colors:l,color:e,onSelect:y},n&&i.createElement(i.Fragment,null,a.PLATFORM_ACCESSIBILITY_ENABLED?(null==l?void 0:l.length)?i.createElement("button",{title:G,onClick:b,className:r()($.customButton,$.accessible,"apply-common-tooltip"),tabIndex:-1}):i.createElement("div",{"data-role":"row"},i.createElement("button",{title:G,onClick:b,className:r()($.customButton,$.accessible,"apply-common-tooltip"),tabIndex:-1})):i.createElement("div",{className:r()($.customButton,"apply-common-tooltip"),onClick:b,title:G,tabIndex:-1}))))),s&&i.createElement(i.Fragment,null,i.createElement("div",{
className:$.sectionTitle},q),i.createElement(I.Opacity,{color:e,opacity:_,onChange:function(t){h&&h(t)}})));function y(e){const{onColorChange:o}=t;o&&o(e,g)}function b(t){m(!0),null==d||d(!0)}}},54368:(t,e,o)=>{"use strict";o.d(e,{Opacity:()=>d});var i=o(50959),n=o(97754),r=o(50151),s=o(37160),a=o(68335),l=o(16838),c=o(30099);class d extends i.PureComponent{constructor(t){super(t),this._container=null,this._pointer=null,this._raf=null,this._refContainer=t=>{this._container=t},this._refPointer=t=>{this._pointer=t},this._handlePosition=t=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{const e=(0,r.ensureNotNull)(this._container),o=(0,r.ensureNotNull)(this._pointer),i=e.getBoundingClientRect(),n=o.offsetWidth,a=t.clientX-n/2-i.left,l=(0,s.clamp)(a/(i.width-n),0,1);this.setState({inputOpacity:Math.round(100*l).toString()}),this.props.onChange(l),this._raf=null})))},this._onSliderClick=t=>{this._handlePosition(t.nativeEvent),this._dragSubscribe()},this._mouseUp=t=>{this.setState({isPointerDragged:!1}),this._dragUnsubscribe(),this._handlePosition(t)},this._mouseMove=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t)},this._onTouchStart=t=>{this._handlePosition(t.nativeEvent.touches[0])},this._handleTouch=t=>{this.setState({isPointerDragged:!0}),this._handlePosition(t.nativeEvent.touches[0])},this._handleTouchEnd=()=>{this.setState({isPointerDragged:!1})},this._handleInput=t=>{const e=t.currentTarget.value,o=Number(e)/100;this.setState({inputOpacity:e}),Number.isNaN(o)||o>1||this.props.onChange(o)},this._handleKeyDown=t=>{const e=(0,a.hashFromEvent)(t);if(37!==e&&39!==e)return;t.preventDefault();const o=Number(this.state.inputOpacity);37===e&&0!==o&&this._changeOpacity(o-1),39===e&&100!==o&&this._changeOpacity(o+1)},this.state={inputOpacity:Math.round(100*t.opacity).toString(),isPointerDragged:!1}}componentWillUnmount(){null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),this._dragUnsubscribe()}render(){const{color:t,opacity:e,hideInput:o,disabled:r}=this.props,{inputOpacity:s,isPointerDragged:a}=this.state,d={color:t||void 0};return i.createElement("div",{className:c.opacity},i.createElement("div",{className:n(c.opacitySlider,l.PLATFORM_ACCESSIBILITY_ENABLED&&c.accessible),style:d,tabIndex:l.PLATFORM_ACCESSIBILITY_ENABLED&&!r?0:-1,ref:this._refContainer,onMouseDown:this._onSliderClick,onTouchStart:this._onTouchStart,onTouchMove:this._handleTouch,onTouchEnd:this._handleTouchEnd,onKeyDown:this._handleKeyDown,"aria-disabled":r},i.createElement("div",{className:c.opacitySliderGradient,style:{backgroundImage:`linear-gradient(90deg, transparent, ${t})`}}),i.createElement("div",{className:c.opacityPointerWrap},i.createElement("div",{className:n(c.pointer,a&&c.dragged),style:{left:100*e+"%"},ref:this._refPointer}))),!o&&i.createElement("div",{className:c.opacityInputWrap},i.createElement("input",{type:"text",className:c.opacityInput,value:s,onChange:this._handleInput}),i.createElement("span",{className:c.opacityInputPercent},"%")))}_dragSubscribe(){const t=(0,
r.ensureNotNull)(this._container).ownerDocument;t&&(t.addEventListener("mouseup",this._mouseUp),t.addEventListener("mousemove",this._mouseMove))}_dragUnsubscribe(){const t=(0,r.ensureNotNull)(this._container).ownerDocument;t&&(t.removeEventListener("mousemove",this._mouseMove),t.removeEventListener("mouseup",this._mouseUp))}_changeOpacity(t){this.setState({inputOpacity:t.toString()}),this.props.onChange(t/100)}}},6914:(t,e,o)=>{"use strict";o.d(e,{basic:()=>a,extended:()=>c,white:()=>n});var i=o(48891);const n=i.colorsPalette["color-white"],r=["ripe-red","tan-orange","banana-yellow","iguana-green","minty-green","sky-blue","tv-blue","deep-blue","grapes-purple","berry-pink"],s=[200,300,400,500,600,700,800,900].map((t=>`color-cold-gray-${t}`));s.unshift("color-white"),s.push("color-black"),r.forEach((t=>{s.push(`color-${t}-500`)}));const a=s.map((t=>i.colorsPalette[t])),l=[];[100,200,300,400,700,900].forEach((t=>{r.forEach((e=>{l.push(`color-${e}-${t}`)}))}));const c=l.map((t=>i.colorsPalette[t]))},56512:(t,e,o)=>{"use strict";o.d(e,{useCustomColors:()=>l});var i=o(50959),n=o(56840),r=o(76422);function s(t,e){(0,i.useEffect)((()=>(r.subscribe(t,e,null),()=>{r.unsubscribe(t,e,null)})),[t,e])}var a=o(24377);function l(){const[t,e]=(0,i.useState)((0,n.getJSON)("pickerCustomColors",[]));s("add_new_custom_color",(o=>e(c(o,t)))),s("remove_custom_color",(o=>e(d(o,t))));const o=(0,i.useCallback)((e=>{const o=e?(0,a.parseRgb)(e):null;t.some((t=>null!==t&&null!==o&&(0,a.areEqualRgb)((0,a.parseRgb)(t),o)))||(r.emit("add_new_custom_color",e),(0,n.setJSON)("pickerCustomColors",c(e,t)))}),[t]),l=(0,i.useCallback)((e=>{(e>=0||e<t.length)&&(r.emit("remove_custom_color",e),(0,n.setJSON)("pickerCustomColors",d(e,t)))}),[t]);return[t,o,l]}function c(t,e){const o=e.slice();return o.push(t),o.length>29&&o.shift(),o}function d(t,e){return e.filter(((e,o)=>t!==o))}},84877:(t,e,o)=>{"use strict";o.d(e,{MatchMediaMap:()=>s});var i=o(50959),n=o(66783),r=o.n(n);class s extends i.Component{constructor(t){super(t),this._handleMediaChange=()=>{const t=l(this.state.queries,((t,e)=>e.matches));let e=!1;for(const o in t)if(t.hasOwnProperty(o)&&this.state.matches[o]!==t[o]){e=!0;break}e&&this.setState({matches:t})};const{rules:e}=this.props;this.state=a(e)}shouldComponentUpdate(t,e){return!r()(t,this.props)||(!r()(e.rules,this.state.rules)||!r()(e.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(t,e){r()(t.rules,this.props.rules)||this._migrate(e.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(t,e){if(r()(t.rules,e.rules))return null;const{rules:o}=t;return a(o)}_migrate(t,e){null!==t&&l(t,((t,e)=>{e.removeListener(this._handleMediaChange)})),null!==e&&l(e,((t,e)=>{e.addListener(this._handleMediaChange)}))}}function a(t){const e=l(t,((t,e)=>window.matchMedia(e)));return{queries:e,matches:l(e,((t,e)=>e.matches)),rules:{...t}}}function l(t,e){const o={}
;for(const i in t)t.hasOwnProperty(i)&&(o[i]=e(i,t[i]));return o}},40173:(t,e,o)=>{"use strict";function i(t,e,o={}){return Object.assign({},t,function(t,e,o={}){const i=Object.assign({},e);for(const n of Object.keys(e)){const r=o[n]||n;r in t&&(i[n]=[t[r],e[n]].join(" "))}return i}(t,e,o))}o.d(e,{mergeThemes:()=>i})},9629:(t,e,o)=>{"use strict";o.r(e),o.d(e,{FavoriteDrawingToolbar:()=>_});var i=o(44352),n=o(3809),r=o(71810),s=(o(50151),o(32563)),a=o(70027),l=o(39347),c=o(10643),d=o(88348),h=o(54819),u=o(56840),p=o(11417),g=o(97145),m=o(92249);o(70132);class _ extends n.FloatingToolbar{constructor(t){super({allowSortable:!s.mobiletouch,dragOnlyInsideToolbar:!0,defaultPosition:t,positionSettingsKey:"chart.favoriteDrawingsPosition",positionStorageType:"device"}),this._linetoolsWidgets={},this._canBeShownValue=new g.WatchedValue(!1),this._attachHandlers(),this._loadVisibilityState(),this._hideAction=this._createHideToolbarAction()}show(){this._canBeShownValue.value()&&(this.isVisible()||this._renderAllLinetools(),super.show())}showAndSaveSettingsValue(){this._canBeShownValue.value()&&(p.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","true"),this.show())}hideAndSaveSettingsValue(){p.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible","false"),this.hide()}canBeShown(){return this._canBeShownValue.readonly()}_onFavoriteAdded(t){this.addWidget(this._createLinetoolWidget(t)),r.LinetoolsFavoritesStore.favorites().filter(v).length>0&&(this._canBeShownValue.setValue(!0),this.showAndSaveSettingsValue())}_onFavoriteRemoved(t){this.removeWidget(this._linetoolsWidgets[t]),delete this._linetoolsWidgets[t],0===r.LinetoolsFavoritesStore.favorites().filter(v).length&&(this._canBeShownValue.setValue(!1),this.hide())}_onFavoriteMoved(){this._renderAllLinetools()}_onSelectedLinetoolChanged(t){Object.keys(this._linetoolsWidgets).forEach((e=>{this._linetoolsWidgets[e].classList.toggle("i-active",t===e)}))}_createLinetoolWidget(t){const e=`<span class="tv-favorited-drawings-toolbar__widget apply-common-tooltip ${t===d.tool.value()?"i-active":""}" title="${h.lineToolsInfo[t].localizedName}" data-name="FavoriteToolbar${t}">${h.lineToolsInfo[t].icon}</span>`,o=(0,a.parseHtmlElement)(e);return o.addEventListener("click",(async e=>{e.preventDefault(),await(0,m.initLineTool)(t),d.tool.value()!==t&&d.tool.setValue(t)})),this._linetoolsWidgets[t]=o,o}_renderAllLinetools(){this._linetoolsWidgets={},this.removeWidgets(),r.LinetoolsFavoritesStore.favorites().filter((t=>h.lineToolsInfo[t]&&!0)).forEach((t=>{this.addWidget(this._createLinetoolWidget(t))}))}_attachHandlers(){r.LinetoolsFavoritesStore.favoriteAdded.subscribe(this,this._onFavoriteAdded),r.LinetoolsFavoritesStore.favoriteRemoved.subscribe(this,this._onFavoriteRemoved),r.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved),r.LinetoolsFavoritesStore.favoritesSynced.subscribe(null,(()=>{this._loadVisibilityState(),this._renderAllLinetools()})),this.onWidgetsReordered().subscribe(this,((t,e)=>{
if(r.LinetoolsFavoritesStore.favoriteMoved.unsubscribe(this,this._onFavoriteMoved),!r.LinetoolsFavoritesStore.moveFavorite(r.LinetoolsFavoritesStore.favorite(t),e))throw new Error("Something went wrong");r.LinetoolsFavoritesStore.favoriteMoved.subscribe(this,this._onFavoriteMoved)})),this.onContextMenu((t=>{t.preventDefault(),c.ContextMenuManager.showMenu([this._hideAction],t)})),d.tool.subscribe(this._onSelectedLinetoolChanged.bind(this))}_createHideToolbarAction(){return new l.Action({actionId:"Chart.FavoriteDrawingToolsToolbar.Hide",label:i.t(null,void 0,o(74813)),onExecute:()=>{this.hideAndSaveSettingsValue()}})}_loadVisibilityState(){const t=r.LinetoolsFavoritesStore.favorites().filter(v).length>0;this._canBeShownValue.setValue(t);const e=r.LinetoolsFavoritesStore.favoritesCount()>0;let o;const i=u.getValue("ChartFavoriteDrawingToolbarWidget.visible");void 0!==i?(u.remove("ChartFavoriteDrawingToolbarWidget.visible",{forceFlush:!0}),o="false"!==i,p.TVLocalStorage.setItem("ChartFavoriteDrawingToolbarWidget.visible",i)):o="false"!==p.TVLocalStorage.getItem("ChartFavoriteDrawingToolbarWidget.visible"),o&&e?this.show():this.hide()}}function v(t){return!0}},3809:(t,e,o)=>{"use strict";o.d(e,{FLOATING_TOOLBAR_REACT_WIDGETS_CLASS:()=>b,FloatingToolbar:()=>C});var i=o(59064),n=o(32563),r=o(61345),s=o(56840),a=o(57898),l=o(97145),c=o(38881);class d extends c.ChunkLoader{_startLoading(){return Promise.all([o.e(1553),o.e(2377)]).then(o.bind(o,13367)).then((t=>t.HammerJS))}}var h=o(11417),u=o(50151),p=o(56073);var g=o(49483);o(65719);class m{constructor(t){var e,o;this._helper=null,this._handleDragStart=t=>{var e;if(null!==this._helper)return;const o=this._source;o.classList.add("ui-draggable-dragging");const[i,n]=[(0,p.outerWidth)(o),(0,p.outerHeight)(o)];this._helper={startTop:parseFloat(o.style.top)||0,startLeft:parseFloat(o.style.left)||0,nextTop:null,nextLeft:null,raf:null,size:[i,n],containment:this._containment instanceof HTMLElement?[parseInt(getComputedStyle(this._containment).borderLeftWidth)+parseInt(getComputedStyle(this._containment).paddingLeft),parseInt(getComputedStyle(this._containment).borderTopWidth)+parseInt(getComputedStyle(this._containment).paddingTop),this._containment.offsetWidth-parseInt(getComputedStyle(this._containment).borderRightWidth)-parseInt(getComputedStyle(this._containment).paddingRight)-parseInt(getComputedStyle(o).marginLeft)-parseInt(getComputedStyle(o).marginRight)-i,this._containment.offsetHeight-parseInt(getComputedStyle(this._containment).borderBottomWidth)-parseInt(getComputedStyle(this._containment).paddingBottom)-parseInt(getComputedStyle(o).marginTop)-parseInt(getComputedStyle(o).marginBottom)-n]:"window"===this._containment?[window.scrollX,window.scrollY,window.scrollX+document.documentElement.offsetWidth-i,window.scrollY+document.documentElement.offsetHeight-n]:null},null===(e=this._start)||void 0===e||e.call(this)},this._handleDragMove=t=>{var e;if(null===this._helper)return
;const{current:o,initial:i}=t.detail,n=this._source,r=this._helper.nextTop,s=this._helper.nextLeft,a="y"===this._axis||!1===this._axis||0!==o.movementY;if(a){const t=this._helper.startTop;isFinite(t)&&(this._helper.nextTop=o.clientY-i.clientY+t)}const l="x"===this._axis||!1===this._axis||0!==o.movementY;if(l){const t=this._helper.startLeft;isFinite(t)&&(this._helper.nextLeft=o.clientX-i.clientX+t)}if(null!==this._helper.containment){const[t,e,o,i]=this._helper.containment;a&&this._helper.nextTop&&(this._helper.nextTop=Math.min(this._helper.nextTop,i),this._helper.nextTop=Math.max(this._helper.nextTop,e)),l&&this._helper.nextLeft&&(this._helper.nextLeft=Math.min(this._helper.nextLeft,o),this._helper.nextLeft=Math.max(this._helper.nextLeft,t))}null!==this._helper.raf||r===this._helper.nextTop&&s===this._helper.nextLeft||(this._helper.raf=requestAnimationFrame((()=>{null!==this._helper&&(null!==this._helper.nextTop&&(n.style.top=this._helper.nextTop+"px",this._helper.nextTop=null),null!==this._helper.nextLeft&&(n.style.left=this._helper.nextLeft+"px",this._helper.nextLeft=null),this._helper.raf=null)}))),null===(e=this._drag)||void 0===e||e.call(this)},this._handleDragStop=t=>{var e;if(null===this._helper)return;this._source.classList.remove("ui-draggable-dragging"),this._helper=null,null===(e=this._stop)||void 0===e||e.call(this)};const i=this._source=t.source;i.classList.add("ui-draggable");const n=this._handle=null!==(e=t.handle?i.querySelector(t.handle):null)&&void 0!==e?e:i;n.classList.add("ui-draggable-handle"),this._start=t.start,this._stop=t.stop,this._drag=t.drag,this._backend=new _({handle:n,onDragStart:this._handleDragStart,onDragMove:this._handleDragMove,onDragStop:this._handleDragStop}),this._axis=null!==(o=t.axis)&&void 0!==o&&o,this._containment=t.containment}destroy(){const t=this._source;t.classList.remove("ui-draggable"),t.classList.remove("ui-draggable-dragging");this._handle.classList.remove("ui-draggable-handle"),this._backend.destroy(),null!==this._helper&&(this._helper.raf&&cancelAnimationFrame(this._helper.raf),this._helper=null)}}class _{constructor(t){this._initial=null,this._handlePointerDown=t=>{if(null!==this._initial)return;if(!(t.target instanceof Element&&this._handle.contains(t.target)))return;if(this._initial=t,!this._dispatchEvent(this._createEvent("pointer-drag-start",t)))return void(this._initial=null);t.preventDefault();const e=this._getEventTarget();e.addEventListener("pointermove",this._handlePointerMove),e.addEventListener("pointerup",this._handlePointerUp),e.addEventListener("pointercancel",this._handlePointerUp),e.addEventListener("lostpointercapture",this._handlePointerUp),e.setPointerCapture(t.pointerId)},this._handlePointerMove=t=>{null!==this._initial&&this._initial.pointerId===t.pointerId&&(t.preventDefault(),this._dispatchEvent(this._createEvent("pointer-drag-move",t)))},this._handlePointerUp=t=>{if(null===this._initial||this._initial.pointerId!==t.pointerId)return;t.preventDefault();const e=this._getEventTarget()
;e.removeEventListener("pointermove",this._handlePointerMove),e.removeEventListener("pointerup",this._handlePointerUp),e.removeEventListener("pointercancel",this._handlePointerUp),e.removeEventListener("lostpointercapture",this._handlePointerUp),e.releasePointerCapture(this._initial.pointerId),this._dispatchEvent(this._createEvent("pointer-drag-stop",t)),this._initial=null};const e=this._handle=t.handle;this._onDragStart=t.onDragStart,this._onDragMove=t.onDragMove,this._onDragStop=t.onDragStop,e.style.touchAction="none";this._getEventTarget().addEventListener("pointerdown",this._handlePointerDown)}destroy(){this._handle.style.touchAction="";const t=this._getEventTarget();t.removeEventListener("pointerdown",this._handlePointerDown),t.removeEventListener("pointermove",this._handlePointerMove),t.removeEventListener("pointerup",this._handlePointerUp),t.removeEventListener("pointercancel",this._handlePointerUp),t.removeEventListener("lostpointercapture",this._handlePointerUp),null!==this._initial&&(t.releasePointerCapture(this._initial.pointerId),this._initial=null)}_getEventTarget(){return g.CheckMobile.iOS()||(0,g.isMac)()&&n.touch?window.document.documentElement:this._handle}_dispatchEvent(t){switch(t.type){case"pointer-drag-start":this._onDragStart(t);break;case"pointer-drag-move":this._onDragMove(t);break;case"pointer-drag-stop":this._onDragStop(t)}return!t.defaultPrevented}_createEvent(t,e){return(0,u.assert)(null!==this._initial),new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:{backend:this,initial:this._initial,current:e}})}}var v=o(70027),f=o(51768),y=o(25388);o(19619);const b="floating-toolbar-react-widgets",w=`<div class="tv-floating-toolbar i-closed i-hidden"><div class="tv-floating-toolbar__widget-wrapper"><div class="tv-floating-toolbar__drag js-drag">${y}</div><div class="tv-floating-toolbar__content js-content"></div><div class="${b}"></div></div></div>`;class C{constructor(t){this._widget=document.createElement("div"),this._isVertical=!1,this._hiddingTimeoutId=null,this._visibility=new l.WatchedValue(!1),this._windowResizeListener=this._onWindowResize.bind(this),this._reorderedDelegate=new a.Delegate,this._responsiveResizeFunction=null,this._showTimeStamp=null,this._draggable=null,this._preventClickUntilAnimation=t=>{null!==this._showTimeStamp&&performance.now()-this._showTimeStamp<this.hideDuration()&&t.stopPropagation()},C._toolbars.push(this),this._options=t,this._widget=(0,v.parseHtmlElement)(w),this._content=this._widget.getElementsByClassName("js-content").item(0),this._reactWidgetsContainer=this._widget.getElementsByClassName(b).item(0),this._setZIndex(C._startZIndex+C._toolbars.length-1),this._options.addClass&&(this._widget.className+=` ${this._options.addClass}`),this._options["data-name"]&&(this._widget.dataset.name=this._options["data-name"]),this._options.layout&&"auto"!==this._options.layout&&(this._isVertical="vertical"===this._options.layout,this._updateLayoutType(),this._updateAxisOption()),this._widget.addEventListener("click",this._preventClickUntilAnimation,!0)}destroy(){
this.hide(!0),C._toolbars.splice(C._toolbars.indexOf(this),1),this._widget.removeEventListener("click",this._preventClickUntilAnimation,!0),document.body.contains(this._widget)&&document.body.removeChild(this._widget),null!==this._draggable&&this._draggable.destroy(),this._widget.innerHTML="",this._responsiveResizeFunction=null}setResponsiveResizeFunc(t){this._responsiveResizeFunction=t}isVisible(){return this._visibility.value()}visibility(){return this._visibility.readonly()}isVertical(){return this._isVertical}show(){this.isVisible()||(document.body.contains(this._widget)||(this._init(),document.body.appendChild(this._widget)),this._setHiddingTimeout(null),window.addEventListener("resize",this._windowResizeListener),this.raise(),this._visibility.setValue(!0),this._showTimeStamp=performance.now(),this._widget.classList.contains("i-hidden")?(this._widget.classList.remove("i-hidden"),setTimeout((()=>{this.isVisible()&&this._widget.classList.remove("i-closed")}))):this._widget.classList.remove("i-closed"),this._onWindowResize())}hide(t=!1){if(!this.isVisible())return;const e=this._widget.classList.contains("i-closed");if(this._widget.classList.add("i-closed"),this._visibility.setValue(!1),t||e)this._setHiddingTimeout(null),this._widget.classList.add("i-hidden");else{const t=setTimeout((()=>{this._setHiddingTimeout(null),this._widget.classList.add("i-hidden")}),this.hideDuration());this._setHiddingTimeout(t)}window.removeEventListener("resize",this._windowResizeListener)}raise(){C._toolbars.length+C._startZIndex!==this._zIndex()&&(C._toolbars.splice(C._toolbars.indexOf(this),1),C._toolbars.push(this),C._updateAllZIndexes())}hideDuration(){return.75*r.dur}addWidget(t,e={}){const o=this.widgetsCount();if(void 0===e.index&&(e.index=o),e.index<0||e.index>o)throw new Error(`Index must be in [0, ${o}]`);const i=document.createElement("div");i.className="tv-floating-toolbar__widget js-widget",i.appendChild(t);const n=e.index===o?null:this._content.childNodes.item(e.index);this._content.insertBefore(i,n),this._onWindowResize()}getReactWidgetContainer(){return this._reactWidgetsContainer}removeWidget(t){const e=this._findWrapperForWidget(t);e&&(this._content.removeChild(e),this._onWindowResize())}widgetsCount(){return this._content.childNodes.length}showWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.remove("i-hidden")}hideWidget(t){const e=this._findWrapperForWidget(t);e&&e.classList.add("i-hidden")}removeWidgets(){for(;this._content.firstChild;)this._content.removeChild(this._content.firstChild);this._onWindowResize()}onWidgetsReordered(){return this._reorderedDelegate}onContextMenu(t){if(n.mobiletouch){(new d).load().then((e=>{const o=new e(this._widget);o.get("press").set({time:500}),o.on("press",(e=>{this._preventWidgetTouchEndEvent(),t(e.srcEvent)}))}))}else this._widget.addEventListener("contextmenu",t)}checkPosition(){const t=this._getCorrectedWidgetRect(),e={left:t.left,top:t.top};this._correctPosition(e),t.left===e.left&&t.top===e.top||(this._widget.style.left=e.left+"px",
this._widget.style.top=e.top+"px")}_determineCurrentLayoutVertical(t){const e=this._isVertical?t.height:t.width;return window.innerWidth<e&&window.innerWidth<window.innerHeight}_getWidget(){return this._widget}_findWrapperForWidget(t){const e=this._content.getElementsByClassName("js-widget");for(let o=0;o<e.length;++o){const i=e.item(o);if(i.contains(t))return i}return null}_onVerticalChanged(t,e){}_correctPosition(t){const e=this._getCorrectedWidgetRect(),o=this._getSavedPosition(),i=window.innerWidth-e.right,n=window.innerHeight-e.bottom;i<0?t.left=Math.max(0,window.innerWidth-e.width):o&&o.left>t.left&&(t.left=Math.min(t.left+i,o.left)),n<0?t.top=Math.max(0,window.innerHeight-e.height):o&&o.top>t.top&&(t.top=Math.min(t.top+n,o.top))}_getCorrectedWidgetRect(){const t=this._widget.getBoundingClientRect();if(this._widget.classList.contains("i-closed")){const e=1/.925-1,o=t.width*e,i=t.height*e;return{bottom:t.bottom+i/2,height:t.height+i,left:t.left-o/2,right:t.right+o/2,top:t.top-i/2,width:t.width+o}}return t}_getSavedPosition(){var t;let e;if("device"===this._options.positionStorageType){const t=h.TVLocalStorage.getItem(this._options.positionSettingsKey);e=null!==t?JSON.parse(t):null}else e=null!==(t=(0,s.getJSON)(this._options.positionSettingsKey))&&void 0!==t?t:null;return null!==e&&"top"in e&&"left"in e?e:null}_setHiddingTimeout(t){null!==this._hiddingTimeoutId&&clearTimeout(this._hiddingTimeoutId),this._hiddingTimeoutId=t}_preventWidgetTouchEndEvent(){const t=e=>{e.preventDefault(),this._widget.removeEventListener("touchend",t)};this._widget.addEventListener("touchend",t)}_updateLayoutType(){this._widget.classList.toggle("i-vertical",this._isVertical)}_updateAxisOption(){0}_onWindowResize(){if("auto"===(this._options.layout||"auto")){const t=this._isVertical,e=this._getCorrectedWidgetRect();this._isVertical=this._determineCurrentLayoutVertical(e),this._updateLayoutType(),t!==this._isVertical&&(this._onVerticalChanged(this._isVertical,t),this._updateAxisOption())}this.checkPosition(),this._resizeResponsive()}_resizeResponsive(){if(null===this._responsiveResizeFunction)return;let t=this._options.layout||"auto";"auto"===t&&(t=this._isVertical?"vertical":"horizontal");const e="vertical"===t?this._widget.clientHeight:this._widget.clientWidth,o=("vertical"===t?window.innerHeight:window.innerWidth)-e;this._responsiveResizeFunction(e,o,t)}_setZIndex(t){this._widget.style.zIndex=String(t)}_zIndex(){return Number(this._widget.style.zIndex)}_loadPosition(){var t;const e=null!==(t=this._getSavedPosition())&&void 0!==t?t:this._options.defaultPosition;this._widget.style.left=Math.round(e.left)+"px",this._widget.style.top=Math.round(e.top)+"px",this._onWindowResize()}_savePosition(){const t=this._widget.getBoundingClientRect();if("device"===this._options.positionStorageType)try{h.TVLocalStorage.setItem(this._options.positionSettingsKey,JSON.stringify({left:t.left,top:t.top}))}catch(t){}else(0,s.setJSON)(this._options.positionSettingsKey,{left:t.left,top:t.top})}_init(){this._loadPosition(),this._draggable=new m({
source:this._widget,containment:"window",handle:".js-drag",start:i.globalCloseMenu,stop:this._savePosition.bind(this)}),this._widget.addEventListener("pointerdown",this.raise.bind(this))}_initSortable(){let t=-1;lazyJqueryUI(this._content).sortable({start:(e,o)=>{t=o.item.index()},stop:(e,o)=>{const i=o.item.index();t!==i&&((0,f.trackEvent)("Floating Toolbar","User Sort"),this._reorderedDelegate.fire(t,i))},tolerance:"pointer",distance:5,containment:!!this._options.dragOnlyInsideToolbar&&"parent",scroll:!1,placeholder:"sortable-placeholder",forcePlaceholderSize:!0}),this._updateAxisOption()}static _updateAllZIndexes(){C._toolbars.forEach(((t,e)=>{t._setZIndex(C._startZIndex+e)}))}}C._startZIndex=20,C._toolbars=[]},89824:(t,e,o)=>{"use strict";o.d(e,{LineToolPropertiesWidgetBase:()=>wt});var i=o(50959),n=o(962),r=o(44352),s=o(36298),a=o(14483),l=o(97145),c=o(1722),d=o(88348),h=o(68806),u=o(50151),p=o(59452),g=o.n(p);class m extends(g()){constructor(t,e,o){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t,this._propertyApplier=e,this._undoText=o}destroy(){this._baseProperty.destroy(),super.destroy()}value(){return this._baseProperty.value()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(t,void 0,{applyValue:(t,e)=>this._propertyApplier.setProperty(t,e,this._undoText)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this)}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this)},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){var o;const i=(0,u.ensureDefined)(null===(o=this._listenersMappers.find((o=>o.obj===t&&o.method===e)))||void 0===o?void 0:o.callback);this._baseProperty.unsubscribe(t,i)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}var _=o(69152),v=o(97754),f=o.n(v),y=o(84877),b=o(3809),w=o(24437);const C=b.FLOATING_TOOLBAR_REACT_WIDGETS_CLASS+"__button";function T(t){const{templateButton:e,propertyButtons:o,commonButtons:n,isDrawingFinished:r,isToolbarFixed:s,buttonClassName:a,activeChartWidget:l}=t,c=l.hasModel()&&l.model().selection().dataSources();return c&&c.length?i.createElement(y.MatchMediaMap,{rules:{isSmallWidth:w.DialogBreakpoints.TabletSmall,isSmallHeight:"screen and (max-height: 430px)"}},(({isSmallWidth:t,isSmallHeight:e})=>i.createElement(i.Fragment,null,d(),r&&i.createElement(i.Fragment,null,Boolean(o.length)&&o.map(((o,n)=>i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:f()(C,a),isSmallScreen:t||e,isToolbarFixed:s}))),Boolean(n.length)&&n.map(((o,n)=>{const r=t||e;return r?o.showForSmallScreen?i.createElement(o.component,{...o.props,isSmallScreen:r,key:`${o.props.title}_${n}`,className:f()(C,a)}):null:i.createElement(o.component,{...o.props,key:`${o.props.title}_${n}`,className:f()(C,a)})})))))):d();function d(){return null===e?null:i.createElement(e.component,{...e.props,isToolbarFixed:s,isDrawingFinished:r,className:f()(C,a)})}}var x=o(51768),S=o(50813),P=o(61964);function E(t){const{title:e,activeChartWidget:o,className:n}=t
;return i.createElement(S.ToolWidgetIconButton,{className:n,icon:P,title:e,onClick:async function(){(0,x.trackEvent)("GUI","Context action on drawings","Settings");const t=o.model().selection().lineDataSources(),e=t.length;1===e?await o.showChartPropertiesForSource(t[0],void 0,{onWidget:o.onWidget()}):e>1&&await o.showChartPropertiesForSources({sources:t})},"data-name":"settings"})}var W=o(43982),L=o(66427),B=o(31188);function D(t){const{className:e,...o}=t;return i.createElement(L.ToolButton,{className:v(e,B.button),tooltipPosition:"horizontal",...o})}var k=o(65186),N=o(91244);function I(t){const{activeChartWidget:e,className:n}=t,s=e.model().selection().lineDataSources();if(0===s.length)return null;const a=s[0].properties().frozen,l=(0,W.useProperty)(a),c=l?{tooltip:r.t(null,void 0,o(15101)),icon:k}:{tooltip:r.t(null,void 0,o(42284)),icon:N};return i.createElement(D,{className:n,isActive:Boolean(l),onClick:function(){(0,x.trackEvent)("GUI","Context action on drawings","Lock"),e.toggleLockSelectedObject()},"data-name":Boolean(l)?"unlock":"lock",...c})}var A=o(35149);function M(t){const{title:e,activeChartWidget:o,className:n}=t;return i.createElement(S.ToolWidgetIconButton,{className:n,icon:A,title:e,"data-name":"remove",onClick:function(){(0,x.trackEvent)("GUI","Context action on drawings","Remove"),o.removeSelectedSources()}})}var R=o(9745),F=o(32563),V=o(8087),z=o(58451),O=o(10643),H=o(73212),U=o(68335),j=o(4741),Z=o(39347),$=o(84959);function G(t,e){const i=[(0,H.createVisualOrderAction)(t,e),(0,H.createChangeIntervalsVisibilitiesAction)(t,e)],n=function(t,e){const i=[],n=U.isMacKeyboard?" +":"",s=e.filter((t=>t.cloneable()));s.length>0&&i.push(new Z.Action({actionId:"Chart.LineTool.Clone",name:"clone",icon:o(36296),shortcutHint:U.humanReadableModifiers(j.Modifiers.Mod)+n+" Drag",label:r.t(null,void 0,o(52977)),onExecute:()=>{t.model().cloneLineTools(s,!1),(0,x.trackEvent)("GUI","Context action on drawings","Clone")}}));const a=e.filter((t=>t.copiable()));if(a.length>0){const e={actionId:"Chart.Clipboard.CopyLineTools",name:"copy",label:r.t(null,void 0,o(35216)),shortcutHint:U.humanReadableModifiers(j.Modifiers.Mod)+n+" C",onExecute:()=>{t.chartWidgetCollection().clipboard.uiRequestCopy(a)}};i.push(new Z.Action(e,"Copy"))}return function(t,e){return!!(null==t?void 0:t.isMultipleLayout().value())&&e.some((t=>t.isSynchronizable()))}(t,e)?(i.push(...(0,H.createSyncDrawingActions)(t,e)),i):i}(t,e);return n.length&&i.push(new Z.Separator,...n),i.push(new Z.Separator,function(t){return new Z.Action({actionId:"Chart.SelectedObject.Hide",label:r.t(null,void 0,o(31971)),icon:$,onExecute:()=>{t.hideSelectedObject()},name:"hide"})}(t)),i}var q=o(44996);function Y(t){const{title:e,activeChartWidget:o,isSmallScreen:n,className:r}=t,s=o.model().selection().lineDataSources(),[a,l]=(0,i.useState)([]),c=(0,i.useRef)(null),d=(0,i.useMemo)((()=>new H.ActionsProvider(o)),[o]),h=(0,i.useCallback)((()=>d.contextMenuActionsForSources(s)),[d,s]),u=(0,i.useCallback)((()=>{if(n)return;const t=G(o,s);l(K(t))}),[n,o,s]),p=(0,
i.useCallback)((t=>{n&&h().then((e=>{window.matchMedia(w.DialogBreakpoints.TabletSmall).matches?O.ContextMenuManager.showMenu(K(e),t,{mode:"drawer","data-name":"more-menu"}):l(K(e))}))}),[n,h]);return(0,i.useEffect)((()=>{var t;a.length&&(null===(t=c.current)||void 0===t||t.update())}),[a]),i.createElement(V.ToolWidgetMenu,{className:r,ref:c,arrow:!1,onOpen:u,onClick:p,title:e,content:i.createElement(R.Icon,{icon:q}),"data-name":"more",menuDataName:"more-menu",closeOnEsc:!0},i.createElement(z.ActionsTable,{parentIsOpened:!0,items:a}))}function K(t){if(F.touch&&!window.matchMedia("(pointer:fine)").matches){const e=t.filter((t=>"Copy"!==t.id));if(e.length===t.length)return e;const o=[];return e.forEach((t=>{("separator"!==t.type||o.length>0&&"separator"!==o[o.length-1].type)&&o.push(t)})),o}return t}var X=o(95586),J=o(12811),Q=o(501),tt=o(23851),et=o(57740);function ot(t){const{property:e,propertyApplier:n,title:s,undoText:a,isToolbarFixed:l,className:c}=t,d=(0,W.useProperty)(e),h=(0,i.useMemo)((()=>[new Z.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToSolid",icon:Q,label:r.t(null,void 0,o(1277)),active:X.LineStyle.Solid===d,onExecute:()=>n.setProperty(e,X.LineStyle.Solid,a)}),new Z.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDashed",icon:tt,label:r.t(null,void 0,o(59317)),active:X.LineStyle.Dashed===d,onExecute:()=>n.setProperty(e,X.LineStyle.Dashed,a)}),new Z.Action({actionId:"Chart.LineTool.Toolbar.ChangeLineStyleToDotted",icon:et,label:r.t(null,void 0,o(42973)),active:X.LineStyle.Dotted===d,onExecute:()=>n.setProperty(e,X.LineStyle.Dotted,a)})]),[n,e,d]);return i.createElement(V.ToolWidgetMenu,{className:c,arrow:!1,content:i.createElement(R.Icon,{icon:it(d)}),title:s,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`,verticalDropDirection:l?J.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:l?J.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:l?J.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:l?J.VerticalAttachEdge.Top:void 0},i.createElement(z.ActionsTable,{items:h}))}function it(t){switch(t){case X.LineStyle.Solid:return Q;case X.LineStyle.Dashed:return tt;case X.LineStyle.Dotted:return et;default:return""}}const nt=[10,11,12,14,16,20,24,28,32,40];function rt(t){const{property:e,propertyApplier:o,title:n,undoText:r,isToolbarFixed:s,className:a}=t,l=(0,W.useProperty)(e),c=nt.map((t=>new Z.Action({actionId:"Chart.LineTool.Toolbar.ChangeFontSizeProperty",label:t.toString(),onExecute:()=>o.setProperty(e,t,r),active:t===l})));return i.createElement(V.ToolWidgetMenu,{arrow:!1,content:l,className:a,title:n,verticalDropDirection:s?J.VerticalDropDirection.FromBottomToTop:void 0,horizontalDropDirection:s?J.HorizontalDropDirection.FromRightToLeft:void 0,horizontalAttachEdge:s?J.HorizontalAttachEdge.Right:void 0,verticalAttachEdge:s?J.VerticalAttachEdge.Top:void 0,"data-name":t["data-name"],menuDataName:`${t["data-name"]}-menu`},i.createElement(z.ActionsTable,{items:c}))}var st=o(57898);o(51338),o(26006);class at extends b.FloatingToolbar{
constructor(t){super(at._prepareOptions(t)),this._onWidgetStateChangedDelegate=new st.Delegate,this._statedWidgets=[],this._currentPopup=null,this._onWindowClickedListener=this._onWindowClicked.bind(this)}show(){super.show(),document.addEventListener("mousedown",this._onWindowClickedListener)}hide(t){super.hide(t),document.removeEventListener("mousedown",this._onWindowClickedListener)}destroy(){this._closePopup(),super.destroy()}addGroupedWidget(t,e={}){(0,u.assert)(t.states.length>0&&-1!==at._getStateIndexById(t,t.currentStateId),`Argument is invalid (count: ${t.states.length}, state: ${t.currentStateId})`);const o=document.createElement("div");o.className="tv-grouped-floating-toolbar__widget-wrapper apply-common-tooltip",t.widgetAddClass&&o.classList.add(t.widgetAddClass),o.setAttribute("title",t.tooltip);const i={isEnabled:!0,statedWidget:t,toolbarWidget:o},n=this._onWidgetClicked.bind(this,i);i.clickListener=n,o.addEventListener("click",n),this._updateWidgetPreview(i),this.addWidget(o,e),this._statedWidgets.push(i)}findGroupedWidget(t){const e=this._statedWidgets.length;for(let o=0;o<e;++o){if(this._statedWidgets[o].statedWidget.id===t)return o}return-1}removeGroupedWidget(t){const e=this._statedWidgets.length;for(let o=0;o<e;++o){const e=this._statedWidgets[o];if(e.statedWidget.id===t)return this._isPopupCreatedForWidget(t)&&this._closePopup(),void 0!==e.clickListener&&e.toolbarWidget.removeEventListener("click",e.clickListener),this.removeWidget(e.toolbarWidget),this._statedWidgets.splice(o,1),void this._updatePopupPosition()}(0,u.assert)(!1,`Unknown groupId(${t})`)}updateGroupedWidget(t,e){this._closePopup();const o=this._ensuredGetWidgetDataForId(t);o.statedWidget.currentStateId=e.currentStateId,o.statedWidget.states=e.states,this._updateWidgetPreview(o)}setGroupedWidgetEnabled(t,e){const o=this._ensuredGetWidgetDataForId(t);o.isEnabled=e,o.toolbarWidget.classList.toggle("i-disabled",!e)}setGroupedWidgetState(t,e){const o=this._ensuredGetWidgetDataForId(t);(0,u.assert)(-1!==at._getStateIndexById(o.statedWidget,e),`Unknown stateId (${e})`),o.statedWidget.currentStateId=e,this._updateSubWidgetsState(o.statedWidget),this._updateWidgetPreview(o)}onWidgetStateChanged(){return this._onWidgetStateChangedDelegate}_onVerticalChanged(t,e){this._updatePopupPosition()}_ensuredGetWidgetDataForId(t){for(const e of this._statedWidgets)if(e.statedWidget.id===t)return e;throw new Error(`Unknown groupId(${t})`)}_onWidgetClicked(t,e){const o=this._currentPopup&&this._isPopupCreatedForWidget(t.statedWidget.id);this._closePopup(),!o&&t.isEnabled&&this._createPopup(t)}_createPopup(t){const e={createdFor:t.toolbarWidget,element:document.createElement("div"),stateWidgetId:t.statedWidget.id,widgets:[]};e.element.className+="tv-grouped-floating-toolbar__popup js-popup";const o=t.statedWidget.states.length;t.statedWidget.states.forEach(((i,n)=>{const r=this._createSubWidget(t,i),s=this._onSubWidgetClicked.bind(this,t,i.id);r.addEventListener("click",s),e.widgets.push({clickListener:s,stateWidget:i,widget:r}),
r.classList.add(`tv-grouped-floating-toolbar__sub-widget--slide-right-${n}`),r.classList.add("tv-grouped-floating-toolbar__sub-widget--slide-left-"+(o-n+1)),i.readonly||e.element.appendChild(r)})),this._currentPopup=e,this._updateSubWidgetsState(t.statedWidget),t.toolbarWidget.classList.add("i-dropped"),this._getWidget().appendChild(this._currentPopup.element),Promise.resolve().then((()=>{this._currentPopup&&this._currentPopup.element.classList.add("i-opened")})),this._updatePopupPosition()}_closePopup(){if(this._statedWidgets.forEach((t=>{t.toolbarWidget.classList.remove("i-dropped")})),!this._currentPopup)return;const t=this._currentPopup.widgets,e=this._currentPopup.element;this._currentPopup=null,t.forEach((t=>{t.widget.removeEventListener("click",t.clickListener)})),e.classList.remove("i-opened"),e.addEventListener("transitionend",(t=>{t.target===e&&this._getWidget().removeChild(e)}))}_updateWidgetPreview(t){const e=at._getStateIndexById(t.statedWidget,t.statedWidget.currentStateId);(0,u.assert)(-1!==e,`Unknown state id: ${t.statedWidget.currentStateId}`);const o=t.statedWidget.states[e].widget.cloneNode(!0);t.toolbarWidget.firstChild?t.toolbarWidget.replaceChild(o,t.toolbarWidget.firstChild):(t.toolbarWidget.appendChild(o),t.toolbarWidget.appendChild(at._createCaret()))}_updateSubWidgetsState(t){this._currentPopup&&this._isPopupCreatedForWidget(t.id)&&this._currentPopup.widgets.forEach((e=>{e.widget.classList.toggle(at._activeSubWidgetClass,e.stateWidget.id===t.currentStateId)}))}_updatePopupPosition(){if(!this._currentPopup)return;const t=this._currentPopup.createdFor,e=this._getWidget().getBoundingClientRect(),o=this._findWrapperForWidget(t);if(!o||!this._currentPopup)throw new Error("Toolbar has no wrapper for preview's widget or there is no popup");const i=o.getBoundingClientRect(),n=this._currentPopup.element.getBoundingClientRect(),r=this._currentPopup.element;if(this.isVertical())r.classList.remove("tv-grouped-floating-toolbar__popup--at-top"),r.style.top=i.top-e.top+1+"px",r.style.left="",e.left>window.innerWidth-e.right?r.classList.add("tv-grouped-floating-toolbar__popup--at-left"):r.classList.remove("tv-grouped-floating-toolbar__popup--at-left");else{r.classList.remove("tv-grouped-floating-toolbar__popup--at-left");let t=0;i.left+n.width>window.innerWidth?e.left+n.width>window.innerWidth&&(t=e.width-n.width):t=i.left-e.left+1,r.style.left=`${t}px`,e.bottom+n.height>window.innerHeight?r.classList.add("tv-grouped-floating-toolbar__popup--at-top"):(r.classList.remove("tv-grouped-floating-toolbar__popup--at-top"),r.style.top="")}}_isPopupCreatedForWidget(t){return Boolean(this._currentPopup&&this._currentPopup.stateWidgetId===t)}_createSubWidget(t,e){const o=document.createElement("div");return o.className+="tv-grouped-floating-toolbar__sub-widget",t.statedWidget.stateWidgetAddClass&&o.classList.add(t.statedWidget.stateWidgetAddClass),o.appendChild(e.widget),o}_onSubWidgetClicked(t,e){this._closePopup(),t.statedWidget.currentStateId!==e&&(this.setGroupedWidgetState(t.statedWidget.id,e),
this._onWidgetStateChangedDelegate.fire(t.statedWidget.id,e))}_onWindowClicked(t){if(this.isVisible()&&this._currentPopup&&!at._isEventInElement(t,this._currentPopup.element)){for(let e=0;e<this._statedWidgets.length;++e)if(at._isEventInElement(t,this._statedWidgets[e].toolbarWidget))return;this._closePopup()}}static _getStateIndexById(t,e){for(let o=0;o<t.states.length;++o)if(t.states[o].id===e)return o;return-1}static _createCaret(){const t=document.createElement("div");return t.className="tv-caret tv-caret--small tv-caret--colored tv-grouped-floating-toolbar__caret",t}static _prepareOptions(t){const e=" tv-grouped-floating-toolbar";return t.addClass?t.addClass+=e:t.addClass=e,t}static _isEventInElement(t,e){return t.target===e||e.contains(t.target)}}at._activeSubWidgetClass="tv-grouped-floating-toolbar__sub-widget--current";var lt=o(69774),ct=o(92249),dt=o(15367),ht=o(3587),ut=o(87919);const pt=!1,gt=new s.TranslatedString("change line tool(s) font size",r.t(null,void 0,o(36819))),mt=new s.TranslatedString("change line tool(s) line style",r.t(null,void 0,o(54769))),_t=(new s.TranslatedString("apply drawing template",r.t(null,void 0,o(49037))),r.t(null,void 0,o(89517))),vt=r.t(null,void 0,o(34596)),ft=r.t(null,void 0,o(41610)),yt=r.t(null,void 0,o(32733)),bt=r.t(null,void 0,o(17006));class wt{constructor(t){this._isDrawingFinished=new l.WatchedValue(!0),this._currentTool=null,this._updateVisibilityTimeout=null,this._lineWidthsProperty=null,this._lineColorsProperty=null,this._currentProperties=null,this._floatingContainer=null,this._fixedContainer=null,this._fixedToolbarRendered=!1,this._floatingToolbarRendered=!1,this._toolbarVisible=!1,this._propertiesVisible=!1,this._templatesButton=null,this._propertyButtons=[],this._commonButtons=[],this._handleSourceEdit=t=>{d.isDirectionalMovementActive.value()||(t?(this._fixedContainer&&(this._fixedContainer.style.display="none"),this._floatingToolbar.hide(!0)):(this._fixedToolbarRendered&&this._fixedContainer&&(this._fixedContainer.style.display="block"),this._floatingToolbarRendered&&this._floatingToolbar.show()))},this._handleMediaChange=()=>{this._updateVisibility()},this._chartWidgetCollection=t,this._floatingToolbar=new at({defaultPosition:{top:lt.HEADER_TOOLBAR_HEIGHT_EXPANDED+15,left:window.innerWidth/2},positionSettingsKey:"properties_toolbar.position",positionStorageType:"device",layout:"horizontal","data-name":"drawing-toolbar"}),this._floatingContainer=this._floatingToolbar.getReactWidgetContainer(),this._isToolMovingNowSpawn=d.isToolMovingNow.spawn(),this._isToolEditingNowSpawn=d.isToolEditingNow.spawn(),this._toolSpawn=d.tool.spawn(),this._iconToolSpawn=d.iconTool.spawn(),this._emojiToolSpawn=d.emojiTool.spawn(),this._selectedSourcesSpawn=this._chartWidgetCollection.selectedSources.spawn(),this._isToolMovingNowSpawn.subscribe(this._handleSourceEdit),this._isToolEditingNowSpawn.subscribe(this._handleSourceEdit),this._toolSpawn.subscribe(this._onToolChanged.bind(this),{callWithLast:!0}),this._iconToolSpawn.subscribe((()=>this._onToolChanged(d.tool.value()))),
this._emojiToolSpawn.subscribe((()=>this._onToolChanged(d.tool.value()))),this._selectedSourcesSpawn.subscribe((()=>this.onSourceChanged(this.selectedSources()))),this._chartWidgetCollection.onAboutToBeDestroyed.subscribe(this,this.destroy,!0)}destroy(){this._isToolMovingNowSpawn.destroy(),this._isToolEditingNowSpawn.destroy(),this._toolSpawn.destroy(),this._iconToolSpawn.destroy(),this._emojiToolSpawn.destroy(),this._selectedSourcesSpawn.destroy()}refresh(){this.onSourceChanged(this.selectedSources())}onSourceChanged(t){if(!(null==t?void 0:t.length))return this._propertiesVisible=!1,this._toolbarVisible=!1,void this.hide();if(this._createCommonButtons(),t.every((e=>e.toolname===t[0].toolname))?this._showTemplatesOf({sources:t}):this._templatesButton&&this._clearTemplatesButton(),1===t.length){const e=t[0];e.isAvailableInFloatingWidget()&&this.activeChartWidget().model().model().dataSourceForId(e.id())?(!e.userEditEnabled()||!(0,dt.isLineDrawnWithPressedButton)(e.toolname)&&this.activeChartWidget().model().lineBeingCreated()||this._isDrawingFinished.setValue(!0),this.showPropertiesOf(e.toolname,e.properties(),!0),this._toolbarVisible=!0):this.hide()}else this._clearProperties(),this._createWidthsButton(void 0,!0),this._createColorsButton(void 0,!0),this._createBackgroundsButton(void 0,!0),this._createTextColorsButton(void 0,!0),this._propertiesVisible=!0;this._updateVisibility()}activeChartWidget(){return this._chartWidgetCollection.activeChartWidget.value()}selectedSources(){return this._chartWidgetCollection.selectedSources.value().filter(ct.isLineTool)}hide(){this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{(0,ct.unsetNewToolProperties)(),Ct()&&this._fixedContainer?this._fixedContainer.style.display="none":this._floatingToolbar.hide(!0),this._isToolbarRendered()&&this._unmountToolbar(),this._clearProperties(),this._clearCommonButtons()}),0),delete this._propertyApplier}templatesList(){return this._templatesList}_onToolChanged(t,e){this._currentTool=t;const o=this.selectedSources();this._isDrawingToolExcludingCustomUrlEventTool(t)?(this._isDrawingFinished.setValue(!1),this._updateVisibility()):o&&o.length?(o.length>1&&this._isDrawingFinished.setValue(!0),this.onSourceChanged(this.selectedSources())):this.hide()}_propertyApplierImpl(){return this._propertyApplier||(this._propertyApplier=new ut.PropertyApplierWithoutSavingChart((()=>this.activeChartWidget().model()),new l.WatchedValue(false))),this._propertyApplier}_clearProperties(){this._clearPropertyButtons(),this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._currentProperties&&(this._currentProperties=null)}_show(){this._updateVisibilityTimeout&&clearTimeout(this._updateVisibilityTimeout),this._updateVisibilityTimeout=setTimeout((()=>{this._renderToolbar(),
Ct()&&this._fixedContainer?this._fixedContainer.style.display="block":(this._floatingToolbar.show(),this._floatingToolbar.checkPosition())}),0)}_addPropertyButton(t){this._propertyButtons.push(t),this._renderToolbar()}_addCommonButton(t){this._commonButtons.push(t),this._renderToolbar()}_addTemplatesButton(t){this._templatesButton=t}_renderFloatingToolbar(){null!==this._floatingContainer&&this.activeChartWidget()&&this.activeChartWidget().hasModel()&&(n.render(i.createElement(T,{templateButton:this._templatesButton,propertyButtons:this._propertyButtons,commonButtons:this._commonButtons,isDrawingFinished:this._isDrawingFinished.value(),activeChartWidget:this.activeChartWidget()}),this._floatingContainer),this._floatingToolbarRendered=!0)}_unmountFloatingToolbar(){null!==this._floatingContainer&&(n.unmountComponentAtNode(this._floatingContainer),this._floatingToolbarRendered=!1)}_clearTemplatesButton(){this._templatesButton=null}_clearPropertyButtons(){this._propertyButtons=[]}_clearCommonButtons(){this._commonButtons=[]}_isToolbarRendered(){return this._floatingToolbarRendered||this._fixedToolbarRendered}_createSettingsButton(){const t={component:E,props:{title:_t,activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createLockButton(){const t={component:I,props:{title:"Lock",activeChartWidget:this.activeChartWidget()}};this._addCommonButton(t)}_createRemoveButton(){const t={component:M,props:{title:vt,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0};this._addCommonButton(t)}_createDotsButton(){this._addCommonButton({component:Y,props:{title:ft,activeChartWidget:this.activeChartWidget()},showForSmallScreen:!0})}_createAlertButton(){}_createSourceActions(){this._createLockButton()}_createLineStyleButton(t){const e=this.selectedSources();if(0===e.length)return!1;const o=e[0];if(!(0,ht.isDataSource)(o))return!1;const i={component:ot,props:{property:o.properties().linestyle||t,title:yt,propertyApplier:this._propertyApplierImpl(),"data-name":"style",undoText:mt}};return this._addPropertyButton(i),!0}_createFontSizeButton(t){const e=this.selectedSources();if(0===e.length)return!1;const o=e[0];if(!(0,ht.isDataSource)(o))return!1;const i={component:rt,props:{property:o.properties().fontsize||t,title:bt,propertyApplier:this._propertyApplierImpl(),"data-name":"font-size",undoText:gt}};return this._addPropertyButton(i),!0}_createCommonButtons(){this._commonButtons.length&&this._clearCommonButtons(),a.enabled("property_pages")&&this._createSettingsButton(),this._createSourceActions(),this._createRemoveButton(),this._createDotsButton()}_prepareProperties(t){const e=this.selectedSources().filter((e=>e.properties()[t]));if(!(e.filter((e=>e.properties()[t].visible())).length<1))return e.map((e=>e.properties()[t])).filter(c.notNull)}_createProperty(t,e,o,i){if(e){const t=this._prepareProperties(o);if(!t)return
;return this._isWidthProperty(t[0])?new m(new h.MultipleLineWidthsProperty(t),this._propertyApplierImpl(),i):new _.CollectibleColorPropertyUndoWrapper(new h.MultipleLineColorsProperty(t),this._propertyApplierImpl(),i)}if(t&&t.visible())return this._isWidthProperty(t)?new h.MultipleLineWidthsProperty([t]):new _.CollectibleColorPropertyDirectWrapper(new h.MultipleLineColorsProperty([t]))}_shouldShowBackgroundProperty(t,e){return!e||!e.fillBackground||!!e.fillBackground.value()}_isDrawingToolExcludingCustomUrlEventTool(t){return Boolean(null==t?void 0:t.toLowerCase().includes("linetool"))&&"LineToolTweet"!==t&&"LineToolIdea"!==t&&"LineToolImage"!==t}_updateVisibility(){this._isDrawingFinished.value()&&(this._toolbarVisible||this._propertiesVisible)?this._show():this.hide()}_showTemplatesOf(t){}_isWidthProperty(t){return t instanceof h.LineToolWidthsProperty}_renderFixedToolbar(){null!==this._fixedContainer&&this.activeChartWidget()&&this.activeChartWidget().hasModel()&&(n.render(i.createElement(FixedDrawingToolbar,{templateButton:this._templatesButton,propertyButtons:this._propertyButtons,commonButtons:this._commonButtons,isDrawingFinished:this._isDrawingFinished.value(),activeChartWidget:this.activeChartWidget()}),this._fixedContainer),this._fixedToolbarRendered=!0)}_renderToolbar(){this._renderFloatingToolbar()}_unmountFixedToolbar(){null!==this._fixedContainer&&(n.unmountComponentAtNode(this._fixedContainer),this._fixedToolbarRendered=!1)}_unmountToolbar(){this._floatingToolbarRendered&&this._unmountFloatingToolbar(),this._fixedToolbarRendered&&this._unmountFixedToolbar()}}function Ct(){return pt}},26916:(t,e,o)=>{"use strict";var i=o(36298).TranslatedString,n=o(89824).LineToolPropertiesWidgetBase;const r=o(70114).ColorPickerButton,s=o(61259).LineWidthButton;var a=o(94071),l=o(21065),c=o(48984),d=new i("change line tool(s) color",o(44352).t(null,void 0,o(68519))),h=new i("change line tool(s) background color",o(44352).t(null,void 0,o(74350))),u=new i("change line tool(s) text color",o(44352).t(null,void 0,o(16631))),p=new i("change line tool(s) line width",o(44352).t(null,void 0,o(41648))),g=o(44352).t(null,void 0,o(40054)),m=o(44352).t(null,void 0,o(12928)),_=o(44352).t(null,void 0,o(19221)),v=o(44352).t(null,void 0,o(21327)),f=o(44352).t(null,void 0,o(38455)),y=o(44352).t(null,void 0,o(71845)),b=o(44352).t(null,void 0,o(32733)),w=o(44352).t(null,void 0,o(23886)),C=o(44352).t(null,void 0,o(86327)),T=o(44352).t(null,void 0,o(47059)),x=o(44352).t(null,void 0,o(36785)),S=o(44352).t(null,void 0,o(49593)),P=o(44352).t(null,void 0,o(67455)),E=o(44352).t(null,void 0,o(79964)),W=o(44352).t(null,void 0,o(45320));class L extends n{constructor(t){super(t),this._templatesButton=null}_createWidthsButton(t,e){if(this._lineWidthsProperty&&(this._lineWidthsProperty.destroy(),this._lineWidthsProperty=null),this._lineWidthsProperty=this._createProperty(t,e,"linesWidths",p),!this._lineWidthsProperty)return!0;var o=C;e&&(1!==this.selectedSources().filter((t=>t.properties().linesWidths)).length&&(o=T));return this._addPropertyButton({
component:s,props:{title:o,multipleProperty:this._lineWidthsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-width",undoText:p}}),!0}_createColorsButton(t,e){return this._lineColorsProperty&&(this._lineColorsProperty.destroy(),this._lineColorsProperty=null),this._lineColorsProperty=this._createProperty(t,e,"linesColors",d),!this._lineColorsProperty||(this._addPropertyButton({component:r,props:{icon:a,title:m,property:this._lineColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"line-tool-color",undoText:d}}),!0)}_createBackgroundsButton(t,e){return this._backgroundsProperty&&(this._backgroundsProperty.destroy(),this._backgroundsProperty=null),this._backgroundsProperty=this._createProperty(t,e,"backgroundsColors",h),!this._backgroundsProperty||(this._addPropertyButton({component:r,props:{icon:l,title:y,property:this._backgroundsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"background-color",undoText:h}}),!0)}_createTextColorsButton(t,e){return this._textColorsProperty&&(this._textColorsProperty.destroy(),this._textColorsProperty=null),this._textColorsProperty=this._createProperty(t,e,"textsColors",u),!this._textColorsProperty||(this._addPropertyButton({component:r,props:{icon:c,title:v,property:this._textColorsProperty,propertyApplier:this._propertyApplierImpl(),"data-name":"text-color",undoText:u}}),!0)}_getPossibleProperty(t){for(var e=[],o=this._defaultToolProperties(),i=0;i<o.length;i++){var n=o[i];n.name in t&&e.push(n)}return e}showPropertiesOf(t,e,o){this._toolExceptionCases||(this._toolExceptionCases=this._createToolExceptionCases());var i=this._toolExceptionCases[t]||this._getPossibleProperty(e);if(this._clearProperties(),this._propertiesVisible=!1,i.length){for(var n={},s=0;s<i.length;s++){for(var a=i[s],l=e,c=a.name.split("."),d=0;d<c.length;++d)l=l&&l[c[d]];var h=a.showIf;if("function"!=typeof h||h(l,e)){var u=a.factory;if(u&&u.call(this,l,o))continue;if(!l)continue;if(this._propertiesVisible=!0,"combobox"!==a.inputType){const t={component:r,props:{icon:a.iconSvgCode,title:a.title,"data-name":a.dataName,property:l,propertyApplier:this._propertyApplierImpl(),undoText:a.undoText}};this._addPropertyButton(t);continue}n[a.name]=l}}this._currentProperties=n}}_defaultToolProperties(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:g,factory:L.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:f,factory:L.prototype._createBackgroundsButton,dataName:"background-color",showIf:this._shouldShowBackgroundProperty},{name:"textsColors",title:_,inputType:"colorPicker",iconSvgCode:c,factory:L.prototype._createTextColorsButton,dataName:"text-color"},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton},{name:"linestyle",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_regressionToolExceptionCases(){return[{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}
_pathExceptionCases(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:g,factory:L.prototype._createColorsButton,dataName:"line-tool-color"},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton},{name:"lineStyle",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_riskPropertiesExceptionCases(){return[{name:"textcolor",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:u},{name:"profitBackground",title:x,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h},{name:"stopBackground",title:S,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h}]}_rangeExceptionCases(){return[{name:"linecolor",inputType:"colorPicker",iconSvgCode:a,title:g,dataName:"line-tool-color",undoText:d},{name:"backgroundColor",inputType:"colorPicker",iconSvgCode:l,title:f,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:h},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}_brushPropertiesExceptionCase(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:g,factory:L.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,title:f,dataName:"background-color",factory:L.prototype._createBackgroundsButton},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}]}_bezierPropertiesExceptionCases(){return[{name:"linesColors",inputType:"colorPicker",iconSvgCode:a,title:g,factory:L.prototype._createColorsButton,dataName:"line-tool-color"},{name:"backgroundsColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:f,factory:L.prototype._createBackgroundsButton,showIf:this._shouldShowBackgroundProperty},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton},{name:"linestyle",title:b,inputType:"combobox",factory:L.prototype._createLineStyleButton}]}_textPropertiesExceptionCases(){return[{name:"color",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:u},{name:"backgroundColor",title:f,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",showIf:this._shouldShowBackgroundProperty,undoText:h},{name:"fontsize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}]}_notePropertiesExceptionCases(){return[{name:"markerColor",title:P,inputType:"colorPicker",iconSvgCode:a,dataName:"line-tool-color",undoText:d},{name:"textColor",title:_,inputType:"colorPicker",iconSvgCode:c,dataName:"text-color",undoText:u},{name:"fontSize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}]}_createToolExceptionCases(){return{LineToolBrush:L.prototype._brushPropertiesExceptionCase(),LineToolBezierQuadro:L.prototype._bezierPropertiesExceptionCases(),LineToolBezierCubic:L.prototype._bezierPropertiesExceptionCases(),LineToolText:L.prototype._textPropertiesExceptionCases(),LineToolTextAbsolute:L.prototype._textPropertiesExceptionCases(),
LineToolBalloon:L.prototype._textPropertiesExceptionCases(),LineToolComment:L.prototype._textPropertiesExceptionCases(),LineToolCallout:L.prototype._textPropertiesExceptionCases(),LineToolPriceLabel:L.prototype._textPropertiesExceptionCases(),LineToolDateRange:L.prototype._rangeExceptionCases(),LineToolPriceRange:L.prototype._rangeExceptionCases(),LineToolDateAndPriceRange:L.prototype._rangeExceptionCases(),LineToolNote:L.prototype._notePropertiesExceptionCases(),LineToolNoteAbsolute:L.prototype._notePropertiesExceptionCases(),LineToolRiskRewardLong:L.prototype._riskPropertiesExceptionCases(),LineToolRiskRewardShort:L.prototype._riskPropertiesExceptionCases(),LineToolPath:L.prototype._pathExceptionCases(),LineToolRegressionTrend:L.prototype._regressionToolExceptionCases(),LineToolBarsPattern:[{name:"color",title:g,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:d}],LineToolProjection:[{name:"color1",title:E,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h},{name:"color2",title:W,inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",undoText:h},{name:"linesWidths",inputType:"combobox",factory:L.prototype._createWidthsButton}],LineToolSignpost:[{name:"linesColors",inputType:"colorPicker",iconSvgCode:l,dataName:"background-color",title:g,factory:L.prototype._createBackgroundsButton,showIf:function(t,e){return e&&e.showImage.value()}},{name:"fontSize",title:w,inputType:"combobox",factory:L.prototype._createFontSizeButton}]}}}t.exports=L},69152:(t,e,o)=>{"use strict";o.d(e,{CollectibleColorPropertyDirectWrapper:()=>l,CollectibleColorPropertyUndoWrapper:()=>a});var i=o(50151),n=o(59452),r=o.n(n);class s extends(r()){constructor(t){super(),this._listenersMappers=[],this._isProcess=!1,this._baseProperty=t}destroy(){this._baseProperty.destroy(),super.destroy()}value(){const t=this._baseProperty.value();return"mixed"===t?"":t}visible(){return this._baseProperty.visible()}setValue(t){this._isProcess=!0,this._baseProperty.setValue(""===t?"mixed":t,void 0,{applyValue:this._applyValue.bind(this)}),this._isProcess=!1,this._listenersMappers.forEach((t=>{t.method.call(t.obj,this)}))}subscribe(t,e){const o=o=>{this._isProcess||e.call(t,this)},i={obj:t,method:e,callback:o};this._listenersMappers.push(i),this._baseProperty.subscribe(t,o)}unsubscribe(t,e){var o;const n=(0,i.ensureDefined)(null===(o=this._listenersMappers.find((o=>o.obj===t&&o.method===e)))||void 0===o?void 0:o.callback);this._baseProperty.unsubscribe(t,n)}unsubscribeAll(t){this._baseProperty.unsubscribeAll(t)}}class a extends s{constructor(t,e,o){super(t),this._propertyApplier=e,this._undoText=o}_applyValue(t,e){this._propertyApplier.setProperty(t,e,this._undoText)}}class l extends s{_applyValue(t,e){t.setValue(e)}}},87919:(t,e,o)=>{"use strict";o.d(e,{PropertyApplierWithoutSavingChart:()=>i});class i{constructor(t,e){this._undoModelSupplier=t,this._featureToggle=e}setProperty(t,e,o){this._undoModelSupplier().setProperty(t,e,o,this._featureToggle.value())}beginUndoMacro(t){
return this._undoModelSupplier().beginUndoMacro(t,this._shouldWeKeepChartValidated())}endUndoMacro(){this._undoModelSupplier().endUndoMacro()}setWatchedValue(t,e,o){this._undoModelSupplier().undoHistory().setWatchedValue(t,e,o,!0)}_shouldWeKeepChartValidated(){const t=this._undoModelSupplier().model().isAutoSaveEnabled().value();return this._featureToggle.value()&&t}}},44996:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},23851:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},57740:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},501:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},36296:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},21065:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" fill="none"><path stroke="currentColor" d="M13.5 6.5l-3-3-7 7 7.59 7.59a2 2 0 0 0 2.82 0l4.18-4.18a2 2 0 0 0 0-2.82L13.5 6.5zm0 0v-4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v6"/><path fill="currentColor" d="M0 16.5C0 15 2.5 12 2.5 12S5 15 5 16.5 4 19 2.5 19 0 18 0 16.5z"/><circle fill="currentColor" cx="9.5" cy="9.5" r="1.5"/></svg>'},25388:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 8 12" width="8" height="12" fill="currentColor"><rect width="2" height="2" rx="1"/><rect width="2" height="2" rx="1" y="5"/><rect width="2" height="2" rx="1" y="10"/><rect width="2" height="2" rx="1" x="6"/><rect width="2" height="2" rx="1" x="6" y="5"/><rect width="2" height="2" rx="1" x="6" y="10"/></svg>'},22978:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 1" width="18" height="1"><rect width="18" height="1" fill="currentColor" rx=".5"/></svg>'},14631:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 2" width="18" height="2"><rect width="18" height="2" fill="currentColor" rx="1"/></svg>'},6096:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 3" width="18" height="3"><rect width="18" height="3" fill="currentColor" rx="1.5"/></svg>'},6483:t=>{
t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 4" width="18" height="4"><rect width="18" height="4" fill="currentColor" rx="2"/></svg>'},66611:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><rect width="18" height="2" rx="1" x="5" y="14"/><rect width="18" height="1" rx=".5" x="5" y="20"/><rect width="18" height="3" rx="1.5" x="5" y="7"/></svg>'},94071:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 17 17" width="17" height="17" fill="none"><path stroke="currentColor" d="M1.5 11.5l-.7.7a1 1 0 0 0-.3.71v3.59h3.59a1 1 0 0 0 .7-.3l.71-.7m-4-4l9-9m-9 9l2 2m2 2l9-9m-9 9l-2-2m11-7l1.3-1.3a1 1 0 0 0 0-1.4l-2.6-2.6a1 1 0 0 0-1.4 0l-1.3 1.3m4 4l-4-4m-7 11l9-9"/></svg>'},48984:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 15" width="13" height="15" fill="none"><path stroke="currentColor" d="M4 14.5h2.5m2.5 0H6.5m0 0V.5m0 0h-5a1 1 0 0 0-1 1V4m6-3.5h5a1 1 0 0 1 1 1V4"/></svg>'},61964:t=>{t.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentcolor" fill-rule="evenodd" clip-rule="evenodd" d="M13 5.5c0-.28.22-.5.5-.5h1c.28 0 .5.22.5.5V7.05l.4.09c.9.18 1.73.53 2.46 1.02l.34.23.29-.3.81-.8c.2-.2.52-.2.71 0l.7.7.36-.35-.35.35c.******** 0 .7l-.82.82-.29.29.23.34c.49.73.84 1.57 1.02 2.46l.08.4H22.5c.28 0 .5.22.5.5v1a.5.5 0 0 1-.5.5H20.95l-.09.4c-.18.9-.53 1.73-1.02 2.46l-.**********.8.81c.******** 0 .71l-.7.7a.5.5 0 0 1-.7 0l-.82-.8-.29-.3-.34.23c-.73.49-1.57.84-2.46 1.02l-.4.08V22.5a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5V20.95l-.4-.09a6.96 6.96 0 0 1-2.46-1.02l-.34-.23-.29.3-.**********-.35-.35a.5.5 0 0 1-.71 0l-.7-.71a.5.5 0 0 1 0-.7l-.36-.***********-.81.29-.29-.23-.34a6.96 6.96 0 0 1-1.02-2.46l-.08-.4H5.5a.5.5 0 0 1-.5-.5v-1c0-.28.22-.5.5-.5H7.05l.09-.4c.18-.9.53-1.73 1.02-2.46l.23-.34-.3-.29-.8-.81a.5.5 0 0 1 0-.71l.7-.7c.2-.2.51-.2.7 0l.*********.34-.23a6.96 6.96 0 0 1 2.46-1.02l.4-.08V5.5zm.5-1.5c-.83 0-1.5.67-1.5 1.5v.75c-.73.2-1.43.48-2.06.86l-.54-.53a1.5 1.5 0 0 0-2.12 0l-.7.7a1.5 1.5 0 0 0 0 2.12l.53.54A7.95 7.95 0 0 0 6.25 12H5.5c-.83 0-1.5.67-1.5 1.5v1c0 .83.67 1.5 1.5 1.5h.75c.2.73.48 1.43.86 2.06l-.53.54a1.5 1.5 0 0 0 0 2.12l.7.7a1.5 1.5 0 0 0 2.12 0l.54-.53c.63.38 1.33.67 2.06.86v.75c0 .83.67 1.5 1.5 1.5h1c.83 0 1.5-.67 1.5-1.5v-.75a7.95 7.95 0 0 0 2.06-.86l.54.53a1.5 1.5 0 0 0 2.12 0l.7-.7a1.5 1.5 0 0 0 0-2.12l-.53-.54c.38-.63.67-1.33.86-2.06h.75c.83 0 1.5-.67 1.5-1.5v-1c0-.83-.67-1.5-1.5-1.5h-.75a7.95 7.95 0 0 0-.86-2.06l.53-.54a1.5 1.5 0 0 0 0-2.12l-.7-.7a1.5 1.5 0 0 0-2.12 0l-.54.53A7.95 7.95 0 0 0 16 6.25V5.5c0-.83-.67-1.5-1.5-1.5h-1zM12 14a2 2 0 1 1 4 0 2 2 0 0 1-4 0zm2-3a3 3 0 1 0 0 6 3 3 0 0 0 0-6z"/></svg>'}}]);