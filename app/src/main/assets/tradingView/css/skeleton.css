:root {
    --background-skeleton : rgb(134,134,134,0.2);
}
.theme-dark {
    --background-skeleton : rgba(255, 255, 255, 0.1);
}
.skeleton-box {
    display: block;
    height: 1.3rem;
    position: relative;
    overflow: hidden;
    background-color: #dddbdd !important;
    border-color: #dddbdd !important;
    animation-name: skeletonAnimation;
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    border-radius: 0.3rem;
}

.skeleton-box-2 {
    display: block;
    height: 10%;
    width: 60%;
    position: relative;
    overflow: hidden;
    background-color: #dddbdd !important;
    border-color: #dddbdd !important;
    animation-name: skeletonAnimation;
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}
.skeleton{
    position: relative;
    height: 50%;
}
.wick-top {
    position: absolute;
    width: 0.1rem;
    height: 0.5rem;
    background-color: #dddbdd;
    left: 28%;
    top: 1.5rem;
    z-index: 1;
    animation-name: skeletonAnimation;
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.wick-bottom {
    position: absolute;
    width: 0.1rem;
    height: 0.5rem;
    background-color: #dddbdd;
    left: 28%;
    z-index: 1;
    animation-name: skeletonAnimation;
    animation-duration: 1.5s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

.skeleton-box.bar{
    border-radius: 0.3rem 0.3rem 0 0;
}

.skeleton-box-2.bar{
    border-radius: 0.2rem;
}


#bars{
    display: grid;
    align-items: center;
    grid-template-columns: repeat(auto-fit, minmax(0.1rem, 1fr));
    height: 100%;
    width: 90%;
    background-image: repeating-linear-gradient(var(--background-skeleton) 0 1px, transparent 1px 100%), repeating-linear-gradient(90deg, var(--background-skeleton) 0 1px, transparent 1px 100%);
    background-size: 5rem 5rem;
}

#loading-animation{
    height: 100%;
    display: none;
    flex-direction: row;
    background-color: var(--background);
}

.right-axis{
    width: 10%;
    display: grid;
    gap: 1px;
    grid-template-rows: repeat(auto-fit, minmax(0.1rem, 1fr));
    align-items: center;
}

.right-axis .skeleton-box{
    border-radius: 0.2rem;
    width: 80%;
    height: 0.9rem;
}

@keyframes skeletonAnimation {
    0% {
      opacity: 0.8;
    }
    50% {
      opacity: 0.4;
    }
    100% {
      opacity: 0.8;
    }
}

.pt-2 {
    padding-top: 2rem;
}

.pt-3 {
    padding-top: 3rem;
}

.pt-4 {
    padding-top: 4rem;
}

.pt-5 {
    padding-top: 5rem;
}

.top-0-5{
    top: -0.5rem;
}

.top-2-5{
    top: 2.5rem;
}

.top-3-5{
    top: 3.5rem;
}

.top-4-5{
    top: 4.5rem;
}

.height70-per{
    height: 70%;
}

.height65-per{
    height: 65%;
}

.height60-per{
    height: 60%;
}

.height55-per{
    height: 55%;
}