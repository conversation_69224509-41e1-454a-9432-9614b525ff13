.theme-dark,
:root {
  --header--gray: rgb(134, 134, 134);
  --dropdown-color: rgb(198, 198, 198);
  --box-shadow: 4px 8px 24px -6px rgba(0, 0, 0, 0.2);
}
:root {
  --background--light: rgb(243, 245, 247);
  --background: var(--background--light);
  --background-light: var(--background--light);
  --header--active: rgba(26, 29, 32, 1);
  --indicator-color: rgba(26, 29, 32, 1);
  --indicator-background: rgb(224, 224, 224);
  --dropdown-background: white;
  --dropdown-active: rgb(224, 224, 224);
  --dropdown-active-color: rgba(26, 29, 32, 1);
  --drawing-close-background: rgb(255, 255, 255);
  --drawing-close-color: rgb(61, 61, 61);
}
.theme-dark {
  --background--dark: rgba(26, 29, 32, 1);
  --background: var(--background--dark);
  --background-light: rgb(243, 245, 247);
  --header--active: white;
  --indicator-color: rgb(0, 221, 206);
  --indicator-background: rgba(26, 29, 32, 1);
  --dropdown-background: rgba(26, 29, 32, 1);
  --dropdown-active: rgb(5, 55, 64);
  --dropdown-active-color: white;
  --drawing-close-background: rgba(26, 29, 32, 1);
  --drawing-close-color: rgba(255, 255, 255, 0.87);
}
@font-face {
  font-family: Gilroy-SemiBold;
  src: url("./font/Gilroy-SemiBold.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
}
@font-face {
  font-family: Gilroy-Regular;
  src: url("./font/Gilroy-Regular.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
}
@font-face {
  font-family: Gilroy-Medium;
  src: url("./font/Gilroy-Medium.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
}
@font-face {
  font-family: Gilroy-Light;
  src: url("./font/Gilroy-Light.ttf") format("truetype");
  font-style: normal;
  font-display: auto;
}
body {
  margin: 0;
  font-family: Gilroy-Medium;
}
html,
html body {
  background-color: var(--background);
  height: 100%;
}
.topAppendDrawing {
  position: absolute;
  top: 40px;
  right: 75px;
  width: 5.5rem;
  height: 2.5rem;
  background: var(--drawing-close-background);
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: var(--box-shadow);
  -webkit-box-shadow: var(--box-shadow);
  -moz-box-shadow: var(--box-shadow);
}
.topAppendDrawing > .closeImg {
  width: 15px;
  border-radius: 3px;
  height: 18px;
  margin-right: 0.5rem;
}
.exit {
  font-family: Gilroy-Regular;
  font-size: 0.9rem;
  color: var(--drawing-close-color);
}
.topAppendDrawing > .closeImg > img {
  width: 100%;
}
.theme-dark .topAppendDrawing > .closeImg > img {
  filter: brightness(0) invert(1);
}
:root:not(.theme-dark) {
  --tv-color-platform-background: var(--background);
  --tv-color-pane-background: var(--background);
}
:root.theme-dark {
  --tv-color-platform-background: var(--background);
  --tv-color-pane-background: var(--background);
}
.floating-toolbar-react-widgets__button:last-child {
  display: none;
}
#tv_chart_container.loading-indicator {
  display: none !important;
}

#tv_chart_container {
  display: none;
  position: relative;
  z-index: 9999;
}
#tv_chart_container {
  animation: fadeIn 1s;
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
