import { deductTimestampUTC, getNextDailyBarTime } from './helpers.js';

const channelToSubscription = new Map();


export function reconnect () {
	if(channelToSubscription.size > 0){
		redirect('223');
		const subscriptionItem = Array.from(channelToSubscription)[0][1];
		const lastDailyBar = subscriptionItem.lastDailyBar;
		subscriptionItem.handlers.forEach(handler => {
			handler.onResetCacheNeededCallback(lastDailyBar);
			window.tvWidget.chart().resetData();
		});
		redirect('224');
	}
}


export async function getFromWebView(timestamp, ask, bid, tickRate) {
	if(timestamp){
		const subscriptionItem = Array.from(channelToSubscription)[0][1];
		const lastDailyBar = subscriptionItem.lastDailyBar;
	
		let timeframe = (Array.from(channelToSubscription)[0][0]).split('~')[3];

		let nextDailyBarTime = getNextDailyBarTime(lastDailyBar.time, timeframe);
		
		let bar1 = {};

		let time = deductTimestampUTC(timestamp * 1000, utc);
		if (time >= nextDailyBarTime) {
			bar1 = {
				time: nextDailyBarTime,
				low: parseFloat(bid),
				high: parseFloat(bid),
				open: parseFloat(bid),
				close: parseFloat(bid),
			};

		} else {
			if(timeframe === '1D' || timeframe === '1W' || timeframe === '1M'){
				bar1 = {
					...lastDailyBar,
					high: Math.max(lastDailyBar.high, bid),
					low: Math.min(lastDailyBar.low, bid),
					close: parseFloat(bid),
					time: time
				};
			}else{
				bar1 = {
					...lastDailyBar,
					high: Math.max(lastDailyBar.high, bid),
					low: Math.min(lastDailyBar.low, bid),
					close: parseFloat(bid),
				};
			}
		}

		askPrice = parseFloat(ask);
		bidPrice = parseFloat(bid);

		rate = tickRate ? parseFloat(tickRate) : 1;
		updateTickChartRate(rate);

		if (Object.keys(bar1).length > 0) {
			subscriptionItem.lastDailyBar = bar1;
			// send data to every subscriber of that symbol
			subscriptionItem.handlers.forEach(handler => {
				handler.callback(bar1);

			});
		}
		return bar1;
	}
}


export function subscribeOnStream(
	symbolInfo,
	resolution,
	onRealtimeCallback,
	subscribeUID,
	onResetCacheNeededCallback,
	lastDailyBar,
) {

	const channelString = `0~${symbolInfo.name}~${symbolInfo.name}~${resolution}`;

	const handler = {
		id: subscribeUID,
		callback: onRealtimeCallback,
		onResetCacheNeededCallback: onResetCacheNeededCallback
	};
	let subscriptionItem = channelToSubscription.get(channelString);

	if (subscriptionItem) {
		subscriptionItem.handlers.push(handler);
		return;
	}

	subscriptionItem = {
		subscribeUID,
		resolution,
		lastDailyBar,
		handlers: [handler],
	};

	channelToSubscription.clear();
	channelToSubscription.set(channelString, subscriptionItem);
	// let t = a();

}

export function unsubscribeFromStream(subscriberUID) {
	// find a subscription with id === subscriberUID
	for (const channelString of channelToSubscription.keys()) {
		const subscriptionItem = channelToSubscription.get(channelString);
		const handlerIndex = subscriptionItem.handlers
			.findIndex(handler => handler.id === subscriberUID);
		if (handlerIndex !== -1) {
			// remove from handlers
			subscriptionItem.handlers.splice(handlerIndex, 1);
			if (subscriptionItem.handlers.length === 0) {
				channelToSubscription.delete(channelString);
				break;
			}
		}
	}
}
// addEventListener("visibilitychange", (event) => {
// 	redirect('223');
// 	reconnect();
// 	redirect('224');
// });
