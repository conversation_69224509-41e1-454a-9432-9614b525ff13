// Datafeed implementation, will be added later
import Datafeed from "./datafeeds.js";
async function initReady() {
  const shareOverrides = {
    "paneProperties.legendProperties.showStudyArguments": false,
    "paneProperties.legendProperties.showStudyTitles": false,
    "paneProperties.legendProperties.showStudyValues": false,
    "paneProperties.legendProperties.showSeriesTitle": false,
    "paneProperties.legendProperties.showSeriesOHLC": false,
    "paneProperties.legendProperties.showLegend": false,
    "paneProperties.legendProperties.showBarChange": false,
    "paneProperties.crossHairProperties.style": 0,
    "scalesProperties.crosshairLabelBgColorLight": "#818181",
    "scalesProperties.crosshairLabelBgColorDark": "#262930",
    "scalesProperties.showStudyPlotLabels": false,
    "scalesProperties.showSeriesLastValue": false,
    "mainSeriesProperties.showPriceLine": false,
    "mainSeriesProperties.areaStyle.color1":
      rate > 0 ? "rgba(64, 211, 179, 0.28)" : "rgba(244, 64, 64, 0.28)",
    "mainSeriesProperties.areaStyle.color2": rate > 0 ? "#40d3b3" : "#F44040",
    "mainSeriesProperties.areaStyle.linecolor":
      rate > 0 ? "#40d3b3" : "#F44040",
    "linetoolposition.bodyFontSize": 8,
    "scalesProperties.showStudyLastValue": false,
    "mainSeriesProperties.priceLineColor": "#007FFF",
    timezone: `Etc/GMT${-utc}`,
    "paneProperties.topMargin": 5,
    "paneProperties.bottomMargin": 5,
    "scalesProperties.fontSize": 10,
    ...overrides,
  };
  openChart(false);
  redirect("223");
  getDrawingRecord();

  window.tvWidget = new TradingView.widget({
    symbol: symbol, // default symbol
    interval: interval, // default interval
    fullscreen: true, // displays the chart in the fullscreen mode
    container: "tv_chart_container",
    datafeed: Datafeed,
    library_path: "charting_library/",
    timezone: `Etc/GMT${-utc}`,
    custom_timezones: [
      {
        id: `Etc/GMT${-utc}`,
        alias: `Etc/GMT${-utc}`,
        title: `Etc/GMT${-utc}`,
      },
    ],
    locale: locale,
    theme: theme,
    load_last_chart: true,
    loading_screen: loadingScreen,
    auto_save_delay: 1,
    custom_css_url: "../css/custom_css.css",
    enabled_features: [
      "header_widget",
      "chart_zoom",
      "pinch_scale",
      "save_chart_properties_to_local_storage",
      "move_logo_to_main_pane",
      "request_only_visible_range_on_reset",
      "iframe_loading_compatibility_mode",
      "studies_extend_time_scale",
      "determine_first_data_request_size_using_visible_range"
    ],
    disabled_features: [
      "header_widget",
      "pane_context_menu",
      "header_fullscreen_button",
      "header_symbol_search",
      "header_compare",
      "use_localstorage_for_settings",
      "header_indicators",
      "header_interval_dialog_button",
      "header_resolutions",
      "header_search",
      "header_screenshot",
      "header_undo_redo",
      "header_chart_type",
      "go_to_date",
      "timeframes_toolbar",
      "left_toolbar",
      "header_settings",
      "legend_widget",
      "scales_context_menu",
      "main_series_scale_menu",
      "axis_pressed_mouse_move_scale",
      "control_bar",
      "volume_force_overlay",
      "widget_logo",
    ],
    overrides: overrides,
    saved_data: symbolChart,
    custom_font_family: "'Gilroy-Medium'",
    studies_overrides: {
      "macd.histogram.plottype": "columns",
      "macd.histogram.color.0": "rgba(11, 197, 164, 0.4)",
      "macd.histogram.color.1": "rgba(11, 197, 164, 0.4)",
      "macd.histogram.color.2": "rgba(226, 79, 30, 0.4)",
      "macd.histogram.color.3": "rgba(226, 79, 30, 0.4)",
      "macd.macd.color": "#5c94ff",
      "macd.signal.color": "#eee15b",
    },
    custom_indicators_getter: function (PineJS) {
      return Promise.resolve(custom_indicators(PineJS));
    },
    custom_formatters: {
      dateFormatter: {
        format: (date) => {
          return date.getUTCMonth() + 1 + "/" + date.getUTCDate();
        },
      },
      timeFormatter: {
        format: (date) => {
          return (
            String(date.getUTCHours()).padStart(2, "0") +
            ":" +
            String(date.getUTCMinutes()).padStart(2, "0")
          );
        },
      },
      tickMarkFormatter: (date, tickMarkType) => {
        let day = String(date.getUTCDate()).padStart(2, "0");
        let month = String(date.getUTCMonth() + 1).padStart(2, "0");
        let year = date.getUTCFullYear();
        let time =
          String(date.getUTCHours()).padStart(2, "0") +
          ":" +
          String(date.getUTCMinutes()).padStart(2, "0");

        switch (tickMarkType) {
          case "Year":
            return year;

          case "Month":
            return month + "/" + year;

          case "DayOfMonth":
            return day + "/" + month;

          case "Time":
            return time;

          case "TimeWithSeconds":
            return time;
        }
      },
    },
  });

  window.tvWidget.onChartReady(function () {
    window.getMinAndMaxPrice = () => getMinAndMaxPrice();
    window.removeDrawingRecord = () => removeDrawingRecord();
    window.getDrawingRecord = () => getDrawingRecord();
    window.closeAllActiveLine = () => closeAllActiveLine();
    window.tvWidget.chart().getTimeScale().setBarSpacing(5);
    const priceScale1 = window.tvWidget.chart().getPanes()[0].getRightPriceScales()[0];
    priceScale1.setMode(1);

    // FE replace websocket
    import("./streaming_webview.js").then((myModule) => {
      window.reconnect = () => {
        redirect("223");
        myModule.reconnect();
        redirect("224");
      };

      window.getFromWebView = async (timestamp, ask, bid, rate) => {
        let result = null;
        await myModule
          .getFromWebView(timestamp, ask, bid, rate)
          .then((response) => {
            result = response;
          });
        return result;
      };
    });

    if (isTickChart) {
      window.tvWidget.chart().setChartType(getChartType("Area"));
    }

    window.tvWidget
      .chart()
      .getTimeScale()
      .barSpacingChanged()
      .subscribe(this, function (e) {
        if (e >= 84) {
          window.tvWidget.chart().getTimeScale().setBarSpacing(84);
        } else {
          window.tvWidget.chart().getTimeScale().setBarSpacing(e);
        }
      });

    window.tvWidget
      .changeTheme(theme.toLocaleLowerCase())
      .then(() => window.tvWidget.applyOverrides(shareOverrides));
    window.tvWidget.mainSeriesPriceFormatter()._priceScale = priceScale;
    window.tvWidget.chart().getTimeScale().defaultRightOffsetPercentage().setValue(0);

    if (symbolChart === null) {
      updateIndicator("MACD", "MACD");
    }

    const iframe = document.querySelector("iframe").contentDocument;

    const toggleTopDropdown = () => {
      openIndicatorSelection({
        value: window.currentMainIndicator,
        type: "main",
      });

      const topPriceLineAppend = iframe.querySelector("#topPriceLineAppend");
      if (topPriceLineAppend) topPriceLineAppend.style.display = "none";

      closeAllActiveLine();
    };

    const div = document.createElement("div");
    div.className = "topAppend";
    div.innerHTML = `
				<div id="topIndicator">
					<div id="currentIndicator">${window.currentMainIndicator}</div>
					<div id="topIndicatorInfo">${currentMainIndicatorValue}</div>
					<div class="arrowContainer"  id="indicatorArrow">
						<img src="${window.arrow}" class="indicatorArrow"/>
					</div>
				</div>	
		`;

    const targetElement = iframe.querySelector(
      "html body .chart-markup-table tr:first-child > td:nth-child(2) > div"
    );
    targetElement.appendChild(div);

    const topIndicator = iframe.getElementById("topIndicator");
    topIndicator.addEventListener("click", toggleTopDropdown);

    // });
    // const divLogo = document.createElement("div");
    // divLogo.className = "logo";
    // divLogo.innerHTML = `
    // 	<img src="${window.logo}" class="logoImg" />
    // `;

    // targetElement.appendChild(divLogo);

    getMinAndMaxPrice();

    window.tvWidget
      .chart()
      .onIntervalChanged()
      .subscribe(null, function (changedInterval, obj) {
        interval = changedInterval;
      });

    window.tvWidget
      .chart()
      .onVisibleRangeChanged()
      .subscribe(null, ({ from, to }) => {
          getMinAndMaxPrice();
      });

    let current = null;

    window.tvWidget.subscribe("onTick", function (callback) {
      if (showAskPrice) {
        if (positionLine.ask) {
          window.tvWidget.chart().removeEntity(positionLine.ask);
          // window.tvWidget
          //   .chart()
          //   .getShapeById(positionLine.ask)
          //   .setPoints([{ price: askPrice }]);
        }

        positionLine.ask = window.tvWidget.chart().createShape({ price: askPrice },
							{
								shape: 'horizontal_line',
								disableSelection: true,
								disableSave: true,
								disableUndo: false,
								lock: true,
								zOrder: 'bottom',
								overrides: {
                  linecolor: '#03c79b',
									linewidth: 1.0,
									linestyle: 1,
									showPrice: true,
									showLabel: true,
								}
							}
						)
      }

      if (showBidPrice) {
        if (positionLine.bid) {
          window.tvWidget.chart().removeEntity(positionLine.bid);
          // window.tvWidget
          //   .chart()
          //   .getShapeById(positionLine.bid)
          //   .setPoints([{ price: bidPrice }]);
        }

        positionLine.bid = window.tvWidget.chart().createShape({ price: bidPrice },
          {
            shape: 'horizontal_line',
            disableSelection: true,
            disableSave: true,
            disableUndo: false,
            lock: true,
            zOrder: 'bottom',
            overrides: {
              linecolor: '#f44041',
              linewidth: 1.0,
              linestyle: 1,
              showPrice: true,
              showLabel: true
            }
          }
        )
      }

      let callbackTime = new Date(callback.time * 1000000);
      if (callbackTime.getTime() > current?.getTime()) {
        getMinAndMaxPrice();
      }
      current = callbackTime;
    });

    window.tvWidget.subscribe("onAutoSaveNeeded", function () {
      let shapes = window.tvWidget.chart().getAllShapes();
      let checkShape =
        shapes.length > 0 &&
        shapes.filter(
          (shape) =>
            shape.id !== positionLine?.ask && shape.id !== positionLine?.bid
        );
      if (checkShape.length > 0) {
        window.tvWidget.save(function (saveObject) {
          const { charts, layout } = saveObject;
          let save = { layout, charts };
          storeDrawingRecord(save);
        });
      } else if (
        shapes.filter(
          (shape) =>
            shape.id !== positionLine?.ask && shape.id !== positionLine?.bid
        ).length === 0
      ) {
        removeDrawingRecord();
      }
    });
    window.tvWidget.subscribe("drawing", function () {
      storeDrawingRecordBeforeClose();
    });

    window.tvWidget.subscribe("drawing_event", function (id, data) {
      if (data === "click") {
        checkActiveLine(id);
      }

      storeDrawingRecordBeforeClose();
    });

    window.tvWidget.chart().dataReady(() => {
      if (!isMobile) {
        setTimeout(
          () =>
            storeIndicators(
              {
                MA: [5, 10, 30],
                EMA: [5, 10, 30],
                BOLL: [25, 2],
                MIKE: [12],
                BBI: [],
                SAR: [],
                MACD: [12, 10, 9],
                KDJ: [14, 3, 3],
                RSI: [14],
                WR: [14],
                CCI: [14],
                KD: [14, 3, 3],
                DMI: [14, 6],
              },
              "MIKE",
              "CCI"
            ),
          0
        );
      }

      redirect("209");
      redirect("224");

      openChart(true);
    });
  });

  window.tvWidget.headerReady().then(function () {
    const iframe = document.querySelector("iframe").contentDocument;

    if (symbolChart != null) {
      window.tvWidget.setSymbol(symbol, interval, function () {});
    }
    var tickButton = window.tvWidget.createButton({ align: "left" });
    tickButton.setAttribute("title", text.tickChart[locale]);
    tickButton.textContent = text.tickChart[locale];
    tickButton.addEventListener("click", function () {
      if(window.tvWidget.chart().resolution() != 1){
        openChart(false);
      }
      window.tvWidget.setSymbol(symbol, 1, function () {
        openChart(true);
        getMinAndMaxPrice();
      });
      iframe
        .querySelector(".group-MBOVGQRI > .wrap-n5bmFxyX > .active")
        ?.classList.remove("active");
      this.classList.add("tick-active");
      window.tvWidget.chart().setChartType(getChartType("Area"), function(){
        openChart(true);
      });
      isTickChart = true;
      changeInterval("0");
    });

    const btnList = [
      {
        label: "1m",
        resolution: 1,
      },
      {
        label: "5m",
        resolution: 5,
      },
      {
        label: "15m",
        resolution: 15,
      },
      {
        label: "30m",
        resolution: 30,
      },
      {
        label: "1h",
        resolution: 60,
      },
      {
        label: "4h",
        resolution: "4H",
      },
      {
        label: "1D",
        resolution: "1D",
      },
      {
        label: "1W",
        resolution: "1W",
      },
      {
        label: "1M",
        resolution: "1M",
      },
    ];

    btnList.forEach(function (item, index) {
      var button = window.tvWidget.createButton({ align: "left" });
      button.setAttribute("title", item.resolution);
      button.textContent = item.label;

      if (
        item.resolution == window.tvWidget.chart().resolution() &&
        isTickChart === false
      ) {
        button.classList.add("active");
      }

      if (index + 1 == btnList.length) {
        button.classList.add("last-interval");
      }
      button.addEventListener("click", function () {
        openChart(false);
        const topPriceLineAppend = iframe.querySelector("#topPriceLineAppend");
        if (topPriceLineAppend) topPriceLineAppend.style.display = "none";
        closeAllActiveLine();
        let clickedResolution = window.tvWidget.chart().resolution();

        if (clickedResolution == 240) {
          clickedResolution = "4H";
        } else if (clickedResolution == 1440) {
          clickedResolution = "1D";
        } else if (clickedResolution == 10080) {
          clickedResolution = "1W";
        } else if (clickedResolution == 43200) {
          clickedResolution = "1M";
        }

        changeInterval(item.resolution);

        if (item.resolution != clickedResolution) {
          iframe
            .querySelector(".group-MBOVGQRI > .wrap-n5bmFxyX > .active")
            ?.classList.remove("active");
          this.classList.add("active");
          window.tvWidget.chart().setChartType(getChartType(chartType));
          window.tvWidget.setSymbol(symbol, item.resolution, function () {
            getMinAndMaxPrice();
            openChart(true);
          });
        } else {
          this.classList.add("active");
          window.tvWidget.chart().setChartType(getChartType(chartType));
          openChart(true);
        }
        isTickChart = false;
        iframe
          .querySelector(".group-MBOVGQRI > .wrap-n5bmFxyX > .tick-active")
          ?.classList.remove("tick-active");
      });
    });

    if (isTickChart === true) {
      iframe
        .querySelector(".group-MBOVGQRI > .wrap-n5bmFxyX > .active")
        ?.classList.remove("active");
      tickButton.classList.add("tick-active");
    } else {
      window.tvWidget.chart().setChartType(getChartType(chartType));
    }
  });

  document.querySelector("iframe").addEventListener("load", function () {
    observeIframe();
    observePriceLine();
    const iframe = document.querySelector("iframe");

    const indicators = iframe.contentDocument.querySelectorAll(
      ".topDropdownIndicator, .bottomDropdownIndicator"
    );
    indicators.forEach((indicator) => {
      indicator.addEventListener("click", handleIndicatorClick);
    });

    // Add click and touchend event listener to the iframe content
    iframe.contentDocument.body.addEventListener(
      "click",
      handleIframeContentClick
    );
    iframe.contentDocument.body.addEventListener(
      "touchend",
      handleIframeContentClick,
      { passive: false }
    );
  });
}

const handleIndicatorClick = (event) => {
  const element = event.target;
  const indicator = element.dataset.full_value;
  const shortValue = element.dataset.value;
  window.parent.updateIndicator(indicator, shortValue);
};

// Function to handle click and touchend events on the iframe content
const handleIframeContentClick = (event) => {
  const iframe = document.querySelector("iframe");
  if (event.target.tagName === "CANVAS") {
    const button = iframe.contentDocument.querySelector("#bottomDropdown");
    const top = iframe.contentDocument.querySelector("#topDropdown");
    if (top) top.style.display = "none";
    if (button) button.style.display = "none";
    const topPriceLineAppend = iframe.contentDocument.querySelector(
      "#topPriceLineAppend"
    );
    if (topPriceLineAppend) topPriceLineAppend.style.display = "none";


    closeAllActiveLine();
    if(iframe.contentDocument.querySelector("#bottomToast").style.display === "flex"){
      iframe.contentDocument.querySelector("#bottomToast").style.display = "none";
      iframe.contentDocument.querySelector("#bottomToastClose").setAttribute("data-clicked", "true");
    }
  }
  if(event.target.parentNode.id == 'bottomToastClose' || event.target.id === 'bottomToastClose'){
    if(iframe.contentDocument.getElementById("bottomToastClose").getAttribute('data-clicked') === "false"){
      iframe.contentDocument.getElementById("bottomToast").style.display = 'none';
      iframe.contentDocument.getElementById("bottomToastClose").setAttribute('data-clicked', 'true');
    }
  }

};

const observeIframe = () => {
  let counter = 0;
  const iframe = document.querySelector("iframe").contentDocument;
  // Create a new MutationObserver
  const observer = new MutationObserver(function (mutations) {
    // Check if the target element (#bottomIndicatorInfo) is added or modified
    if (
      iframe.querySelector("html body .chart-markup-table > tr:nth-child(3)") &&
      counter < 1
    ) {
      counter++;
      const div = document.createElement("div");
      div.className = "bottomAppend";
      div.innerHTML = `
				<div id="bottomIndicator">
					<div id="currentBottomIndicator">${window.currentSubIndicator}</div>
					<div id="bottomIndicatorInfo"></div>
					<div class="arrowContainer"  id="indicatorArrow">
						<img src="${window.arrow}" class="indicatorArrow"/>
					</div>
				</div>	
			
				
			`;
      const targetElement = iframe.querySelector(
        "html body .chart-markup-table > tr:nth-child(3)"
      );
      targetElement.appendChild(div);
      // Click event handler for the bottomIndicator
      function toggleBottomDropdown() {
        openIndicatorSelection({
          value: window.currentSubIndicator,
          type: "sub",
        });
        const topPriceLineAppend = iframe.querySelector("#topPriceLineAppend");
        if (topPriceLineAppend) topPriceLineAppend.style.display = "none";
        closeAllActiveLine();
      }

      const bottomIndicator = iframe.getElementById("bottomIndicator");
      bottomIndicator.addEventListener("click", toggleBottomDropdown);

      const div1 = document.createElement("div");
      div1.id = "bottomToast";
      div1.innerHTML = `
          <div id="bottomToastContainer">
            <p id="content">${text.orders_hint[lang]}</p>
            <div id="bottomToastClose" data-clicked="false">
              <img src='../hint-close.webp' />
            </div>
          </div>
			`;
   
      targetElement.appendChild(div1);
      const bottomToastClose = iframe.getElementById("bottomToastClose");
      function toggleBottomToastClose(element) {
        if(element.target.getAttribute('data-clicked') === "false"){
          iframe.getElementById("bottomToast").style.display = 'none';
          element.target.setAttribute('data-clicked', 'true');
        }
      }

      bottomToastClose.addEventListener("click", toggleBottomToastClose);

      observer.disconnect();
    }
  });
  // Start observing the iframe's DOM changes
  observer.observe(iframe, { childList: true, subtree: true });
};

const observePriceLine = () => {
  let counter = 0;
  const iframe = document.querySelector("iframe").contentDocument;
  // Create a new MutationObserver
  const observer = new MutationObserver(function (mutations) {
    // Check if the target element (#bottomIndicatorInfo) is added or modified
    if (iframe.querySelector("#topPriceLine") && counter < 1) {
      counter++;
      const rightArrow = iframe.getElementById("rightArrow");
      rightArrow.addEventListener("click", function (event) {
        const priceLineContent = iframe.getElementById("priceLineContent");
        let orderSort = priceLineContent.dataset.order;

        if (orderSort == activeOrders.length - 1) {
          orderSort = 0;
          priceLineContent.dataset.order = orderSort;
        } else {
          orderSort++;
          priceLineContent.dataset.order = orderSort;
        }

        createPriceLineContent(orderSort, "slide-out 0.5s forwards", false, true);

      });

      const leftArrow = iframe.getElementById("leftArrow");

      leftArrow.addEventListener("click", function (event) {
        const priceLineContent = iframe.getElementById("priceLineContent");
        let orderSort = priceLineContent.dataset.order;

        if (orderSort == 0) {
          orderSort = activeOrders.length - 1;
          priceLineContent.dataset.order = orderSort;
        } else {
          orderSort--;
          priceLineContent.dataset.order = orderSort;
        }

        createPriceLineContent(orderSort, "slide-in 0.5s forwards", false, true);

      });

      observer.disconnect();
    }
  });
  // Start observing the iframe's DOM changes
  observer.observe(iframe, { childList: true, subtree: true });
};



const getMinAndMaxPrice = async () => {
  fromTime = window.tvWidget.chart().getVisibleRange().from;
  toTime = window.tvWidget.chart().getVisibleRange().to;
  try{
    setTimeout(async () => {
      let bars = await window.tvWidget
            .chart()
            .exportData({
              includeTime: false,
              includeSeries: true,
              includeDisplayedValues: true,
              includeUserTime: true,
              from: fromTime,
              to: toTime,
            })
            .then((callback) => {
              return Promise.resolve(callback.data);
            });
          maxPrice = 0;
          minPrice = 99999;
          bars.forEach(function (bar) {
            let time = bar[0];
            let high = Math.max(bar[1], bar[2], bar[3], bar[4]);
            let low = Math.min(bar[1], bar[2], bar[3], bar[4]);
              if (high > maxPrice) {
                maxPrice = high;
                maxPriceTime = time;
              }
              if (low < minPrice) {
                minPrice = low;
                minPriceTime = time;
              }
          });

          minMaxcallback(minPrice, maxPrice, minPriceTime, maxPriceTime);
    }, 0)
  }catch(e){

  }
};

const minMaxcallback = (minPrice, maxPrice, minPriceTime, maxPriceTime) => {
  removePriceLabel("low");
  removePriceLabel("high");

  let low = window.tvWidget
    .chart()
    .createExecutionShape()
    .setText(minPrice.toString())
    .setPrice(minPrice)
    .setTextColor("#FF0000")
    .setDirection("sell")
    .setArrowHeight(3)
    .setArrowSpacing(4)
    .setArrowColor("#FF0000")
    .setTime(minPriceTime);

  let high = window.tvWidget
    .chart()
    .createExecutionShape()
    .setText(maxPrice.toString())
    .setPrice(maxPrice)
    .setTextColor("rgb(134,134,134)")
    .setArrowHeight(3)
    .setArrowSpacing(4)
    .setDirection("buy")
    .setArrowColor("rgb(134,134,134)")
    .setTime(maxPriceTime);

  priceLabel.low = low;
  priceLabel.high = high;
};

const removePriceLabel = (type) => {
  Object.entries(priceLabel).forEach(([key, value]) => {
    if (key === type && value !== null) {
      value.remove();
      priceLabel[key] = null;
    }
  });
};

const checkActiveLine = (id) => {
  closeAllActiveLine();
  
  let type = "";
  Object.entries(positionLine).forEach(([key, value]) => {
    if (value?._line?._id === id || value === id) {
      type = key;
    }
  });
  if (type == "") {
    const iframe = document.querySelector("iframe");
    const topPriceLineAppend = iframe.contentDocument.querySelector(
      "#topPriceLineAppend"
    );
    if (topPriceLineAppend) topPriceLineAppend.style.display = "none";
  } else {
    //callback to FE current clicked element
    setTimeout(() => activeLine(type), 0);
  }

  return type;
};

const closeAllActiveLine = () => {
  Object.entries(positionLine).forEach(([key, value]) => {
    if (value !== null && key != "ask" && key != "bid") {
      if(!createdOrder || (createdOrder && value.ordernumber !== createdOrder.ordernumber)){
        key != "open" ?
          value?.setCancelButtonBackgroundColor("#fff")
              .setCancelButtonBorderColor("#fff")
              .setCancelButtonIconColor("#000")
              .setBodyTextColor("#000").setBodyBackgroundColor("#fff")
              .setQuantityBackgroundColor("#fff")
              .setQuantityBorderColor("#fff")
              .setBodyBorderColor("#fff") : 
              value.setBodyTextColor("#000")
                .setBodyBackgroundColor("#fff")
                .setQuantityBackgroundColor("#fff")
                .setQuantityBorderColor("#fff");
      }
    }
  });
  currentActivePositionLine = "";

};

const activeLine = (type) => {
  Object.entries(positionLine).forEach(([key, value]) => {
    if (key === type) {
      const iframe = document.querySelector("iframe").contentDocument;
      iframe.querySelector("#topPriceLineAppend").style.display = "block";
      let search =
        key !== "ask" && key !== "bid"
          ? activeOrders.filter(
              (order) =>
                order.ordernumber == value.ordernumber &&
                order.typeName == value.type
            )[0]
          : activeOrders.filter((order) => order.type == key)[0];

      let orderSort = activeOrders.indexOf(search);

      createPriceLineContent(orderSort, "slide-out 0.5s forwards");

      currentActivePositionLine = key;
      
      if(key !== "ask" && key !== "bid" && 
      iframe.getElementById("bottomToastClose").getAttribute("data-clicked") === "false") iframe.querySelector("#bottomToast").style.display = "flex";
    }
  });
};

const compressData = (data) => {
  // Convert the array of objects to a JSON string
  const jsonString = JSON.stringify(data);

  // Compress the JSON string using Gzip with pako
  const compressedData = pako.gzip(jsonString);

  // Convert the compressed data to base64
  const base64EncodedData = btoa(
    String.fromCharCode.apply(null, compressedData)
  );

  return base64EncodedData;
};

const depressData = (data) => {
  // Decode the base64 data
  const compressedDataFromStorage = new Uint8Array(
    atob(data)
      .split("")
      .map((char) => char.charCodeAt(0))
  );

  // Decompress the Gzip data
  const decompressedJsonString = pako.ungzip(compressedDataFromStorage, {
    to: "string",
  });

  // Parse the decompressed JSON string back into an array of objects
  const decompressedData = JSON.parse(decompressedJsonString);

  return decompressedData;
};

const checkLocalStorageCanStore = () => {
  var data = "";
  for (var key in window.localStorage) {
    if (window.localStorage.hasOwnProperty(key)) {
      data += window.localStorage[key];
    }
  }

  const dataUsed = ((data.length * 16) / (8 * 1024)).toFixed(2);
  if (dataUsed > 4300) {
    cleanDrawingRecord();
  }

  return;
};

const cleanDrawingRecord = () => {
  let record = localStorage.getItem(drawingKey);

  if (record) {
    record = depressData(record);
    record.length > 0 ? record.shift() : null;
  }
};

const storeDrawingRecord = (data) => {
  checkLocalStorageCanStore();
  let record = localStorage.getItem(drawingKey);

  let acctValue = null;
  if (record) {
    record = depressData(record);

    Object.entries(record).forEach(([key, value]) => {
      if (value[symbol]) {
        value[symbol] = data;
        acctValue = data;
      }
    });
  } else {
    record = [];
  }

  if (acctValue == null) {
    record.push({ [symbol]: data });
  }

  if (record.length >= 20) {
    record.shift();
  }

  record = compressData(record);

  localStorage.setItem(drawingKey, record);
};

const removeDrawingRecord = () => {
  if (localStorage.getItem(drawingKey)) {
    let record = depressData(localStorage.getItem(drawingKey));

    if (record) {
      let final = record.filter(function (value) {
        return !value[symbol];
      });
      localStorage.setItem(drawingKey, compressData(final));
    }
  }
};

const storeDrawingRecordBeforeClose = () => {
  try {
    let shapes = window.tvWidget.chart().getAllShapes();
    let checkShapeChanges =
      shapes.length > 0 &&
      shapes.filter(
        (shape) =>
          shape.id !== positionLine?.ask && shape.id !== positionLine?.bid
      );
    if (checkShapeChanges.length > 0) {
      window.tvWidget.save(function (saveObject) {
        const { charts, layout } = saveObject;
        let save = { layout, charts };
        storeDrawingRecord(save);
      });
    }
  } catch (error) {
    localStorage.setItem("error", error, toString());
  }
};

const getDrawingRecord = () => {
  if (localStorage.getItem(drawingKey)) {
    const record = depressData(localStorage.getItem(drawingKey));
    if (record) {
      let search = record.filter(function (value) {
        return value[symbol];
      });
      if (search.length > 0) {
        symbolChart = search[0][symbol];
      }
    }
  }
  return symbolChart;
};

const openChart = (open) => {
  if(open){
    document.getElementById('loading-animation').style.display = 'none';
        document.getElementById('tv_chart_container').style.display = 'block';
  }else{
    document.getElementById('loading-animation').style.display = 'flex';
        document.getElementById('tv_chart_container').style.display = 'none';
  }
}


window.addEventListener("DOMContentLoaded", initReady, false);

window.addEventListener(
  "unload",
  function () {
    storeDrawingRecordBeforeClose();
  },
  false
);


