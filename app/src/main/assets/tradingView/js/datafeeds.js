

import {
	parseFullSymbol,
	addTimestampUTC,
	deductTimestampUTC,
	getData
} from './helpers.js';

const lastBarsCache = new Map();

const configurationData = {
	supported_resolutions: ['1', '5', '15', '30', '60', '120', '240', '360', '720', '1440' ,'1D', '1W', '1M'],
	supports_marks: true,
	supports_timescale_marks: true,
};

async function getAllSymbols(symbolName) {
	const parsedSymbol = await parseFullSymbol(symbolName);
	const symbol = parsedSymbol.symbol;

	return [
		{
			symbol: symbol,
			full_name: symbolName,
			description: symbol,
			exchange: parsedSymbol.exchange,
			type: parsedSymbol.type,
		}
	];
}

export default {
	onReady: (callback) => {
		setTimeout(() => callback(configurationData));
	},
	searchSymbols: async (
		userInput,
		exchange,
		symbolType,
		onResultReadyCallback,
	) => {
	},

	resolveSymbol: async (
		symbolName,
		onSymbolResolvedCallback,
		onResolveErrorCallback,
	) => {

		const symbols = await getAllSymbols(symbolName);

		const symbolItem = symbols.find(({
			full_name,
		}) => full_name === symbolName);
		if (!symbolItem) {
			onResolveErrorCallback('cannot resolve symbol');
			return;
		}
		
		const symbolInfo = {
			ticker: symbolItem.full_name,
			session: '1;24x7',
			name: symbolItem.symbol,
			description: symbolItem.description,
			type: symbolItem.type,
			timezone: `Etc/GMT${-utc}`,  
			exchange: symbolItem.exchange,
			minmov: 1,
			pricescale: parseInt(priceScale),
			has_intraday: true,
			has_daily: true,
			has_weekly_and_monthly: true,
			supported_resolutions: configurationData.supported_resolutions,
			intraday_multipliers: ['1', '5', '15', '30', '60', '120', '240', '360', '720', '1440'],
			daily_multipliers : ['1'],
			weekly_multipliers : ['1'],
			monthly_multipliers : ['1'],
			visible_plots_set: 'ohlc',
			data_status: 'streaming',
		};

	
		setTimeout(function () {
			onSymbolResolvedCallback(symbolInfo)
		}, 0);
	},

	getBars: async (symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback) => {
		const { from, to, firstDataRequest, countBack } = periodParams;
		
		let period =  resolution;
		if(resolution == '1D'){
			period = 1440
		}
		if(resolution == '1M'){
			period = 43200;
		}
		if(resolution == '1W'){
			period = 10080
		}
		if(resolution == '4H'){
			period = 240;
		}
		
		
		try {
			let dateTo =  to * 1000;
			let dateFrom =  from * 1000;
			
			dateTo = addTimestampUTC(dateTo, utc);
			dateFrom = addTimestampUTC(dateFrom, utc);
			
			if(dateFrom < 0 ){
				let test = new Date(dateTo);
				let Y1970 = test.setFullYear(1970);
				dateFrom = Y1970;
			}

			if(dateTo < 0){
				onHistoryCallback([], {
					noData: true,
				});
				return;
			}
			let body = null;

			if(userType === 'mt4'){
				body = {
					'period': period,
					'symbol': symbolInfo.name,
					'serverId': serverId,
					'server': serverId,
					'from': dateFrom / 1000,
					'to': dateTo / 1000,
					'size': 350
				};
			}else{
				body = {
					'period': period,
					'symbol': symbolInfo.name,
					'serverId': serverId,
					'server': serverId,
					'from': dateFrom,
					'to': dateTo,
					'size': 350
				};
			}
			if(firstDataRequest && userType !== 'mt4'){
				body = {
					'period': period,
					'symbol': symbolInfo.name,
					'serverId': serverId,
					'server': serverId,
					'from': '',
					'to': '',
					'size': 350
				};
			}

			body.login = login;
			const list = await getData(body, firstDataRequest);
			sessionStorage.getItem(symbol) ? sessionStorage.removeItem(symbol) : null; 
		
			if (list.length === 0) {
				// "noData" should be set if there is no data in the requested period.
				onHistoryCallback([], {
					noData: true,
				});
				return;
			}
			let bars = [];

			list.forEach(bar => {
				let barTime = bar.timestamp * 1000;
				
				if(period < 1440){
					barTime =  deductTimestampUTC(barTime, utc);
				}
				else {
					barTime = new Date(barTime);
					barTime.setUTCHours(0);
					barTime.setUTCMinutes(0);
					barTime.setUTCMilliseconds(0);
					barTime = barTime.getTime();
				}
			
				if (barTime <= deductTimestampUTC(dateTo, utc)) {
					bars = [...bars, {
						time: barTime,
						low: parseFloat(bar.low),
						high: parseFloat(bar.high),
						open: parseFloat(bar.open),
						close: parseFloat(bar.close),
					}];
				}
			});
	
			if (firstDataRequest) {
				lastBarsCache.set(symbolInfo.full_name, {
					...bars[bars.length - 1],
				});
			}
			if (bars.length > 0) {
				onHistoryCallback(bars, {
					noData: false,
				})
			}else{
				onHistoryCallback([], {
					noData: true,
				});
			}
			

		} catch (error) {
			onErrorCallback(error);
		}
	},
	// FE replace websocket
	subscribeBars: (
		symbolInfo,
		resolution,
		onRealtimeCallback,
		subscribeUID,
		onResetCacheNeededCallback,
	) => {
		import('./streaming_webview.js').then(myModule => {
			myModule.subscribeOnStream(
				symbolInfo,
				resolution,
				onRealtimeCallback,
				subscribeUID,
				onResetCacheNeededCallback,
				lastBarsCache.get(symbolInfo.full_name),
			)
		})
	},

	unsubscribeBars: (subscriberUID) => {
		import('./streaming_webview.js').then(myModule => {
			myModule.unsubscribeFromStream(subscriberUID)
		})
	},
};