function custom_indicators(PineJS) { 
    return [
        {
            name: "<PERSON><PERSON><PERSON>",
            metainfo: {
                _metainfoVersion: 41,
                id: "KDJ-custom@tv-basicstudies-1",
                scriptIdPart: "",
                name: "<PERSON><PERSON><PERSON>",
                description: "<PERSON>D<PERSON>",
                shortDescription: "KDJ",
                is_price_study: false,
                isCustomIndicator: true,
                canExtendTimeScale: true,
                defaults: {
                    styles: {
                    plot_1: {
                        linestyle: 0,
                        linewidth: 1, //红色
                        plottype: 0,
                        trackPrice: !1,
                        transparency: 0,
                        visible: !0,
                        color: "#007FFF",
                    },
                    plot_2: {
                        linestyle: 0,
                        linewidth: 1,
                        plottype: 0,
                        trackPrice: !1,
                        transparency: 0,
                        visible: !0,
                        color: "#0BC5A4",
                    },
                    plot_3: {
                        linestyle: 0,
                        linewidth: 1,
                        plottype: 0,
                        trackPrice: !1,
                        transparency: 0,
                        visible: !0,
                        color: "#d6c313",
                    },
                    },
                    precision: 2 /**保留小数点 */,
                    inputs: {
                    in_0: 14,
                    in_1: 1,
                    in_2: 1,
                    in_3: 1,
                    },
                },
                plots: [
                    {
                    id: "plot_1",
                    type: "line",
                    },
                    {
                    id: "plot_2",
                    type: "line",
                    },
                    {
                    id: "plot_3",
                    type: "line",
                    },
                ],
                styles: {
                    plot_1: {
                    // Output name will be displayed in the Style window
                    title: "Equity value",
                    histogramBase: 0,
                    },
                    plot_2: {
                    // Output name will be displayed in the Style window
                    title: "Equity value",
                    histogramBase: 0,
                    },
                    plot_3: {
                    // Output name will be displayed in the Style window
                    title: "Equity value",
                    histogramBase: 0,
                    },
                },
                inputs: [
                    {
                    id: "in_0",
                    name: "Range Length(K Length)",
                    defval: 14,
                    type: "integer",
                    min: 1,
                    max: 2e3,
                    },
                    {
                    id: "in_1",
                    name: "K Smoothing Length",
                    defval: 14,
                    type: "integer",
                    min: 1,
                    max: 2e3,
                    },
                    {
                    id: "in_2",
                    name: "D Smoothing Length",
                    defval: 1,
                    type: "integer",
                    min: 1,
                    max: 2e3,
                    },
                    {
                    id: "in_3",
                    name: "J Smoothing Length",
                    defval: 3,
                    type: "integer",
                    min: 1,
                    max: 2e3,
                    },
                ],
            },
            constructor: function () {
                this.init = function (context, inputCallback) {
                    this._context = context;
                    this._input = inputCallback;
                };
                this.main = function (a, b) {
                    this._context = a;
                    this._input = b;
                    let RangeLength = this._input(0);
                    let KsmoothLength = this._input(1);
                    let DsmoothLength = this._input(2);
                    let JsmoothLength = this._input(3);
                    this._context.setMinimumAdditionalDepth(
                    RangeLength + JsmoothLength
                    );
                    let close = this._context.new_unlimited_var(
                        PineJS.Std.close(this._context)
                    );
                    let high = this._context.new_unlimited_var(
                        PineJS.Std.high(this._context)
                    );
                    let low = this._context.new_unlimited_var(
                        PineJS.Std.low(this._context)
                    );
                    var RSV = PineJS.Std.stoch(
                    close,
                    high,
                    low,
                    RangeLength,
                    this._context
                    );
                    var K = 0.0;
                    var D = 0.0;
                    K +=
                    0.666 * PineJS.Std.nz(K[1], 50) +
                    0.334 * PineJS.Std.nz(RSV, 50);
                    D +=
                    0.666 * PineJS.Std.nz(D[1], 50) + 0.334 * PineJS.Std.nz(K, 50);
                    let J = PineJS.Std.sma(
                    this._context.new_var(3 * K - 2 * D),
                    JsmoothLength,
                    this._context
                    );
                    let K_Smoothed = PineJS.Std.sma(
                    this._context.new_var(K),
                    KsmoothLength,
                    this._context
                    );
                    let D_Smoothed = PineJS.Std.sma(
                    this._context.new_var(D),
                    DsmoothLength,
                    this._context
                    );
                    return [K_Smoothed, D_Smoothed, J];
                };
            },
        },
        {
            name: "BBI",
            metainfo: {
                _metainfoVersion: 51,
                id: "BBI-custom@tv-basicstudies-1",
                scriptIdPart: "",
                name: "BBI",
                format: {
                    type: 'price',
                },
                description: "Bull and Bear Index",
                shortDescription: "BBI",
                is_price_study: true,
                is_hidden_study: false,
                is_custom_indicator: true,
                plots: [
                    { id: "BBI", type: "line" }
                ],
                defaults: {
                    styles: {
                        BBI: {
                            linestyle: 0,
                            linewidth: 1,
                            plottype: 0,
                            trackPrice: false,
                            transparency: 0,
                            visible: true,
                            color: "#007FFF"
                        }
                    },
                    precision: 2,
                    inputs: []
                },
                styles: {
                    BBI: {
                        title: "BBI",
                        histogramBase: 0
                    }
                },
                inputs: []
            },
            constructor: function () {
                this.init = function (context, inputCallback) {
                    this._context = context;
                    this.inputCallback = inputCallback;
                    this.prices = [];
                };

                this.main = function (context, inputCallback) {
                    this._context = context;
                    this.inputCallback = inputCallback;
                    let close = this._context.new_unlimited_var(
                        PineJS.Std.close(this._context)
                    );
                    var ma3 = PineJS.Std.sma(close, 3, this._context);
                    var ma6 = PineJS.Std.sma(close, 6, this._context);
                    var ma12 = PineJS.Std.sma(close, 12, this._context);
                    var ma24 = PineJS.Std.sma(close, 24, this._context);

                    var BBI = (ma3 + ma6 + ma12 + ma24) / 4;

                    return [BBI];
                }
            }
        },
        {
            name: "MIKE",
            metainfo: {
                _metainfoVersion: 51,
                id: "MIKE-custom@tv-basicstudies-1",
                scriptIdPart: "",
                name: "MIKE",
                format: {
                    type: 'price',
                },
                description: "MIKE",
                shortDescription: "MIKE",
                is_price_study: true,
                is_hidden_study: false,
                is_custom_indicator: true,
                plots: [
                    { id: "WR", type: "line" },  // 弱阻力
                    { id: "MR", type: "line" },  // 中阻力
                    { id: "SR", type: "line" },  // 强阻力
                    { id: "WS", type: "line" },  // 弱支撑
                    { id: "MS", type: "line" },  // 中支撑
                    { id: "SS", type: "line" }   // 强支撑
                ],
                defaults: {
                    styles: {
                        WR: { color: "#FF0000", linewidth: 1 },  // 红色代表弱阻力
                        MR: { color: "#FF8800", linewidth: 1 },  // 橙色代表中阻力
                        SR: { color: "#FFDD00", linewidth: 1 },  // 黄色代表强阻力
                        WS: { color: "#00FF00", linewidth: 1 },  // 绿色代表弱支撑
                        MS: { color: "#00AAFF", linewidth: 1 },  // 浅蓝色代表中支撑
                        SS: { color: "#0000FF", linewidth: 1 }   // 蓝色代表强支撑
                    },
                    precision: 2,
                    inputs: []
                },
                styles: {
                    WR: { title: "WR", histogramBase: 0 },
                    MR: { title: "MR", histogramBase: 0 },
                    SR: { title: "SR", histogramBase: 0 },
                    WS: { title: "WS", histogramBase: 0 },
                    MS: { title: "MS", histogramBase: 0 },
                    SS: { title: "SS", histogramBase: 0 },
                },
                inputs: [
                    {
                        id: "in_0",
                        name: "周期天数",
                        defval: 12,
                        type: "integer",
                        min: 1,
                        max: 100,
                        step: 1,
                        description: "用于计算支撑和阻力水平的天数"
                    }
                ]
            },
            constructor: function () {
                this.init = function (context, inputCallback) {
                    this.context = context;
                };

                this.main = function (context, inputCallback) {
                    this.context = context;
                    this.period = inputCallback(0);

                    let high = this.context.new_unlimited_var(
                        PineJS.Std.high(this.context)
                    );
                    let low = this.context.new_unlimited_var(
                        PineJS.Std.low(this.context)
                    );
                    let close = this.context.new_unlimited_var(
                        PineJS.Std.close(this.context)
                    );
                    // 获取周期内的最高价和最低价
                    var highestHigh = PineJS.Std.highest(high, this.period);
                    var lowestLow = PineJS.Std.lowest(low, this.period);
                    var TYP = (highestHigh + lowestLow + close)  / 3;

                    // 计算支撑和阻力水平
                    var WR = TYP + (TYP - lowestLow);  // 弱阻力
                    var MR = TYP + (highestHigh - lowestLow);   // 中阻力
                    var SR = 2*highestHigh - lowestLow; // 强阻力

                    var WS = TYP - (highestHigh - TYP); // 弱支撑
                    var MS = TYP - (highestHigh - lowestLow);       // 中支撑
                    var SS = 2*lowestLow - highestHigh;   // 强支撑

                    // 返回计算出的水平
                    return [WR, MR, SR, WS, MS, SS];
                }
            }
        }
    ];
}