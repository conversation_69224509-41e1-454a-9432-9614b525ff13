
function signVAU(timestamp) {
    // sign
    return timestamp + 'vau'+'H5'+ api_ip + uuid + '1457929724SJHKJ1590405016hytech';
}

function signVJP(timestamp1) {
    // sign
    return timestamp1 + 'vjp'+'H5'+ api_ip + uuid + '1457929724SJHKJ1590405016hytech';
}

export async function getData(data, firstDataRequest) {
	if(firstDataRequest && sessionStorage.getItem(symbol)){
		try {
			let sessionData = JSON.parse(sessionStorage.getItem(symbol));
			if(typeof sessionData !== 'object' || 
				(typeof sessionData === 'object' && (sessionData.length === 0 || Object.keys(sessionData).length === 0))){
				return makeApiRequest(data);
			}

			return JSON.parse(sessionStorage.getItem(symbol));
		} catch (e) {
			return makeApiRequest(data);
		}
	}else{
		return makeApiRequest(data);
	}
}

export async function makeApiRequestVJP(data) {
	try {
		let timestamp = new Date().getTime();
		let link = apiURL;
		
		let body = JSON.stringify({"data": JSON.stringify(data)});
		if(userType !== 'mt4' && userType !== 'mt5'){
			link = mtsApiURL;
			body = JSON.stringify(data);
		}
	
		const response = await fetch(link, {
			method: 'POST', // or 'PUT'
			headers: {
				'Content-Type': 'application/json',
				'apiVer': apiVer,
				'language': 'en_US',
				'sign': hex_md5(signVJP(timestamp)).toLocaleUpperCase(),
				'ts': timestamp,
				'uuid': uuid,
				'model': "H5",
				'ip': api_ip,
				'systemType': "h5",
				'product': 'vjp',
			},
				body: body
			})
			.then((response) => response.json())
			.then((res) => {
				if(userType === 'mt4' || userType === 'mt5'){
					if(res.code == '10100010' || res.code == '10500073'){
						return [];
					}

					let depressData = gzipDecompress(res.obj.data);
					depressData = JSON.parse(depressData);
					return depressData?.list;
				}
				return res.data?.data?.list;
			})
			.catch((error) => {
				return [];
			});

		return response;
	} catch (error) {
		throw new Error(`API request error: ${error}`);
		
	}
}

// Make requests to API
export async function makeApiRequest(data) {
	if(product == 'vjp'){
		return makeApiRequestVJP(data);
	}else{
		try {
			let t = new Date().getTime();
			let link = apiURL;
	
			let extraHeader = {};
			let body = JSON.stringify({"data": JSON.stringify(data)});
			if(userType !== 'mt4' && userType !== 'mt5'){
				link = mtsApiURL;
				extraHeader = {
					'Product': 'vau'
				};
				body = JSON.stringify(data);
			}
			const response = await fetch(link, {
				method: 'POST', // or 'PUT'
				headers: {
					'Content-Type': 'application/json',
					'model': mobileType,
					'token': token,  
					'sign': hex_md5(signVAU(t)).toLocaleUpperCase(),
					'ts': t,  
					uuid: uuid,
					model: "H5",
					IP: api_ip,
					systemType: "h5",
					'Product': 'vau',
					...extraHeader
				},
					body: body
				})
				.then((response) => response.json())
				.then((res) => {
					if(userType === 'mt4' || userType === 'mt5'){
						if(res.code == '10100010' || res.code == '10500073'){
							return [];
						}
	
						let depressData = gzipDecompress(res.obj.data);
						depressData = JSON.parse(depressData);
						return depressData?.list;
					}
					return res.data?.data?.list;
				})
				.catch((error) => {
					return [];
				});
			return response;
		} catch (error) {
			throw new Error(`API request error: ${error.status}`);
			
		}
	}
	
}




export const gzipDecompress = (compressedData) => {
	// Decode base64 data to binary (Uint8Array)
	const binaryData = new Uint8Array(atob(compressedData).split('').map(char => char.charCodeAt(0)));
	// Decompress the binary data using pako.inflate() or pako.ungzip()
	const decompressedData = pako.inflate(binaryData, { to: 'string' });
	// const jsonString = JSON.parse(decompressedData);
	return decompressedData;
}

// Generate a symbol ID from a pair of the coins
export function generateSymbol(exchange, fromSymbol, toSymbol) {
	const short = `${fromSymbol}/${toSymbol}`;
	return {
		short,
		full: `${exchange}:${short}`,
	};
}

export function parseFullSymbol(fullSymbol) {
	return {
		exchange: fullSymbol,
		symbol: fullSymbol,
		type: fullSymbol,
	};
}

export function getNextDailyBarTime(barTime, timeframe) {
	let date1 = new Date(barTime);
	let minitFrame = [1, 5, 15, 30];

	if(minitFrame.includes(Number(timeframe))){
		date1.setMinutes(date1.getMinutes() + Number(timeframe));
	}else if(timeframe === 60){
		date1.setHours(date1.getHours() + 1);
	}else if(timeframe === 240){
		date1.setHours(date1.getHours() + 4);
	}else if(timeframe === '1D'){
		date1.setDate(date1.getDate() + 1);
	}
	else if(timeframe === '1W'){
		date1.setDate(date1.getDate() + 7);
	}
	else {
		date1.setMonth(date1.getMonth() + 1);
	}

	return date1.getTime();
}


export function addTimestampUTC(convertTime, utc) {

	let time = new Date(convertTime);
	// Parse the input timestamp into a Date object
	const inputDate = time;
	// Get the UTC time in milliseconds
	const utcTimeMs = inputDate.getTime();

	// Calculate the UTC+3 time in milliseconds
	const utcPlus3TimeMs = utcTimeMs + utc * 60 * 60 * 1000; // 3 hours in milliseconds
	// Create a new Date object representing the UTC+3 time
	const utcPlus3Date = new Date(utcPlus3TimeMs);

	// Format the UTC+3 timestamp as a string
	time = utcPlus3Date.getTime();

	return time;
}

export function deductTimestampUTC(convertTime, utc) {

	let time = new Date(convertTime);
	// Parse the input timestamp into a Date object
	const inputDate = time;
	// Get the UTC time in milliseconds
	const utcTimeMs = inputDate.getTime();

	// Calculate the UTC+3 time in milliseconds
	const utcPlus3TimeMs = utcTimeMs - utc * 60 * 60 * 1000; // 3 hours in milliseconds
	// Create a new Date object representing the UTC+3 time
	const utcPlus3Date = new Date(utcPlus3TimeMs);

	// Format the UTC+3 timestamp as a string
	time = utcPlus3Date.getTime();

	return time;
}

  