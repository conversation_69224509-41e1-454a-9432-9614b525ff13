<!DOCTYPE html>
<html>

<head>

	<title>TradingView Charting</title>
	<meta charset="UTF-8">
	<!-- Fix for iOS Safari zooming bug -->
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0">
	<script rel="preload" type="text/javascript" src="./charting_library/charting_library.js"></script>
	<script src="./js/pako.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="./js/customIndicators.js"></script>
	<script src="./js/lang.js"></script>
	<script src="./js/env.js"></script>
	<script src="./js/md5.js"></script>
	<script type="module" src="./js/mobile_main.js"></script>
	<script>
		let symbol = getQueryString('symbol');
		let chartType = getQueryString('chartType');
		let locale = getQueryString('locale') || 'en';
		let lang = locale;
		let interval = getQueryString('interval') || '1';
		let isTickChart = false;

		if (interval === '0') {
			interval = '1';
			isTickChart = true;
		}
		let priceScale = getQueryString('priceScale') || 100;
		let theme = getQueryString('theme') || 'Light';
		let token = getQueryString('token') || '';
		let login = getQueryString('login') || '';
		let drawingKey = login || 'no-login';
		let serverId = getQueryString('serverId');
		let mobileType = getQueryString('mobileType') || 'H5';
		let userType = getQueryString('userType') || 'mt4';
		let utc = getQueryString('utc') || 3;
		let rate = getQueryString('rate') || 1; // control tick chart color
		let currency = getQueryString('currency') || ''; // control tick chart color
		chartType = chartType.charAt(0).toUpperCase() + chartType.slice(1).toLocaleLowerCase();
		window.arrow = '../right.webp';
		window.leftArrow = '../left.webp';
		window.rightArrow = '../right.webp';
		window.logo = '../vantage.webp';
		let symbolChart = null;
		let isMobile = false;

		let askPrice = 0;
		let bidPrice = 0;
		
		/** 指标 **/
		window.currentMainIndicator = 'MA';
		window.currentSubIndicator = 'MACD';
		let currentMainIndicatorValue = '';
		let currentSubIndicatorValue = '';
		let createdIndicator = false;
	
		let priceLabel = {
			high: null,
			low: null,
		};

		let minPrice = 99999, maxPrice = 0;
		let minPriceTime, maxPriceTime, fromTime, toTime;
		const moreThanOneLine = [
			'Moving Average',
			'Moving Average Exponential',
		];

		const oneLine = [
			'Bollinger Bands',
			'Parabolic SAR',
			'MACD',
			'KDJ',
			'MIKE',
			'Bull and Bear Index',
			'Relative Strength Index',
			'Williams %R',
			'Directional Movement',
			'Commodity Channel Index',
			'Stochastic',
		];


		const mainIndicatorsWithKeys = [
			{ 'MA': 'Moving Average' },
			{ 'EMA': 'Moving Average Exponential' },
			{ 'BOLL': 'Bollinger Bands' },
			{ 'MIKE': 'MIKE' },
			{ 'BBI': 'Bull and Bear Index' },
			{ 'SAR': 'Parabolic SAR' },
		];

		const subIndicatorsWithKeys = [
			{ 'MACD': 'MACD' },
			{ 'KDJ': 'KDJ' },
			{ 'RSI': 'Relative Strength Index' },
			{ 'WR': 'Williams %R' },
			{ 'CCI': 'Commodity Channel Index' },
			{ 'KD': 'Stochastic' },
			{ 'DMI': 'Directional Movement' }
		];

		const maIndicatorColors = [
			'#00ddce',
			'#d67cf6',
			'#eee15b',
		];

		let mainShortIndicators = [];
		let subShortIndicators = [];
		let mainIndicators = [];
		let subIndicators = [];

		let showAskPrice = false;
		let showBidPrice = false;
		let positionLine = {
			ask: null,
			bid: null,
			profit: null,
			loss: null,
			open: null,
		};
		let createdOrder = null;
		const priceLineInfo = {
			TP: {activeColor: "#00C79C"},
			SL: {activeColor: "#FF8E5C"},
			Buy: {activeColor: "#FF3C70"},
			Sell: {activeColor: "#FF3C70"},
		};
		let orders = [];
		let activeOrders = [];
		let currentActivePositionLine = "";

		mainIndicatorsWithKeys.forEach((val) => {
			mainShortIndicators.push(Object.keys(val).toString());
		});
		subIndicatorsWithKeys.forEach((val) => {
			subShortIndicators.push(Object.keys(val).toString());
		});

		mainIndicatorsWithKeys.forEach((val) => {
			mainIndicators.push(Object.values(val).toString());
		});
		subIndicatorsWithKeys.forEach((val) => {
			subIndicators.push(Object.values(val).toString());
		});

		let allIndicators = {
			MACD: [12, 26, 9],
			MA: [5, 10, 30],
			EMA: [5, 10, 30],
			BOLL: [25, 2],
			MIKE: [12],
			BBI: [],
			SAR: [],
			KDJ: [14, 3, 3],
			RSI: [14],
			WR: [14],
			CCI: [14],
			KD: [14, 3, 3],
			DMI: [14, 6],
		}

		let overrides = {};
		let loadingScreen = {};
		setTheme(theme);
		function connectWebViewJavascriptBridge(callback) {
			if (window.WebViewJavascriptBridge) {
				return callback(WebViewJavascriptBridge)
			} else {
				document.addEventListener(
					'WebViewJavascriptBridgeReady'
					, function () {
						return callback(WebViewJavascriptBridge)
					},
					false
				);
			}
		}

		document.addEventListener('DOMContentLoaded', function () {
			if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
				// true for mobile device
				isMobile = true;
			}
			if (theme == 'Dark') {
				document.body.classList.add('theme-dark');
				window.arrow = '../right_dark.webp';
				window.leftArrow = '../left_dark.webp';
				window.rightArrow = '../right_dark.webp';
				window.logo = '../vantage_dark.webp'
			}

			// setup function that ios can call 
			if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				setupWebViewJavascriptBridge(function (bridge) {
					bridge.registerHandler('drawingTools', function (data, responseCallback) {
						drawingTools(data.title);
					});
					bridge.registerHandler('storeIndicators', function (data, responseCallback) {
						storeIndicators(data.value, data.mainCurrent, data.subCurrent);
					});
					bridge.registerHandler('storeOrders', function (data, responseCallback) {
						storeOrders(data.value);
					});
					bridge.registerHandler('websocketUpdate', function (data, responseCallback) {
						window.getFromWebView(data.lasttime, data.ask, data.bid, data.rate);
					});
					bridge.registerHandler('websocketReconnect', function (data, responseCallback) {
						window.reconnect();
					});
				})
			} else {
				connectWebViewJavascriptBridge(function (bridge) {
					bridge.init(function (message, responseCallback) {
						var data = {
							'Javascript Responds': 'Connected'
						};
						responseCallback(data);
					});
					bridge.registerHandler('drawingTools', function (data, responseCallback) {
						let result = JSON.parse(data);
						drawingTools(result.title);
						responseCallback(data);
					});
					bridge.registerHandler('storeIndicators', function (data, responseCallback) {
						let result = JSON.parse(data);
						storeIndicators(result.value, result.mainCurrent, result.subCurrent);
						responseCallback(data);
					});
					bridge.registerHandler('storeOrders', function (data, responseCallback) {
						let result = JSON.parse(data);
						storeOrders(result.value);
						responseCallback(data);
					});
					bridge.registerHandler('websocketUpdate', function (data, responseCallback) {
						let resultData = JSON.parse(data);
						let result = window.getFromWebView(resultData.lasttime, resultData.ask, resultData.bid, resultData.rate);
						responseCallback(result);
					});
				})
			}
		});

		// setup ios bridge
		function setupWebViewJavascriptBridge(callback) {
			if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
			if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
			window.WVJBCallbacks = [callback];
			var WVJBIframe = document.createElement('iframe');
			WVJBIframe.style.display = 'none';
			WVJBIframe.src = 'https://__bridge_loaded__';
			document.documentElement.appendChild(WVJBIframe);
			setTimeout(function () { document.documentElement.removeChild(WVJBIframe) }, 0)
		}



		function getChartType(type) {

			switch (type) {
				case "Bars":
					return 0;
				case "Candles":
					return 1;
				case "Line":
					return 2;
				case "Area":
					return 3;
				case "Heikin-ashi":// 平均K线图
					return 8;
				case "Hollow-candles":// 空心K线图
					return 9;
				case "Baseline":// 空心K线图
					return 10;
				default:
					return 1;
			}

		}

		function toast(message) {
			const json = { code: '500', message: message };
			sendMessage(json)
		}

		function redirect(code) {
			const json = { code: code };
			sendMessage(json)
		}

		function sendMessage(data) {
			if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				setupWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('sendEvent', data, function responseCallback(responseData) {
					});
				})
			}
			else {
				connectWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('sendEvent', data, function responseCallback(responseData) {
					});
				});

			}
		}

		function changeInterval(data) {
			if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				setupWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('changeInterval', data, function responseCallback(responseData) {

					});
				})
			}
			else {
				connectWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('changeInterval', data, function responseCallback(responseData) {
					});
				});
			}
		}

		function updatePriceLineStatus(type, action, price, ordernumber) {
			price = price ? price : "";
			let data = { type: type, action: action, price: price, ordernumber: ordernumber };
		
			if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				setupWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('updatePriceLineStatus', data, function responseCallback(responseData) {
					});
				})
			}
			else {
				connectWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('updatePriceLineStatus', data, function responseCallback(responseData) {
					});
				});
			}

		}

		function openIndicatorSelection(data) {
			if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
				setupWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('openIndicatorSelection', data, function responseCallback(responseData) {
					});
				})
			}
			else {
				connectWebViewJavascriptBridge(function (bridge) {
					bridge.callHandler('openIndicatorSelection', data, function responseCallback(responseData) {
					});
				});
			}

		}
		let lastCallTime = 0;
		function getProfitLoss(price, ordernumber){
			let data = { price: price, ordernumber: ordernumber };
			return new Promise((resolve, reject) => {
				if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
					setupWebViewJavascriptBridge(function (bridge) {
						bridge.callHandler('getProfitLoss', data, function responseCallback(responseData) {
							resolve(responseData);
						});
					})
				}
				else {
					connectWebViewJavascriptBridge(function (bridge) {
						function throttledCallHandler(handlerName, data, responseCallback) {
							const currentTime = Date.now();
							console.log('Call throttled to prevent flooding - 1', lastCallTime, currentTime - lastCallTime);
							if (currentTime - lastCallTime >= 30) {
								lastCallTime = currentTime;
								bridge.callHandler(handlerName, data, responseCallback);
							} else {
								console.log('Call throttled to prevent flooding');
								return 0;
							}
						}
						throttledCallHandler('getProfitLoss', data, function responseCallback(responseData) {
							resolve(responseData);
						});
					});
				}
			});
		}



		// allow frontend to open drawing tools
		function drawingTools(title) {
			document.querySelector('#topAppendDrawing') && document.querySelector('#topAppendDrawing').remove('active') && closeDrawingTools();
			window.tvWidget.selectLineTool(title);
			if (title === 'brush' || title === 'highlighter' || title === 'eraser') {
				appendCloseDrawing();
			}
		}
		let currentIndicators = null;
		// allow frontend to pass the all indicators value
		function storeIndicators(allIndicatorsValue, mainCurrent, subCurrent) {
			currentIndicators = allIndicatorsValue;

			if (mainCurrent) {
				window.currentMainIndicator = mainCurrent;
			}

			if (subCurrent) {
				window.currentSubIndicator = subCurrent;
			}

			if (allIndicatorsValue) {
				const keys = Object.keys(allIndicatorsValue);

				keys.map((index) => {
					allIndicators[index] = allIndicatorsValue[index];

				});
			}
			let value = "";
			mainIndicatorsWithKeys.forEach((val) => {
				if (Object.keys(val).toString() === window.currentMainIndicator) {
					value = Object.values(val).toString();
					updateIndicator(value, window.currentMainIndicator);
				}
			});

			subIndicatorsWithKeys.forEach((val) => {
				if (Object.keys(val).toString() === window.currentSubIndicator) {
					value = Object.values(val).toString();
					updateIndicator(value, window.currentSubIndicator);
				}
			});


		}
		let main = [];
		let sub = [];
		// show indicator value
		function processIndicatorValue() {
			const iframe = document.querySelector("iframe").contentDocument;
			main = allIndicators[window.currentMainIndicator].filter((value) => {
				return value != 0;
			});

			sub = allIndicators[window.currentSubIndicator].filter((value) => {
				return value != 0;
			});


			currentMainIndicatorValue = main.toString();
			const currentMainIndicatorDiv = iframe.querySelector('html body .chart-markup-table tr:first-child td:nth-child(2) div .topAppend #topIndicator #currentIndicator');
			currentMainIndicatorDiv.textContent = window.currentMainIndicator;
			const indicatorMain = iframe.querySelector('html body .chart-markup-table tr:first-child td:nth-child(2) div .topAppend #topIndicatorInfo');
			indicatorMain.textContent = main.length > 0 ? `(${currentMainIndicatorValue})` : "";

			currentSubIndicatorValue = sub.toString();
			const currentSubIndicatorDiv = iframe.querySelector('html body .chart-markup-table > tr:nth-child(3) .bottomAppend #bottomIndicator #currentBottomIndicator');
			currentSubIndicatorDiv.textContent = window.currentSubIndicator;
			const indicatorSub = iframe.querySelector('html body .chart-markup-table > tr:nth-child(3) .bottomAppend #bottomIndicatorInfo');
			indicatorSub.textContent = sub.length > 0 ? `(${currentSubIndicatorValue})` : "";
		}

		// update indicator value
		function updateIndicator(indicator, shortValue) {
			if (mainShortIndicators.includes(shortValue)) {
				window.currentMainIndicator = shortValue;
			}

			if (subShortIndicators.includes(shortValue)) {
				window.currentSubIndicator = shortValue;
			}
			let cratedId = null;
			window.tvWidget
				.chart().getAllStudies()
				.forEach(({ id, name }) => {
					if (((mainIndicators.includes(indicator) && mainIndicators.includes(name)))) {
						window.tvWidget.chart().removeEntity(id);
					}

					if (subIndicators.includes(indicator) && subIndicators.includes(name) && indicator == name) {
						createdIndicator = true;
						cratedId = id;
					}
					else if (subIndicators.includes(indicator) && subIndicators.includes(name) && indicator != name) {
						createdIndicator = false;
						window.tvWidget.chart().removeEntity(id);
					}
				});


			let style = {};
			let length = 0;

			if (moreThanOneLine.includes(indicator)) {
				length = 2;
			}

			for (let i = 0; i <= length; i++) {
				let value = [];
				switch (shortValue) {
					case 'MA':
						value = { length: allIndicators[shortValue][i], smoothingLength: 0 };
						style = { "plot.color": maIndicatorColors[i] };
						break;
					case 'EMA':
						value = { length: allIndicators[shortValue][i], smoothingLength: 0 };
						style = { "plot.color": maIndicatorColors[i] };
						break;
					case 'MACD':
						value = { in_0: allIndicators[shortValue][0], in_1: allIndicators[shortValue][1], in_3: 'close', in_2: allIndicators[shortValue][2] };
						break;
					case 'BOLL':
						value = { in_0: allIndicators[shortValue][0], in_1: allIndicators[shortValue][1] };
						break;
					case 'MIKE':
						value = { in_0: allIndicators[shortValue][0] };
						break;
					case 'BBI':
						value = { BBI: allIndicators[shortValue][0] };
						break;
					case 'SAR':
						value = { length: allIndicators[shortValue][i], smoothingLength: 0 };
						break;
					case 'KDJ':
						value = { in_0: 14, in_1: allIndicators[shortValue][0], in_2: allIndicators[shortValue][1], in_3: allIndicators[shortValue][2] };
						break;
					case 'RSI':
						value = { length: allIndicators[shortValue][0], smoothingLength: allIndicators[shortValue][0] };
						style = { 'Hlines Background.visible': 0, 'UpperLimit.visible': 0, 'MiddleLimit.visible': 0, 'LowerLimit.visible': 0 }
						break;
					case 'WR':
						value = { in_0: allIndicators[shortValue][0] };
						style = { 'Hlines Background.visible': 0, 'UpperLimit.visible': 0, 'LowerLimit.visible': 0 }
						break;
					case 'CCI':
						value = { in_0: allIndicators[shortValue][0] };
						style = { 'Hlines Background.visible': 0, 'UpperLimit.visible': 0, 'LowerLimit.visible': 0 }
						break;
					case 'KD':
						value = { in_0: allIndicators[shortValue][0], in_1: allIndicators[shortValue][1], in_2: allIndicators[shortValue][2] };
						style = { 'Hlines Background.visible': 0, 'UpperLimit.visible': 0, 'LowerLimit.visible': 0 }
						break;
					case 'DMI':
						value = { in_0: allIndicators[shortValue][0], in_1: allIndicators[shortValue][1] };
						style = { 'Hlines Background.visible': 0, 'UpperLimit.visible': 0, 'LowerLimit.visible': 0 }
						break;
				}

				var subIn = subIndicatorsWithKeys.filter(function (o) {
					return o.hasOwnProperty(shortValue);
				}).length > 0;
				if (!createdIndicator && subIn) {
					window.tvWidget.chart().createStudy(indicator, false, false, value, style);
				}
				if (createdIndicator && cratedId && subIn) {
					const res = [];
					const keys = Object.keys(value);
					keys.forEach(key => {
						let v = key;
						let temp = {}
						temp['id'] = key;
						temp['value'] = value[key];
						res.push(temp);
					});

					window.tvWidget.chart().getStudyById(cratedId).setInputValues(res);
				}

				if (!subIn) {
					window.tvWidget.chart().createStudy(indicator, false, false, value, style);
				}
			}


			setTimeout(() => processIndicatorValue(), 0);
		}

		function storeOrders(allOrders = []) {
			const iframe = document.querySelector("iframe").contentDocument;
			window.closeAllActiveLine();
			const topPriceLineAppend = iframe.querySelector('#topPriceLineAppend');
			if (topPriceLineAppend) topPriceLineAppend.style.display = 'none';
			createdOrder?.remove();
			createdOrder = null;
			if (allOrders.length > 0) {
				orders = allOrders;
				activeOrders = orders.filter((value) => value.status == 1);
				const priceLineDiv = document.createElement('div');
				priceLineDiv.id = 'topPriceLineAppend';
				closeUnactivePriceLine();
				let firstElement = activeOrders.filter((value) => value.status == 1);

				if (firstElement.length > 0) {
					let orderSort = activeOrders.indexOf(firstElement[0]);
					priceLineDiv.innerHTML = `
							<div id="topPriceLine">
								<div class="arrowContainer leftRightArrowContainer"  id="leftArrow">
									<img src="${window.leftArrow}" class="priceLineArrow left"/>
								</div>
								<div id="priceLineContent" data-order=${orderSort}>
									<div class="data" id="ordernumber">#${activeOrders[orderSort].ordernumber}</div>
									<div class="data" id="volume">${activeOrders[orderSort].volume} Lots</div>
									<div class="data" id="price">${activeOrders[orderSort].price}</div>
									<div class="data" id="type">${activeOrders[orderSort].typeName}</div>
								</div>
								<div class="arrowContainer leftRightArrowContainer"  id="rightArrow">
									<img src="${window.rightArrow}" class="priceLineArrow right"/>
								</div>
							</div>	
					`;


					const targetElement = iframe.querySelector('html body .chart-markup-table tr:first-child > td:nth-child(2) > div');
					targetElement.appendChild(priceLineDiv);
					createPriceLineContent(orderSort, "slide-out 0.5s forwards", true);
				}
			}
		}

		function closeUnactivePriceLine() {
			let unactiveLines = orders.filter((value) => value.status == 0);
			unactiveLines.forEach((item) => {
				item.type != 'ask' && item.type != 'bid' ? positionLine[item.type]?.remove() : window.tvWidget.chart().removeEntity(positionLine[item.type]);
				positionLine[item.type] = null;
				item.type === 'ask' ? showAskPrice = false : null;
				item.type === 'bid' ? showBidPrice = false : null;
			})
		}
		
		function createPriceLineContent(orderSort, animation, firstGenerate = false, slide = false) {
			const iframe = document.querySelector("iframe").contentDocument;
			const priceLineContent = iframe.getElementById('priceLineContent');

			if (activeOrders[orderSort].status == 1) {
				if (activeOrders[orderSort].typeName !== 'Ask' && activeOrders[orderSort].typeName !== 'Bid') {
					priceLineContent.innerHTML = `
							<div class="data" id="ordernumber">#${activeOrders[orderSort].ordernumber}</div>
							<div class="data" id="volume">${activeOrders[orderSort].volume} Lots</div>
							<div class="data" id="price">${activeOrders[orderSort].price} </div>
							<div class="data" id="type">${activeOrders[orderSort].typeName}</div>
					`;
				} else {
					priceLineContent.innerHTML = `
							<div class="data" id="price">${activeOrders[orderSort].typeName === 'Ask' ? askPrice : bidPrice} </div>
							<div class="data" id="type">${activeOrders[orderSort].typeName}</div>
					`;
				}
				priceLineContent.dataset.order = orderSort;
				priceLineContent.style.animation = animation;
				let data = [];

				let orderPriceLines = activeOrders.filter((order) => order.ordernumber != '' && order.ordernumber == activeOrders[orderSort].ordernumber && order.type != activeOrders[orderSort].type && order.status == 1);

				let askBidPriceLines = activeOrders.filter((order) => order.ordernumber == '' && order.type != activeOrders[orderSort].type && order.status == 1);

				let lastestPrice = activeOrders[orderSort].price;
				let text = `#${activeOrders[orderSort].ordernumber} ${activeOrders[orderSort].volume} Lots ${lastestPrice}`;

				if (activeOrders[orderSort].typeName == 'Ask' || activeOrders[orderSort].typeName == 'Bid') {
					lastestPrice = activeOrders[orderSort].typeName == 'Ask' ? askPrice : bidPrice;
					orderPriceLines = activeOrders.filter((order) => order.ordernumber == positionLine.open?.ordernumber)[0];
					orderPriceLines = orderPriceLines ? activeOrders.filter((order) => order.ordernumber == orderPriceLines.ordernumber) : null;
					text = "";
				}

				orderPriceLines?.forEach((value) => {
					let recordText = `#${value.ordernumber} ${value.volume} Lots ${value.price}`;
					data.push({ text: recordText, price: value.price, type: value.typeName, 
								status: value.status, ordernumber: value.ordernumber,
								tradetype: value.tradetype });
				});


				askBidPriceLines?.forEach((value) => {
					let price = value.typeName == 'Ask' ? askPrice : bidPrice;
					data.push({ text: "", price: price, type: value.typeName, status: value.status });
				});

				let latestRecord = { text: text, price: lastestPrice, type: activeOrders[orderSort].typeName, status: activeOrders[orderSort].status, ordernumber: activeOrders[orderSort].ordernumber, currentActive: firstGenerate ? 0 : 1 ,
								tradetype: activeOrders[orderSort].tradetype}
				
				orders.filter((value) => {
					if(value.type === 'open'){
						openPrice = value.price;
					}
				})				
				data.push(latestRecord);
				(firstGenerate || slide) && storePriceLines(data);
				!firstGenerate && updatePriceLines(data);
			}
		}
	
		function storePriceLines(allPriceLines) {
			removeAllPriceLine();
			allPriceLines?.forEach((value, index) => {
				const type = value.type;
				const tradetype = value.tradetype;
				const text = value.text;
				const price = value.price;
				const status = value.status;
				
				if (status === 1) {
					if (type == 'Ask') {
						positionLine.ask ? removePriceLine('ask') : null;
						showAskPrice = true;
					} else if (type == 'Bid') {
						positionLine.bid ? removePriceLine('bid') : null;
						showBidPrice = true;
					}
					else if (type == 'TP') {
						let priceLine = generateOrderLine(price, text, type, tradetype, value.ordernumber, value.currentActive , true);
				
						positionLine.profit = priceLine;
						positionLine.profit.ordernumber = value.ordernumber;
						positionLine.profit.type = value.type;
					}
					else if (type == 'SL') {
						let priceLine = generateOrderLine(price, text, type, tradetype, value.ordernumber, value.currentActive , true);
						
						positionLine.loss = priceLine;
						positionLine.loss.ordernumber = value.ordernumber;
						positionLine.loss.type = value.type;

					} else if (type == 'Sell' || type == 'Buy') {
						let priceLine = generateOrderLine(price, text, type, tradetype, value.ordernumber, value.currentActive);

						positionLine.open = priceLine;
						positionLine.open.ordernumber = value.ordernumber;
						positionLine.open.type = value.type;
					}

				} else {
					type === 'Ask' ? showAskPrice = false : null;
					type === 'Bid' ? showBidPrice = false : null;
					removePriceLine(type.toLowerCase());
				}

			});

		}


		function updatePriceLines(allPriceLines) {
			window.closeAllActiveLine();
			allPriceLines?.forEach((value, index) => {
				const type = value.type;
				const text = value.text;
				const price = value.price;
				const status = value.status;

				if (status === 1) {
					if (type == 'Ask') {
						positionLine.ask ? removePriceLine('ask') : null;
						showAskPrice = true;
					} else if (type == 'Bid') {
						positionLine.bid ? removePriceLine('bid') : null;
						showBidPrice = true;
					}
					else if (type == 'TP' && value.currentActive == 1) {
						positionLine?.profit?.setBodyTextColor("#fff")
							.setBodyBorderColor(positionLine.profit.getLineColor())
							.setBodyBackgroundColor(positionLine.profit.getLineColor())
							.setQuantityBackgroundColor(positionLine.profit.getLineColor())
							.setQuantityBorderColor(positionLine.profit.getLineColor())
							.setCancelButtonIconColor("#000")
							.setCancelButtonBackgroundColor(positionLine.profit.getLineColor())
							.setCancelButtonBorderColor(positionLine.profit.getLineColor())
						positionLine.profit.ordernumber = value.ordernumber;
						positionLine.profit.type = value.type;
					}
					else if (type == 'SL' && value.currentActive == 1) {
						positionLine?.loss?.setBodyTextColor("#fff")
							.setBodyBorderColor(positionLine.loss.getLineColor())
							.setBodyBackgroundColor(positionLine.loss.getLineColor())
							.setQuantityBackgroundColor(positionLine.loss.getLineColor())
							.setQuantityBorderColor(positionLine.loss.getLineColor())
							.setCancelButtonIconColor("#000")
							.setCancelButtonBackgroundColor(positionLine.loss.getLineColor())
							.setCancelButtonBorderColor(positionLine.loss.getLineColor())
						positionLine.loss.ordernumber = value.ordernumber;
						positionLine.loss.type = value.type;

					} else if ((type == 'Sell' || type == 'Buy') && value.currentActive == 1) {
						positionLine?.open?.setBodyTextColor("#fff")
							.setBodyBorderColor(positionLine.open?.getLineColor())
							.setBodyBackgroundColor(positionLine.open?.getLineColor())
							.setQuantityBackgroundColor(positionLine.open?.getLineColor())
							.setQuantityBorderColor(positionLine.open?.getLineColor())
							.setCancelButtonIconColor("#000")
							.setCancelButtonBackgroundColor(positionLine.open?.getLineColor())
							.setCancelButtonBorderColor(positionLine.open?.getLineColor())
						positionLine.open.ordernumber = value.ordernumber;
						positionLine.open.type = value.type;

					}

				} else {
					type === 'Ask' ? showAskPrice = false : null;
					type === 'Bid' ? showBidPrice = false : null;
					removePriceLine(type.toLowerCase());
				}

			});
		}

		// function generateSpacesBasedOnStringLength(str, removeLength = 0) {
		function generateSpacesBasedOnStringLength(length) {
			const spaces = ' '.repeat(length);

			return spaces;
		}


		function countZeros(number) {
			// Convert number to string
			let numString = number.toString();
			
			// Count the number of zeros
			let zeroCount = 0;
			for (let i = 0; i < numString.length; i++) {
				if (numString[i] === '0') {
					zeroCount++;
				}
			}
			
			return zeroCount;
		}

		let timeoutId;
	
		function createOrderEditLine(orderPrice, currentPrice, priceLine, currentType, tradetype, text) {
			if(!createdOrder){
				createdOrder = window.tvWidget.chart().createOrderLine()
					.setEditable(false)
					.setExtendLeft(false)
					.setLineLength(70)
					.setPrice(orderPrice)
					.setText(`${text} ${currentType}`)
					.setBodyTextColor("#000")
					.setQuantity("")
					.setLineColor(priceLine.getLineColor())
					.setBodyBorderColor("#fff")
					.setBodyBackgroundColor("#fff");
					if(currentType != 'Sell' && currentType != 'Buy'){
						createdOrder
							.setCancelButtonBackgroundColor("#fff")
							.setCancelButtonBorderColor("#fff")
							.setCancelButtonIconColor("#000")
							.onCancel("onClose called", function (text) {
								let shortType = currentType == "SL" ? 'loss' : 'profit'
								updatePriceLineStatus(shortType, 'delete', 0, positionLine[shortType]?.ordernumber);
							});
					}
					createdOrder.ordernumber = priceLine.ordernumber
					delete priceLine._onCancelCallback;
					delete priceLine._onCancelData;
		
			}
			
			if(tradetype === 'Sell'){
				if(currentPrice < askPrice){ // 止盈
					priceLine
						.setLineColor(priceLineInfo.TP.activeColor)
						.setBodyBorderColor(priceLineInfo.TP.activeColor)
						.setBodyBackgroundColor(priceLineInfo.TP.activeColor)
						.setBodyTextColor("#fff")
						.setText(`${generateSpacesBasedOnStringLength(2)}TP ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(14)}- ${currency}${generateSpacesBasedOnStringLength(4)}`)

				}else{ // 止损
					priceLine.setLineColor(priceLineInfo.SL.activeColor)
						.setBodyBorderColor(priceLineInfo.SL.activeColor)
						.setBodyBackgroundColor(priceLineInfo.SL.activeColor)
						.setBodyTextColor("#fff")
						.setText(`${generateSpacesBasedOnStringLength(2)}SL ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(14)}- ${currency}${generateSpacesBasedOnStringLength(4)}`)
				}
			}else{
				if(currentPrice > bidPrice){ // 止盈
					priceLine.setLineColor(priceLineInfo.TP.activeColor)
						.setBodyBorderColor(priceLineInfo.TP.activeColor)
						.setBodyBackgroundColor(priceLineInfo.TP.activeColor)
						.setBodyTextColor("#fff")
						.setText(`${generateSpacesBasedOnStringLength(2)}TP ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(14)}- ${currency}${generateSpacesBasedOnStringLength(4)}`)
				}else{ // 止损
					priceLine.setLineColor(priceLineInfo.SL.activeColor)
					.setBodyBorderColor(priceLineInfo.SL.activeColor)
					.setBodyBackgroundColor(priceLineInfo.SL.activeColor)
					.setBodyTextColor("#fff")
					.setText(`${generateSpacesBasedOnStringLength(2)}SL ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(14)}- ${currency}${generateSpacesBasedOnStringLength(4)}`)
				}
			}	

			if (timeoutId) {
				clearTimeout(timeoutId);
			}

			timeoutId = setTimeout(() => {
				getProfitLoss(currentPrice, priceLine.ordernumber).then((cal) => { 
					if(tradetype === 'Sell'){
						sign = cal < 0 ? "" : "+";
						let text = `${sign}${cal} ${currency}`;
						if(currentPrice < askPrice){ // 止盈
							priceLine.setBodyTextColor("#fff").setText(`${generateSpacesBasedOnStringLength(2)}TP ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(4)}${text}${generateSpacesBasedOnStringLength(4)}`);
						}else{ // 止损
							priceLine.setBodyTextColor("#fff").setText(`${generateSpacesBasedOnStringLength(2)}SL ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(4)}${text}${generateSpacesBasedOnStringLength(4)}`);
						}
					}else{
						sign = cal < 0 ? "" : "+";
						let text = `${sign}${cal} ${currency}`;
						if(currentPrice > bidPrice){ // 止盈
							priceLine.setBodyTextColor("#fff").setText(`${generateSpacesBasedOnStringLength(2)}TP ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(4)}${text}${generateSpacesBasedOnStringLength(4)}`);
						}else{ // 止损
							priceLine.setBodyTextColor("#fff").setText(`${generateSpacesBasedOnStringLength(2)}SL ${generateSpacesBasedOnStringLength(2)} ${currentPrice.toFixed(countZeros(Number(priceScale)))}${generateSpacesBasedOnStringLength(4)}${text}${generateSpacesBasedOnStringLength(4)}`);
						}
					}	
				});
			}, 100)
			
		}
		
		function generateOrderLine(price, text, type, tradetype, ordernumber, active = 0, closable = false) {
			let orderLine = window.tvWidget.chart().createOrderLine()
						.setExtendLeft(false)
						.setLineLength(70)
						.setPrice(price)
						.setText(text + ' ' + type)
						.setBodyTextColor('#000')
						.setQuantity("")
						.setLineColor(priceLineInfo[type]?.activeColor)
						.setBodyBorderColor("#fff")
						.setBodyBackgroundColor("#fff")
						.setQuantityBackgroundColor("#fff")
						.setQuantityBorderColor("#fff")
						.onMove(function() {
							updatePriceLineStatus(
								"open",
								"move",
								this.getPrice(),
								ordernumber
							);
						})
						.onMoving(function() {
							const topPriceLineAppend = document.querySelector("iframe").contentDocument.querySelector("#topPriceLineAppend");
							if (topPriceLineAppend) topPriceLineAppend.style.display = "none";
							if(document.querySelector("iframe").contentDocument.getElementById("bottomToastClose").getAttribute('data-clicked') === "false"){
								document.querySelector("iframe").contentDocument.getElementById("bottomToast").style.display = 'none';
								document.querySelector("iframe").contentDocument.getElementById("bottomToastClose").setAttribute('data-clicked', 'true');
							}
							this.setCancelButtonIconColor(orderLine.getLineColor())
									.setCancelButtonBackgroundColor(orderLine.getLineColor())
									.setCancelButtonBorderColor(orderLine.getLineColor())
									.setBodyBorderColor(orderLine.getLineColor())
									
							createOrderEditLine(price, this.getPrice(), this, type, tradetype, text);
						});
		
			if(type == "SL" || type == "TP"){
				orderLine.setCancelButtonBackgroundColor("#fff")
					.setCancelButtonBorderColor("#fff")
					.setCancelButtonIconColor("#000")
					.onCancel("onClose called", function (text) {
						let shortType = type == "SL" ? 'loss' : 'profit'
						updatePriceLineStatus(shortType, 'delete', 0, ordernumber);
					});
			}
			if(type == "Sell" || type == "Buy"){
				orderLine.setBodyBorderColor(priceLineInfo[type]?.activeColor)
			}

			if(active == 1){
				orderLine
					.setBodyTextColor("#fff")
					.setBodyBorderColor(orderLine.getLineColor())
					.setBodyBackgroundColor(orderLine.getLineColor())
					.setQuantityBackgroundColor(orderLine.getLineColor())
					.setQuantityBorderColor(orderLine.getLineColor())
					.setCancelButtonIconColor("#000")
					.setCancelButtonBackgroundColor(orderLine.getLineColor())
					.setCancelButtonBorderColor(orderLine.getLineColor())
			}

			
						
			return orderLine;
		}
		// remove ask/bid... price line
		function removePriceLine(type) {
			Object.entries(positionLine).forEach(([key, value]) => {
				if (key === type && value !== null) {
					key != 'ask' && key != 'bid' ? value.remove() : window.tvWidget.chart().removeEntity(value);
					positionLine[key] = null;
					return 0;
				}
			});
		}



		// remove all ask/bid... price line
		function removeAllPriceLine() {
			removePriceLine('ask');
			removePriceLine('bid');
			removePriceLine('profit');
			removePriceLine('loss');
			removePriceLine('open');
		}



		// drawing like brush etc.. need to close
		function closeDrawingTools() {
			drawingTools('cursor');
		}

		function appendCloseDrawing() {
			let targetElementTop = document.querySelector("html body #tv_chart_container"); // Find the target element inside the iframe document
			let topHTMLString = `<div class='topAppendDrawing' id='topAppendDrawing' onclick='removeDrawingClose()'>
									<div class='closeImg'>
										<img src="close.webp"/>
									</div>
									<span class='exit'>Exit</span>
								</div>`;
			let targetAppendDrawing = targetElementTop.querySelector("div#appendDrawing");
			if (targetAppendDrawing !== null) {
				targetElementTop.removeChild(targetAppendDrawing);
			}
			let topDivElement = document.createElement('div');
			topDivElement.id = 'appendDrawing';
			topDivElement.innerHTML = topHTMLString;
			let topExternalScript = document.createElement('script');
			topExternalScript.textContent = `
					function removeDrawingClose() {
						let element = document.querySelector('#topAppendDrawing');
						element.remove('active');
						closeDrawingTools();
						toast('Closed drawing tool');
					};
				`;

			targetElementTop.appendChild(topDivElement); // Append the created HTML to the target element
			targetElementTop.appendChild(topExternalScript);
		};


		function updateTickChartRate(value) {
			let tickChartOverride = {
				'mainSeriesProperties.areaStyle.color1': value >= 0 ? 'rgba(64, 211, 179, 0.28)' : 'rgba(244, 64, 64, 0.28)',
				'mainSeriesProperties.areaStyle.color2': value >= 0 ? '#40d3b3' : '#F44040',
				'mainSeriesProperties.areaStyle.linecolor': value >= 0 ? '#40d3b3' : '#F44040',
			}

			window.tvWidget.applyOverrides(tickChartOverride);
		}

		function setTheme(value) {
			overrides = {
				'paneProperties.backgroundType': 'solid', // or 'gradient'
				'paneProperties.background': "rgba(26, 29, 32, 1)",
				'paneProperties.horzGridProperties.color': 'rgba(255, 255, 255, 0.1)',
				'paneProperties.horzGridProperties.style': 2,
				'paneProperties.vertGridProperties.color': 'rgba(26, 29, 32, 1)',
				'mainSeriesProperties.candleStyle.borderUpColor': '#40D3B3',
				'mainSeriesProperties.candleStyle.borderDownColor': '#E24F1E',
				'mainSeriesProperties.candleStyle.upColor': '#40D3B3',
				'mainSeriesProperties.candleStyle.downColor': '#E24F1E',
				'mainSeriesProperties.hollowCandleStyle.borderUpColor': '#40D3B3',
				'mainSeriesProperties.hollowCandleStyle.borderDownColor': '#E24F1E',
				'mainSeriesProperties.hollowCandleStyle.upColor': '#40D3B3',
				'mainSeriesProperties.hollowCandleStyle.downColor': '#E24F1E',
				'mainSeriesProperties.barStyle.upColor': '#40D3B3',
				'mainSeriesProperties.barStyle.downColor': '#E24F1E',
				'scalesProperties.lineColor': 'rgba(26, 29, 32, 1)',
				'scalesProperties.textColor': '#586465',
				'paneProperties.crossHairProperties.color': "#fff",

			};
			loadingScreen = { backgroundColor: "rgba(26, 29, 32, 1)", foregroundColor: 'rgba(26, 29, 32, 1)' }
			if (value == 'Light') {
				overrides = {
					'paneProperties.background': "#FFF",
					'paneProperties.backgroundType': 'solid', // or 'gradient'
					'mainSeriesProperties.candleStyle.upColor': '#40D3B3',
					'mainSeriesProperties.candleStyle.downColor': '#E24F1E',
					'paneProperties.vertGridProperties.color': '#F3F5F7',
					'paneProperties.horzGridProperties.color': 'rgb(134,134,134,0.2)',
					'paneProperties.horzGridProperties.style': 1,
					'mainSeriesProperties.candleStyle.borderUpColor': '#40D3B3',
					'mainSeriesProperties.candleStyle.borderDownColor': '#E24F1E',
					'mainSeriesProperties.candleStyle.upColor': '#40D3B3',
					'mainSeriesProperties.candleStyle.downColor': '#E24F1E',
					'mainSeriesProperties.hollowCandleStyle.borderUpColor': '#40D3B3',
					'mainSeriesProperties.hollowCandleStyle.borderDownColor': '#E24F1E',
					'mainSeriesProperties.hollowCandleStyle.upColor': '#40D3B3',
					'mainSeriesProperties.hollowCandleStyle.downColor': '#E24F1E',
					'mainSeriesProperties.barStyle.upColor': '#40D3B3',
					'mainSeriesProperties.barStyle.downColor': '#E24F1E',
					'scalesProperties.lineColor': '#F3F5F7',
					'scalesProperties.textColor': '#979898',
					'paneProperties.crossHairProperties.color': "#818181",
				};
				loadingScreen = { backgroundColor: "#F3F5F7", foregroundColor: '#F3F5F7' }
			}
		}

	</script>
	<link rel="stylesheet" href="./css/index.css">
	<link rel="stylesheet" href="./css/skeleton.css">
</head>

<body>
	<div id="tv_chart_container">
	</div>

	<div id="loading-animation">
		<div id="bars">
			<div class="skeleton height60-per">
				<div class="skeleton-box-2 bar" style="height:10rem;"></div>
				<div class="wick-top top-0-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-3">
				<div class="skeleton-box-2 bar" style="height:1rem;"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-2 height65-per">
				<div class="skeleton-box-2 bar" style="height:1rem;"></div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-3 height65-per">
				<div class="skeleton-box-2 bar" style="height:4rem;"></div>
				<div class="wick-top top-2-5"></div>
			</div>

			<div class="skeleton pt-3 height65-per">
				<div class="skeleton-box-2 bar" style="height:3rem;"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-3">
				<div class="skeleton-box-2 bar" style="height:5rem;"></div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-2">
				<div class="skeleton-box-2 bar" style="height:5rem;"></div>
				<div class="wick-top top-1"></div>
			</div>
			<div class="skeleton">
				<div class="skeleton-box-2 bar" style="height:10rem;">
				</div>
				<div class="wick-top top-0-5"></div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:9rem;">
				</div>
				<div class="wick-top top-3-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:2rem;"></div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-2">
				<div class="skeleton-box-2 bar" style="height:6rem;">
				</div>
				<div class="wick-top top-1"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton height70-per">
				<div class="skeleton-box-2 bar" style="height: 5rem;">
				</div>
				<div class="wick-top top-0-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton">
				<div class="skeleton-box-2 bar" style="height:4rem;">
				</div>
				<div class="wick-top top-0-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:7rem;">
				</div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:8rem;">
				</div>
				<div class="wick-top top-3-5"></div>
			</div>
			<div class="skeleton pt-3 height70-per">
				<div class="skeleton-box-2 bar" style="height:8rem;">
				</div>
				<div class="wick-top top-2-5"></div>
			</div>
			<div class="skeleton pt-2">
				<div class="skeleton-box-2 bar" style="height:4rem;">
				</div>
				<div class="wick-top top-1"></div>
			</div>
			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:1rem;">
				</div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-3">
				<div class="skeleton-box-2 bar" style="height:2rem;">
				</div>
				<div class="wick-top top-2-5"></div>
			</div>
			<div class="skeleton">
				<div class="skeleton-box-2 bar" style="height:2rem;">
				</div>
				<div class="wick-bottom"></div>
			</div>

			<div class="skeleton pt-4">
				<div class="skeleton-box-2 bar" style="height:4rem;">
				</div>
				<div class="wick-top top-3-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-2">
				<div class="skeleton-box-2 bar" style="height:4rem;">
				</div>
				<div class="wick-top"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-3">
				<div class="skeleton-box-2 bar" style="height:9rem;">
				</div>
				<div class="wick-top top-2-5"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-2">
				<div class="skeleton-box-2 bar" style="height:1rem;"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-3">
				<div class="skeleton-box-2 bar" style="height:1rem;"></div>
				<div class="wick-bottom"></div>
			</div>
			<div class="skeleton pt-5 height56-per">
				<div class="skeleton-box-2 bar" style="height:3rem;"></div>
				<div class="wick-top top-4-5"></div>
			</div>
			<div class="skeleton height70-per">
				<div class="skeleton-box-2 bar" style="height:3rem;"></div>
				<div class="wick-bottom"></div>
			</div>
		</div>

		<div class="right-axis">
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
			<div class="skeleton-box"></div> 
		</div>
	</div>
</body>

</html>