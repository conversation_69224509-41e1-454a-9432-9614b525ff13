package com.kit;

import com.kit.extra.ZLExtraEntry;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.common.greendao.dbUtils.UserDataUtil;
import cn.com.vau.common.storage.SpManager;
import cn.com.vau.kit.LibraryInfo;
import cn.com.vau.kit.biz.APiFilterBean;
import cn.com.vau.kit.biz.APiFilterConfigBean;
import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.biz.AppInfoBean;
import cn.com.vau.kit.biz.ButtonConfigBean;
import cn.com.vau.kit.biz.DetailConfigBean;
import cn.com.vau.kit.biz.IExtraBean;
import cn.com.vau.kit.biz.SwitchConfigBean;
import cn.com.vau.page.html.HtmlActivity;
import okhttp3.Headers;
import okhttp3.ResponseBody;


public class KitAppCallBackImp implements AppCallBackItf {
    public static final String TAG = "KitAppCallBackImp";

    @Override
    public AppInfoBean getAppInfo() {
        AppInfoBean appInfoBean = new AppInfoBean();
        if (UserDataUtil.isLogin()) {
            appInfoBean.setUserId(SpManager.getCrmUserId(""));
            appInfoBean.setPhoneNumber(UserDataUtil.userTel());
            appInfoBean.setEmail(UserDataUtil.email());
            appInfoBean.setAreaCode(UserDataUtil.areaCode());
        } else {
            appInfoBean.setUserId("未登录");
            appInfoBean.setPhoneNumber("未登录");
            appInfoBean.setEmail("未登录");
            appInfoBean.setAreaCode("未登录");
        }

        return appInfoBean;
    }



    /**
     * 业务层webviewActivity,用于加载h5的界面
     */
    @Override
    public List<Class<?>> getWebViewActivityList() {
        final List<Class<?>> result = new ArrayList<>();
        result.add(HtmlActivity.class);
        return result;
    }


    @Override
    public List<ButtonConfigBean> getButtonConfigList() {

        List<ButtonConfigBean> result = new ArrayList<>();



        return result;
    }

    @Override
    public List<SwitchConfigBean> getSwitchConfigList() {
        ArrayList<SwitchConfigBean> list = new ArrayList();
        return list;
    }


    private List<DetailConfigBean> detailConfigList;

    @Override
    public List<DetailConfigBean> getDetailConfigList() {
        if (detailConfigList == null) {
            detailConfigList = new ArrayList<>();
            detailConfigList.add(getDetailConfigBean("三方库信息", getLibraryInfo()));
        }
        return detailConfigList;
    }

    @Override
    public APiFilterConfigBean getGroupApiFilterList() {
        APiFilterConfigBean configBean = new APiFilterConfigBean();
        List<APiFilterBean> result = new ArrayList<>();
        result.add(getBusinessFilter());
        configBean.setaPiFilterBeanList(result);
        configBean.setDefaultSelectIndex(result.size() - 1);
        return configBean;
    }


    @Override
    public List<IExtraBean> getExtraConfigList() {
        List<IExtraBean> list = new ArrayList<>();
        list.add(new ZLExtraEntry());
        return list;
    }


    private APiFilterBean getBusinessFilter() {
        APiFilterBean filterBean = new APiFilterBean();
        filterBean.setGroupName("查看业务接口");

        List<APiFilterBean.APiUrlBean> result = new ArrayList<>();

        APiFilterBean.APiUrlBean statisticBean = new APiFilterBean.APiUrlBean();
        statisticBean.setUnLikeUrl("%pay/finish%");
        result.add(statisticBean);


        filterBean.setaPiUrlList(result);
        return filterBean;
    }



    @Override
    public String decryHttpPostParam(String encryText, String url) {
        return encryText;
    }

    @Override
    public String decryHttpGetParams(String encryText, String url) {
        return encryText;
    }


    @Override
    public boolean isInterceptRequest(Headers header, ResponseBody responseBody, String url) {
        return false;
    }


    private DetailConfigBean getDetailConfigBean(String name, String info) {
        DetailConfigBean detailConfigBean = new DetailConfigBean();
        detailConfigBean.setName(name);
        detailConfigBean.setDetailText(info);
        return detailConfigBean;
    }


    private String getLibraryInfo() {
        String info = LibraryInfo.libraryInfo;

        try {
            JSONArray jsonObject = new JSONArray();
            String[] split = info.split("###");
            for (String text : split) {
                jsonObject.put(text);
            }
            return jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


}
