package com.kit.extra

import android.app.Activity
import android.content.Intent
import cn.com.vau.kit.biz.IExtraBean
import com.kit.ZLActivity


class ZLExtraEntry : IExtraBean {
    override fun getName(): String {
        return "Array"
    }

    override fun onClick(context: Activity) {
        val intent = Intent(context, ZLActivity::class.java)
        context.startActivity(intent)
//        ReverseOrderDialog.Builder(context).setMainOrderId("123").build().show()
    }


}