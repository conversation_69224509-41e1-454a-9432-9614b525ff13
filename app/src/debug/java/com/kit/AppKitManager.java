package com.kit;

import android.app.Application;
import android.util.Log;

import cn.com.vau.kit.Kit;
import okhttp3.Interceptor;


public class AppKitManager {

    private static final AppKitManager instance = new AppKitManager();

    public static AppKitManager getInstance() {
        return instance;
    }

    public void installKit(Application application) {
        Kit.getInstance().install(application, new KitAppCallBackImp());
    }

    public Interceptor getChuckInterceptor() {
        return Kit.getInstance().getChuckInterceptor();
    }

}