#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android无用资源扫描器 - 快速运行脚本
"""

import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🔍 Android无用资源扫描器")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = Path.cwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查是否为Android项目
    if not (current_dir / "app" / "build.gradle").exists() and \
       not (current_dir / "app" / "build.gradle.kts").exists():
        print("\n❌ 当前目录不是有效的Android项目")
        print("请在Android项目根目录下运行此脚本")
        print("项目根目录应包含 app/build.gradle 或 app/build.gradle.kts 文件")
        return 1
    
    print("✅ 检测到Android项目")
    
    # 导入并运行扫描器
    try:
        from android_unused_scanner import scan_android_project
        
        print("\n🚀 开始扫描...")
        print("💡 特性: 支持ViewBinding、DataBinding、Navigation组件")
        print("💡 已自动排除多语言资源文件")
        
        scan_android_project(str(current_dir))
        
        print("\n🎉 扫描完成！请查看生成的报告文件")
        
        return 0
        
    except ImportError:
        print("❌ 无法导入扫描器模块，请确保 android_unused_scanner.py 文件存在")
        return 1
    except KeyboardInterrupt:
        print("\n⚠️ 扫描被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 扫描过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
