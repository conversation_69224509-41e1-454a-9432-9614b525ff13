package com.github.tifezh.kchartlib.chart;

import static com.github.tifezh.kchartlib.chart.BaseKChartView.OnTakeProfitStopLossChanged.TYPE_SL;
import static com.github.tifezh.kchartlib.chart.BaseKChartView.OnTakeProfitStopLossChanged.TYPE_TP;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp0;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp0_5;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp1;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp10;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp12;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp18;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp2;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp20;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp22;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp29;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp3;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp36;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp4;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp5;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp6;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp7;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp7_5;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp8;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp88;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp9;
import static com.github.tifezh.kchartlib.utils.DpConstant.dp90;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.TypedArray;
import android.database.DataSetObserver;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.ViewParent;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.LruCache;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.GestureDetectorCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.OrderRecordEntity;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.draw.MainDraw;
import com.github.tifezh.kchartlib.chart.formatter.TimeFormatter;
import com.github.tifezh.kchartlib.chart.formatter.ValueFormatter;
import com.github.tifezh.kchartlib.chart.impl.IAdapter;
import com.github.tifezh.kchartlib.chart.impl.IChartDraw;
import com.github.tifezh.kchartlib.chart.impl.IDateTimeFormatter;
import com.github.tifezh.kchartlib.chart.impl.IKChartView;
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;
import com.github.tifezh.kchartlib.chart.impl.SimpleChartDraw;
import com.github.tifezh.kchartlib.data.OrderData;
import com.github.tifezh.kchartlib.helper.bean.KLineEntity;
import com.github.tifezh.kchartlib.helper.bean.KlineEnum;
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum;
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum;
import com.github.tifezh.kchartlib.helper.chart.KChartAdapter;
import com.github.tifezh.kchartlib.utils.DateUtil;
import com.github.tifezh.kchartlib.utils.ObjectPool;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.upex.common.utils.ExpandKt;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * k线图
 */
public abstract class BaseKChartView extends ScrollAndScaleView implements IKChartView,
        IKChartView.OnCrossMoveListener,
        IKChartView.OnScreenScrollChangeListener {
    public static final String TAG = "BaseKChartView";

    private Typeface font400;
    private Typeface font500;

    public Typeface getFont600() {
        return font600;
    }

    public void setFont600(Typeface font600) {
        this.font600 = font600;
    }

    public Typeface getFont400() {
        return font400;
    }

    public void setFont400(Typeface font400) {
        this.font400 = font400;
    }

    public Typeface getFont500() {
        return font500;
    }

    public void setFont500(Typeface font500) {
        this.font500 = font500;
    }

    private Typeface font600;

    @Nullable
    public Integer redColor = Color.RED;
    @Nullable
    public Integer greenColor = Color.GREEN;

    public Integer mRateColor = Color.RED;

    public Integer mFallColor = Color.GREEN;

    public int buyPriceTextBgColor = Color.WHITE;

    private float mTranslateX = Float.MIN_VALUE;

    private int mWidth = 0;
    private int mRightTextContentWidth = 0;
    private boolean isShowFullScreen = true;
    private boolean isShowLogo = true;
    private boolean isFullLeft = false;

    private int mTopPadding;
    private int mMainSubValueTop;
    private int mMainValueTextTop;//主指标文案y轴计算坐标
    private int mChildHeight;
    private int mBottomPadding;
    private int mBottomTimeHeigh;
    private int mValueHeigh;
    private int maxRectHeight = 0;

    private float mMainScaleY = 1;
    // ItemCount长度的 mPointWidth
    private float mDataLen = 0;

    public float mMainMaxValue = Float.MAX_VALUE;

    private float mMainMinValue = Float.MIN_VALUE;

    private int mStartIndex = 0;    // 屏幕起始数据Index

    private int mStopIndex = 0;     // 屏幕结束数据Index
    // Candle width + gap
    public float mPointWidth = 8;

    private float mCandleGapWidth = 0;

    private int mGridRows = 4;

    private int mGridColumns = 4;

    private Paint mGridPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    /**
     * 十字线文字画笔
     */
    private Paint mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mBuyTextPaint = new TextPaint(Paint.ANTI_ALIAS_FLAG);
    /**
     * 刻度文字画笔
     */
    private Paint mPriceTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mTimePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mSelectedValueTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mSelectedValueTextTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mBackgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mSelectedValueBackgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mSelectedLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPriceLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPricePopBorderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPricePopBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPricePopTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPriceBorderPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCurrentPriceTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mBuyPriceLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mSellPriceLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mBuyPriceTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mBuyPriceTextBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mInstantBuyingSellingTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mInstantBuyingSelling = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mCountDownTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mCountDownPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private Paint mTpSLLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mTextTpSlBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mScaleTextBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mTpSlTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mScaleTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint closeBitmapPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mStrokePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private Paint mTextBgPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint dividerPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private int mSelectedIndex = -1;

    private IChartDraw mMainDraw;

    private IAdapter mAdapter;

    private float mCandleWidth;

    private int digit = 2;

    //网格线的值距右边的间距
    private float KLINE_GRID_VALUE_MARGIN_RIGHT;
    private float KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER;
    private float KLINE_CURRENT_PRICE_VALUE_BORDER_RAD;
    private float KLINE_CURRENT_PRICE_LINE_WIDTH;
    private float KLINE_SELECT_LINE_WIDTH;

    private float TRADE_TYPE_ROUND_BG_PADDING;
    private static final boolean IS_SELECTED_LINE_DRAW_CLOSE_PRICE = false;
    private static int TIME_SHOW_INDEX_INTEGER = 9;

    private boolean isDrawCurrentPriceLine = false;
    //订单线
    private boolean isDrawPositionLine = false;
    private boolean showOrder = false;
    private boolean showKline = true;
    private boolean showCountDown = true;
    //买价线
    private boolean showBuyLine = false;
    //卖价线
    private boolean showSellLine = false;
    private boolean showInstantBuySell = false;
    //是否显示选中线
    protected boolean isShowCrossLine = false;
    //是否启用十字线
    protected boolean isEnableCrossLine = true;
    //止盈线
    private boolean isShowTpLine = false;
    //止损线
    private boolean isShowSlLine = false;

    //当前价
    @Nullable
    private Float currentPrice = null;
    private float buyPrice = 0f;
    private float sellPrice = 0f;

    //当前价点击回调
    private OnKChartViewCurrentPriceClickListener mCurrentPriceClickListener;
    private OnScreenScrollChangeListener mOnScreenScrollChangeListener;

    private int logo = R.drawable.img_kline_logo;

    public void setLogo(@DrawableRes int logo) {
        this.logo = logo;
    }

    private DataSetObserver mDataSetObserver = new DataSetObserver() {
        @Override
        public void onChanged() {
            mItemCount = getAdapter().getCount();
            Log.i("wj", "onChanged: itemCount = " + mItemCount);
            notifyChanged();
            mScroller.forceFinished(true);
        }

        @Override
        public void onInvalidated() {
            mItemCount = getAdapter().getCount();
            Log.i("wj", "onInvalidated: itemCount = " + mItemCount);
            notifyChangedInvalidated();
        }
    };

    //当前点的个数
    private int mItemCount;
    //每个点的x坐标
    private float[] mXs;
    private HashMap<KlineEnum, SimpleChartDraw> mChildDrawAllList = new HashMap<>();
    private HashMap<KlineEnum, IChartDraw> mMainChildDraws = new HashMap<>();

    private IValueFormatter mValueFormatter;
    private IDateTimeFormatter mDateTimeFormatter;
    private ValueAnimator mAnimator;
    private long mAnimationDuration = 500;
    private float mOverScrollRange = 0;
    private int mOverScrollRange1 = 0;
    private Rect mMainRect;
    private Rect mMainRect1;

    private List<SimpleChartDraw> mChildDrawSelectedList = new ArrayList<>();
    private IChartDraw mMainChildDraw;
    private List<IChartDraw> mMainChildDrawSelectedList = new ArrayList<>();

    private float mSelectedLineWidth = 2;
    private int mTotalWidth;
    private Bitmap mLogoBitmap;
    private RectF mCurrentPriceClickRect;
    private int minuterStartColor, minuteStopColor;
    private final LruCache<Long, LinearGradient> minuteGradientCache = new LruCache<>(5);
    private float mSelectedY;
    private float mDownY;
    private OnFullScreenIconClickListener mOnFullScreenIconClickListener;
    private OnCrossMoveListener mOnCrossMoveListener = null;
    private OnKChartViewClickListener mOnKChartViewClickListener;
    private boolean isAllowTimer = true;

    //文本测量复用
    private final Rect mTextBounds = new Rect();

    public boolean isLightTheme() {
        return isLightTheme;
    }

    public void setLightTheme(boolean lightTheme) {
        isLightTheme = lightTheme;
    }

    private boolean isLightTheme = true;

    public OnTakeProfitStopLossChanged getOnTakeProfitStopLossChanged() {
        return onTakeProfitStopLossChanged;
    }

    public void setOnTakeProfitStopLossChanged(OnTakeProfitStopLossChanged onTakeProfitStopLossChanged) {
        this.onTakeProfitStopLossChanged = onTakeProfitStopLossChanged;
    }

    private OnTakeProfitStopLossChanged onTakeProfitStopLossChanged;
    @NonNull
    private OnKChartViewShowIndexChanged mOnKChartViewShowIndexChanged;
    private float mCurrentPriceX;
    private float mCurrentPriceMainY;
    protected Typeface typeface;

    public OrderData getOrderData() {
        return orderData;
    }

    public void setOrderData(OrderData orderData) {
        this.orderData = orderData;
    }

    private OrderData orderData;
    // 是否可拖动 == (是否选中了订单线/止盈/止损线)
    private boolean enableDragStatus = false;
    // 是否正在拖动中ing
    private boolean isDragging = false;
    // 订单线/止盈/止损线 点击状态
    private boolean isActivedOrderLine = false;
    private boolean isActivedTPLine = false;
    private boolean isActivedSLLine = false;

    //默认的overScrollRange
    private float mDefault0verScrollRange;

    //上一次的数据是否是全屏
    private boolean mLastDataIsFullscreen;

    private boolean isUTC8;

    public boolean isUTC8() {
        return isUTC8;
    }

    public void setUTC8(boolean UTC8) {
        isUTC8 = UTC8;
    }

    private static final int MESSAGE_WHAT_INVALIDATE = 0;

    private static final int MESSAGE_WHAT_TIMER = 1;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MESSAGE_WHAT_INVALIDATE:
                case MESSAGE_WHAT_TIMER:
                    localInvalidate();
                    break;
            }
        }
    };

    private ScheduledFuture<?> scheduledFuture;

    private final ScheduledExecutorService scheduledService = Executors.newScheduledThreadPool(1);

    private final Runnable timerRunnable = () -> {
        if(!isAllowTimer) {
            return;
        }
        mHandler.removeMessages(MESSAGE_WHAT_INVALIDATE);
        Message message = Message.obtain();
        message.what = MESSAGE_WHAT_TIMER;
        mHandler.sendMessage(message);
    };

    public void setAllowTimerInvalidate(boolean isAllowTimer) {
        this.isAllowTimer = isAllowTimer;
    }

    public BaseKChartView(Context context) {
        this(context, null);
    }

    public BaseKChartView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BaseKChartView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initAttr(attrs);
        init();
    }

    private void initAttr(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.KChartView);
            int fontFamilyId = a.getResourceId(R.styleable.KChartView_android_fontFamily, 0);
            if (fontFamilyId != 0) {
                typeface = ResourcesCompat.getFont(getContext(), fontFamilyId);
            }
            mFullScreenMarginBottom = a.getDimension(R.styleable.KChartView_full_screen_margin_bottom, dp10());
            mLogoMarginBottom = a.getDimension(R.styleable.KChartView_logo_margin_bottom, dp12());
            a.recycle();
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (scheduledFuture == null || scheduledFuture.isCancelled() || scheduledFuture.isDone()) {
            scheduledFuture = scheduledService.scheduleWithFixedDelay(timerRunnable, 0, 1000, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);
            scheduledFuture = null;
        }
//        if (mAdapter != null) {
//            try {
//                mAdapter.unregisterDataSetObserver(mDataSetObserver);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        super.onDetachedFromWindow();
        minuteGradientCache.evictAll();
    }

    private void init() {
        setWillNotDraw(false);

        mCandleWidth = getContext().getResources().getDimension(R.dimen.chart_candle_width);

        KLINE_GRID_VALUE_MARGIN_RIGHT = getResources().getDimension(R.dimen.kline_grid_value_margin_right);
        KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER = getResources().getDimension(R.dimen.kline_current_price_value_margin_border);
        KLINE_CURRENT_PRICE_VALUE_BORDER_RAD = getResources().getDimension(R.dimen.kline_current_price_value_border_rad);
        KLINE_CURRENT_PRICE_LINE_WIDTH = getResources().getDimension(R.dimen.kline_current_price_line_width);
        KLINE_SELECT_LINE_WIDTH = getResources().getDimension(R.dimen.kline_select_line_width);
        TRADE_TYPE_ROUND_BG_PADDING = dp3();

        mDetector = new GestureDetectorCompat(getContext(), this);
        mScaleDetector = new ScaleGestureDetector(getContext(), this);
        mMainSubValueTop = (int) getResources().getDimension(R.dimen.chart_top_padding);
        mTopPadding = -dp1() + mMainSubValueTop + dp7() + dp5() + dp7() + dp2();
        mMainValueTextTop = 0;
        mChildHeight = (int) getResources().getDimension(R.dimen.child_height);
        mBottomPadding = (int) getResources().getDimension(R.dimen.chart_bottom_padding);
        mBottomTimeHeigh = (int) getResources().getDimension(R.dimen.chart_bottom_time_height);
        mValueHeigh = (int) getResources().getDimension(R.dimen.chart_value_height);
        mPointWidth = getResources().getDimension(R.dimen.chart_candle_width) + getResources().getDimension(R.dimen.chart_candle_gap);
        mCandleGapWidth = getResources().getDimension(R.dimen.chart_candle_gap);
        mBackgroundPaint.setColor(ContextCompat.getColor(getContext(), R.color.white));

        mSelectedValueBackgroundPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_selected_value_bg));
        minuterStartColor = ContextCompat.getColor(getContext(), R.color.chart_minuter_start);
        minuteStopColor = ContextCompat.getColor(getContext(), R.color.chart_minuter_stop);

        mGridPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_grid_line));
        mGridPaint.setStrokeWidth(getResources().getDimension(R.dimen.chart_grid_line_width));
        mTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_text));
        mTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_text_size));

        mBuyTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_buy_sell_text_size));
        mBuyTextPaint.setColor(Color.WHITE);

        mPriceTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_price_text));
        mPriceTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_text_size));
        mPriceTextPaint.setTypeface(font500);

        mTimePaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_text));
        mTimePaint.setTextSize(getResources().getDimension(R.dimen.chart_time_text_size));
        mTimePaint.setTypeface(font500);

        mSelectedValueTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_selected_value_text));
        mSelectedValueTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_text_size));

        mSelectedValueTextTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_selected_value_text));
        mSelectedValueTextTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_text_size_10));

        mSelectedLinePaint.setStyle(Paint.Style.FILL);
        mSelectedLinePaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_selected_line));
        mSelectedLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2(), dp3()}, 0));

        mCurrentPriceLinePaint.setStyle(Paint.Style.STROKE);
        mCurrentPriceLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2(), dp3()}, 0));
        mCurrentPriceLinePaint.setStrokeWidth(KLINE_CURRENT_PRICE_LINE_WIDTH);

        mBuyPriceLinePaint.setStyle(Paint.Style.STROKE);
        mBuyPriceLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2(), dp3()}, 0));
        mBuyPriceLinePaint.setStrokeWidth(KLINE_CURRENT_PRICE_LINE_WIDTH);

        mSellPriceLinePaint.setStyle(Paint.Style.STROKE);
        mSellPriceLinePaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_red));
        mSellPriceLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2(), dp3()}, 0));
        mSellPriceLinePaint.setStrokeWidth(KLINE_CURRENT_PRICE_LINE_WIDTH);

        mBuyPriceTextPaint.setStyle(Paint.Style.FILL);
        mBuyPriceTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_text_size));
        mBuyPriceTextPaint.setTypeface(font500);

        mBuyPriceTextBgPaint.setStrokeWidth(KLINE_CURRENT_PRICE_LINE_WIDTH);

        mCurrentPricePopBgPaint.setStyle(Paint.Style.FILL);
        mCurrentPricePopBgPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_current_price_pop_bg_cd0e1024));
        mCurrentPricePopBgPaint.setStrokeWidth(dp1());

        mCurrentPricePopBorderPaint.setStyle(Paint.Style.STROKE);
        mCurrentPricePopBorderPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_current_price_pop_border_55628a));
        mCurrentPricePopBorderPaint.setStrokeWidth(dp1());

        mCurrentPricePopTextPaint.setStyle(Paint.Style.FILL);
        mCurrentPricePopTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_current_price_pop_text_a2abc6));
        mCurrentPricePopTextPaint.setStrokeWidth(dp1());
        mCurrentPricePopTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_buy_sell_text_size));

        mCurrentPriceTextPaint.setStyle(Paint.Style.FILL);
        mCurrentPriceTextPaint.setColor(ContextCompat.getColor(getContext(), R.color.chart_current_price_text));
        mCurrentPriceTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_buy_sell_text_size));

        mInstantBuyingSellingTextPaint.setStyle(Paint.Style.FILL);
        mInstantBuyingSellingTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_buy_sell_text_size));

        mInstantBuyingSelling.setStyle(Paint.Style.STROKE);
        mInstantBuyingSelling.setStrokeWidth(dp0_5());

        mCountDownTextPaint.setStyle(Paint.Style.FILL);
        mCountDownTextPaint.setTextSize(getResources().getDimension(R.dimen.chart_buy_sell_text_size));

        mCountDownPaint.setStyle(Paint.Style.FILL);

        mCurrentPriceBorderPaint.setStyle(Paint.Style.STROKE);

        mAnimator = ValueAnimator.ofFloat(0f, 1f);
        mAnimator.setDuration(mAnimationDuration);
        mAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                invalidate();
            }
        });
//        startAnimation();

        setTextTypeFace(mGridPaint);
        setTextTypeFace(mTextPaint);
        setTextTypeFace(mPriceTextPaint);
        setTextTypeFace(mTimePaint);
        setTextTypeFace(mSelectedValueTextPaint);
        setTextTypeFace(mSelectedValueTextTextPaint);
        setTextTypeFace(mBackgroundPaint);
        setTextTypeFace(mSelectedValueBackgroundPaint);
        setTextTypeFace(mSelectedLinePaint);
        setTextTypeFace(mCurrentPriceLinePaint);
        setTextTypeFace(mCurrentPriceBorderPaint);
        setTextTypeFace(mCurrentPriceTextPaint);
        setTextTypeFace(mCurrentPricePopBgPaint);
        setTextTypeFace(mCurrentPricePopBorderPaint);
        setTextTypeFace(mCurrentPricePopTextPaint);

        initTpSlPaints();
    }

    private void initTpSlPaints() {
        mTpSLLinePaint.setStyle(Paint.Style.STROKE);
        mTpSLLinePaint.setAntiAlias(true);
        mTpSLLinePaint.setStrokeWidth(dp1());
        mTpSLLinePaint.setPathEffect(new DashPathEffect(new float[]{8f, 8f, 8f, 8f}, 1f));

        mTpSlTextPaint.setStyle(Paint.Style.FILL);
//        mTpSlTextPaint.setTypeface(textTypeface);
        mTpSlTextPaint.setTextSize(dp9());
        mTpSlTextPaint.setTextAlign(Paint.Align.LEFT);

        mTextTpSlBgPaint.setStyle(Paint.Style.FILL);
        mTextTpSlBgPaint.setAntiAlias(true);

        mStrokePaint.setStyle(Paint.Style.STROKE);
        mStrokePaint.setStrokeWidth(dp0_5());
        mStrokePaint.setAntiAlias(true);

        mTextBgPaint.setStyle(Paint.Style.FILL);
        mTextBgPaint.setAntiAlias(true);

        PorterDuffColorFilter pdcf = new PorterDuffColorFilter(ContextCompat.getColor(getContext(), isLightTheme ? R.color.c731e1e1e : R.color.c61ffffff), PorterDuff.Mode.SRC_IN);
        closeBitmapPaint.setColorFilter(pdcf);
    }

    private void setTextTypeFace(Paint paint) {
        if (paint != null && typeface != null) {
            paint.setTypeface(typeface);
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.mWidth = w;
        this.mTotalWidth = w;
//        mOverScrollRange1 = (int) -(getWidth() / 6f + 100);
        if (mScrollX == 0) {
            mScrollX = (int) (mOverScrollRange1 / mScaleX); // -306
        }
        initRect(w, h);
        setTranslateXFromScrollX(mScrollX);
        onScreenChange(false);
    }

    public Rect getmMainRect1() {
        return mMainRect1;
    }

    private void initRect(int w, int h) {
        int displayHeight = h - mTopPadding - mBottomPadding - mBottomTimeHeigh;
        int mMainHeight;
        mMainHeight = (int) (displayHeight - mChildHeight * mChildDrawSelectedList.size());
        mMainRect = new Rect(0, mTopPadding, mWidth, mTopPadding + mMainHeight);
        mMainRect1 = new Rect(0, 0, mWidth, mTopPadding + mMainHeight);
        if (!mChildDrawSelectedList.isEmpty()) {
            int size = mChildDrawSelectedList.size();
            for (int i = 0; i < size; i++) {
                var simpleChartDraw = mChildDrawSelectedList.get(i);
                simpleChartDraw.rect.set(0, mMainRect.bottom + mBottomTimeHeigh + mChildHeight * (i), mWidth, mMainRect.bottom + mBottomTimeHeigh + mChildHeight * (i + 1));
            }
        }
        onRectChanged();
    }

    private void onRectChanged() {
        mFullScreenPaint = null;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        try {
            Log.d(TAG, getTag() + "绘制：" +(mWidth == 0 || mMainRect.height() == 0 || mItemCount == 0));
            canvas.drawColor(mBackgroundPaint.getColor());
            if (mWidth == 0 || mMainRect.height() == 0 || mItemCount == 0) {
                return;
            }
            calculateValue();   // 耗时
            canvas.save();
            canvas.scale(1, 1);
            //表格
            drawGird(canvas);
            drawLogo(canvas);
            //刻度值对应价格和时间
            drawText(canvas);
            //蜡烛
            drawK(canvas);
            //买卖点
            drawOrder(canvas);
            //主副指标
            onDrawScreen(canvas);
            drawMaxAndMinValue(canvas);
//            drawFullScreen(canvas);
//            drawCurrentPrice(canvas);
//            drawCountDownTime(canvas);
//            drawInstantBuyingSelling(canvas);
            //买卖价线
            drawBuyAndSellLine(canvas);
            //十字线
            drawSelectedText(canvas);
            //十字线选中的蜡烛图对应的主副指标值
            drawValue(canvas, isShowCrossLine ? mSelectedIndex : mStopIndex);
            //持仓线
            drawPositionLine(canvas);
            //止盈线
            drawTPLine(canvas);
            //止损线
            drawSlLine(canvas);
            //正在拖动止盈/止损线
            if (isDragging) {
                drawMoveableLine(canvas);
            }
            canvas.restore();

            dealCallBack();
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(new Exception("BaseKChartView.onDraw e" + e.getMessage()));
        }
    }

    protected void dealCallBack() {
        //把当前价的坐标传出去
        if (mOnCurrentPricePositionListener != null) {
            mOnCurrentPricePositionListener.OnCurrentPricePosition(mCurrentPriceX, mCurrentPriceMainY);
        }
        //把当前缩放比例传出去
        if (mOnMainScaleYListener != null) {
            mOnMainScaleYListener.onMainScaleY(mMainScaleY);
        }
    }

    Rect mLogoRect;
    Paint mLogoPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    int mLogoHeight = dp20();
    int mLogoWidth = dp88();
    int mLogoMargin = dp12();

    int mLogoMarginStart = dp36();
    int mLogoMarginEnd = dp36();
    int mLogoMarginTop = dp12();
    float mLogoMarginBottom = dp0();

    public void setLogoHeight(int height) {
        this.mLogoHeight = height;
    }

    public void setLogoWidth(int width) {
        this.mLogoWidth = width;
    }

    public void setLogoMargin(int logoMargin) {
        this.mLogoMargin = logoMargin;
    }

    private void drawLogo(Canvas canvas) {
        if (!isShowLogo) return;
        try {
            setTextTypeFace(mLogoPaint);
            mLogoPaint.setFilterBitmap(true);
            Drawable drawable = ContextCompat.getDrawable(getContext(), logo);
            mLogoMarginBottom = dp29();
            if (drawable != null) {
                mLogoBitmap = ((BitmapDrawable) drawable).getBitmap();
                mLogoRect = new Rect(0, 0, mLogoBitmap.getWidth(), mLogoBitmap.getHeight());

                RectF mLogoDstRect = ObjectPool.obtainRectF();
                mLogoDstRect.set(mMainRect.left + mLogoMarginStart, mMainRect.bottom - mLogoHeight / 2 - mLogoMarginBottom, mMainRect.left + mLogoWidth + mLogoMarginEnd, mMainRect.bottom - mLogoMarginBottom + mLogoHeight / 2);
                if (isFullLeft) {
                    mLogoDstRect.offset(mFullScreenWidth + KLINE_GRID_VALUE_MARGIN_RIGHT, 0f);
                }
                canvas.drawBitmap(mLogoBitmap, mLogoRect, mLogoDstRect, mLogoPaint);
                ObjectPool.recycleRectF(mLogoDstRect);
            }
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(new Exception("BaseKChartView.drawLogo e" + e.getMessage()));
        }
    }

    private void drawMaxAndMinValue(Canvas canvas) {
        if (mMainDraw instanceof MainDraw && mItemCount > 0) {
            if (!(mMainMaxIndex >= 0 && mMainMinIndex >= 0 && mMainMaxValueY >= 0 && mMainMinValueY >= 0)) {
                return;
            }
            float highX = translateXtoX(getX(mMainMaxIndex));
            float highY = getMainY(mMainMaxValueY);
            float lowX = translateXtoX(getX(mMainMinIndex));
            float lowY = getMainY(mMainMinValueY);
            KLineEntity minItem = (KLineEntity) getItem(mMainMinIndex);
            KLineEntity maxItem = (KLineEntity) getItem(mMainMaxIndex);

            if (null == minItem || null == maxItem) {
                return;
            }

            ((MainDraw) mMainDraw).drawMaxAndMinValue(canvas, mWidth, highX, highY, lowX, lowY, "" + maxItem.getHighPrice(), "" + minItem.getLowPrice());
        }
    }

    /**
     * 由价格获取Y坐标
     *
     * @param value
     * @return
     */
    public float getMainY(float value) {
        return (mMainMaxValue - value) * mMainScaleY + mMainRect.top;
    }

    /**
     * 由Y坐标获取价格
     *
     * @param y
     * @return
     */
    public float getMainYValue(float y) {
        return mMainMaxValue - (y - mMainRect.top) / mMainScaleY;
    }

    /**
     * 解决text居中的问题
     */
    public float fixTextY(Paint paint, float y) {
        paint.getFontMetrics(mFontMetrics);
        return (y + (mFontMetrics.descent - mFontMetrics.ascent) / 2 - mFontMetrics.descent);
    }

    // 预计算的横向网格线坐标,主图
    private float[] mGridRowLines;
    // 预计算的纵向网格线坐标,主图
    private float[] mGridColumnLines;
    // 预计算的横向网格线坐标，副图
    private float[] mChildRowLines;
    // 预计算的纵向网格线坐标，副图
    private float[] mChildColumnLines;

    private void preComputeGridLines() {
        // 主图横向网格预计算
        float rowSpace = (float) mMainRect.height() / mGridRows;
        int length = (mGridRows + 1) * 4;
        if (mGridRowLines == null || mGridRowLines.length != length) {
            mGridRowLines = new float[length];
        }
        float step;
        for (int i = 0, idx = 0; i <= mGridRows; i++) {
            step = rowSpace * i + mMainRect.top;
            mGridRowLines[idx++] = 0;
            mGridRowLines[idx++] = step;
            mGridRowLines[idx++] = mWidth;
            mGridRowLines[idx++] = step;
        }
        //主图纵向网格预计算
        float columnSpace = mWidth / mGridColumns;
        length = (mGridColumns + 1) * 4;
        if (mGridColumnLines == null || mGridColumnLines.length != length) {
            mGridColumnLines = new float[length];
        }
        for (int i = 0, idx = 0; i <= mGridColumns; i++) {
            step = columnSpace * i;
            mGridColumnLines[idx++] = step;
            mGridColumnLines[idx++] = 0;
            mGridColumnLines[idx++] = step;
            mGridColumnLines[idx++] = mMainRect.bottom;
        }
        // 副图横向网格预计算
        int size = mChildDrawSelectedList.size();
        length = size * 2 * 4;
        if (mChildRowLines == null || mChildRowLines.length != length) {
            mChildRowLines = new float[length];
        }
        for (int i = 0, idx = 0; i < size; i++) {
            var simpleChartDraw = mChildDrawSelectedList.get(i);
            mChildRowLines[idx++] = 0;
            mChildRowLines[idx++] = simpleChartDraw.rect.bottom;
            mChildRowLines[idx++] = mWidth;
            mChildRowLines[idx++] = simpleChartDraw.rect.bottom;

            mChildRowLines[idx++] = 0;
            mChildRowLines[idx++] = simpleChartDraw.rect.top;
            mChildRowLines[idx++] = mWidth;
            mChildRowLines[idx++] = simpleChartDraw.rect.top;
        }
        // 副图纵向网格预计算
        length = (mGridColumns - 1) * size * 4;
        if (mChildColumnLines == null || mChildColumnLines.length != length) {
            mChildColumnLines = new float[length];
        }
        for (int i = 1, idx = 0; i < mGridColumns; i++) {
            for (int j = 0; j < size; j++) {
                var simpleChartDraw = mChildDrawSelectedList.get(j);
                step = columnSpace * i;
                mChildColumnLines[idx++] = step;
                mChildColumnLines[idx++] = simpleChartDraw.rect.top;
                mChildColumnLines[idx++] = step;
                mChildColumnLines[idx++] = simpleChartDraw.rect.bottom;
            }
        }
    }

    /**
     * 画表格
     *
     * @param canvas
     */
    private void drawGird(Canvas canvas) {

        //准备表格数据
        preComputeGridLines();

        canvas.drawLine(0, 0, mWidth, 0, mGridPaint);
        //横向的grid
        canvas.drawLines(mGridRowLines, mGridPaint);
        canvas.drawLines(mChildRowLines, mGridPaint);
        //纵向的grid
        canvas.drawLines(mGridColumnLines, mGridPaint);
        canvas.drawLines(mChildColumnLines, mGridPaint);
    }
    private List<Pair<Float, KLineImpl>> mPointList = new ArrayList<>();

    private void preparePointList(KLineImpl lastPoint, KLineImpl curPoint, float lastX, float screenLastX, float curX, float screenCurX, int position) {
        if (position == mStartIndex) {
            mPointList.add(new Pair<>(screenLastX, lastPoint));
        }
        mPointList.add(new Pair<>(screenCurX, curPoint));
    }

    @Override
    public List<Pair<Float, KLineImpl>> getPointList() {
        if(mPointList==null){
            mPointList = new ArrayList<>();
        }
        return mPointList;
    }

    /**
     * 画k线图
     *
     * @param canvas
     */
    private void drawK(Canvas canvas) {
        //保存之前的平移，缩放
        canvas.save();
        canvas.translate(mTranslateX * mScaleX, 0);
        canvas.scale(mScaleX, 1);
        if (mMainDraw != null) {
            mMainDraw.onDrawKStart(canvas, this);
        }

        if (!mMainChildDrawSelectedList.isEmpty()) {
            for (var iChartDraw : mMainChildDrawSelectedList) {
                iChartDraw.onDrawKStart(canvas, this);
            }
        }

        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                simpleChartDraw.onDrawKStart(canvas, this);
            }
        }
        mPointList.clear();

        //选定要绘制的数据，将数据传给各个子图
        for (int i = mStartIndex; i <= mStopIndex; i++) {
            KLineEntity currentPoint = (KLineEntity) getItem(i);
            if (null == currentPoint) {
                continue;
            }

            float currentPointX = getX(i);

            KLineEntity lastPoint = i == 0 ? currentPoint : (KLineEntity) getItem(i - 1);
            if (null == lastPoint) {
                continue;
            }

            float lastX = i == 0 ? currentPointX : getX(i - 1);

            float screenCurrentX = translateXtoX(currentPointX);
            float screenLastX = translateXtoX(lastX);

            preparePointList(lastPoint, currentPoint, lastX, screenLastX, currentPointX, screenCurrentX, i);

            if (mMainDraw != null) {
                mMainDraw.drawTranslated(lastPoint, currentPoint, lastX, screenLastX, currentPointX, screenCurrentX, canvas, this, i);
            }

            if (!mMainChildDrawSelectedList.isEmpty()) {
                for (var iChartDraw : mMainChildDrawSelectedList) {
                    iChartDraw.drawTranslated(lastPoint, currentPoint, lastX, screenLastX, currentPointX, screenCurrentX, canvas, this, i);
                }
            }

            if (!mChildDrawSelectedList.isEmpty()) {
                for (var simpleChartDraw : mChildDrawSelectedList) {
                    simpleChartDraw.drawTranslated(lastPoint, currentPoint, lastX, screenLastX, currentPointX, screenCurrentX, canvas, this, i);
                }
            }
        }

        if (mMainDraw != null) {
            mMainDraw.onDrawKEnd(canvas, this);
        }

        if (!mMainChildDrawSelectedList.isEmpty()) {
            for (var iChartDraw : mMainChildDrawSelectedList) {
                iChartDraw.onDrawKEnd(canvas, this);
            }
        }

        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                simpleChartDraw.onDrawKEnd(canvas, this);
            }
        }

        //画十字线
        if (isEnableCrossLine && isShowCrossLine && mSelectedIndex >= 0) {

            KLineImpl point = (KLineImpl) getItem(mSelectedIndex);
            if (point == null) {
                return;
            }
            float lineX = getX(mSelectedIndex);
            float lineY;
            if (IS_SELECTED_LINE_DRAW_CLOSE_PRICE) {
                lineY = getMainY(point.getClosePrice());
            } else {
                lineY = mSelectedY;
            }

            lineY = fixSelectY(lineY);
            // 画经线
            mSelectedLinePaint.setStrokeWidth(KLINE_SELECT_LINE_WIDTH);
            mSelectedLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2px(2 / mScaleX), dp2px(3 / mScaleX)}, 0));
            canvas.drawLine(-mTranslateX, lineY, -mTranslateX + (mWidth - KLINE_GRID_VALUE_MARGIN_RIGHT) / mScaleX, lineY, mSelectedLinePaint);
            // 画纬线
            mSelectedLinePaint.setStrokeWidth(KLINE_SELECT_LINE_WIDTH / mScaleX);
            mSelectedLinePaint.setPathEffect(new DashPathEffect(new float[]{dp2(), dp3()}, 0));
            canvas.drawLine(lineX, 0, lineX, getHeight(), mSelectedLinePaint);

            if (mSelectedIndex == mItemCount - 1) {
                Object item = getItem(mSelectedIndex);
                if (null == item) {
                    return;
                }
                onCrossMoved(this, item, mSelectedIndex, true, isLeft, false);
            }
        }
        //还原 平移缩放
        canvas.restore();
    }

    RectF bgOrderRect = null;

    private void drawPositionLine(Canvas canvas) {
        if (!isDrawPositionLine) {
            return;
        }
        if (orderData == null) {
            return;
        }
        float openPrice = orderData.getOpenPrice();
        if (openPrice > mMainMaxValueY || openPrice < mMainMinValueY) {
            return;
        }
        float y = getMainY(openPrice);

        float paddingStart = dp4();
        float paddingTop = dp2();
        float bgR = dp2();
        float dividerWidth = dp0_5();
        boolean isBuy = orderData.isBuy();
        int tempColor = isBuy ? tpColor : slColor;
        String buySell = isBuy ? "BUY" : "SELL";
        String volume = orderData.getVolume() + getContext().getString(R.string.lots);
        String profit = orderData.getProfitUI();
        //测量文案
        mTpSlTextPaint.getTextBounds(buySell + volume + profit, 0, (buySell + volume + profit).length(), mTextBounds);
        if (maxRectHeight <= 0) {
            maxRectHeight = mTextBounds.height();
        } else {
            maxRectHeight = Math.max(maxRectHeight, mTextBounds.height());
        }
        int textWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(buySell, 0, buySell.length(), mTextBounds);
        int buySellWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(volume, 0, volume.length(), mTextBounds);
        int volumeWidth = mTextBounds.width();
        //范围
        if (bgOrderRect == null) {
            bgOrderRect = ObjectPool.obtainRectF();
        }
        bgOrderRect.set(dp20(), y - maxRectHeight * 0.5f - paddingTop,
                dp20() + textWidth + 6 * paddingStart,
                y + maxRectHeight * 0.5f + paddingTop);
        //背景绘制
        mTextBgPaint.setColor(tempColor);
        canvas.drawRoundRect(bgOrderRect, bgR, bgR, mTextBgPaint);

        mStrokePaint.setColor(tempColor);
        canvas.drawRoundRect(bgOrderRect, bgR, bgR, mStrokePaint);
        //起始位置
        float currentX = bgOrderRect.left + paddingStart;
        //Buy/Sell 文案
        mTpSlTextPaint.setColor(Color.WHITE);
        canvas.drawText(buySell, currentX, fixTextY(mTpSlTextPaint, bgOrderRect.centerY()), mTpSlTextPaint);
        //计算位置
        currentX += buySellWidth + paddingStart;
        //手数和盈亏背景
        RectF volumeBgRect = ObjectPool.obtainRectF();
        volumeBgRect.set(currentX, bgOrderRect.top, bgOrderRect.right, bgOrderRect.bottom);
        mTextBgPaint.setColor(isActivedOrderLine ? tempColor : Color.WHITE);
        canvas.drawRoundRect(volumeBgRect, bgR, bgR, mTextBgPaint);
        //分割线
        dividerPaint.setStrokeWidth(isActivedOrderLine ? dividerWidth : dp2());
        dividerPaint.setColor(isActivedOrderLine ? 0x61FFFFFF : tempColor);
        canvas.drawLine(currentX, bgOrderRect.top, currentX, bgOrderRect.bottom, dividerPaint);
        //手数
        mTpSlTextPaint.setColor(isActivedOrderLine ? Color.WHITE : leftTextColor);
        currentX += dividerWidth + paddingStart;
        canvas.drawText(volume, currentX, fixTextY(mTpSlTextPaint, bgOrderRect.centerY()), mTpSlTextPaint);
        //分割线
        dividerPaint.setStrokeWidth(dividerWidth);
        currentX += volumeWidth + paddingStart;
        canvas.drawLine(currentX, bgOrderRect.top, currentX, bgOrderRect.bottom, dividerPaint);
        //盈亏
        currentX += dividerWidth + paddingStart;
        canvas.drawText(profit, currentX, fixTextY(mTpSlTextPaint, bgOrderRect.centerY()), mTpSlTextPaint);
        //点数
        mTpSlTextPaint.setColor(rightTextColor);
        String priceText = String.valueOf(openPrice);
        mTpSlTextPaint.getTextBounds(priceText, 0, priceText.length(), mTextBounds);
        mTextBgPaint.setColor(tempColor);
        float bgTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * paddingStart;
        RectF bgTextRect = ObjectPool.obtainRectF();
        bgTextRect.set(bgTextLeft, y - mTextBounds.height() * 0.5f - paddingTop,
                bgTextLeft + mTextBounds.width() + 2 * paddingStart, y + mTextBounds.height() * 0.5f + paddingTop);
        canvas.drawRoundRect(bgTextRect, bgR, bgR, mTextBgPaint);
        canvas.drawText(priceText, bgTextRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgTextRect.centerY()), mTpSlTextPaint);
        //虚线
        Path path = ObjectPool.obtainPath();
        path.moveTo(bgTextRect.left, y);
        path.lineTo(bgOrderRect.right, y);
        mTpSLLinePaint.setColor(tempColor);
        canvas.drawPath(path, mTpSLLinePaint);

        ObjectPool.recycleRectF(volumeBgRect);
        ObjectPool.recycleRectF(bgTextRect);
        ObjectPool.recyclePath(path);
    }

    float tpSlprice = 0f;
    RectF bgTpRect = null;
    RectF bgSlRect = null;
    RectF tpIconRect = null;
    RectF slIconRect = null;
    private Bitmap cancelIcon;

    private void drawTPLine(Canvas canvas) {
        if (orderData == null || !isShowTpLine) {
            return;
        }
        float takeProfit = orderData.getTakeProfit();
        if (takeProfit > mMainMaxValue || takeProfit < mMainMinValue) {
            return;
        }
        float y = getMainY(takeProfit);
        String tpText = "TP";
        String volumeText = orderData.getVolume() + getContext().getString(R.string.lots);
        float paddingStart = dp4();
        float paddingTop = dp2();
        float bgR = dp2();
        // 测量文案
        mTpSlTextPaint.getTextBounds(tpText + volumeText, 0, (tpText + volumeText).length(), mTextBounds);
        if (maxRectHeight <= 0) {
            maxRectHeight = mTextBounds.height();
        } else {
            maxRectHeight = Math.max(maxRectHeight, mTextBounds.height());
        }
        mTpSlTextPaint.getTextBounds(tpText, 0, tpText.length(), mTextBounds);
        int tpWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(volumeText, 0, volumeText.length(), mTextBounds);
        int volumeWidth = mTextBounds.width();
        // 范围
        if (bgTpRect == null) {
            bgTpRect = ObjectPool.obtainRectF();
        }
        bgTpRect.set(dp20(), y - maxRectHeight * 0.5f - paddingTop,
                dp20() + tpWidth + volumeWidth + 5 * paddingStart, y + maxRectHeight * 0.5f + paddingTop);
        //背景绘制
        mTextBgPaint.setColor(tpColor);
        canvas.drawRoundRect(bgTpRect, bgR, bgR, mTextBgPaint);
        //手数背景
        RectF volumeBgRect = ObjectPool.obtainRectF();
        volumeBgRect.set(bgTpRect.left + paddingStart + tpWidth + paddingStart,
                bgTpRect.top, bgTpRect.right, bgTpRect.bottom);
        mTextBgPaint.setColor(isActivedTPLine ? tpColor : Color.WHITE);
        canvas.drawRoundRect(volumeBgRect, bgR, bgR, mTextBgPaint);
        //分割线
        dividerPaint.setStrokeWidth(isActivedTPLine ? dp0_5() : dp2());
        dividerPaint.setColor(isActivedTPLine ? 0x61FFFFFF : tpColor);
        float dividerX = bgTpRect.left + paddingStart + tpWidth + paddingStart; // 分割线位置
        canvas.drawLine(dividerX, bgTpRect.top, dividerX, bgTpRect.bottom, dividerPaint);

        mStrokePaint.setColor(tpColor);
        canvas.drawRoundRect(bgTpRect, bgR, bgR, mStrokePaint);
        // TP文案绘制
        mTpSlTextPaint.setColor(Color.WHITE);
        canvas.drawText(tpText, bgTpRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgTpRect.centerY()), mTpSlTextPaint);
        // 手数文案绘制
        mTpSlTextPaint.setColor(isActivedTPLine ? Color.WHITE : Color.BLACK);
        canvas.drawText(volumeText, volumeBgRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgTpRect.centerY()), mTpSlTextPaint);
        //点数
        mTpSlTextPaint.setColor(rightTextColor);
        String priceText = String.valueOf(takeProfit);
        mTpSlTextPaint.getTextBounds(priceText, 0, priceText.length(), mTextBounds);
        mTextBgPaint.setColor(tpColor);
        float bgTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * paddingStart;

        RectF bgTextRect = ObjectPool.obtainRectF();
        bgTextRect.set(bgTextLeft, y - maxRectHeight * 0.5f - paddingTop,
                bgTextLeft + mTextBounds.width() + 2 * paddingStart, y + maxRectHeight * 0.5f + paddingTop);
        canvas.drawRoundRect(bgTextRect, bgR, bgR, mTextBgPaint);
        canvas.drawText(priceText, bgTextRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgTextRect.centerY()), mTpSlTextPaint);

        //取消按钮
        float lineMoveTarget;
        if (isActivedTPLine) {
            try {
                if (cancelIcon == null || cancelIcon.isRecycled()) {
                    cancelIcon = BitmapFactory.decodeResource(this.getResources(), R.mipmap.icon_source3_close_16x16);
                }
                float size = bgTextRect.bottom - bgTextRect.top - dp2() * 2;
                if (tpIconRect == null) {
                    tpIconRect = ObjectPool.obtainRectF();
                }
                tpIconRect.set(bgTextRect.left - paddingStart - size, bgTextRect.top + dp2(),
                        bgTextRect.left - paddingStart, bgTextRect.bottom - dp2());
                canvas.drawBitmap(cancelIcon, null, tpIconRect, closeBitmapPaint);
            } catch (Exception e) {
                e.printStackTrace();
            }
            lineMoveTarget = tpIconRect.left;
        } else {
            lineMoveTarget = bgTextRect.left;
        }
        //虚线
        Path path = ObjectPool.obtainPath();
        path.moveTo(lineMoveTarget, y);
        path.lineTo(bgTpRect.right, y);
        mTpSLLinePaint.setColor(tpColor);
        canvas.drawPath(path, mTpSLLinePaint);

        ObjectPool.recyclePath(path);
        ObjectPool.recycleRectF(volumeBgRect);
        ObjectPool.recycleRectF(bgTextRect);
    }

    private void drawSlLine(Canvas canvas) {
        if (orderData == null || !isShowSlLine) {
            return;
        }
        float stopLoss = orderData.getStopLoss();
        if (stopLoss > mMainMaxValue || stopLoss < mMainMinValue) {
            return;
        }
        float y = getMainY(stopLoss);
        String slText = "SL";
        String volumeText = orderData.getVolume() + getContext().getString(R.string.lots);
        float paddingStart = dp4();
        float paddingTop = dp2();
        float bgR = dp2();
        // 测量文案
        mTpSlTextPaint.getTextBounds(slText + volumeText, 0, (slText + volumeText).length(), mTextBounds);
        if (maxRectHeight <= 0) {
            maxRectHeight = mTextBounds.height();
        } else {
            maxRectHeight = Math.max(maxRectHeight, mTextBounds.height());
        }
        mTpSlTextPaint.getTextBounds(slText, 0, slText.length(), mTextBounds);
        int slWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(volumeText, 0, volumeText.length(), mTextBounds);
        int volumeWidth = mTextBounds.width();
        // 范围
        if (bgSlRect == null) {
            bgSlRect = ObjectPool.obtainRectF();
        }
        bgSlRect.set(dp20(), y - maxRectHeight * 0.5f - paddingTop,
                dp20() + slWidth + volumeWidth + 5 * paddingStart, y + maxRectHeight * 0.5f + paddingTop);
        //背景绘制
        mTextBgPaint.setColor(slColor);
        canvas.drawRoundRect(bgSlRect, bgR, bgR, mTextBgPaint);
        //手数背景
        RectF volumeBgRect = ObjectPool.obtainRectF();
        volumeBgRect.set(bgSlRect.left + paddingStart + slWidth + paddingStart,
                bgSlRect.top, bgSlRect.right, bgSlRect.bottom);
        mTextBgPaint.setColor(isActivedSLLine ? slColor : Color.WHITE);
        canvas.drawRoundRect(volumeBgRect, bgR, bgR, mTextBgPaint);
        //分割线
        dividerPaint.setStrokeWidth(isActivedSLLine ? dp0_5() : dp2());
        dividerPaint.setColor(isActivedSLLine ? 0x61FFFFFF : slColor);
        float dividerX = bgSlRect.left + paddingStart + slWidth + paddingStart; // 分割线位置
        canvas.drawLine(dividerX, bgSlRect.top, dividerX, bgSlRect.bottom, dividerPaint);

        mStrokePaint.setColor(slColor);
        canvas.drawRoundRect(bgSlRect, bgR, bgR, mStrokePaint);
        // SL文案绘制
        mTpSlTextPaint.setColor(Color.WHITE);
        canvas.drawText(slText, bgSlRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgSlRect.centerY()), mTpSlTextPaint);
        // 手数文案绘制
        mTpSlTextPaint.setColor(isActivedSLLine ? Color.WHITE : Color.BLACK);
        canvas.drawText(volumeText, volumeBgRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgSlRect.centerY()), mTpSlTextPaint);
        //点数
        mTpSlTextPaint.setColor(rightTextColor);
        String priceText = String.valueOf(stopLoss);
        mTpSlTextPaint.getTextBounds(priceText, 0, priceText.length(), mTextBounds);
        mTextBgPaint.setColor(slColor);
        float bgTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * paddingStart;
        RectF bgTextRect = ObjectPool.obtainRectF();
        bgTextRect.set(bgTextLeft, y - maxRectHeight * 0.5f - paddingTop,
                bgTextLeft + mTextBounds.width() + 2 * paddingStart, y + maxRectHeight * 0.5f + paddingTop);
        // 止损线价格
        canvas.drawRoundRect(bgTextRect, bgR, bgR, mTextBgPaint);
        canvas.drawText(priceText, bgTextRect.left + paddingStart, fixTextY(mTpSlTextPaint, bgTextRect.centerY()), mTpSlTextPaint);
        //取消按钮
        float lineMoveTarget;
        if (isActivedSLLine) {
            try {
                if (cancelIcon == null || cancelIcon.isRecycled()) {
                    cancelIcon = BitmapFactory.decodeResource(this.getResources(), R.mipmap.icon_source3_close_16x16);
                }
                float size = bgTextRect.bottom - bgTextRect.top - dp2() * 2;
                if (slIconRect == null) {
                    slIconRect = ObjectPool.obtainRectF();
                }
                slIconRect.set(
                        bgTextRect.left - paddingStart - size,
                        bgTextRect.top + dp2(),
                        bgTextRect.left - paddingStart,
                        bgTextRect.bottom - dp2()
                );
                canvas.drawBitmap(cancelIcon, null, slIconRect, closeBitmapPaint);
            } catch (Exception e) {
                e.printStackTrace();
            }
            lineMoveTarget = slIconRect.left;
        } else {
            lineMoveTarget = bgTextRect.left;
        }

        //虚线
        Path path = ObjectPool.obtainPath();
        path.moveTo(lineMoveTarget, y);
        path.lineTo(bgSlRect.right, y);
        mTpSLLinePaint.setColor(slColor);
        canvas.drawPath(path, mTpSLLinePaint);

        ObjectPool.recyclePath(path);
        ObjectPool.recycleRectF(volumeBgRect);
        ObjectPool.recycleRectF(bgTextRect);
    }

    float tpSlMovePrice;
    String estimatedPnl;
    int tpColor = getContext().getColor(R.color.c00c79c);
    int sellPriceColor = getContext().getColor(R.color.cff3c70);
    int buyPriceColor = getContext().getColor(R.color.c00c79c);
    int slColor = getContext().getColor(R.color.chart_red);
    int c007fff = getContext().getColor(R.color.c007fff);
    int leftTextColor = getContext().getColor(R.color.c1e1e1e);
    int rightTextColor = Color.WHITE;
    int moveTextColor = Color.WHITE;

    float originW = 0;

    private void drawMoveableLine(Canvas canvas) {
        if (orderData == null) {
            return;
        }
        Log.d("==", "tpSlMovePrice===" + tpSlMovePrice + "==mMainMinValueY=" + mMainMinValue);
        if (tpSlMovePrice > mMainMaxValue) {
            tpSlMovePrice = mMainMaxValue;
        }
        if (tpSlMovePrice < mMainMinValue) {
            tpSlMovePrice = mMainMinValue;
        }
        int strokePaintColor;
        String tlSl;
        String profitLoss = estimatedPnl;
        String currencyType = orderData.getCurrencyType();
        if (orderData.isBuy()) {
            if(tpSlMovePrice >= sellPrice) {
                tlSl = "TP";
                strokePaintColor = tpColor;
            } else {
                tlSl = "SL";
                strokePaintColor = slColor;
            }
        } else {
            if(tpSlMovePrice <= buyPrice) {
                tlSl = "TP";
                strokePaintColor = tpColor;
            } else {
                tlSl = "SL";
                strokePaintColor = slColor;
            }
        }
        float y = getMainY(tpSlMovePrice);

        float paddingStart = dp4();
        float paddingTop = dp2();
        float bgR = dp2();
        float dividerWidth = dp0_5();
        mTpSlTextPaint.setColor(moveTextColor);
        //测量文案
        mTpSlTextPaint.getTextBounds(tlSl + profitLoss + currencyType, 0, (tlSl + profitLoss + currencyType).length(), mTextBounds);
        if (maxRectHeight <= 0) {
            maxRectHeight = mTextBounds.height();
        } else {
            maxRectHeight = Math.max(maxRectHeight, mTextBounds.height());
        }
        int textWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(tlSl, 0, tlSl.length(), mTextBounds);
        int tpSlWidth = mTextBounds.width();
        mTpSlTextPaint.getTextBounds(profitLoss, 0, profitLoss.length(), mTextBounds);
        int profitLossWidth = mTextBounds.width();
        //范围
        RectF bgRect = ObjectPool.obtainRectF();
        bgRect.set(dp20(), y - maxRectHeight * 0.5f - paddingTop,
                dp20() + textWidth + 6 * paddingStart,
                y + maxRectHeight * 0.5f + paddingTop);
        //背景绘制
        mTextBgPaint.setColor(strokePaintColor);
        canvas.drawRoundRect(bgRect, bgR, bgR, mTextBgPaint);

        mStrokePaint.setColor(strokePaintColor);
        canvas.drawRoundRect(bgRect, bgR, bgR, mStrokePaint);
        //起始位置
        float currentX = bgRect.left + paddingStart;
        //TP/SL文案
        mTpSlTextPaint.setColor(Color.WHITE);
        canvas.drawText(tlSl, currentX, fixTextY(mTpSlTextPaint, bgRect.centerY()), mTpSlTextPaint);
        //计算位置
        currentX += tpSlWidth + paddingStart;
        //分割线
        canvas.drawLine(currentX, bgRect.top, currentX, bgRect.bottom, mTpSlTextPaint);
        //盈亏文案
        currentX += dividerWidth + paddingStart;
        canvas.drawText(profitLoss, currentX, fixTextY(mTpSlTextPaint, bgRect.centerY()), mTpSlTextPaint);
        //分割线
        currentX += profitLossWidth + paddingStart;
        canvas.drawLine(currentX, bgRect.top, currentX, bgRect.bottom, mTpSlTextPaint);
        //货币单位
        currentX += dividerWidth + paddingStart;
        canvas.drawText(currencyType, currentX, fixTextY(mTpSlTextPaint, bgRect.centerY()), mTpSlTextPaint);
        //点数
        mTpSlTextPaint.setColor(rightTextColor);
        String priceText = String.valueOf(tpSlMovePrice);
        mTpSlTextPaint.getTextBounds(priceText, 0, priceText.length(), mTextBounds);
        mTextBgPaint.setColor(strokePaintColor);
        float bgTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * paddingStart;
        RectF bgTextRect = ObjectPool.obtainRectF();
        bgTextRect.set(bgTextLeft, y - maxRectHeight * 0.5f - paddingTop,
                bgTextLeft + mTextBounds.width() + 2 * paddingStart, y + maxRectHeight * 0.5f + paddingTop);
        canvas.drawRoundRect(bgTextRect, bgR, bgR, mTextBgPaint);
        canvas.drawText(priceText, bgTextRect.left + paddingStart, bgTextRect.bottom - paddingTop, mTpSlTextPaint);
        //选中圆圈
        mStrokePaint.setColor(c007fff);
        canvas.drawCircle(bgTextRect.left - dp3(), bgTextRect.centerY(), dp3(), mStrokePaint);
        //虚线
        Path path = ObjectPool.obtainPath();
        path.moveTo(bgTextRect.left - dp6(), y);
        path.lineTo(bgRect.right, y);
        mTpSLLinePaint.setColor(strokePaintColor);
        canvas.drawPath(path, mTpSLLinePaint);

        ObjectPool.recyclePath(path);
        ObjectPool.recycleRectF(bgRect);
        ObjectPool.recycleRectF(bgTextRect);
    }

    private int findOrderIndex(long time) {
        for (int i = mStartIndex; i <= mStopIndex; i++) {
            KLineEntity item = (KLineEntity) mAdapter.getItem(i);
            KLineEntity itemNext = null;
            if (i + 1 < mAdapter.getCount()) {
                itemNext = (KLineEntity) mAdapter.getItem(i + 1);
            }
            if (itemNext == null) {
                if (time >= item.timestampLong) {
                    return i;
                }
                return -1;
            }
            if (time >= item.getDatetimeLong() && time < itemNext.getDatetimeLong()) {
                return i;
            }
        }
        return -1;
    }

    private long[] timestampArray;

    // 预生成时间戳数组用于二分查找
    private void prepareTimestampsArray() {
        int length = mStopIndex - mStartIndex + 1;
        if (length <= 0) {
            timestampArray = null;
            return;
        }
        if (timestampArray == null || timestampArray.length != length) {
            timestampArray = new long[length];
        }
        for (int i = mStartIndex; i <= mStopIndex; i++) {
            timestampArray[i - mStartIndex] = ((KLineEntity) getItem(i)).getDatetimeLong();
        }
    }

    private int binarySearchIndex(long time) {
        if (timestampArray == null) {
            return -1;
        }
        // 使用二分查找优化索引查询
        int index = Arrays.binarySearch(timestampArray, time);
        if (index < 0) {
            index = -index - 2; // 获取前一个有效索引
        }
        index = Math.min(Math.max(index + mStartIndex, mStartIndex), mStopIndex);
        return index;
    }

    private void drawOrder(Canvas canvas) {
        //提前判断orderRecordEntityList，减少迭代器生成
        if (!showOrder || orderRecordEntityList.isEmpty()) {
            return;
        }
        //预先或者开始时间和结束时间，避免遍历的时候，每次都生成
        KLineEntity item = (KLineEntity) mAdapter.getItem(mStartIndex);
        long stopTime = Long.MAX_VALUE;
        if (mStopIndex + 1 < mAdapter.getCount()) {
            KLineEntity itemStop = (KLineEntity) mAdapter.getItem(mStopIndex);
            stopTime = itemStop.getDatetimeLong();
        }
        prepareTimestampsArray();

        for (OrderRecordEntity orderRecordEntity : orderRecordEntityList) {
            long time = orderRecordEntity.getTime();
            if (time < item.getDatetimeLong() || time > stopTime) {
                continue;
            }

            //TODO 需计算时间对应的index
//                    int l = mStartIndex + (int) ((time - Long.parseLong(item.timestamp)) / (mAdapter.getStep()));
            // int l = findOrderIndex(time);
            int l = binarySearchIndex(time);
            if (l == -1) {
                continue;
            }
            KLineEntity item1 = (KLineEntity) mAdapter.getItem(l);
            float high = item1.high;
            float low = item1.low;

            high = getMainY(high);
            low = getMainY(low);

            float x = translateXtoX(getX(l));
            int i = dp2();
            if (orderRecordEntity.isBuy()) {
                float rectL = dp6();

                RectF rectF = ObjectPool.obtainRectF();
                rectF.set(x - rectL, low + dp5(), x + rectL, low + dp5() + rectL * 2);
                float centerX = rectF.centerX();

                Path path = ObjectPool.obtainPath();
                path.moveTo(centerX - rectL / 2, rectF.top + 0.5f);
                path.lineTo(centerX + rectL / 2, rectF.top + 0.5f);
                path.lineTo(centerX, rectF.top - dp3());
                path.close();
                canvas.drawPath(path, ((MainDraw) mMainDraw).mGreenPaint);
                canvas.drawRoundRect(rectF, (float) i, (float) i, ((MainDraw) mMainDraw).mGreenPaint);
                float b1 = measureBuyTextWidth("B");
                canvas.drawText("B", centerX - b1 * 0.5f, fixTextY(mBuyTextPaint, rectF.centerY()), mBuyTextPaint);

                ObjectPool.recyclePath(path);
                ObjectPool.recycleRectF(rectF);
            } else {
                float rectL = dp6();

                RectF rectF = ObjectPool.obtainRectF();
                rectF.set(x - rectL, high - dp5() - rectL * 2, x + rectL, high - dp5());
                float centerX = rectF.centerX();

                Path path = ObjectPool.obtainPath();
                path.moveTo(centerX - rectL / 2, rectF.bottom - 0.5f);
                path.lineTo(centerX + rectL / 2, rectF.bottom - 0.5f);
                path.lineTo(centerX, rectF.bottom + dp3());
                path.close();
                canvas.drawPath(path, ((MainDraw) mMainDraw).mRedPaint);
                canvas.drawRoundRect(rectF, (float) i, (float) i, ((MainDraw) mMainDraw).mRedPaint);
                float b1 = measureBuyTextWidth("S");
                canvas.drawText("S", centerX - b1 * 0.5f, fixTextY(mBuyTextPaint, rectF.centerY()), mBuyTextPaint);

                ObjectPool.recyclePath(path);
                ObjectPool.recycleRectF(rectF);
            }
        }
    }

    private float buyTextWidth = 0f;
    private float sellTextWidth = 0f;

    private float measureBuyTextWidth(String text) {
        if (TextUtils.equals("B", text)) {
            if (buyTextWidth == 0f) {
                buyTextWidth = mBuyTextPaint.measureText(text);
            }
            return buyTextWidth;
        }
        if (TextUtils.equals("S", text)) {
            if (sellTextWidth == 0f) {
                sellTextWidth = mBuyTextPaint.measureText(text);
            }
            return buyTextWidth;
        }
        return 0f;
    }

    private Rect getTextRect(String text, Paint textPaint) {
        Rect rect = new Rect();
        textPaint.getTextBounds(text, 0, text.length(), rect);
        return rect;
    }

    private float fixSelectY(float selectY) {
        float lineY = selectY;
        if (lineY > mMainRect.bottom && lineY <= mMainRect.bottom + mBottomTimeHeigh) {
            lineY = mMainRect.bottom;
        }
        return lineY;
    }

    //在屏幕上画
    private void onDrawScreen(Canvas canvas) {
        if (mMainDraw != null) {
            mMainDraw.drawOnScreen(canvas, this);
        }

        if (!mMainChildDrawSelectedList.isEmpty()) {
            for (var iChartDraw : mMainChildDrawSelectedList) {
                iChartDraw.drawOnScreen(canvas, this);
            }
        }
//        if (mMainChildDraw != null && mMainDraw != null /*&& !((MainDraw)mMainDraw).isDrawMinute()*/) {
//            mMainChildDraw.drawOnScreen(canvas, this);
//        }

        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                simpleChartDraw.drawOnScreen(canvas, this);
            }
        }
    }

    private final LruCache<String, Float> mSelectedValueTextTextCache = new LruCache<>(16);

    private float measureSelectedValueTextTextWidth(String text) {
        if (TextUtils.isEmpty(text)) {
            return 0;
        }
        Float timeTextWidth = mSelectedValueTextTextCache.get(text);
        if (timeTextWidth == null) {
            timeTextWidth = mSelectedValueTextTextPaint.measureText(text);
            mSelectedValueTextTextCache.put(text, timeTextWidth);
        }
        return timeTextWidth;
    }

    private float mSelectedTextHeight = -1f;

    // 初始化字体测量基准
    private float measureSelectedTextHeightIfNeed() {
        if (mSelectedTextHeight < 0) {
            mSelectedValueTextPaint.getFontMetrics(mFontMetrics);
            mSelectedTextHeight = mFontMetrics.descent - mFontMetrics.ascent;
        }
        return mPriceTextHeight;
    }

    /**
     * 十字线
     *
     * @param canvas
     */
    private void drawSelectedText(Canvas canvas) {
        if(!isEnableCrossLine) {
            return;
        }
        if (isShowCrossLine && mSelectedIndex >= 0) {
            KLineImpl point = (KLineImpl) getItem(mSelectedIndex);
            if (point == null) {
                return;
            }
            float textHeight = measureSelectedTextHeightIfNeed();
            String text = "";
            float selectedScaleY;
            if (!IS_SELECTED_LINE_DRAW_CLOSE_PRICE) {
                selectedScaleY = fixSelectY(mSelectedY);
                float selectedPrice = getMainYValue(selectedScaleY);//todo 十字线价格
                if (selectedScaleY <= mMainRect.bottom) {
                    //在主图里面
                    if (mMainDraw.getValueFormatter() != null) {
                        text = mMainDraw.getValueFormatter().format(selectedPrice);
                    } else {
                        text = formatValue(selectedPrice);
                    }
                    float bid = getCurrentPrice();
                    double rate = bid == 0 ? 0.0 : (selectedPrice - bid) / bid;
                    String rateStr = (rate > 0 ? "+" : "") + ExpandKt.formatProductPrice(rate * 100, 2, false, "--") + "%";

                    //十字线 滑动到k线的最下边的时候 这个rect 会产出屏幕 所以控制了一下
                    if (selectedScaleY + textHeight / 2 > getHeight()) {
                        selectedScaleY = getHeight() - textHeight / 2;
                    } else if(selectedScaleY - textHeight <= getTop()) {
                        selectedScaleY = textHeight;
                    }

                    mSelectedValueTextTextPaint.getFontMetrics(mFontMetrics);
                    float textWidth = measureSelectedValueTextTextWidth(text);
                    float rateTextWidth = measureSelectedValueTextTextWidth(rateStr);
                    float bothMaxWidth = Math.max(textWidth, rateTextWidth);

                    RectF bgRectf = ObjectPool.obtainRectF();
                    bgRectf.set(
                            mWidth - bothMaxWidth - KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER * 2,
                            selectedScaleY - textHeight,
                            mWidth - KLINE_GRID_VALUE_MARGIN_RIGHT,
                            selectedScaleY + textHeight
                    );
                    canvas.drawRoundRect(
                            bgRectf,
                            KLINE_CURRENT_PRICE_VALUE_BORDER_RAD, KLINE_CURRENT_PRICE_VALUE_BORDER_RAD,
                            mSelectedValueBackgroundPaint);
                    canvas.drawText(text, mWidth - textWidth - KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER,
                            selectedScaleY - mFontMetrics.descent, mSelectedValueTextTextPaint);
                    canvas.drawText(rateStr, mWidth - rateTextWidth - KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER,
                            selectedScaleY + Math.abs(mFontMetrics.ascent), mSelectedValueTextTextPaint);

                    ObjectPool.recycleRectF(bgRectf);
                } else {
                    if (!mChildDrawSelectedList.isEmpty()) {
                        for (var simpleChartDraw : mChildDrawSelectedList) {
                            if (selectedScaleY > simpleChartDraw.rect.top && selectedScaleY <= simpleChartDraw.rect.bottom) {
                                if (simpleChartDraw.getValueFormatter() != null) {
                                    text = simpleChartDraw.getValueFormatter().format(simpleChartDraw.getChildYValue(selectedScaleY));
                                } else {
                                    text = formatValue(simpleChartDraw.getChildYValue(selectedScaleY));
                                }
                            }
                        }
                        //十字线 滑动到k线的最下边的时候 这个rect 会产出屏幕 所以控制了一下
                        if (selectedScaleY + textHeight / 2 > getHeight()) {
                            selectedScaleY = getHeight() - textHeight / 2;
                        }

                        float textWidth = measureSelectedValueTextTextWidth(text);

                        RectF bgRectf = ObjectPool.obtainRectF();
                        bgRectf.set(
                                mWidth - textWidth - KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER * 2,
                                selectedScaleY - textHeight / 2,
                                mWidth - KLINE_GRID_VALUE_MARGIN_RIGHT,
                                selectedScaleY + textHeight / 2
                        );
                        canvas.drawRoundRect(
                                bgRectf,
                                KLINE_CURRENT_PRICE_VALUE_BORDER_RAD, KLINE_CURRENT_PRICE_VALUE_BORDER_RAD,
                                mSelectedValueBackgroundPaint);
                        canvas.drawText(text, mWidth - textWidth - KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER,
                                fixTextY(mSelectedValueTextTextPaint, selectedScaleY), mSelectedValueTextTextPaint);

                        ObjectPool.recycleRectF(bgRectf);
                    }
                }
            }

            String selectedTimeText = mAdapter.getDate(mSelectedIndex);
            float selectedTimeWidth = measureSelectedValueTextTextWidth(selectedTimeText);
            float timeSelectedX = translateXtoX(getX(mSelectedIndex));
            float left = timeSelectedX - selectedTimeWidth * 0.5f - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER;
            float right = timeSelectedX + selectedTimeWidth * 0.5f + KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER;
            if (left < 0) {
                left = 0;
                right = selectedTimeWidth + KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER;
            }
            RectF selectedTimeRectF = ObjectPool.obtainRectF();
            selectedTimeRectF.set(
                    left,
                    mMainRect.bottom,
                    right,
                    mMainRect.bottom + textHeight);
            canvas.drawRoundRect(selectedTimeRectF,
                    KLINE_CURRENT_PRICE_VALUE_BORDER_RAD,
                    KLINE_CURRENT_PRICE_VALUE_BORDER_RAD,
                    mSelectedValueBackgroundPaint);
            canvas.drawText(selectedTimeText,
                    selectedTimeRectF.centerX() - selectedTimeWidth * 0.5f,
                    fixTextY(mSelectedValueTextTextPaint, selectedTimeRectF.centerY()),
                    mSelectedValueTextTextPaint);

            ObjectPool.recycleRectF(selectedTimeRectF);
        }
    }

    private float getCurrentPrice() {
        if (currentPrice != null) {
            return currentPrice;
        }
        KLineImpl lastItem = (KLineImpl) getItem(mItemCount - 1);
        if (lastItem == null) {
            return 0;
        }
        return lastItem.getClosePrice();
    }

    private void drawCurrentPrice(Canvas canvas) {
        if (mItemCount <= 0) {
            return;
        }
        float currentPrice = getCurrentPrice();
        String closePriceStr = getValueFormatter().format(currentPrice);
        mCurrentPriceMainY = getMainY(currentPrice);
        Paint.FontMetrics fm = mCurrentPriceTextPaint.getFontMetrics();
        float textWidth = mCurrentPriceTextPaint.measureText(closePriceStr);
        float textHeight = fm.descent - fm.ascent;

        if (mMainRect.top > mCurrentPriceMainY) {
            mCurrentPriceMainY = mMainRect.top + textHeight / 2;
        }
        if (mMainRect.bottom < mCurrentPriceMainY) {
            mCurrentPriceMainY = mMainRect.bottom - textHeight / 2;
        }
        mCurrentPriceX = translateXtoX(getX(mItemCount - 1));

        //解决在不显示现价线的情况下，分时没有呼吸灯
        //不显示现价线的情况下，不会计算 mCurrentPriceMainY 和 mCurrentPriceX
        if (!isDrawCurrentPriceLine) {
            return;
        }

        mCurrentPriceTextPaint.setColor(greenColor);
        mCurrentPriceLinePaint.setColor(greenColor);

        if (mWidth - mCurrentPriceX >= mRightTextContentWidth) {
            RectF bgRectf = ObjectPool.obtainRectF();
            bgRectf.set(
                    mWidth - textWidth /*- KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER*/ - TRADE_TYPE_ROUND_BG_PADDING * 2,
                    mCurrentPriceMainY - dp18() / 2,
                    mWidth /*- KLINE_GRID_VALUE_MARGIN_RIGHT + KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER*/,
                    mCurrentPriceMainY + dp18() / 2
            );

            canvas.drawRoundRect(bgRectf, dp3(), dp3(), mBackgroundPaint);

            mCurrentPriceBorderPaint.setStyle(Paint.Style.FILL);
            mCurrentPriceBorderPaint.setColor(greenColor);
            mCurrentPriceBorderPaint.setAlpha(36);
            canvas.drawRoundRect(bgRectf, dp3(), dp3(), mCurrentPriceBorderPaint);

            canvas.drawText(closePriceStr, bgRectf.centerX() - textWidth / 2, fixTextY(mCurrentPriceTextPaint, mCurrentPriceMainY), mCurrentPriceTextPaint);

            Path path = ObjectPool.obtainPath();
            path.moveTo(mCurrentPriceX + mCandleWidth * mScaleX / 2, mCurrentPriceMainY);
            //文字宽距左显示
//            path.lineTo(mWidth - mRightTextContentWidth, mCurrentPriceMainY);
            //文字距右显示
            path.lineTo(bgRectf.left, mCurrentPriceMainY);
            canvas.drawPath(path, mCurrentPriceLinePaint);
            mCurrentPriceClickRect = null;

            ObjectPool.recyclePath(path);
            ObjectPool.recycleRectF(bgRectf);
        } else {
            float x = mMainRect.right + mOverScrollRange1 - textWidth / 2;
            if (mCurrentPriceClickRect == null) {
                mCurrentPriceClickRect = ObjectPool.obtainRectF();
            }
            mCurrentPriceClickRect.set(x - textWidth * 0.5f - dp6(), mCurrentPriceMainY - dp18() / 2, x + textWidth * 0.5f + dp10() + dp6(), mCurrentPriceMainY + dp18() / 2);

            canvas.drawRoundRect(mCurrentPriceClickRect, dp3(), dp3(), mBackgroundPaint);

            mCurrentPriceBorderPaint.setStyle(Paint.Style.FILL);
            mCurrentPriceBorderPaint.setColor(greenColor);
            mCurrentPriceBorderPaint.setAlpha(36);
            canvas.drawRoundRect(mCurrentPriceClickRect, dp3(), dp3(), mCurrentPriceBorderPaint);

            Path path = ObjectPool.obtainPath();
            //leftLine
            path.moveTo(mMainRect.left, mCurrentPriceMainY);
            path.lineTo(mCurrentPriceClickRect.left, mCurrentPriceMainY);
            canvas.drawPath(path, mCurrentPriceLinePaint);

            path.reset();

            //rightLine
            path.moveTo(mCurrentPriceClickRect.right, mCurrentPriceMainY);
            path.lineTo(mMainRect.right, mCurrentPriceMainY);
            canvas.drawPath(path, mCurrentPriceLinePaint);

            canvas.drawText(closePriceStr, x - textWidth / 2, fixTextY(mCurrentPriceTextPaint, mCurrentPriceMainY), mCurrentPriceTextPaint);

            path.reset();
            //tri
            path.moveTo(mCurrentPriceClickRect.right - dp10(), mCurrentPriceMainY - dp3());
            path.lineTo(mCurrentPriceClickRect.right - dp7(), mCurrentPriceMainY);
            path.lineTo(mCurrentPriceClickRect.right - dp10(), mCurrentPriceMainY + dp3());
//            triPath.close();
            canvas.drawPath(path, mCurrentPricePopBorderPaint);

            ObjectPool.recyclePath(path);
        }
    }

    private void drawBuyAndSellLine(Canvas canvas) {
        if (!showBuyLine && !showSellLine) return;

        float padding = dp3();
        float bgR = dp2();
        RectF textRect = ObjectPool.obtainRectF();
        Path path = ObjectPool.obtainPath();
        // 买价线
        if (showBuyLine) {
            String buyPriceStr = formatValue(buyPrice);
            float buyPriceMainY = getMainY(buyPrice);
            mBuyPriceTextPaint.setColor(buyPriceColor);
            mBuyPriceTextPaint.getTextBounds(buyPriceStr, 0, buyPriceStr.length(), mTextBounds);
            float bgTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * padding;

            textRect.set(bgTextLeft, buyPriceMainY - mTextBounds.height() - 2 * padding, bgTextLeft + mTextBounds.width() + 2 * padding, buyPriceMainY);

            mBuyPriceTextBgPaint.setColor(buyPriceTextBgColor);
            mBuyPriceTextBgPaint.setStyle(Paint.Style.FILL);
            canvas.drawRoundRect(textRect, bgR, bgR, mBuyPriceTextBgPaint);

            mBuyPriceTextBgPaint.setColor(buyPriceColor);
            mBuyPriceTextBgPaint.setStyle(Paint.Style.STROKE);
            canvas.drawRoundRect(textRect, bgR, bgR, mBuyPriceTextBgPaint);

            canvas.drawText(buyPriceStr, textRect.left + padding, textRect.bottom - padding, mBuyPriceTextPaint);

            mBuyPriceLinePaint.setColor(buyPriceColor);

            path.reset();
            path.moveTo(mMainRect.left, buyPriceMainY);
            path.lineTo(bgTextLeft, buyPriceMainY);
            canvas.drawPath(path, mBuyPriceLinePaint);
        }

        // 卖价线
        if (showSellLine) {
            String sellPriceStr = formatValue(sellPrice);
            float sellPriceMainY = getMainY(sellPrice);
            mBuyPriceTextPaint.setColor(sellPriceColor);
            mBuyPriceTextPaint.getTextBounds(sellPriceStr, 0, sellPriceStr.length(), mTextBounds);
            float bgSellTextLeft = getWidth() - KLINE_GRID_VALUE_MARGIN_RIGHT - mTextBounds.width() - 2 * padding;

            textRect.set(bgSellTextLeft, sellPriceMainY, bgSellTextLeft + mTextBounds.width() + 2 * padding, sellPriceMainY + mTextBounds.height() + 2 * padding);

            mBuyPriceTextBgPaint.setColor(buyPriceTextBgColor);
            mBuyPriceTextBgPaint.setStyle(Paint.Style.FILL);
            canvas.drawRoundRect(textRect, bgR, bgR, mBuyPriceTextBgPaint);

            mBuyPriceTextBgPaint.setColor(sellPriceColor);
            mBuyPriceTextBgPaint.setStyle(Paint.Style.STROKE);
            canvas.drawRoundRect(textRect, bgR, bgR, mBuyPriceTextBgPaint);
            canvas.drawText(sellPriceStr, textRect.left + padding, textRect.bottom - padding, mBuyPriceTextPaint);

            mBuyPriceLinePaint.setColor(sellPriceColor);

            path.reset();
            path.moveTo(mMainRect.left, sellPriceMainY);
            path.lineTo(bgSellTextLeft, sellPriceMainY);
            canvas.drawPath(path, mBuyPriceLinePaint);
        }

        ObjectPool.recyclePath(path);
        ObjectPool.recycleRectF(textRect);
    }

    private void drawInstantBuyingSelling(Canvas canvas) {
        if (showInstantBuySell && (mWidth - mCurrentPriceX >= mRightTextContentWidth) && getCurrentPrice() != 0 && buyPrice != 0) {
            if (mItemCount <= 0) {
                return;
            }
            float currentPrice = getCurrentPrice();

            mCurrentPriceMainY = getMainY(currentPrice);

            String closePriceStr = getValueFormatter().format(currentPrice);
            float closePriceTextWidth = mCurrentPriceTextPaint.measureText(closePriceStr);
            float closeLeft = mWidth - closePriceTextWidth - TRADE_TYPE_ROUND_BG_PADDING * 2 - dp8();

            KLineEntity kLineEntity1 = (KLineEntity) getItem(mItemCount - 1);
            long l1 = kLineEntity1.timestampLong;
            long step = mAdapter.getStep();
            long l = l1 + step - System.currentTimeMillis();
            String s = formatTime(l, step);
            float left1 = mWidth - mCountDownTextPaint.measureText(s) - TRADE_TYPE_ROUND_BG_PADDING * 2 - dp8();

            closeLeft = Math.min(closeLeft, left1);

            String sell = sellStr + " " + currentPrice;
            String buy = buyStr + " " + buyPrice;

//            Paint.FontMetrics fm = mCurrentPriceTextPaint.getFontMetrics();
//            float textHeight = fm.descent - fm.ascent;
            float textWidth = mInstantBuyingSellingTextPaint.measureText(buy);

            RectF rect = ObjectPool.obtainRectF();

            float y = mCurrentPriceMainY - dp18() / 2 - dp1();
            float y1 = mCurrentPriceMainY + dp18() / 2 + dp1();

            rect.set((closeLeft - textWidth - dp4() * 2), (y - dp18() / 2), (closeLeft), (y + dp18() / 2));
            canvas.drawRoundRect(rect, dp3(), dp3(), mBackgroundPaint);

            mInstantBuyingSellingTextPaint.setColor(mFallColor);
            mInstantBuyingSelling.setColor(mFallColor);
            canvas.drawRoundRect(rect, dp3(), dp3(), mInstantBuyingSelling);

            canvas.drawText(buy, rect.centerX() - textWidth / 2, fixTextY(mInstantBuyingSellingTextPaint, rect.centerY()), mInstantBuyingSellingTextPaint);

            rect.set((closeLeft - textWidth - dp4() * 2), (y1 - dp18() / 2), (closeLeft), (y1 + dp18() / 2));
            canvas.drawRoundRect(rect, dp3(), dp3(), mBackgroundPaint);

            mInstantBuyingSellingTextPaint.setColor(mRateColor);
            mInstantBuyingSelling.setColor(mRateColor);
            canvas.drawRoundRect(rect, dp3(), dp3(), mInstantBuyingSelling);
            canvas.drawText(sell, rect.centerX() - textWidth / 2, fixTextY(mInstantBuyingSellingTextPaint, rect.centerY()), mInstantBuyingSellingTextPaint);

            ObjectPool.recycleRectF(rect);

        }
    }

    /**
     * 倒数计时
     *
     * @param canvas
     */
    private void drawCountDownTime(Canvas canvas) {
        if (showCountDown && (mWidth - mCurrentPriceX >= mRightTextContentWidth)) {
            if (mItemCount <= 0) {
                return;
            }
            if (mItemCount >= 1) {

                KLineEntity kLineEntity1 = (KLineEntity) getItem(mItemCount - 1);
                String date1 = kLineEntity1.timestamp;
                long l1 = 0;
                long step = mAdapter.getStep();

                if (step == KChartAdapter.STEP_ONE_MONTH) {
                    step = DateUtil.getMaxDayOfMonthTimeMillis(l1);
                }

                long l = l1 + step - System.currentTimeMillis();
                if (l <= 0) {
                    l = 0L;
                }
                String s = DateUtil.formatTime(l);

                float currentPrice = getCurrentPrice();

                mCurrentPriceMainY = getMainY(currentPrice);
                mCurrentPriceX = translateXtoX(getX(mItemCount - 1));

                String buy = s;

//                Paint.FontMetrics fm = mCountDownTextPaint.getFontMetrics();
//                float textHeight = fm.descent - fm.ascent;
                float textWidth = mCountDownTextPaint.measureText(buy);

                RectF rect = ObjectPool.obtainRectF();

                float y = 0;

                float left;
                float right;

                float x = mMainRect.right + mOverScrollRange1 - textWidth / 2;

                if (mWidth - mCurrentPriceX >= mRightTextContentWidth) {
                    left = mWidth - textWidth - TRADE_TYPE_ROUND_BG_PADDING * 2;
                    right = mWidth;
                } else {
                    right = x + textWidth * 0.5f + dp10() + dp6();
                    left = right - textWidth /*- KLINE_GRID_VALUE_MARGIN_RIGHT - KLINE_CURRENT_PRICE_VALUE_MARGIN_BORDER*/ - TRADE_TYPE_ROUND_BG_PADDING * 2;
                }
                float top;
                float bottom;

                if (isDrawCurrentPriceLine) {
                    y = mCurrentPriceMainY + dp20();
                    top = (y - dp18() / 2);
                    bottom = (y + dp18() / 2);
                } else {
                    y = mCurrentPriceMainY;
                    top = (y - dp18() / 2);
                    bottom = (y + dp18() / 2);
                }

                rect.set(left, top, (right), bottom);

                mCountDownTextPaint.setColor(greenColor);
                mCountDownPaint.setColor(greenColor);
                mCountDownPaint.setAlpha(36);
//                mInstantBuyingSelling.setAlpha(36);

                canvas.drawRoundRect(rect, dp3(), dp3(), mBackgroundPaint);
                canvas.drawRoundRect(rect, dp3(), dp3(), mCountDownPaint);
                canvas.drawText(buy, rect.centerX() - textWidth / 2, fixTextY(mCountDownTextPaint, rect.centerY()), mCountDownTextPaint);

                if (!isDrawCurrentPriceLine) {
                    if (mWidth - mCurrentPriceX >= mRightTextContentWidth) {
                        Path path = ObjectPool.obtainPath();
                        path.moveTo(mCurrentPriceX + mCandleWidth * mScaleX / 2, mCurrentPriceMainY);
                        path.lineTo(rect.left, mCurrentPriceMainY);
                        canvas.drawPath(path, mCurrentPriceLinePaint);
                        ObjectPool.recyclePath(path);
                    } else {
                        Path path = ObjectPool.obtainPath();
                        //leftLinePath
                        path.moveTo(mMainRect.left, rect.centerY());
                        path.lineTo(rect.left, rect.centerY());
                        canvas.drawPath(path, mCurrentPriceLinePaint);

                        path.reset();
                        //rightLinePath
                        path.moveTo(rect.right, rect.centerY());
                        path.lineTo(mMainRect.right, rect.centerY());
                        canvas.drawPath(path, mCurrentPriceLinePaint);
                        ObjectPool.recyclePath(path);
                    }
                }
                ObjectPool.recycleRectF(rect);
            }
        }
    }

    public void resetMoveAbelLine() {
        enableDragStatus = false;
        isDragging = false;
        invalidate();
    }

    public boolean isDragging() {
        return isDragging;
    }

    private boolean isInTp(MotionEvent e) {
        if (orderData == null) {
            return false;
        }
        if (orderData.getTakeProfit() > mMainMaxValue || orderData.getTakeProfit() < mMainMinValue) {
            return false;
        }
        if (bgTpRect == null) {
            return false;
        }
        RectF tpSlRect = ObjectPool.obtainRectF();
        tpSlRect.set(0, bgTpRect.top, getWidth(), bgTpRect.bottom);
        Log.d("isInTpSL", "isInTpSL===" + tpSlRect.top + "====" + tpSlRect.bottom);
        Log.d("isInTpSL", "isInTpSL=e.getX()==" + e.getX() + "=e.getY()===" + e.getY());
        boolean isConstains = tpSlRect.contains(e.getX(), e.getY());
        ObjectPool.recycleRectF(tpSlRect);
        return isConstains;
    }

    private boolean isInSL(MotionEvent e) {
        if (orderData == null) {
            return false;
        }
        if (orderData.getStopLoss() > mMainMaxValue || orderData.getStopLoss() < mMainMinValue) {
            return false;
        }
        if (bgSlRect == null) {
            return false;
        }
        RectF tpSlRect = ObjectPool.obtainRectF();
        tpSlRect.set(0, bgSlRect.top, getWidth(), bgSlRect.bottom);
        Log.d("isInTpSL", "isInTpSL===" + tpSlRect.top + "====" + tpSlRect.bottom);
        Log.d("isInTpSL", "isInTpSL=e.getX()==" + e.getX() + "=e.getY()===" + e.getY());
        boolean isConstains = tpSlRect.contains(e.getX(), e.getY());
        ObjectPool.recycleRectF(tpSlRect);
        return isConstains;
    }

    private boolean isInOrderRect(MotionEvent e) {
        if (orderData == null) {
            return false;
        }
        if (orderData.getOpenPrice() > mMainMaxValue || orderData.getOpenPrice() < mMainMinValue) {
            return false;
        }
        if (bgOrderRect == null) {
            return false;
        }
        RectF tpSlRect = ObjectPool.obtainRectF();
        tpSlRect.set(0, bgOrderRect.top, getWidth(), bgOrderRect.bottom);
        Log.d("isInOrderRect", "isInOrderRect===" + tpSlRect.top + "====" + tpSlRect.bottom);
        Log.d("isInOrderRect", "isInOrderRect=e.getX()==" + e.getX() + "=e.getY()===" + e.getY());
        boolean isConstains = tpSlRect.contains(e.getX(), e.getY());
        ObjectPool.recycleRectF(tpSlRect);
        return isConstains;
    }

    public boolean isInTpCancelIcon(MotionEvent e) {
        if (tpIconRect == null) {
            return false;
        }
        float padding = dp3();
        RectF rectF = ObjectPool.obtainRectF();
        rectF.set(tpIconRect.left - padding, tpIconRect.top - padding, tpIconRect.right + padding, tpIconRect.bottom + padding);
        boolean isConstains = rectF.contains(e.getX(), e.getY());
        ObjectPool.recycleRectF(rectF);
        return isConstains;
    }

    public boolean isInSlCancelIcon(MotionEvent e) {
        if (slIconRect == null) {
            return false;
        }
        float padding = dp3();
        RectF rectF = ObjectPool.obtainRectF();
        rectF.set(slIconRect.left - padding, slIconRect.top - padding, slIconRect.right + padding, slIconRect.bottom + padding);
        boolean isConstains = rectF.contains(e.getX(), e.getY());
        ObjectPool.recycleRectF(rectF);
        return isConstains;
    }

    @Override
    protected boolean onClick(MotionEvent e) {
        if (isInTpCancelIcon(e)) {
            if (onTakeProfitStopLossChanged != null) {
                onTakeProfitStopLossChanged.onCancelTp();
            }
            return true;
        }

        if (isInSlCancelIcon(e)) {
            if (onTakeProfitStopLossChanged != null) {
                onTakeProfitStopLossChanged.onCancelSp();
            }
            return true;
        }

        if (isInOrderRect(e)) {
            setLineViewSelected(0);
            originW = bgOrderRect.width();
            if (onTakeProfitStopLossChanged != null) {
                onTakeProfitStopLossChanged.onPositionLineClick();
            }
            invalidate();
            return true;
        }
        if (isInTp(e)) {
            setLineViewSelected(1);
            originW = bgTpRect.width();
            if (onTakeProfitStopLossChanged != null) {
                onTakeProfitStopLossChanged.onTPLineClick();
            }
            invalidate();
            return true;
        }

        if (isInSL(e)) {
            setLineViewSelected(2);
            originW = bgSlRect.width();
            if (onTakeProfitStopLossChanged != null) {
                onTakeProfitStopLossChanged.onSLLineClick();
            }
            invalidate();
            return true;
        }
        if (mCurrentPriceClickRect != null) {
            if (mCurrentPriceClickRect.contains(e.getX(), e.getY())) {
                setUnSelected();
                if (mCurrentPriceClickListener != null && !mCurrentPriceClickListener.onCurrentPriceClick()) {
                    scrollStart();
                }
                return true;
            }
        }
        if (mFullScreenDstRect != null && mFullScreenDstRect.contains(e.getX(), e.getY()) && mOnFullScreenIconClickListener != null) {
            //点击了全屏图片
            mOnFullScreenIconClickListener.onFullScreenIconClick();
            return true;
        }

        if (mIsSupportChangIndex) {
            if (mMainRect.contains((int) e.getX(), (int) e.getY())) {
                //点击了主图
                if (mOnKChartViewClickListener != null) {
                    mOnKChartViewClickListener.onKChartViewMainClick();
                }
                return true;
            }

            //点击了幅图 -- 点击副图的最后一个
            // TODO: 2022/6/7 应该增加点击副图的最后一个 和其他的区分 现在除了最后一个副图，其他没反应
            if (!mChildDrawSelectedList.isEmpty()) {
                SimpleChartDraw simpleChartDraw = mChildDrawSelectedList.get(mChildDrawSelectedList.size() - 1);
                if (simpleChartDraw.rect.contains((int) e.getX(), (int) e.getY())) {
                    if (mOnKChartViewClickListener != null) {
                        mOnKChartViewClickListener.onKChartViewChildClick();
                    }
                    return true;
                }
            }
        }

        return super.onClick(e);
    }

    private void setLineViewSelected(int type) {
        switch (type) {
            // 订单线
            case 0:
                isActivedOrderLine = !isActivedOrderLine;
                isActivedTPLine = false;
                isActivedSLLine = false;
                break;
            // 止盈线
            case 1:
                isActivedTPLine = !isActivedTPLine;
                isActivedOrderLine = false;
                isActivedSLLine = false;
                break;
            // 止损线
            case 2:
                isActivedSLLine = !isActivedSLLine;
                isActivedOrderLine = false;
                isActivedTPLine = false;
                break;
        }
    }

    private void cancelLineViewSelected() {
        isActivedOrderLine = false;
        isActivedTPLine = false;
        isActivedSLLine = false;
        invalidate();
    }

    float mY;

    public float getDownY() {
        if (IS_SELECTED_LINE_DRAW_CLOSE_PRICE) {
            float t = dp5();
            if (mMainRect.bottom <= mY + t) {
                return mMainRect.bottom - t;
            }
            if (mMainRect.top >= mY - t) {
                return mMainRect.top + t;
            }
        } else {
            if (mMainRect.top >= mY) {
                return mMainRect.top;
            }
        }
        return mY;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        mY = event.getY();
        switch (event.getActionMasked()) {
            case MotionEvent.ACTION_DOWN: {
                // 禁止父控件拦截事件，由Move事件判断是否拦截
                ViewParent parent = getParent();
                if (parent != null) {
                    parent.requestDisallowInterceptTouchEvent(true);
                }
                if(isInSlCancelIcon(event) || isInTpCancelIcon(event)) {
                    enableDragStatus = false;
                } else if (isInOrderRect(event)) {
                    enableDragStatus = isActivedOrderLine;
                } else if (isInTp(event)) {
                    enableDragStatus = isActivedTPLine;
                } else if (isInSL(event)) {
                    enableDragStatus = isActivedSLLine;
                } else {
                    enableDragStatus = false;
                    isDragging = false;
                    cancelLineViewSelected();
                }
                break;
            }
            case MotionEvent.ACTION_MOVE: {
                if (enableDragStatus) {
                    float highY = getMainY(mMainMaxValueY);
                    float lowY = getMainY(mMainMinValueY);
                    if (mY > lowY) {
                        mY = lowY;
                    }
                    if (mY < highY) {
                        mY = highY;
                    }
                    tpSlMovePrice = getMainYValue(mY);
                    isDragging = true;
                    cancelLineViewSelected();
                    if (onTakeProfitStopLossChanged != null && orderData != null) {
                        estimatedPnl = onTakeProfitStopLossChanged.onTpSlMoving(tpSlMovePrice,
                                orderData.getOpenPrice(), orderData.getVolume(), orderData.getOrderType());
                    }
                    invalidate();

                    return true;
                }
            }
            case MotionEvent.ACTION_UP: {
                if (enableDragStatus) {
                    tpSlMovePrice = getMainYValue(mY);
                    invalidate();
                    if (onTakeProfitStopLossChanged != null) {
                        int type = orderData.isBuy()
                                ? (tpSlMovePrice >= sellPrice ? TYPE_TP : TYPE_SL)
                                : (tpSlMovePrice <= buyPrice ? TYPE_TP : TYPE_SL);
                        onTakeProfitStopLossChanged.onTakeProfitStopLossChanged(tpSlMovePrice, type);
                    }
                    return true;
                }
            }
        }

        return super.onTouchEvent(event);
    }

    private float mPriceTextHeight = -1;
    //复用测量文字
    private final Paint.FontMetrics mFontMetrics = new Paint.FontMetrics();

    // 初始化字体测量基准
    private float measurePriceTextHeightIfNeed() {
        if (mPriceTextHeight < 0) {
            mPriceTextPaint.getFontMetrics(mFontMetrics);
            mPriceTextHeight = mFontMetrics.descent - mFontMetrics.ascent;
        }
        return mPriceTextHeight;
    }

    private final LruCache<String, Integer> mPriceTextWidthCache = new LruCache<>(16);

    private int measurePriceTextWidth(String price) {
        if (TextUtils.isEmpty(price)) {
            return 0;
        }
        Integer width = mPriceTextWidthCache.get(price);
        if (width == null) {
            mPriceTextPaint.getTextBounds(price, 0, price.length(), mTextBounds);
            width = mTextBounds.width();
            mPriceTextWidthCache.put(price, width);
        }
        return width;
    }

    private final LruCache<String, Float> mTimeTextWidthCache = new LruCache<>(16);

    private float measureTimeTextWidth(String date) {
        if (TextUtils.isEmpty(date)) {
            return 0;
        }
        Float timeTextWidth = mTimeTextWidthCache.get(date);
        if (timeTextWidth == null) {
            timeTextWidth = mTimePaint.measureText(date);
            mTimeTextWidthCache.put(date, timeTextWidth);
        }
        return timeTextWidth;
    }

    /**
     * 画文字
     *
     * @param canvas
     */
    private void drawText(Canvas canvas) {
        float textHeight = measurePriceTextHeightIfNeed();
        //--------------画上方k线图的值-------------
        if (mMainDraw != null) {
            String textValue = formatValue(mMainMaxValue);
            int width = measurePriceTextWidth(textValue);
            mRightTextContentWidth = Math.max(width + 10, mRightTextContentWidth);
            //距右边显示
            //距左边显示，并且所有的值左边对其
//            canvas.drawText(maxText, (getWidth() - mRightTextContentWidth) + 5, baseLine + mMainRect.top, mTextPricePaint);
//            canvas.drawText(maxText, 0, baseLine + mMainRect.top, mTextPricePaint);

            textValue = formatValue(mMainMinValue);
            width = measurePriceTextWidth(textValue);
            mRightTextContentWidth = Math.max(width + 10, mRightTextContentWidth);
            //距左边显示，并且所有的值左边对其
//            canvas.drawText(minText, (getWidth() - mRightTextContentWidth) + 5, mMainRect.bottom - textHeight + baseLine, mTextPricePaint);
//            canvas.drawText(minText, 0, mMainRect.bottom - textHeight + baseLine, mTextPricePaint);
            float rowValue = (mMainMaxValue - mMainMinValue) / mGridRows;
            int rowSpace = mMainRect.height() / mGridRows;

            for (int i = 0; i <= mGridRows; i++) {
                textValue = formatValue(rowValue * (mGridRows - i) + mMainMinValue);//todo 刻度价格
                width = measurePriceTextWidth(textValue);
//                int h = rect.height();
                mRightTextContentWidth = Math.max(width + (int) KLINE_GRID_VALUE_MARGIN_RIGHT, mRightTextContentWidth);
                float y = rowSpace * i + mMainRect.top;
                if (i == mGridRows) {
                    y = y - textHeight * 0.5f;
                } else if (i == 0) {
                    y = y + textHeight * 0.5f;
                }
                canvas.drawText(textValue, (getWidth() - width) - KLINE_GRID_VALUE_MARGIN_RIGHT, fixTextY(mPriceTextPaint, y), mPriceTextPaint);
//                canvas.drawText(text, (getWidth() - mRightTextContentWidth) + 5, fixTextY(rowSpace * i + mMainRect.top), mTextPricePaint);
//                canvas.drawText(text, 0, fixTextY(rowSpace * i + mMainRect.top), mTextPricePaint);
            }
        }
        String text;
        int width;
        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                text = formatValue(simpleChartDraw.mMaxValue + (mValueHeigh / simpleChartDraw.mChildScaleY));
                width = measurePriceTextWidth(text);
                canvas.drawText(text, (getWidth() - width) - KLINE_GRID_VALUE_MARGIN_RIGHT, fixTextY(mPriceTextPaint, simpleChartDraw.rect.top + textHeight * 0.5f), mPriceTextPaint);

                text = formatValue(simpleChartDraw.mMinValue);
                width = measurePriceTextWidth(text);
                canvas.drawText(text, (getWidth() - width) - KLINE_GRID_VALUE_MARGIN_RIGHT, fixTextY(mPriceTextPaint, simpleChartDraw.rect.bottom - textHeight * 0.5f), mPriceTextPaint);
            }
        }

        float y = fixTextY(mTimePaint, mMainRect.bottom + dp7_5());
//        float y = mMainRect.bottom  + baseLine;

//        float startX = getX(mStartIndex) - mPointWidth / 2;
//        float stopX = getX(mStopIndex) + mPointWidth / 2;
        //时间位置跟线绑定

        //一屏幕有多少个
        int i = (int) (mWidth / mScaleX / mPointWidth);

        TIME_SHOW_INDEX_INTEGER = i / 4;
        if (TIME_SHOW_INDEX_INTEGER != 0) {
            int timeStartIndex = mStartIndex - mStartIndex % TIME_SHOW_INDEX_INTEGER;
            int timeStopIndex = mStopIndex - mStopIndex % TIME_SHOW_INDEX_INTEGER + TIME_SHOW_INDEX_INTEGER;
            if (timeStopIndex >= mItemCount - 1) {
                timeStopIndex = mItemCount - 1;
            }
            String date;
            float v;
            for (int index = timeStartIndex; index <= timeStopIndex; index += TIME_SHOW_INDEX_INTEGER) {
                if (index % TIME_SHOW_INDEX_INTEGER == 0) {
                    v = translateXtoX(getX(index));
//                    String text = formatDateTime(mAdapter.getDate(index));
                    date = mAdapter.getDate(index);
                    canvas.drawText(date, v - measureTimeTextWidth(date) / 2, y, mTimePaint);
                }
            }
        }

        //固定时间位置
//        for (int i = 1; i < mGridColumns; i++) {
//            float translateX = xToTranslateX(columnSpace * i);
//            if (translateX >= startX && translateX <= stopX) {
//                int index = indexOfTranslateX(translateX);
//                String text = formatDateTime(mAdapter.getDate(index));
//                canvas.drawText(text, columnSpace * i - mTextPaint.measureText(text) / 2, y, mTextPaint);
//            }
//        }
//
//        float translateX = xToTranslateX(0);
//        if (translateX >= startX && translateX <= stopX) {
//            canvas.drawText(formatDateTime(getAdapter().getDate(mStartIndex)), 0, y, mTextPaint);
//        }
//        translateX = xToTranslateX(mWidth);
//        if (translateX >= startX && translateX <= stopX) {
//            String text = formatDateTime(getAdapter().getDate(mStopIndex));
//            canvas.drawText(text, mWidth - mTextPaint.measureText(text), y, mTextPaint);
//        }

//        if (!isFullScreen) {
//            if (mLastWith - mRightTextContentWidth < 0 || mLastWith - mRightTextContentWidth > 20) {
//                mWidth = getWidth() - mRightTextContentWidth;
//                initRect(getWidth(), getHeight());
//                invalidate();
//            }
//        }
    }

    private float mTextPaintBaseLine = -1f;

    private float measureTextPaintBaseLineIfNeed() {
        if (mTextPaintBaseLine < 0) {
            mTextPaint.getFontMetrics(mFontMetrics);
            float textHeight = mFontMetrics.descent - mFontMetrics.ascent;
            mTextPaintBaseLine = (textHeight - mFontMetrics.bottom - mFontMetrics.top) / 2;
        }
        return mTextPaintBaseLine;
    }

    /**
     * 画值
     *
     * @param canvas
     * @param position 显示某个点的值
     */
    float textY = 0;

    private void drawValue(Canvas canvas, int position) {
        textY = 0;
        float baseLine = measureTextPaintBaseLineIfNeed();
        float startY = mMainValueTextTop - dp1() + mMainSubValueTop + dp6();
        if (position >= 0 && position < mItemCount) {
            if (mMainDraw != null) {
                float y = mMainRect.top - dp1();
                float x = dp1();
                mMainDraw.drawText(canvas, this, position, x, y);
            }
            if (!mMainChildDrawSelectedList.isEmpty()) {
                for (var iChartDraw : mMainChildDrawSelectedList) {
                    float y = startY + textY;
                    float x = dp1();
                    iChartDraw.drawText(canvas, this, position, x, y);//主指标文案绘制
                    textY += (iChartDraw.getTextH() + dp5());
                }
            }

            if (!mChildDrawSelectedList.isEmpty()) {
                for (var simpleChartDraw : mChildDrawSelectedList) {
                    float y = simpleChartDraw.rect.top + baseLine;
                    float x = dp1();
                    simpleChartDraw.drawText(canvas, this, position, x, y);//副图指标文案
                }
            }
        }
    }

    RectF mFullScreenDstRect;
    Bitmap mFullScreenBitmap;
    Rect mFullScreenRect;
    Paint mFullScreenPaint;
    int mFullScreenHeight;
    int mFullScreenWidth = dp22();
    float mFullScreenMarginBottom;
    float mFullScreenMarginRight;

    private void drawFullScreen(Canvas canvas) {
        if (!isShowFullScreen) return;
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            //竖屏
            try {
                if (mFullScreenPaint == null) {
                    mFullScreenPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
                    setTextTypeFace(mFullScreenPaint);
                    mFullScreenPaint.setFilterBitmap(true);
                    mFullScreenBitmap = ((BitmapDrawable) getResources().getDrawable(R.drawable.ic_full_screen)).getBitmap();
                    mFullScreenHeight = dp22();
//                    mFullScreenWidth = ViewUtil.Dp2Px(getContext(), 25);
                    mFullScreenMarginBottom = dp20();
                    mFullScreenMarginRight = dp12();
                    mFullScreenRect = new Rect(0, 0, mFullScreenBitmap.getWidth(), mFullScreenBitmap.getHeight());
                    if (mFullScreenDstRect == null) {
                        mFullScreenDstRect = ObjectPool.obtainRectF();
                    }
                    if (isFullLeft) {
                        mFullScreenDstRect.set(
                                mFullScreenMarginRight,
                                mMainRect.bottom - mFullScreenMarginBottom - mFullScreenHeight * 0.5f,
                                mFullScreenWidth + mFullScreenMarginRight,
                                mMainRect.bottom - mFullScreenMarginBottom + mFullScreenHeight * 0.5f);
                    } else {
                        mFullScreenDstRect.set(
                                mMainRect.right - mFullScreenWidth - mFullScreenMarginRight,
                                mMainRect.bottom - mFullScreenMarginBottom - mFullScreenHeight * 0.5f,
                                mMainRect.right - mFullScreenMarginRight,
                                mMainRect.bottom - mFullScreenMarginBottom + mFullScreenHeight * 0.5f);
                    }

                    if (mOnFullScreenIconClickListener != null) {
                        mOnFullScreenIconClickListener.onFullScreenIconRectChanged(mFullScreenDstRect);
                    }
                }
                canvas.drawBitmap(mFullScreenBitmap, mFullScreenRect, mFullScreenDstRect, mFullScreenPaint);

            } catch (Exception e) {
                e.printStackTrace();
                FirebaseCrashlytics.getInstance().recordException(new Exception("BaseKChartView.drawFullScreen e" + e.getMessage()));
            }
        }

    }

    public int dp2px(float dp) {
        final float scale = getContext().getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    //格式化缓存，减少格式化次数
    private final LruCache<Float, String> formatLruCache = new LruCache<>(128);

    @Override
    public String formatValue(float value) {
        if (getValueFormatter() == null || getValueFormatter().getDigit() != digit) {
            setValueFormatter(new ValueFormatter(digit));
        }
        if (Float.isNaN(value)) {
            return "";
        }
        String format = formatLruCache.get(value);
        if (TextUtils.isEmpty(format)) {
            format = getValueFormatter().format(value);
            formatLruCache.put(value, format);
        }
        return format;
    }

    public void notifyChangedInvalidated() {
        mScroller.forceFinished(true);
        setScrollX((int) (mOverScrollRange1 / mScaleX));
    }

    /**
     * 重新计算并刷新线条
     */
    public void notifyChanged() {
        mItemCount = mAdapter != null ? mAdapter.getCount() : 0;
        if (mItemCount != 0) {
            mDataLen = (mItemCount - 1) * mPointWidth;
            if (mXs == null || mXs.length != mItemCount) {
                mXs = new float[mItemCount];
            }
            float x;
            for (int i = 0; i < mItemCount; i++) {
                x = i * mPointWidth;
                mXs[i] = x;
            }
            checkAndFixScrollX();
            if (mAdapter instanceof KChartAdapter) {
                //加载更多数据的处理
                int sizeForMoreScroll = ((KChartAdapter) mAdapter).getSizeForMoreScroll();
                int sizeForMoreScale = ((KChartAdapter) mAdapter).getSizeForMoreScale();
                Log.d("notifyChanged", "===notifyChanged==sizeForMoreScroll==" + sizeForMoreScroll);
                Log.d("notifyChanged", "===notifyChanged==sizeForMoreScale==" + sizeForMoreScale);
                ((KChartAdapter) mAdapter).setSizeForMoreScroll(0);
                ((KChartAdapter) mAdapter).setSizeForMoreScale(0);
                //解决设置全部数据时滑动到头部
                if (sizeForMoreScroll != Integer.MAX_VALUE) {
                    setScrollXForMore(sizeForMoreScroll);
                    changeFocusIndexForMore(sizeForMoreScale);
                } else {
                    scrollStart();
                }
            }
            Log.d("notifyChanged", "===notifyChanged==mScrollX==" + mScrollX);
            setTranslateXFromScrollX(mScrollX);
        } else {
            setScrollX((int) (mOverScrollRange1 / mScaleX));
        }

        invalidate();
    }

    @Override
    public void invalidate() {
        mHandler.removeMessages(MESSAGE_WHAT_TIMER);
        Message message = Message.obtain();
        message.what = MESSAGE_WHAT_INVALIDATE;
        mHandler.sendMessage(message);
    }

    public void postInvalidate() {
        mHandler.removeMessages(MESSAGE_WHAT_TIMER);
        Message message = Message.obtain();
        message.what = MESSAGE_WHAT_INVALIDATE;
        mHandler.sendMessage(message);
    }

    private void localInvalidate() {
        super.invalidate();
    }

    public int calculateSelectedX(float x) {
        if (mXs == null || mItemCount != mXs.length) {
            return mSelectedIndex;
        }
        int mSelectedIndex = indexOfTranslateX(xToTranslateX(x));
        if (mSelectedIndex < mStartIndex) {
            mSelectedIndex = mStartIndex;
        }
        if (mSelectedIndex > mStopIndex) {
            mSelectedIndex = mStopIndex;
        }
        return mSelectedIndex;
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        setTranslateXFromScrollX(mScrollX);
    }

    float mTranslateX1 = 0;

    @Override
    protected void onScaleChanged(float scale, float oldScale) {
        if (mFocusIndex == 0) {
            mFocusIndex = indexOfTranslateX(xToTranslateX(mFocusX));
            mTranslateX1 = xToTranslateX(mFocusX);
        }
        mScrollX = (int) (-(mTranslateX1 - mFocusX / scale) - getMinTranslateX());
        checkAndFixScrollX();
        setTranslateXFromScrollX(mScrollX);
        super.onScaleChanged(scale, oldScale);
    }

    private void changeFocusIndexForMore(int sizeForMore) {
        if (sizeForMore == 0) return;
        int temp = mFocusIndex;
        if (mFocusIndex != 0) {
            mFocusIndex = mFocusIndex - sizeForMore;
        }
    }

    private void setScrollXForMore(int sizeForMore) {
        if (sizeForMore == 0) return;
        int offsetX = getPointDistance(sizeForMore);
        setOffsetX(offsetX);
    }

    /**
     * 计算当前的显示区域
     */
    int mMainMaxIndex = -1;
    int mMainMinIndex = -1;
    float mMainMaxValueY = -1;
    float mMainMinValueY = -1;
    boolean isMaxAndMinEqual;

    private void calculateValue() {
        if (!isShowCrossLine()) {
            mSelectedIndex = -1;
        }
        mMainMaxValueY = Float.MIN_VALUE;
        mMainMinValueY = Float.MAX_VALUE;
        mMainMaxValue = Float.MIN_VALUE;
        mMainMinValue = Float.MAX_VALUE;

        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                simpleChartDraw.mMinValue = Float.MAX_VALUE;
                simpleChartDraw.mMaxValue = -Float.MAX_VALUE;
            }
        }
        setStartIndex(indexOfTranslateX(xToTranslateX(0)));
        setStopIndex(indexOfTranslateX(xToTranslateX(mWidth)));
        for (int i = mStartIndex; i <= mStopIndex; i++) {
            KLineImpl point = (KLineImpl) getItem(i);
            if (point == null) {
                continue;
            }
            if (mMainDraw != null) {
                float highPrice = point.getHighPrice();
                float lowPrice = point.getLowPrice();
                if (mMainMaxValueY < highPrice) {
                    mMainMaxValueY = highPrice;
                    mMainMaxIndex = i;
                }
                if (mMainMinValueY > lowPrice) {
                    mMainMinValueY = lowPrice;
                    mMainMinIndex = i;
                }

                mMainMaxValue = Math.max(mMainMaxValue, mMainDraw.getMaxValue(point, i));
                mMainMinValue = Math.min(mMainMinValue, mMainDraw.getMinValue(point, i));

                if (!mMainChildDrawSelectedList.isEmpty()) {
                    for (var iChartDraw : mMainChildDrawSelectedList) {
                        mMainMaxValue = Math.max(mMainMaxValue, iChartDraw.getMaxValue(point, i));
                        mMainMinValue = Math.min(mMainMinValue, iChartDraw.getMinValue(point, i));
                    }
                }

            }
            if (!mChildDrawSelectedList.isEmpty()) {
                for (SimpleChartDraw simpleChartDraw : mChildDrawSelectedList) {
                    simpleChartDraw.mMinValue = Math.min(simpleChartDraw.mMinValue, simpleChartDraw.getMinValue(point, i));
                    simpleChartDraw.mMaxValue = Math.max(simpleChartDraw.mMaxValue, simpleChartDraw.getMaxValue(point, i));
                }
            }
        }

        //主图最大值和最小值 和 当前买卖价进行比较
        if (buyPrice != 0 && showBuyLine) {
            mMainMaxValue = Math.max(mMainMaxValue, buyPrice);
            mMainMinValue = Math.min(mMainMinValue, buyPrice);
        }
        if (sellPrice != 0 && showSellLine) {
            mMainMaxValue = Math.max(mMainMaxValue, sellPrice);
            mMainMinValue = Math.min(mMainMinValue, sellPrice);
        }

        //取消主图最大值和最小值 和 当前价进行比较
//        float currentPrice = getCurrentPrice();
//        mMainMaxValue = Math.max(mMainMaxValue, currentPrice);
//        mMainMinValue = Math.min(mMainMinValue, currentPrice);

        isMaxAndMinEqual = mMainMaxValue == mMainMinValue;
        float padding = (isMaxAndMinEqual ? mMainMaxValue : (mMainMaxValue - mMainMinValue)) * 0.05f;
        mMainMaxValue += padding;
        mMainMinValue -= padding;
        mMainScaleY = mMainRect.height() * 1f / (mMainMaxValue - mMainMinValue);

//        mChildScaleY = (mChildRect.height() - mValueHeigh) * 1f / (mChildMaxValue - mChildMinValue);

        if (!mChildDrawSelectedList.isEmpty()) {
            for (var simpleChartDraw : mChildDrawSelectedList) {
                simpleChartDraw.mChildScaleY = (simpleChartDraw.rect.height() - mValueHeigh) * 1f / (simpleChartDraw.mMaxValue - simpleChartDraw.mMinValue);
            }
        }

        if (mAnimator.isRunning()) {
            float value = (float) mAnimator.getAnimatedValue();
            mStopIndex = mStartIndex + Math.round(value * (mStopIndex - mStartIndex));
        }
        onScreenChange(true);
    }

    /**
     * 获取平移的最小值,也就是最左边的x
     *
     * @return
     */
    private float getMinTranslateX() {
        return -mDataLen + mWidth / mScaleX - mPointWidth / 2;
    }

    private float getMinTranslateX(float scaleX) {
        if (!isFullScreen()) {
            return getMaxTranslateX();
        }
        return -mDataLen + mWidth / scaleX - mPointWidth / 2;
    }

    /**
     * 获取平移的最大值
     *
     * @return
     */
    private float getMaxTranslateX() {
        return mPointWidth / 2;
    }

    @Override
    public int getMinScrollX() {
        return (int) -(mOverScrollRange / mScaleX);
    }

    public int getMaxScrollX() {
        return Math.round(getMaxTranslateX() - getMinTranslateX()) - getMinScrollX();
    }

    public int indexOfTranslateX(float translateX) {
        return indexOfTranslateX(translateX, 0, mItemCount - 1);
    }

    @Override
    public void drawMainLine(Canvas canvas, Paint paint, float startX, float startValue, float stopX, float stopValue) {
        canvas.drawLine(startX, getMainY(startValue), stopX, getMainY(stopValue), paint);
    }

    @Override
    public Object getItem(int position) {
        if (mAdapter != null && position < mAdapter.getCount()) {
            return mAdapter.getItem(position);
        } else {
            return null;
        }
    }

    @Override
    public float getX(int position) {
        if (mXs == null || mXs.length <= position) {
            return 0;
        }
        try {
            return mXs[position];
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(new Exception("BaseKChartView.getX e" + e.getMessage()));
        }
        return 0;
    }

    @Override
    public IAdapter getAdapter() {
        return mAdapter;
    }

    /**
     * 设置子图的绘制方法
     *
     * @param position
     */
    protected void setChildDraw(int position) {
        if (mChildDrawAllList.isEmpty()) {
            return;
        }
        mChildDrawSelectedList.clear();
        if (position >= 0) {
            mChildDrawSelectedList.add(mChildDrawAllList.get(position));
            initRect(1, getMeasuredHeight());
        } else {
            initRect(0, getMeasuredHeight());
        }
        invalidate();
    }

    protected void setChildDrawList(List<KlineOtherEnum> list) {
        if (mChildDrawAllList.isEmpty()) {
            return;
        }
        List<SimpleChartDraw> childDrawList = new ArrayList<>();
        for (KlineOtherEnum integer : list) {
            childDrawList.add(mChildDrawAllList.get(integer));
        }
        mChildDrawSelectedList.clear();
        mChildDrawSelectedList.addAll(childDrawList);
        initRect(1, getMeasuredHeight());
        invalidate();
    }

    protected void setMainChildDrawList(List<KlineMainEnum> list) {
        if (mMainChildDraws.isEmpty()) {
            return;
        }
        List<IChartDraw> mainChildDrawList = new ArrayList<>();
        for (KlineMainEnum klineMainEnum : list) {
            mainChildDrawList.add(mMainChildDraws.get(klineMainEnum));
        }
        mMainChildDrawSelectedList.clear();
        mMainChildDrawSelectedList.addAll(mainChildDrawList);
        initRect(1, getMeasuredHeight());
        invalidate();
    }

    /**
     * 设置子图的绘制方法
     *
     * @param position
     */
    protected void setMainChildDraw(int position) {
        if (position >= 0) {
            this.mMainChildDraw = mMainChildDraws.get(position);
            initRect(1, getMeasuredHeight());
        } else {
            mMainChildDraw = null;
            initRect(0, getMeasuredHeight());
        }
        invalidate();
    }

    @Override
    public void addChildDraw(KlineEnum name, IChartDraw childDraw) {
        mChildDrawAllList.put(name, (SimpleChartDraw) childDraw);
    }

    public void addMainChildDraw(KlineEnum name, IChartDraw childDraw) {
        mMainChildDraws.put(name, childDraw);
    }

    public IChartDraw getMainChildDraw(String name) {
        return mMainChildDraws.get(name);
    }

    /**
     * scrollX 转换为 TranslateX
     *
     * @param scrollX
     */
    private void setTranslateXFromScrollX(int scrollX) {
        mTranslateX = scrollX + getMinTranslateX(); // -306 + 1226 = 920
    }

    /**
     * 获取ValueFormatter
     *
     * @return
     */
    public IValueFormatter getValueFormatter() {
        return mValueFormatter;
    }

    /**
     * 设置ValueFormatter
     *
     * @param valueFormatter value格式化器
     */
    public void setValueFormatter(IValueFormatter valueFormatter) {
        this.mValueFormatter = valueFormatter;
    }

    /**
     * 获取DatetimeFormatter
     *
     * @return 时间格式化器
     */
    @Override
    public IDateTimeFormatter getDateTimeFormatter() {
        return mDateTimeFormatter;
    }

    /**
     * 设置dateTimeFormatter
     *
     * @param dateTimeFormatter 时间格式化器
     */
    public void setDateTimeFormatter(IDateTimeFormatter dateTimeFormatter) {
        mDateTimeFormatter = dateTimeFormatter;
    }

    /**
     * 格式化时间
     *
     * @param date
     */
    public String formatDateTime(Date date) {
        if (getDateTimeFormatter() == null) {
            setDateTimeFormatter(new TimeFormatter());
        }
        return getDateTimeFormatter().format(date);
    }

    /**
     * 格式化时间
     *
     * @param date
     */
    private DateFormat selectedDateFormat = new SimpleDateFormat("yy-MM-dd HH:mm");

    public String formatSelectedDateTime(Date date) {
        return selectedDateFormat.format(date);
    }

    /**
     * 获取主区域的 IChartDraw
     *
     * @return IChartDraw
     */
    public IChartDraw getMainDraw() {
        return mMainDraw;
    }

    /**
     * 设置主区域的 IChartDraw
     *
     * @param mainDraw IChartDraw
     */
    public void setMainDraw(IChartDraw mainDraw) {
        mMainDraw = mainDraw;
    }

    public void showFullScreen(boolean isShowFullScreen) {
        this.isShowFullScreen = isShowFullScreen;
    }

    public void showLogo(boolean isShowLogo) {
        this.isShowLogo = isShowLogo;
    }

    public void setFullScreenLeft(boolean isLeft) {
        this.isFullLeft = isLeft;
    }

    /**
     * 二分查找当前值的index
     *
     * @param translateX
     * @return
     */
    public int indexOfTranslateX(float translateX, int start, int end) {
        if (start > end) {
            return start;
        }
        if (end == start) {
            return start;
        }
        if (end - start == 1) {
            float startValue = getX(start);
            float endValue = getX(end);
            return Math.abs(translateX - startValue) < Math.abs(translateX - endValue) ? start : end;
        }
        int mid = start + (end - start) / 2;
        float midValue = getX(mid);
        if (translateX < midValue) {
            return indexOfTranslateX(translateX, start, mid);
        } else if (translateX > midValue) {
            return indexOfTranslateX(translateX, mid, end);
        } else {
            return mid;
        }
    }

    @Override
    public void setAdapter(IAdapter adapter) {
        if (mAdapter != null && mDataSetObserver != null) {
            mAdapter.unregisterDataSetObserver(mDataSetObserver);
        }
        mAdapter = adapter;
        if (mAdapter != null) {
            mAdapter.registerDataSetObserver(mDataSetObserver);
            mItemCount = mAdapter.getCount();
        } else {
            mItemCount = 0;
        }
        notifyChanged();
    }

    @Override
    public void startAnimation() {
        if (mAnimator != null) {
            mAnimator.start();
        }
    }

    @Override
    public void setAnimationDuration(long duration) {
        if (mAnimator != null) {
            mAnimator.setDuration(duration);
        }
    }

    /**
     * 设置表格行数
     *
     * @param gridRows
     */
    public void setGridRows(int gridRows) {
        if (gridRows < 1) {
            gridRows = 1;
        }
        mGridRows = gridRows;
        invalidate();
    }

    /**
     * 设置表格列数
     *
     * @param gridColumns
     */
    public void setGridColumns(int gridColumns) {
        if (gridColumns < 1) {
            gridColumns = 1;
        }
        mGridColumns = gridColumns;
        invalidate();
    }

    @Override
    public float xToTranslateX(float x) {
        return -mTranslateX + x / mScaleX;
    }

    //固定坐标转成屏幕坐标系坐标
    @Override
    public float translateXtoX(float translateX) {
        return (translateX + mTranslateX) * mScaleX;
    }

    @Override
    public float getTopPadding() {
        return mTopPadding;
    }

    @Override
    public int getChartWidth() {
        return mWidth;
    }

    @Override
    public int getTotalWidth() {
        return mTotalWidth;
    }

    @Override
    public boolean isShowCrossLine() {
        return isShowCrossLine;
    }

    @Override
    public void setShowCrossLineUntilTouch(boolean isShowCrossLine) {
        this.isShowCrossLine = isShowCrossLine;
    }

    @Override
    public boolean isEnableCrossLine() {
        return isEnableCrossLine;
    }

    @Override
    public void setEnableCrossLine(boolean isEnableCrossLine) {
        this.isEnableCrossLine = isEnableCrossLine;
        invalidate();
    }

    @Override
    public int getSelectedIndex() {
        return mSelectedIndex;
    }

    @Override
    public void setOnCrossMoveListener(OnCrossMoveListener l) {
        this.mOnCrossMoveListener = l;
    }

    @Override
    public void setOnScreenScrollChangeListener(OnScreenScrollChangeListener listener) {
        this.mOnScreenScrollChangeListener = listener;
    }

    @Override
    public void onCrossMoved(IKChartView view, Object point, int index, boolean isUserSelected, boolean left, boolean isTouch) {
        if (this.mOnCrossMoveListener != null) {
            mOnCrossMoveListener.onCrossMoved(view, point, index, isUserSelected, left, isTouch);
        }
    }

    @Override
    public void onScreenScrollChange(int startShowIndex, int stopShowIndex, String startIndexTimestamp) {
        if (this.mOnScreenScrollChangeListener != null) {
            mOnScreenScrollChangeListener.onScreenScrollChange(startShowIndex, stopShowIndex, startIndexTimestamp);
        }
    }

    public void setOnFullScreenIconClickListener(OnFullScreenIconClickListener onFullScreenIconClickListener) {
        this.mOnFullScreenIconClickListener = onFullScreenIconClickListener;
    }

    public void setOnKChartViewClickListener(OnKChartViewClickListener onKChartViewClickListener) {
        this.mOnKChartViewClickListener = onKChartViewClickListener;
    }

    /**
     * 数据是否充满屏幕
     *
     * @return
     */
    @Override
    public boolean isFullScreen() {
        return mDataLen >= mWidth / mScaleX;
    }

    @Override
    public void setOverScrollRange(float overScrollRange) {
        if (overScrollRange < 0) {
            overScrollRange = 0;
        }
        mOverScrollRange = overScrollRange;//滑动到最后一根k线距离边框的宽度
        mOverScrollRange1 = (int) -overScrollRange;//首次进入时最后一根k线距离边框的宽度
    }

    /**
     * 设置上方padding
     *
     * @param topPadding
     */
    public void setTopPadding(int topPadding) {
        mTopPadding = topPadding;
    }

    /**
     * 设置下方padding
     *
     * @param bottomPadding
     */
    public void setBottomPadding(int bottomPadding) {
        mBottomPadding = bottomPadding;
    }

    /**
     * 设置表格线宽度
     */
    public void setGridLineWidth(float width) {
        mGridPaint.setStrokeWidth(width);
    }

    /**
     * 设置表格线颜色
     */
    public void setGridLineColor(int color) {
//        mGridPaint.setColor(Color.BLACK);
        mGridPaint.setColor(color);
    }

    /**
     * 设置十字线宽度
     */
    public void setSelectedLineWidth(float width) {
        mSelectedLineWidth = width;
    }

    /**
     * 设置十字线颜色
     */
    public void setSelectedLineColor(int color) {
        mSelectedLinePaint.setColor(color);
    }

    /**
     * 设置刻度文字颜色
     */
    public void setTextColor(int color) {
        mTextPaint.setColor(color);
    }

    public void setBuyPriceTextBgColor(int color) {
        buyPriceTextBgColor = color;
    }

    /**
     * 设置文字大小
     */
    public void setTextSize(float textSize) {
        mTextPaint.setTextSize(textSize);
    }

    /**
     * 设置背景颜色
     */
    public void setBackgroundColor(int color) {
        mBackgroundPaint.setColor(color);
    }

    public void setMinuterStartColor(int minuterStartColor) {
        this.minuterStartColor = minuterStartColor;
    }

    public void setMinuteStopColor(int minuteStopColor) {
        this.minuteStopColor = minuteStopColor;
    }

    /**
     * 刻度文字
     * 价格颜色
     * 包括
     * 主图右侧价格
     * 成交量右侧量
     * 幅图没有价格
     */
    public void setPriceTextColor(int color) {
        mPriceTextPaint.setColor(color);
    }

    public void setPriceTextSize(float textSize) {
        mPriceTextPaint.setTextSize(textSize);
    }

    public void setPriceTextFont(Typeface tf) {
        if (tf != mPriceTextPaint.getTypeface()) {
            mPriceTextPaint.setTypeface(tf);
        }
    }

    /**
     * 时间颜色
     *
     * @param color
     */
    public void setTextTimeColor(int color) {
        mTimePaint.setColor(color);
    }

    public void setSelectedBgColor(int color) {
        mSelectedValueBackgroundPaint.setColor(color);
    }

    public void setSelectedValueTextColor(int color) {
        mSelectedValueTextTextPaint.setColor(color);
    }

    public void setCurrentPricePopBorderColor(int currentPricePopBorderColor) {
        mCurrentPricePopBorderPaint.setColor(currentPricePopBorderColor);
    }

    public void setCurrentPricePopBgColor(int currentPricePopBgColor) {
        mCurrentPricePopBgPaint.setColor(currentPricePopBgColor);
    }

    public void setCurrentPricePopTextColor(int currentPricePopTextColor) {
        mCurrentPricePopTextPaint.setColor(currentPricePopTextColor);
    }

    public void setCurrentPricePopTextSize(float textSize) {
        mCurrentPricePopTextPaint.setTextSize(textSize);
    }

    public void setCurrentPriceTextSize(float textSize) {
        mCurrentPriceTextPaint.setTextSize(textSize);
    }

    public void setSelectedValueTextSize(float textSize) {
        mSelectedValueTextPaint.setTextSize(textSize);
    }

    public void setLogoPaintAlpha(int alpha) {
        this.mLogoPaint.setAlpha(alpha);
    }

    private boolean isLeft = true;

    @Override
    public void showSelectedLine(MotionEvent e) {
        if (mXs == null || mXs.length == 0 || e == null) {
            return;
        }
        if(enableDragStatus) {
            return;
        }
        if (mItemCount != 0) {
            int lastIndex = mSelectedIndex;
            mSelectedIndex = calculateSelectedX(e.getX());
            //x y 变化重绘
            if (lastIndex != mSelectedIndex || mDownY != e.getY()) {
                Object item = getItem(mSelectedIndex);
                if (null == item) {
                    return;
                }
                //选中变化 震动加更新选中
                if (lastIndex != mSelectedIndex) {
                    isLeft = e.getX() < getWidth() / 2;
                    onCrossMoved(this, item, mSelectedIndex, true, isLeft, true);
                }
                isShowCrossLine = true;
                mDownY = e.getY();
                float selectedY = mDownY;
                if (mDownY > getHeight()) {
                    selectedY = getHeight();
                } else if (mDownY < mMainRect.top) {
                    selectedY = mMainRect.top;
                }
                mSelectedY = selectedY;
                invalidate();
            }
        } else {
            mSelectedIndex = -1;
        }

    }

    @Override
    public void showUnSelectedLine() {
        isShowCrossLine = false;
        if (mItemCount == 0) {
            return;
        }
        int index = mItemCount - 1;
        Object item = getItem(index);
        if (null == item) {
            return;
        }
        onCrossMoved(this, item, index, false, true, false);
        invalidate();
    }

    public void drawMinuteArea(Canvas canvas, Paint paint, float startX, float startValue, float stopX, float stopValue) {
        float mainY = getMainY(stopValue);
        Path path = ObjectPool.obtainPath();
        path.moveTo(startX, mMainRect.bottom);
        path.lineTo(startX, getMainY(startValue));
        path.lineTo(stopX, mainY);
        path.lineTo(stopX, mMainRect.bottom);
        path.close();
        drawMinuteArea(canvas, paint, path);
        ObjectPool.recyclePath(path);
    }

    public void drawMinuteArea(Canvas canvas, Paint paint, Path path) {
        int bottom = mMainRect.bottom;
        long cacheKey = ((long) minuterStartColor << 32) | (bottom & 0xFFFFFFFFL);
        LinearGradient cachedGradient = minuteGradientCache.get(cacheKey);
        if (cachedGradient == null) {
            cachedGradient = new LinearGradient(0, mMainRect.top, 0, bottom,
                    minuterStartColor, minuteStopColor, Shader.TileMode.CLAMP);
            minuteGradientCache.put(cacheKey, cachedGradient);
        }
        paint.setShader(cachedGradient);
        canvas.drawPath(path, paint);
    }

    private OnCurrentPricePositionListener mOnCurrentPricePositionListener;

    public void setOnCurrentPricePostionListener(OnCurrentPricePositionListener onCurrentPricePostionListener) {
        this.mOnCurrentPricePositionListener = onCurrentPricePostionListener;
    }

    private OnMainScaleYListener mOnMainScaleYListener;

    public void setOnMainScaleYListener(OnMainScaleYListener onMainScaleYListener) {
        mOnMainScaleYListener = onMainScaleYListener;
    }

    public int getStartIndex() {
        return mStartIndex;
    }

    public void setDrawCurrentPriceLine(boolean isDrawCurrentPriceLine) {
        this.isDrawCurrentPriceLine = isDrawCurrentPriceLine;
        invalidate();
    }

    public Rect getMainRect() {
        return mMainRect;
    }

    public int getRightTextContentWidth() {
        return mRightTextContentWidth;
    }

    @Override
    public boolean checkSelected(MotionEvent e) {
        if (mXs == null || mXs.length == 0) {
            return false;
        }
        if (mItemCount != 0) {
            int mSelectedIndex = calculateSelectedX(e.getX());
            KLineEntity item = (KLineEntity) getItem(mSelectedIndex);
            if (null == item) {
                return false;
            }
            float mDownY = e.getY();
            float mainHY = getMainY(item.getHighPrice());
            float mainLY = getMainY(item.getLowPrice());
            if (mDownY >= mainHY && mDownY <= mainLY) {
                return true;
            }
            return false;
        } else {
            mSelectedIndex = -1;
        }
        return false;
    }

    public void setBorderColor(@NonNull Integer color) {
        mCurrentPriceBorderPaint.setColor(color);
    }

    public void setCurrentTriColor(@NonNull Integer color) {
        mCurrentPriceBorderPaint.setColor(color);
    }

    /**
     * 滑动到开头
     */
    public void scrollStart() {
        setScrollX((int) (mOverScrollRange1 / mScaleX));
    }

    private int getPointDistance(int size) {
        return (int) ((size * mPointWidth) * mScaleX);
    }

    public void setOnKChartViewShowIndexChanged(OnKChartViewShowIndexChanged onKChartViewShowIndexChanged) {
        mOnKChartViewShowIndexChanged = onKChartViewShowIndexChanged;
    }

    private void setStartIndex(int startIndex) {
        this.mStartIndex = startIndex;
        if (mOnKChartViewShowIndexChanged != null) {
            mOnKChartViewShowIndexChanged.onShowStartChanged(startIndex, 0);
        }
    }

    private void setStopIndex(int stopIndex) {
        this.mStopIndex = stopIndex;
        if (mOnKChartViewShowIndexChanged != null) {
            mOnKChartViewShowIndexChanged.onShowStopChanged(stopIndex, mItemCount);
        }
    }

    public void setCurrentPrice(@Nullable Float currentPrice) {
        this.currentPrice = currentPrice;
    }

    public void setBuyPrice(float buyPrice) {
        this.buyPrice = buyPrice;
    }

    public void setSellPrice(float sellPrice) {
        this.sellPrice = sellPrice;
    }

    public void setDigit(int digit) {
        this.digit = digit;
    }

    public void setOnKChartViewCurrentPriceClickListener(OnKChartViewCurrentPriceClickListener currentPriceClickListener) {
        this.mCurrentPriceClickListener = currentPriceClickListener;
    }

    public interface OnCurrentPricePositionListener {
        void OnCurrentPricePosition(float currentPriceMainX, float currentPriceMainY);
    }

    public interface OnMainScaleYListener {
        void onMainScaleY(float mMainScaleY);
    }

    public interface OnFullScreenIconClickListener {
        void onFullScreenIconClick();

        void onFullScreenIconRectChanged(@NonNull RectF iconRect);
    }

    public interface OnTakeProfitStopLossChanged {
        static final int TYPE_TP = 0;
        static final int TYPE_SL = 1;

        /**
         * @param price 拖动的价格
         * @param type  0：止盈，1止损
         */
        void onTakeProfitStopLossChanged(float price, int type);

        /**
         * @param price     拖动的价格
         * @param openPrice 开盘价
         * @return 根据拖动价格计算盈亏
         */
        String onTpSlMoving(float price, float openPrice, String volume, String orderType);

        void onCancelTp();

        void onCancelSp();

        default void onPositionLineClick() {
        }

        default void onSLLineClick() {
        }

        default void onTPLineClick() {
        }
    }

    public interface OnKChartViewClickListener {
        //点击主图
        void onKChartViewMainClick();

        //点击幅图
        void onKChartViewChildClick();
    }

    public interface OnKChartViewShowIndexChanged {
        void onShowStartChanged(int startShowIndex, int startIndex);

        void onShowStopChanged(int stopShowIndex, int stopIndex);
    }

    public interface OnKChartViewCurrentPriceClickListener {
        boolean onCurrentPriceClick();
    }

    public interface OnKChartViewEdgeListener {
        void onLeftSide();

        void onRightSide();
    }

    public float getChildBottom() {
        return getHeight();
    }

    private KChartRect mkChartRect;

    public void setMkChartRect(KChartRect mkChartRect) {
        this.mkChartRect = mkChartRect;
    }

    public interface KChartRect {
        void rect(long left, double top, long right, double bottom, int startIndex, float startOffset, int stopIndex, float stopOffset);
    }

    public void onScreenChange(boolean isCalculateValue) {
        try {
            if (mItemCount > 1) {
                int mStartIndex = this.mStartIndex;
                float currentPointX = getX(mStartIndex);
                float mStartX = translateXtoX(currentPointX);

                int mStopIndex = this.mStopIndex;
                float currentPointX1 = getX(mStopIndex);
                float mStopX = translateXtoX(currentPointX1);

                KLineEntity kLineEntity = (KLineEntity) getItem(mStartIndex);
                if (isCalculateValue) {
                    onScreenScrollChange(this.mStartIndex, this.mStopIndex, kLineEntity.timestamp);
                }
//                long l = Long.parseLong(kLineEntity.timestamp);

//                        DataUtils.dateStringToTimestamp(kLineEntity.Date,"yyyy/MM/dd");

//                KLineEntity kLineEntity1 = (KLineEntity) getItem(mStopIndex);
//                String date1 = kLineEntity1.timestamp;
//                long l1 = Long.parseLong(date1);
//
//                float v = (l1 - l) / (mStopX - mStartX);
//
//                long t1 = l - ((long) mStartX - (long) mMainRect1.left) * (long) v;
//                long t2 = l1 + (-(long) mStopX + (long) mMainRect1.right) * (long) v;

//                Log.i("wj", "mStartIndex="+mStartIndex+", mStopIndex="+mStopIndex);
//                Log.d("onScreenChange","=mStartX=="+mStartX+"=currentPointX="+currentPointX+"=(t2-t1)=="+(t2-t1)+"===mMainRect1.left=="+mMainRect1.left+"===(mStopX - mStartX)="+(mStopX - mStartX));
//                float max = (mMainMaxValue - mMainMinValue) / (mMainRect.height()) * mMainRect1.height() + mMainMinValue;
                if (mkChartRect == null) {
                    return;
                }
                mkChartRect.rect(0, mMainMaxValue, 0, mMainMinValue, this.mStartIndex, mStartX, this.mStopIndex, mStopX);

            } else {

//                KLineEntity kLineEntity = (KLineEntity) getItem(mStartIndex);
//                String date = kLineEntity.timestamp;
//                long l = DataUtils.dateStringToTimestamp(kLineEntity.timestamp,"yyyy/MM/dd");
//                long l = Long.parseLong(kLineEntity.timestamp);
//                long step = mAdapter.getStep();
//
//                int left = mMainRect1.left;
//                int right = mMainRect1.right;
//                float v = (mPointWidth - mCandleGapWidth * 2) * mScaleX;
//                long v1 = (long) (step / v);
//
//                long v2 = (long) (v1 * (currentPointX - left));
//                long t1 = l - v2;
//                long v3 = (long) (v1 * (right - currentPointX));
//                long t2 = l + v3;
//                float max = (mMainMaxValue - mMainMinValue) / (mMainRect.height()) * mMainRect1.height() + mMainMinValue;
                if (mkChartRect == null) {
                    return;
                }
                int mStartIndex = this.mStartIndex;
                float currentPointX = translateXtoX(getX(mStartIndex));
                mkChartRect.rect(0, mMainMaxValue, 0, mMainMinValue, this.mStartIndex, currentPointX, this.mStopIndex, currentPointX);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<OrderRecordEntity> orderRecordEntityList = new ArrayList<>();

    public List<OrderRecordEntity> getOrderRecordEntityList() {
        return orderRecordEntityList;
    }

    public void setOrderRecordEntityList(List<OrderRecordEntity> orderRecordEntityList) {
        this.orderRecordEntityList = orderRecordEntityList;
        invalidate();
    }

    private String buyStr = "";
    private String sellStr = "";

    public void setBuySell(String sellStr, String buyStr) {
        this.sellStr = sellStr;
        this.buyStr = buyStr;
    }

    public boolean isShowOrder() {
        return showOrder;
    }

    public void setShowOrder(boolean showOrder) {
        this.showOrder = showOrder;
        invalidate();
    }

    public boolean isShowKline() {
        return showKline;
    }

    public void setShowKline(boolean showKline) {
        this.showKline = showKline;
//        ((MainDraw)mMainDraw).drawMaxAndMinValue();
        invalidate();
    }

    public boolean isShowCountDown() {
        return showCountDown;
    }

    public void setShowCountDown(boolean showCountDown) {
        this.showCountDown = showCountDown;
        invalidate();
    }

    public boolean isShowBuyLine() {
        return showBuyLine;
    }

    public void setShowBuyLine(boolean showBuyLine) {
        this.showBuyLine = showBuyLine;
        invalidate();
    }

    public boolean isShowSellLine() {
        return showSellLine;
    }

    public void setShowSellLine(boolean showSellLine) {
        this.showSellLine = showSellLine;
        invalidate();
    }

    public boolean isDrawPositionLine() {
        return isDrawPositionLine;
    }

    public void setDrawPositionLine(boolean drawPositionLine) {
        isDrawPositionLine = drawPositionLine;
        invalidate();
    }

    public boolean isShowTpLine() {
        return isShowTpLine;
    }

    public void setShowTpLine(boolean showTpLine) {
        isShowTpLine = showTpLine;
        invalidate();
    }

    public boolean isShowSlLine() {
        return isShowSlLine;
    }

    public void setShowSlLine(boolean showSlLine) {
        isShowSlLine = showSlLine;
        invalidate();
    }

    public boolean isShowInstantBuySell() {
        return showInstantBuySell;
    }

    public void setShowInstantBuySell(boolean showInstantBuySell) {
        this.showInstantBuySell = showInstantBuySell;
        invalidate();
    }

    public String formatTime(long milliseconds, long step) {
        if (step / 1000 >= 2592000) {
            milliseconds = getMillisToMonthEnd();
        }
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (step / 1000 < 3600) {
            return String.format("%02d:%02d", minutes, seconds % 60);
        } else if (step / 1000 < 86400) {
            return String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%02dD:%02dH", days, hours % 24);
        }
    }

    public long getMillisToMonthEnd() {
        Calendar calendar;
        if (!isUTC8) {
            calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Hong_Kong"));
        } else {
            calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        }
        // 获取当前月份的最大天数
        int maxDaysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置 Calendar 对象为月底的 23:59:59.999
        calendar.set(Calendar.DAY_OF_MONTH, maxDaysInMonth);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        // 计算当前时间到月底的毫秒数
        long currentTimeMillis = System.currentTimeMillis();
        long monthEndTimeMillis = calendar.getTimeInMillis();
        return monthEndTimeMillis - currentTimeMillis;
    }
}


