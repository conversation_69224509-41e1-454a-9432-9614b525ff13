package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.Typeface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.chart.EntityImpl.CandleImpl;
import com.github.tifezh.kchartlib.chart.impl.IKChartView;
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;
import com.github.tifezh.kchartlib.chart.impl.SimpleChartDraw;
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum;
import com.github.tifezh.kchartlib.utils.DpConstant;
import com.github.tifezh.kchartlib.utils.MonotoneX;
import com.github.tifezh.kchartlib.utils.ObjectPool;
import com.github.tifezh.kchartlib.utils.ViewUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 主图的实现类
 * Created by tifezh on 2016/6/14.
 */

public class MainDraw extends SimpleChartDraw<CandleImpl> {
    private static final String TAG = "MainDraw";

    private final float mPointWidth;
    private final float mCandleGapWidth;

    private final Paint mMinuteWhitePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private float mCandleLineWidth;
    public final Paint mGreenPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint mMaxMinTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    public final Paint mRedPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final Context mContext;

    private final Paint mMinuterAreaPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final List<Paint> paintList = new ArrayList<>();

    public static final int TYPE_MAIN_MINUTE = 1;   // 分时图类型
    public static final int TYPE_MAIN_CANDLE = 2;   // 蜡烛图类型

    private int mMainType = TYPE_MAIN_CANDLE;

    public static int mTextMargin;
    public static int mTextMarginStart;
    public static int mTextMarginBottom;

    public String mPopupDataTitle;
    public String mPopupOpenTitle;
    public String mPopupHighTitle;
    public String mPopupLowTitle;
    public String mPopupCloseTitle;
    public String mPopupChangeTitle;
    public String mPopupRangeTitle;
    public String mPopupVolTitle;
    private static final boolean IS_DRAW_SMOOTH_MINUTE_LINE = true;

    private boolean showMaxAndMin = true;

    /**
     * 构造方法
     *
     * @param context context
     */
    public MainDraw(Context context) {
        this.mContext = context;

        mPopupDataTitle = context.getString(R.string.kchart_view_time);
        mPopupOpenTitle = context.getString(R.string.kchart_view_open);
        mPopupHighTitle = context.getString(R.string.kchart_view_high);
        mPopupLowTitle = context.getString(R.string.kchart_view_low);
        mPopupCloseTitle = context.getString(R.string.kchart_view_close);
        mPopupChangeTitle = context.getString(R.string.kchart_view_change);
        mPopupRangeTitle = context.getString(R.string.kchart_view_range);
        mPopupVolTitle = context.getString(R.string.kchart_view_vol);

        mTextMargin = DpConstant.dp4();
        mTextMarginStart = DpConstant.dp12();
        mTextMarginBottom = ViewUtil.Dp2Px(mContext, 2);

        mPointWidth = context.getResources().getDimension(R.dimen.chart_candle_width) + context.getResources().getDimension(R.dimen.chart_candle_gap);
        mCandleGapWidth = context.getResources().getDimension(R.dimen.chart_candle_gap);
        mCandleLineWidth = context.getResources().getDimension(R.dimen.chart_candle_line_width);
        float mChartLineWidth = context.getResources().getDimension(R.dimen.chart_line_width);

        mMinuteWhitePaint.setColor(ContextCompat.getColor(context, R.color.chart_minuter_start));
        mMinuteWhitePaint.setStrokeWidth(mChartLineWidth);
        mMinuteWhitePaint.setStyle(Paint.Style.STROKE);

        mGreenPaint.setColor(ContextCompat.getColor(context, R.color.chart_green));
        mRedPaint.setColor(ContextCompat.getColor(context, R.color.chart_red));

        mMaxMinTextPaint.setColor(ContextCompat.getColor(context, R.color.color_000000));
        int mMaxMinTextSize = 10;
        mMaxMinTextPaint.setTextSize(ViewUtil.Dp2Px(mContext, mMaxMinTextSize));

        mMinuterAreaPaint.setStyle(Paint.Style.FILL);
        mMinuterAreaPaint.setAlpha(100);

        paintList.add(mMinuteWhitePaint);
        paintList.add(mGreenPaint);
        paintList.add(mMaxMinTextPaint);
        paintList.add(mRedPaint);


        paintList.add(mMinuterAreaPaint);
    }

    private final List<MonotoneX.Point> points = new ArrayList<>();


    @Override
    public void onDrawKStart(Canvas canvas, @NonNull IKChartView view) {
        points.clear();
        super.onDrawKStart(canvas, view);
    }

    @Override
    public void drawTranslated(@Nullable CandleImpl lastPoint, @NonNull CandleImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (isDrawMinute()) {
            if (!IS_DRAW_SMOOTH_MINUTE_LINE) {
                if (lastPoint != null) {
                    view.drawMainLine(canvas, mMinuteWhitePaint, lastX, lastPoint.getClosePrice(), curX, curPoint.getClosePrice());
                    if (view instanceof BaseKChartView) {
                        ((BaseKChartView) view).drawMinuteArea(canvas, mMinuterAreaPaint, lastX, lastPoint.getClosePrice(), curX, curPoint.getClosePrice());
                    }
                }
            }
        } else {
            drawCandle(view, canvas, curX, curPoint.getHighPrice(), curPoint.getLowPrice(), curPoint.getOpenPrice(), curPoint.getClosePrice());
        }
    }

    @Override
    public void onDrawScreenLine(@Nullable CandleImpl lastPoint, @NonNull CandleImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (isDrawMinute()) {
            //如果是startIndex 那么就清空points
            if (position == ((BaseKChartView) view).getStartIndex()) {
                if (lastPoint != null) {
                    points.add(new MonotoneX.Point(lastX, (view).getMainY(lastPoint.getClosePrice())));
                }
            }
            //画圆滑线时，先把点收集起来。
            points.add(new MonotoneX.Point(curX, (view).getMainY(curPoint.getClosePrice())));
        }
    }


    //画圆滑的分时线
    public void drawSmoothMinuteLine(Canvas canvas, IKChartView chartView, int bottom) {
        if (IS_DRAW_SMOOTH_MINUTE_LINE) {
            Path path = MonotoneX.add(null, points);
            canvas.drawPath(path, mMinuteWhitePaint);

            MonotoneX.Point lastPoint = points.get(points.size() - 1);
            path.lineTo(lastPoint.x, bottom);
            MonotoneX.Point startPoint = points.get(0);
            path.lineTo(startPoint.x, bottom);
            path.close();
            if (chartView instanceof BaseKChartView) {
                ((BaseKChartView) chartView).drawMinuteArea(canvas, mMinuterAreaPaint, path);
            }
            ObjectPool.recyclePath(path);
        }
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {

    }

    @Override
    public float getMaxValue(CandleImpl point, int position) {
        if (isDrawMinute()) {
            return point.getHighPrice();
        }
        return point.getHighPrice();
    }

    @Override
    public float getMinValue(CandleImpl point, int position) {
        if (isDrawMinute()) {
            return point.getLowPrice();
        }
        return point.getLowPrice();
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace != null) {
            for (Paint paint : paintList) {
                paint.setTypeface(typeFace);
            }
        }
    }

    @Override
    public void setValueFormatter(IValueFormatter valueFormatter) {

    }

    @Override
    public IValueFormatter getValueFormatter() {
        return null;
    }

    /**
     * 画Candle
     *
     * @param canvas canvas
     * @param x      x轴坐标
     * @param high   最高价
     * @param low    最低价
     * @param open   开盘价
     * @param close  收盘价
     */
    private void drawCandle(IKChartView view, Canvas canvas, float x, float high, float low, float open, float close) {
        float scaleX = ((BaseKChartView) view).getmScaleX();

        high = view.getMainY(high);
        low = view.getMainY(low);
        open = view.getMainY(open);
        close = view.getMainY(close);
        float r = (mPointWidth - (mCandleGapWidth / scaleX)) * 0.5f;
        float lineR = mCandleLineWidth / scaleX * 0.5f;
        if (open > close) {
            //实心
            canvas.drawRect(x - r, close, x + r, open, mGreenPaint);
            canvas.drawRect(x - lineR, high, x + lineR, low, mGreenPaint);
        } else if (open < close) {
            canvas.drawRect(x - r, open, x + r, close, mRedPaint);
            canvas.drawRect(x - lineR, high, x + lineR, low, mRedPaint);
        } else {
            canvas.drawRect(x - r, open, x + r, close + 1, mGreenPaint);
            canvas.drawRect(x - lineR, high, x + lineR, low, mGreenPaint);
        }
    }

    @Override
    public void onDrawKEnd(Canvas canvas, @NonNull IKChartView view) {
        super.onDrawKEnd(canvas, view);

    }

    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
        super.drawOnScreen(canvas, view);

        //画圆滑的分时线
        if (isDrawMinute()) {
            if (view instanceof BaseKChartView) {
                int bottom = ((BaseKChartView) view).getMainRect().bottom;
                drawSmoothMinuteLine(canvas, view, bottom);
            }
        }
    }

    private String removeZero(String value) {
        if (value == null){
            return "";
        }
        return new BigDecimal(value).stripTrailingZeros().toPlainString();
    }

    /**
     * 设置蜡烛宽度
     */
    public void setCandleWidth(float candleWidth) {
    }

    /**
     * 设置蜡烛线宽度
     */
    public void setCandleLineWidth(float candleLineWidth) {
        mCandleLineWidth = candleLineWidth;
    }


    public void setGreenColor(int color) {
        mGreenPaint.setColor(color);
    }

    public void setRedColor(int color) {
        mRedPaint.setColor(color);
    }

    public void setMinuteWhiteColor(int color) {
        mMinuteWhitePaint.setColor(color);
    }

    public void setMaxMinTextColor(int maxMinTextColor) {
        mMaxMinTextPaint.setColor(maxMinTextColor);
    }

    public void setShowMaxAndMin(boolean showMaxAndMin) {
        this.showMaxAndMin = showMaxAndMin;
    }

    public void setKlineMain(KlineMainEnum mainEnum) {
    }

    public void setMainType(int mainType) {
        mMainType = mainType;
    }

    public boolean isDrawMinute() {
        return mMainType == TYPE_MAIN_MINUTE;
    }

    public int getMainType() {
        return mMainType;
    }

    public void drawMaxAndMinValue(Canvas canvas, float mainWidth, float highX, float highY, float lowX, float lowY, String maxValue, String minValue) {
        if (!showMaxAndMin || isDrawMinute()) {
            return;
        }
        drawTextMaxAndMin(canvas, mainWidth, highX, highY, maxValue, mMaxMinTextPaint);
        drawTextMaxAndMin(canvas, mainWidth, lowX, lowY, minValue, mMaxMinTextPaint);

    }

    private void drawTextMaxAndMin(Canvas canvas, float viewWidth, float pointX, float pointY, String maxValue, Paint paint) {
        maxValue = removeZero(maxValue);

        Rect maxRect = new Rect();
        paint.getTextBounds(maxValue, 0, maxValue.length(), maxRect);
        int mLineLength = 8;
        int mLineTextSapce = 5;
        int mLineCandleSapce = 0;
        int arrowLineLength = 3;
        int arrowArc = 90;

        arrowLineLength = ViewUtil.Dp2Px(mContext, arrowLineLength);
        if (pointX > viewWidth / 2) {
            pointX -= ViewUtil.Dp2Px(mContext, mLineCandleSapce);

            //文字就在左边
            canvas.drawLine(pointX + ViewUtil.Dp2Px(mContext, -mLineLength), pointY, pointX, pointY, paint);
//            canvas.drawLine(pointX - (float) (arrowLineLength * Math.cos(Math.toRadians(arrowArc / 2d))), pointY - (float) (arrowLineLength * Math.sin(Math.toRadians(arrowArc) / 2d)), pointX, pointY, paint);
//            canvas.drawLine(pointX - (float) (arrowLineLength * Math.cos(Math.toRadians(arrowArc / 2d))), pointY + (float) (arrowLineLength * Math.sin(Math.toRadians(arrowArc / 2d))), pointX, pointY, paint);

            canvas.drawText(maxValue, pointX + ViewUtil.Dp2Px(mContext, -mLineLength - mLineTextSapce) - maxRect.width(), fixTextY(pointY, paint), paint);

        } else {
            pointX += ViewUtil.Dp2Px(mContext, mLineCandleSapce);
            //文字在右边
            canvas.drawLine(pointX, pointY, pointX + ViewUtil.Dp2Px(mContext, mLineLength), pointY, paint);
//            canvas.drawLine(pointX + (float) (arrowLineLength * Math.cos(Math.toRadians(arrowArc / 2d))), pointY - (float) (arrowLineLength * Math.sin(Math.toRadians(arrowArc / 2d))), pointX, pointY, paint);
//            canvas.drawLine(pointX + (float) (arrowLineLength * Math.cos(Math.toRadians(arrowArc / 2d))), pointY + (float) (arrowLineLength * Math.sin(Math.toRadians(arrowArc / 2d))), pointX, pointY, paint);

            canvas.drawText(maxValue, pointX + ViewUtil.Dp2Px(mContext, mLineLength + mLineTextSapce), fixTextY(pointY, paint), paint);
        }
    }

    /**
     * 解决text居中的问题
     */
    public float fixTextY(float y, Paint mTextPaint) {
        Paint.FontMetrics fontMetrics = mTextPaint.getFontMetrics();
        return (y + (fontMetrics.descent - fontMetrics.ascent) * 0.5f - fontMetrics.descent);
    }

}
